<?php


namespace App\Admin\Extensions\Tools;


use Encore\Admin\Grid\Tools\BatchAction;

class offlineExpGoOneOrder extends BatchAction
{
    public function script()
    {
        return <<<EOF
$('{$this->getElementClass()}').on('click', function() {
    layer.confirm("确定要下同一个快递订单?", 
    {   
        btn: ['输入单号', '直接下单', '取消']
    },function(){
        var data = {
            _token:LA.token,
            ids: selectedRows()
        }
        layer.prompt({id: 'offline_express_content',title: '请输入收到的配件，并确认',formType: 0, placeholder: '输入快递单号'}, 
                function(text, index){
                    if($('#go_exp_com').val()===""){
                        layer.tips("请填写快递公司",$('#go_exp_com'));
                        return;
                    }
                    var go_exp_com = $('#go_exp_com').val();
                    layer.close(index);
                    $.ajax({
                        type: "GET",
                        url: "{$this->resource}/offline_express_one_order",
                        data: {
                            go_exp_sn: text,
                            go_exp_com: go_exp_com,
                            ids: selectedRows()
                        },
                        dataType: "json",
                        success: function (data) {
                            layer.msg('下单成功'+data, {time:500},function(){
                                $('.grid-refresh').click();
                            });
                        }
                    });
                    layer.msg("下单成功")
                    window.location.reload();
                });
<!--          $(".layui-layer-content").empty();-->
         $("#offline_express_content").children(":first").attr('placeholder', '输入快递单号');
         $("#offline_express_content").append("<br/><input type=\"text\" id=\"go_exp_com\" placeholder=\'输入快递公司\' class=\"layui-layer-input\"/>");
 
    },function(){
        var data = {
            _token:LA.token,
            ids: selectedRows()
        }
        $.ajax({
            type: "GET",
            url: "{$this->resource}/offline_express_one_order",
            data: {
                       go_exp_sn: '',
                       go_exp_com: '',
                       ids: selectedRows()
                  },
            dataType: "json",
            success: function (data) {
                layer.msg('下单成功'+data, {time:500},function(){
                    $('.grid-refresh').click();
                });
            }                
        });
        layer.msg("下单成功")
        window.location.reload();
    },function(){
        alert("已取消");
    });
    
});
EOF;

    }
}


