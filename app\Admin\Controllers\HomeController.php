<?php

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Notice;
use App\Models\Order;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Column;
use Encore\Admin\Layout\Content;
use Encore\Admin\Layout\Row;
use Encore\Admin\Widgets\Box;
use Encore\Admin\Widgets\Chart\Bar;
use Encore\Admin\Widgets\Chart\Doughnut;
use Encore\Admin\Widgets\Chart\Line;
use Encore\Admin\Widgets\Chart\Pie;
use Encore\Admin\Widgets\Chart\PolarArea;
use Encore\Admin\Widgets\Chart\Radar;
use Encore\Admin\Widgets\Collapse;
use Encore\Admin\Widgets\InfoBox;
use Encore\Admin\Widgets\Tab;
use Encore\Admin\Widgets\Table;
use Encore\Admin\Auth\Permission;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{

    const ONE_WEEK_TIME = 3600 * 24 * 7;
    const ALL_USER_NOTICE_TYPE = 1;
    const REPAIR_USER_NOTICE_TYPE = 2;

    public function index()
    {

        return Admin::content(function (Content $content) {

            $content->header('首页');
            $content->description('首页显示');

            //不显示后台统计信息
            if (!Admin::user()->inroles(['Administrator', 'manager'])) {
                return ;
            }

            //不同的角色显示的数量应该不一样
            $content->row(function ($row) {
                if (!Admin::user()->inRoles(['afterSalesEngineer', 'endpoint'])) {
                    $endpointCount = DB::table('order')
                        ->where('status', '<>', Order::ORDER_CANCEL)
                        ->count(DB::raw('distinct(uid)'));
                    $row->column(2, new InfoBox('寄修总人数', 'users', 'aqua', '/admin', $endpointCount));
                    if (Admin::user()->inRoles(['administrator', 'manager', 'endpointManager'])) {
                        //获得寄修总台数
                        $warrantyCount = DB::table('order')
                            ->where('status', '<>', Order::ORDER_CANCEL)
                            ->count(DB::raw('distinct(barcode)'));
                        $row->column(2,
                            new InfoBox('寄修总台数', 'shopping-cart', 'green', '/admin/post_repair_manage', $warrantyCount));

                    }

                }
            });

        });
    }
}
