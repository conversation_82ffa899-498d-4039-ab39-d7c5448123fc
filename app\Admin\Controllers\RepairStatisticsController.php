<?php
/**
 * Created by PhpStorm.
 * User: Xian
 * Date: 2017/12/12
 * Time: 9:03
 */

namespace App\Admin\Controllers;


use App\Admin\Extensions\RepairStatisticsExporter;
use App\Models\ModelCategory;
use App\Services\Admin\RepairStatisticsService;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use Illuminate\Support\Facades\Input;

class RepairStatisticsController
{
    protected $service;

    protected $exporter;

    public function __construct(RepairStatisticsService $service, RepairStatisticsExporter $exporter)
    {
        $this->service = $service;
        $this->exporter = $exporter;

    }

    public function Summary()
    {
        return Admin::content(function (Content $content) {
            $content->header('终端销售额统计');
            $content->description('终端销售额统计');
            $createdAt = Input::get('created_at');
            $gender = Input::get('gender');
            $summary = $this->service->getSummary($gender, $createdAt);
            $totalCount = ['bill' => 0, 'worker' => 0];
            foreach ($summary as $key => $value) {
                $totalCount['bill'] += $value['bill_count'];
                $totalCount['worker'] += $value['repair_man_count'];
            }
            //获取代理下对应的保单数量统计gitc
            $content->body(view('admin/repair_statistics/summary', compact('summary', 'totalCount')));
        });
    }

    public function exportSummary()
    {
        $createdAt = Input::get('created_at');
        $gender = Input::get('gender');
        $this->exporter->exportSummary($gender, $createdAt);
    }
}