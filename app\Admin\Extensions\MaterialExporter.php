<?php

namespace App\Admin\Extensions;

use App\Models\Endpoint;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;
use App\Models\Agency;

class MaterialExporter extends AbstractExporter
{
    public function export()
    {
        $filename = '物料';
        $data = $this->getData();
        if (!empty($data)) {
            $titles = [
                '编码',
                '对应的旧编码',
                '名称',
                '规格',
                '顾客价格',
                '总代价格',
                '二代价格',
                '库存',
                '仓库来源',
                '最后更新',
            ];
            $formatData = $this->getFormatData($data);
            array_unshift($formatData, $titles);
            ExcelExportTrait::exportToExcel($filename, $formatData);
        }
    }

    private function getFormatData($data)
    {
        foreach ($data as $row) {
            $row = [
                $row['code'],
                $row['old_code'],
                $row['name'],
                $row['specification'],
                $row['price'],
                $row['price_first'],
                $row['price_second'],
                $row['quantity'],
                $row['from']==1 ? '新仓库' : '旧仓库',
                $row['updated_at'],
            ];
            $formatData[] = $row;
        }

        return $formatData;
    }


}