<?php

namespace App\Admin\Extensions\Express;

use App\Models\PostExpress;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use SimpleXMLElement;
use function GuzzleHttp\Psr7\str;
use App\Admin\Extensions\Express\BaseExpress;
use Barryvdh\Debugbar\Facade as Debugbar;

/**
 * 批量取号生产环境地址：https://211.156.195.15/iwaybillno-web/a/iwaybillBatch/receive
 * 订单接入生产环境地址：https://211.156.195.15/ebiistandard-job/a/ChinaPostApi/OrderCreate
 * 批量取号、订单接入生产环境秘钥：b2aaZH1Cnjf27
 *
 * 物流轨迹查询生产环境地址：http://211.156.195.11/mailTrackGjcx/mailTrackGjcxTwswn/plus
 * 物流轨迹查询生产环境秘钥与测试环境保持一致
 */
class EmsExpress extends BaseExpress
{
    const APP_KEY_ROUTE = "E29F083084F6AE6E";
    const APP_KEY_ORDER = "key123xydJDPT";

    private static function get_yz_env(): string
    {
        return env('YZ_ENV');
    }

    private static function get_yz_env_is_release(): bool
    {
        return self::get_yz_env() === 'release';
    }

    private static function get_receive_url(): string
    {
        if (!self::get_yz_env_is_release()) {
            return 'https://211.156.197.233/iwaybillno-web/a/iwaybillBatch/receive';
        } else {
            return 'https://211.156.195.15/iwaybillno-web/a/iwaybillBatch/receive';
        }
    }

    private static function get_route_url(): string
    {
        if (!self::get_yz_env_is_release()) {
            return 'http://211.156.197.242:8080/querypush-gjcx/mailTrackGjcx/mailTrackGjcxTwswn/plus';
        } else {
            return 'http://211.156.195.11/mailTrackGjcx/mailTrackGjcxTwswn/plus';
        }
    }

    private static function get_order_url(): string
    {
        if (!self::get_yz_env_is_release()) {
            return 'https://211.156.197.233/ebiistandard-job/a/ChinaPostApi/OrderCreate';
        } else {
            return 'https://211.156.195.15/ebiistandard-job/a/ChinaPostApi/OrderCreate';
        }
    }

    private static function get_route_api_secret(): string
    {
        if (!self::get_yz_env_is_release()) {
            return 'E29F083084F6AE6E';
        } else {
            return 'E29F083084F6AE6E';
            //return '0D4147E80EE0498C';
        }
    }

    private static function get_order_api_secret(): string
    {
        if (!self::get_yz_env_is_release()) {
            return 'key123xydJDPT';
        } else {
            return 'b2aaZH1Cnjf27';
        }
    }

    private static function get_com_id_param(): string
    {
        if (!self::get_yz_env_is_release()) {
            return 'SDDM-LOGISTICS';
        } else {
            return 'ZSDSL';
        }
    }

    private static function get_sender_no(): string
    {
        if (!self::get_yz_env_is_release()) {
            return '1100094212134';
        } else {
            return '90000002493356';
        }
    }

    private static function get_customer_no(): string
    {
        if (!self::get_yz_env_is_release()) {
            return '90000001885440';
        } else {
            return '90000002493356';
        }
    }

    public function get_route($tracking_number = null)
    {
        $app_key = self::get_route_api_secret();
        $url = self::get_route_url();
        $msgBody = json_encode(["traceNo" => $tracking_number]);
        $dataDigest = md5($msgBody . $app_key, false);
        // dump($dataDigest);
        $dataDigest = base64_encode($dataDigest);
        // dump(base64_encode('a9fb35a79d615fe659063466fe33d35e'));
        // dump($dataDigest);
        $param = [
//            'sign' => 's0sZY6',
            'sendID' => 'ZSDSL',
            'proviceNo' => '99',
            'serialNo' => date('YmdHis') . $tracking_number,
            'msgKind' => 'ZSDSL_JDPT_TRACE',
            'sendDate' => date('YmdHis'),
            'receiveID' => 'JDPT',
//            'param'=>[['number'=>$tracking_number]],
            'batchNo' => 999, // $tracking_number,
            'dataType' => 1,
            'dataDigest' => $dataDigest,
            'msgBody' => $msgBody
        ];
//        $url = $url."?".http_build_query($param);
        $client = new Client();
        $resp = $client->request('post', $url, [
            'headers' => [
                'Content-type' => 'application/x-www-form-urlencoded'
            ],
            'form_params' => $param
        ]);
        Debugbar::info(compact('url', 'app_key', 'param', 'resp'));
        if ($resp->getStatusCode() !== 200) {
            return false;
        }
        return json_decode($resp->getBody()->getContents(), true);
    }

    public function create_order($data): array
    {
        $ret = self::get_billno();
        if (!$ret) {
            return ['ok' => 0, "msg" => "访问外部接口出错"];
        }

        if ($ret['result'] == "false") {
            return ['ok' => 0, "msg" => json_encode($ret, JSON_UNESCAPED_UNICODE)];
        }

        $waybill_no = explode(",", $ret['waybill_no'])[0];
        $data['waybill_no'] = $waybill_no;
        $data['one_bill_flag'] = 0;
        $url = self::get_order_url();
        $xml = self::yz_create_express_xml($data);
//        dd($xml);
        $data_digest = $xml . self::get_order_api_secret();
        $data_digest = md5($data_digest, true);
        $data_digest = base64_encode("$data_digest");

        $param = [
            'logistics_interface' => $xml,
            'data_digest' => $data_digest,
            'msg_type' => 'ORDERCREATE',
            'ecCompanyId' => self::get_com_id_param()
        ];
        Debugbar::info(['url' => $url, 'xml' => $xml, 'param' => $param]);

        $client = new Client([
            'verify' => false,
        ]);
        // $url = $url."?".http_build_query($param);
        $resp = $client->request('post', $url, [
            'headers' => [
                'Content-type' => 'application/x-www-form-urlencoded'
            ],
            'form_params' => $param
        ]);
        if ($resp->getStatusCode() !== 200) {
            return ["ok" => 0, 'msg' => "访问第三方接口失败"];
        }
        $resp_content = $resp->getBody()->getContents();
        Debugbar::info(['create_order' => $resp_content]);
        $resp = simplexml_load_string($resp_content);
        $order = json_decode(json_encode($resp), TRUE);
//        dd($order);
        if ($order['responseItems']['response']["success"] == "false") {
            return ['ok' => 0, "msg" => $order['responseItems']['response']['reason']];
        }
        return ['ok' => 1, "readboy_sn" => $order['responseItems']['response']['txLogisticID'], 'exp_sn' => $waybill_no];
    }

    public function get_billno()
    {
        $url = self::get_receive_url();
        $xml = self::yz_batch_get_waybillNo_xml();
        $data_digest = $xml . self::get_order_api_secret();
        $data_digest = md5($data_digest, true);
        $data_digest = base64_encode("$data_digest");
//        $data_digest = urlencode($data_digest);
//        dd($data_digest);
        $param = [
            'logistics_interface' => $xml,
            'data_digest' => $data_digest,
            'msg_type' => 'BatchGetWaybillNo',
            'ecCompanyId' => self::get_com_id_param()
        ];
        Debugbar::info(['url' => $url, 'xml' => $xml, 'param' => $param]);

        $client = new Client([
            'verify' => false,
        ]);
        // $url = $url."?".http_build_query($param);
        // dump($url);
        $resp = $client->request('post', $url, [
            'headers' => [
                'Content-type' => 'application/x-www-form-urlencoded'
            ],
            'form_params' => $param
        ]);
        if ($resp->getStatusCode() !== 200) {
            return false;
        }
        $resp_content = $resp->getBody()->getContents();
        Debugbar::info(['YZ_ENV' => self::get_yz_env(), 'url' => $url, 'get_billno' => $resp_content]);
//        dump($resp->getBody()->getContents());
//        $response = json_decode($resp->getBody()->getContents(), true);
//        dump($response);
//        return $resp;
        $resp = simplexml_load_string($resp_content);
        // dump($resp);
        return json_decode(json_encode($resp), TRUE);
    }

    private function yz_create_express_xml($data)
    {
        //处理数据
        $data['orderid'] = self::create_exp_sn(explode(',', $data['sn'])[0]);
        //保存快递下单数据
        $data['exp_sn'] = $data['waybill_no'];
        // self::yz_store_express($data);
        self::store_express('邮政', 3, $data);
        $xmlStr = <<<XML
<?xml version="1.0" encoding="UTF-8"?><OrderNormals></OrderNormals>
XML;
        $xml = new SimpleXMLElement($xmlStr);
        $OrderNormal = $xml->addChild("OrderNormal");
        $OrderNormal->addChild('created_time', date('Y-m-d H:i:s'));
        $OrderNormal->addChild('ecommerce_no', self::get_com_id_param());
        $OrderNormal->addChild('ecommerce_user_id', self::get_com_id_param());
        $OrderNormal->addChild('sender_type', 1);
        //$OrderNormal->addChild('sender_no', '1100094212134');
        $OrderNormal->addChild('sender_no', self::get_sender_no());
        $OrderNormal->addChild('inner_channel', 0);
        $OrderNormal->addChild('logistics_order_no', $data['orderid']);
        $OrderNormal->addChild('waybill_no', $data['waybill_no']);
        $OrderNormal->addChild('one_bill_flag', $data['one_bill_flag']);
        $OrderNormal->addChild('contents_attribute', 3);
        $OrderNormal->addChild('product_type', 1);
        $OrderNormal->addChild('none', "");
        $OrderNormal->addChild('project_id', self::get_com_id_param());
        $OrderNormal->addChild('none', "");
        $sender = $OrderNormal->addChild('sender');
        $sender->addChild('name', $data['j_contact']);
        $sender->addChild('mobile', $data['j_tel']);
        $sender->addChild('prov', $data['j_province']);
        $sender->addChild('city', $data['j_city']);
        $sender->addChild('county', $data['j_county']);
        $sender->addChild('address', $data['j_address']);

        $receiver = $OrderNormal->addChild('receiver');
        $receiver->addChild('name', $data['d_contact']);
        $receiver->addChild('mobile', $data['d_tel']);
        $receiver->addChild('prov', $data['d_province']);
        $receiver->addChild('city', $data['d_city']);
        $receiver->addChild('county', $data['d_county']);
        $receiver->addChild('address', $data['d_address']);
        $cargos = $OrderNormal->addChild("cargos");
        $Cargo = $cargos->addChild("Cargo");
        $Cargo->addChild("cargo_name", '读书郎教育产品（含锂电池）');
        $OrderNormal->addChild("logistics_provider", "B");
        return $xml->asXML();
    }

    private function yz_batch_get_waybillNo_xml()
    {
        $xmlStr = <<<XML
<?xml version="1.0" encoding="UTF-8"?><BatchGetWaybillNo></BatchGetWaybillNo>
XML;

        $xml = new SimpleXMLElement($xmlStr);
        $xml->addChild('CreatedTime', date('Y-m-d H:i:s'));
        //$xml->addChild('CustomerNo', '90000001885440');
        $xml->addChild('CustomerNo', self::get_customer_no());
        if (self::get_yz_env_is_release()) {
            // 正式
            $xml->addChild('MailType', 6);
            $xml->addChild('count', 1);
            $xml->addChild('eventSource', 'ZSDSL');
        } else {
            // 测试
            $xml->addChild('MailType', 6);
            $xml->addChild('count', 1);
            $xml->addChild('eventSource', 'SDDM-LOGISTICS');
        }
        return $xml->asXML();
    }

    private function yz_store_express($data = null)
    {
        $sns = explode(',', $data['sn']);
        foreach ($sns as $sn) {
            $express = new PostExpress();
            $express->exp_sn = $data['waybill_no'];
            $express->pr_sn = $sn;
            $express->com = '邮政';
            $express->readboy_sn = $data['orderid'];
            $express->type = isset($data['type']) ? $data['type'] : 0;
            $express->pay_method = isset($data['pay_method']) ? $data['pay_method'] : 0;
            $express->data = $data;
            $express->com_type = 3;
            $express->save();
        }
    }
}
