<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\OrderExporter;
use App\Admin\Extensions\Tools\Button;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Models\AgentOrderCorrelation;
use App\Models\BrokenScreenInsurance;
use App\Models\Machine;
use App\Models\MachineAccessory;
use App\Models\MachineAccessoryRelation;
use App\Models\MachineAccessoryTree;
use App\Models\MachineMalfunction;
use App\Models\Material;
use App\Models\Order;

use App\Models\OrderExtend;
use App\Models\OrderLog;
use App\Models\PostRepairAccessory;
use App\Models\PostRepairMalfunction;
use App\Models\PostRepairMaterial;
use App\Models\PostRepairUsedMaterial;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\MessageBag;

class EndpointRepairManagerController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修订单管理');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修订单');
            $content->description('编辑');

            $content->body($this->form($id)->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修订单查看');
            $order = Order::where(['id' => $id])->first();
            $order_extend = OrderExtend::where(['sn' => $order->sn])->first();

            $pr_material = DB::table('pr_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_material.mat_id')
                ->leftjoin('machine_malfunction', 'machine_malfunction.id', '=', 'pr_material.malfunction_id')
                ->leftjoin('material', 'material.id', '=', 'pr_material.material_id')
                ->where('pr_material.pr_sn', $order['sn'])
                ->select('machine_accessory_tree.title as title', 'machine_malfunction.title as malfunction', 'material.price as price', 'pr_material.count as count')
                ->get()
                ->toArray();
            $pr_used_material = DB::table('pr_used_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_used_material.mat_id')
                ->leftjoin('material', 'material.id', '=', 'pr_used_material.material_id')
                ->where('pr_used_material.pr_sn', $order['sn'])
                ->select('material.name as name', 'material.price as price', 'pr_used_material.count as count',
                    'material.code as code', 'material.old_code as old_code', 'material.specification as specification', 'material.from as from')
                ->get()
                ->toArray();
            $content->body(view('admin/post_repair/view', compact('order', 'pr_material', 'pr_used_material', 'order_extend')));

        });
    }


    public function log($id)
    {
        $data = DB::table('order_log')
            ->leftjoin('admin_users', 'order_log.admin', '=', 'admin_users.id')
            ->where('order_log.pr_sn', $id)
            ->get()->toArray();
        return view('admin/post_repair_manage/log', compact('data'));
    }

    public function printf($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('打印检测报告');
            $order = Order::where(['id' => $id])->first();
            $post_malfunction = DB::table('pr_malfunction')
                ->join('machine_malfunction', 'machine_malfunction.id', '=', 'pr_malfunction.malfunction_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_malfunction.title')->toArray();
            $malfunction = implode('，', $post_malfunction);
            $post_accessory = DB::table('pr_accessory')
                ->join('machine_accessory', 'machine_accessory.id', '=', 'pr_accessory.mar_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_accessory.title')->toArray();
            $accessory = implode('，', $post_accessory);
            $content->body(view('admin/post_order/print', compact('order', 'malfunction', 'accessory')));

        });
    }

    public function cancel(Request $request)
    {
        foreach (Order::find(request()->get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修订单');
            $content->description('创建');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOF
            //查看快递路由信息
            $('.express_route_fresh').click(function(){
                var id = $(this).attr('value');
                layer.open({
                      type: 2,
                      title: '快递路由',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['1000px', '60%'],
                      content: 'express/express_route_fresh?readboy_sn='+id //iframe的url
                });
            });

            //查看订单日志
            $('.log_view').click(function(){
                var id = $(this).attr('value');
                layer.open({
                      type: 2,
                      title: '日志',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['1000px', '60%'],
                      content: 'post_repair_manage/log/'+id //iframe的url
                });
            });
        $("input[name='sn']").focus();
//        $("input[name='come_exp_sn']").focus();
EOF;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
//            $grid->model()->where('status', '>=', 200);
            $grid->disableCreation();
            $grid->disableExport();
//            $grid->exporter(new OrderExporter());
            //快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => [['order.type', '=', '3']],
                ],
                1 => [
                    'name' => '待审核',
                    'param' => [['status', '=', Order::WAIT_AUDIT], ['order.type', '=', '3']],
                ],
                2 => [
                    'name' => '待检测',
                    'param' => [['status', '=', Order::COME_SURE], ['order.type', '=', '3']],
                ],
                3 => [
                    'name' => '待支付',
                    'param' => [['status', '=', Order::CHECK_FINISH], ['order.type', '=', '3']],
                ],
                4 => [
                    'name' => '待维修',
                    'param' => [['status', '=', Order::PAY_FINISH], ['order.type', '=', '3']],
                ],
                5 => [
                    'name' => '待发货',
                    'param' => [['status', '=', Order::REPAIR_FINISH], ['order.type', '=', '3']],
                ],
            ];
            //筛选条数
            foreach ($option as $key => $value) {
                $option[$key]['count'] = Order::where($value['param'])->count();
            }
            //自定义状态快捷筛选按钮
            $grid->tools(function ($tools) use ($option) {
                $tools->append(new QuickPickTool($option));
                $tools->batch(function ($batch) {
//                    $batch->disableDelete();
                    $batch->add('取消订单', new Cancel());
                });
            });
            //根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])->orderBy('id', 'desc');
            }
            $grid->model()->where($option[-1]['param'])->orderBy('id', 'desc');

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('come_exp_sn', '快递单号');
                $filter->where(function ($query){
                    $input = $this->input;
                    $query->whereHas('agent_sn', function ($query) use ($input){
                            $query->where('agent_order_sn', $this->input);
                    });
                }, '大单单号');
                $filter->like('sn', '维修订单号');
                $filter->like('barcode', 'S/N码');
                $filter->like('name', '联系人');
                $filter->like('status', '订单状态')
                    ->select(Order::STATUS);
                $filter->equal('model_name', '产品型号')->select(
                    Machine::orderBy('name')->pluck('name', 'name')->all()
                );
                $filter->like('need_invoice','是否开具发票')->select([1=>'是',0=>'否']);
                $filter->between('created_at', '订单提交日期')->datetime();
                $filter->between('updated_at_last', '回寄下单日期')->datetime();
//                $filter->scope('order_log.date');
                // 多条件查询
                $filter->between('receive_time', '签收时间')->datetime();
                $filter->like('type', '寄修类型')->select(Order::post_repair_type);
                $filter->like('auditor_user.name','审核人');
                $filter->is('repeat_order', '二次寄修')->select(Order::is_direct_sales);

            });
            $grid->id('ID')->sortable();
            $grid->uid('UID');
            Order::order_priority_column($grid);
            $grid->sn('寄修订单编号');
            $grid->column('come_exp_sn', '寄来快递单号');
            $grid->column('go_exp_sn', '寄去快递单号');
            $grid->barcode('S/N码');
            $grid->name('联系人');
            $grid->phone('用户联系方式');
//            $grid->endpoint()->name('寄修售后点');
            $grid->amount('总金额(元)');
            $grid->pay_amount('支付金额(元)');
//
            $grid->created_at('订单提交时间');
//            $grid->updated_at_last('订单回寄时间')->sortable();
            $grid->column('expense.updated_at', '核销时间')->display(function($value) {
                if ($value) {
                    return $value;
                } else {
                    return '未核销';
                }
            });
//            $grid->connect('联系状态')->editable('select', Order::CONNECT);
            $grid->status('订单状态')->display(function ($status) {
                $s = Order::STATUS;
                if (array_key_exists($status, $s)) {
                    return $status . $s[$status];
                } else {
                    return "————";
                }
            });
            $grid->check_user()->name('检测人')->display(function ($name) {
                if ($name == '')
                    return '———';
                return $name;
            });
            $grid->repeat_order('二次维修')->display(function ($value){
                if ($value == 1){
                    return '是';
                }else{
                    return '否';
                }
            });
            $grid->type('寄修类型')->display(function ($value){
                return Order::post_repair_type[$value];
            });
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $status = $actions->row->status;
                $c = 'post_repair/view/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">查看 </span></a>';
                $actions->append($html);
                $data = $actions->row;
//                $c = 'post_repair_manage/log/' . $data->sn;
//                $html = '<a href="javascript:void(0);" value="'.$data->sn.'" class="log_view"><span style="color:green">日志 </span></a>';
//                $actions->append($html);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id = null)
    {
        if (!Admin::user()->inroles(['Administrator'])) {
            return Admin::content(function(Content $content){
                $error = new MessageBag([
                    'title'   => '无法访问',
                    'message' => '没有权限！',
                ]);
                return $content->body($error);
            });
        }
        $script = <<<EOF
            //改变配件物料选项
            $(document).on('change', ".mat_id", function () {
                var target = $(this).closest('.fields-group').find(".material_id");
                var type = $('.type').val();
                var charge_type = $(this).parent().parent().prev().find('.charge_type').val();
                $.get("/admin/repair_check_material?q="+this.value+"&type="+ type + "&charge_type="+charge_type, 
                function (data) {
                    target.find("option").remove();
                    defaultdata = [{"id":0,"text":"请选择"}];
                    setdata = $.map(data, function (d) {
                        d.id = d.id;
                        d.text = d.text;
                        return d;
                    });
                    data = setdata;
                    $(target).select2({
                        data: data
                    }).trigger('change');
                });
            });
            $(document).on('change', ".charge_type", function () {
                var target = $(this).closest('.fields-group').find(".material_id");
                var type = $('.type').val();
                var charge_type = $(this).val();
                var mat_id = $(this).parent().parent().next().find('.mat_id').val();

                if (mat_id != 0){
                    $.get("/admin/repair_check_material?q="+mat_id+"&type="+ type + "&charge_type="+charge_type, 
                    function (data) {
                        target.find("option").remove();
                        defaultdata = [{"id":0,"text":"请选择"}];
                        setdata = $.map(data, function (d) {
                            d.id = d.id;
                            d.text = d.text;
                            return d;
                        });
                        data = setdata;
                        $(target).select2({
                            data: data
                        }).trigger('change');
                    });
                }

            });
            //别问我为什么不直接操作模板div,是因为用了DocumentFragment,操作不了,不是我用的!
            $(document).ready(function() {
                var in_period = $('.in_period').val()
                if (in_period != '1') {
                    calculatePrice();
                }
//              calculatePrice();
              function total(){
                var staff = Number($('#staff_cast').val().replace(/,/g, ""));
//                var staff = 0;
                var exp_cast = 0;
//                var exp_cast = Number($('#exp_cast').val().replace(/,/g, ""));
                var accessory = Number($('#accessory_cast').val().replace(/,/g, ""));
                var accessory_amount = Number($('#accessory_amount').val().replace(/,/g, ""));
                $('#amount').val(staff + accessory_amount + exp_cast);
                $('#pay_amount').val(staff + accessory + exp_cast);
              }

              $(document).on('change','.staff_cast',function(){
                total();
              });


              //统计配件价格
              function calculatePrice(){
                let totalPrice = 0;
                let accessoryAmount = 0;
                $('.material_id:visible').each(function(){
                  let optionText = $(this).find("option:selected").text();
                  let price = optionText.split('价格 : ')[1];
                  let count = $(this).parent().parent().next().find('#count').val();
                  let is_charge = $(this).parent().parent().next().next().find('.is_charge').val();
                  accessoryAmount += price * count;
                  if (price == null){
                    price = 0;
                    }
                  if (is_charge == '1'){
                    totalPrice += price * count;
                  }
                });
//                实际收取配件费用
                $('#accessory_cast').val(totalPrice);
//                总配件费用
                $('#accessory_amount').val(accessoryAmount);
                total();
              }

              //下拉框变化
              $(document).on('change','.material_id',function(){
                calculatePrice();
              });
              $(document).on('change','.is_charge',function(){
                calculatePrice();
              });
              //加减数量按钮点击
              $('.has-many-repair_material-forms').on('click','button',function(){
                calculatePrice();
              });
              //移除按钮点击
              $(document).on('click','.remove',function(){
                calculatePrice();
              });
            });
EOF;

        Admin::script($script);
        return Admin::form(Order::class, function (Form $form) use ($id) {

            $form->text('name', '寄修人');
            $form->text('phone', '联系方式');
            $form->text('province', '省');
            $form->text('city', '市');
            $form->text('district', '区');
            $form->text('address', '详细地址');
            $form->display('sn', '寄修订单号');
            $form->text('barcode', 'S/N码');
//            $form->barcode('sn', '寄修条码条码')->options(['width'=>1,]);
            $form->display('model_name', '机器型号');
            $form->select('in_period', '保修状态')->options(Order::in_period);
            $form->hidden('sn', '寄修订单号');
            $form->divider('');
            $form->select('reason', '损坏原因')->options(Order::reason)->value(function ($reason) {
                return $reason;
            });
            $form->text('deal', '维修方式');
            $form->divider('');
            $form->display('type', '寄修方式')->with(function ($value) {
                if ($value == 2){
                    return '<span style="color:red">代理商寄修</span>';
                }elseif ($value == 3){
                    return '<span style="color:red">终端代寄</span>';
                }
                return '<span style="color:red">用户寄修</span>';

            });
            $form->hidden('type');
            $form->display('has_screen_insurance', '是否有碎屏保')->with(function ($value){
                if ($value == 1){
                    return '<span style="color:red">有碎屏保</span>';
                }
                return '无';
            });
            $form->hidden('has_screen_insurance');
            $form->display('in_si_period', '碎屏保状态')->with(function ($value){
                return Order::in_period[$value];
            });
            $form->hidden('in_si_period');
            $screen_insurance = Order::where('id', '=', $id)->value('used_screen_insurance');
            if ($screen_insurance == 0){
                $form->select('used_screen_insurance','是否使用碎屏保')->options([0=>'不使用', 1=>'使用']);
            }else{
                $html =<<<EOF
                <span style="color: red">此机器已使用过碎屏保</span>
EOF;
                $form->html($html);
            }
            $type = Order::where('id', '=', $id)->value('type');
            $form->hasMany('repair_material', '维修配件列表',
                function (Form\NestedForm $form) use ($id, $type) {
                    $modelId = Order::where('id', '=', $id)->value('model_id');
                    $pr_sn = Order::where('id', '=', $id)->value('sn');
                    $form->select('charge_type', '收费类型')->options(function ($value) use ($type){
                        if ($type == 2){
                            return Order::charge_type;
                        }else{
                            return [1=>'客户价格'];
                        }
                    });
                    $form->select('mat_id', '维修配件')->options(MachineAccessoryTree::model_options($modelId))->load('malfunction_id', admin_url('repair_check_malfunction'));
                    $form->select('malfunction_id', '配件故障')->options(function ($value) {
                        $data = MachineMalfunction::where('id', $value)->get()->toArray();
                        $ret = array();
                        if (count($data) > 0) {
                            foreach ($data as $d) {
                                $ret[$d['id']] = $d['title'];
                            }
                        }
                        return $ret;
                    });
                    $form->select('material_id', '维修物料')->options(function ($value) use ($pr_sn, $form, $type){
                        $data = Material::where('id', $value)->get()->toArray();
                        $ret = array();
                        $charge_type = PostRepairMaterial::where([['pr_sn', '=', $pr_sn], ['material_id', '=', $value]])->value('charge_type');

                        if (count($data) > 0) {
                            foreach ($data as $d) {
                                $from = $d['from'] == 1 ? '新仓库' : '旧仓库';
                                if ($charge_type == 2){
                                    $ret[$d['id']] = $d['name'].$d['specification'].'|编码:'.$d['code'].'|旧编码:'.$d['old_code'].
                                        '|仓库:'.$from.'|库存:'.$d['quantity'].'|价格 : '.$d['price_first'];
                                }else{
                                    $ret[$d['id']] = $d['name'].$d['specification'].'|编码:'.$d['code'].'|旧编码:'.$d['old_code'].
                                        '|仓库:'.$from.'|库存:'.$d['quantity'].'|价格 : '.$d['price'];
                                }
                            }
                        }
                        return $ret;
                    });
                    $form->number('count', '数量')->default(1);
                    $form->select('is_charge', '是否收费')->options([1=>'收费', 0=>'不收费'])->default(1);
                });

            $form->currency('accessory_amount', '总配件价格')->symbol('￥');
            $form->currency('accessory_cast', '实际收取配件价格')->symbol('￥');
            if ($id && $form->model()->find($id)->in_period !== 1) {
                $rb_exp_sn = $form->model()->find($id)->rb_come_exp_sn;
                $exp_sn = $form->model()->find($id)->come_exp_sn;
                $exp_cast = Express::query_order($rb_exp_sn, $exp_sn);
//                $exp_cast = 15;
            } else {
                $exp_cast = 0;
            }
            $form->currency('staff_cast', '快递费用')->symbol('￥')->default(intval($exp_cast))->help('快递费用  '.$exp_cast);
            $form->hidden('exp_coat', '快递费用')->value(0);
//            $form->display('staff_cast', '检测费用')->value($exp_cast)->help('快递费用  '.$exp_cast. '元');
            $form->currency('amount', '总计')->symbol('￥');
            $form->currency('pay_amount', '待支付金额')->symbol('￥')->help('修改前金额：');

            $form->text('receive_case', '收到的配件');
            $form->text('deal_remark', '备注');
            $form->hidden('check_man');

            $form->select('connect', '联系状态')->options(Order::CONNECT);
            $form->select('status', '订单状态')->options(Order::STATUS);
            $form->hidden('audit_status');
            $form->saving(function (Form $form) use ($exp_cast) {
//                $form->check_man = Admin::user()->id;
                //不记录日志
                $form->no_log = 1;
//                dd($form->pay_amount);
                //微信支付如果有发起过支付请求，必须保证金额等信息一致，所以有金额变动，需要去掉内部支付单号重新生成
                if ($form->pay_amount != $form->model()->pay_amount && !empty($form->model()->rb_pay_sn) && empty($form->model()->is_paid)) {
                    $data = ['rb_pay_sn' => '', 'pay_com' => 0, 'pay_sn' => ''];
                    DB::table('order')->where('sn', $form->model()->sn)->update($data);
                }

                if ($form->status == 800){
//                    dd($form->status);
                    $data = ['go_sure' => 1, 'updated_at_last' => date('Y-m-d H:i:s')];
                    DB::table('order')->where('sn', $form->model()->sn)->update($data);
//                    $form->go_sure = 1;
//                    $form->updated_at_last = date('Y-m-d H:i:s');
                }
                if ($form->status == -200 ) {
                    $form->audit_status = 2;
                }
                if ($form->status == 200 ) {
                    $form->audit_status = 1;
                }

//                有碎屏保  碎屏保在保修期内  使用碎屏保
                if ($form->in_si_period == 1 && $form->has_screen_insurance == 1 && $form->used_screen_insurance == 1
                    && empty($form->model()->used_screen_insurance)){
                    $data = BrokenScreenInsurance::where('barcode', $form->barcode)->whereIn('status', [300, 400, 500])
                        ->first();
//                    dd($data);
                    if ($data->status == 300){
                        $data->status = 400;
                        $data->save();
                    }
                }
                if($form->has_screen_insurance == 0 && $form->used_screen_insurance == 1){
                    $form->used_screen_insurance = 0;
                }
            });
            //快递费不保存,保存成staff_cast
            $form->ignore(['exp_cast']);

            $form->saved(function($form) {
                //同步处理已使用的物料
                PostRepairUsedMaterial::where('pr_sn', $form->sn)->delete();
                $pr_material = PostRepairMaterial::where('pr_sn', $form->sn)->get();
                if (count($pr_material) > 0) {
                    foreach ($pr_material as $material) {
//                        dd($material);
                        $new_used_material = new PostRepairUsedMaterial();
                        $new_used_material->pr_sn = $material->pr_sn;
                        $new_used_material->mat_id = $material->mat_id;
                        $new_used_material->material_id = $material->material_id;
                        $new_used_material->is_charge = $material->is_charge;
                        $new_used_material->charge_type = $material->charge_type;
                        $new_used_material->count = $material->count;
                        $new_used_material->save();
                    }
                }
//                dd(PostRepairUsedMaterial::where('pr_sn', $form->sn)->get()->toArray());
                $order = $form->model();
//                dd($order->status == Order::CHECK_FINISH && $order->in_period == 1 && $order->reason == 2 && empty(intval($order->pay_amount)));
                //处理已检测的0元订单，直接修改为已支付的状态
                if ($order->status == Order::CHECK_FINISH && empty(floatval($order->pay_amount))) {
                    $save = Order::where('sn', $order->sn)->first();
                    $save->status = Order::PAY_FINISH;
                    $save->save();
                }
            });
        });
    }
}