<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class OptionalAccessory extends Model
{

    protected $table = 'optional_accessory';

    public function category()
    {
        return $this->belongsTo(OptionalAccessoryCategory::class, 'category_id', 'id');
    }

    public static function getCategoryCount(array $ids = null): Collection
    {
        if ($ids) {
            return self::whereIn('category_id', $ids)
                ->groupBy('category_id')
                ->selectRaw('category_id, count(*) as count')
                ->pluck('count', 'category_id');
        }
        return self::groupBy('category_id')
            ->selectRaw('category_id, count(*) as count')
            ->pluck('count', 'category_id');
    }
}
