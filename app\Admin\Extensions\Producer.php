<?php


namespace App\Admin\Extensions;
use MQ\Model\TopicMessage;
use MQ\MQClient;

class Producer
{
    private $client;
    private $producer;

    public function __construct()
    {
        $HTTP_ENDPOINT = 'http://1106633749770355.mqrest.cn-shenzhen.aliyuncs.com';
        $ACCESS_KEY = 'LTAI4Fn5cptD9sjsJbmUqYew';
        $SECRET_KEY = '******************************';
        $this->client = new MQClient(
        // 设置HTTP接入域名（此处以公共云生产环境为例）
            "${HTTP_ENDPOINT}",
            // AccessKey 阿里云身份验证，在阿里云服务器管理控制台创建
            "${ACCESS_KEY}",
            // SecretKey 阿里云身份验证，在阿里云服务器管理控制台创建
            "${SECRET_KEY}"
        );

        // 所属的 Topic
        $topic = "care";
        // Topic所属实例ID，默认实例为空NULL
        $instanceId = "MQ_INST_1106633749770355_BcCu1ohA";

        $this->producer = $this->client->getProducer($instanceId, $topic);
    }

    public function warranty_return_or_exchange_producer($content)
    {
        try
        {
            $publishMessage = new TopicMessage(

                "${content}"// 消息内容

            );
            $publishMessage->setMessageTag('post_repair_exchange');
                // 设置属性
            $publishMessage->putProperty("a", "i");
                // 设置消息KEY
            $publishMessage->setMessageKey("MessageKey");
            $result = $this->producer->publishMessage($publishMessage);

            print "Send mq message success. msgId is:" . $result->getMessageId() . ", bodyMD5 is:" . $result->getMessageBodyMD5() . "\n";

        } catch (\Exception $e) {
            print_r($e->getMessage() . "\n");
        }
    }

    public function export_data($content)
    {
        try
        {
            $publishMessage = new TopicMessage(

                "${content}"// 消息内容

            );
            $publishMessage->setMessageTag('py_export');
            // 设置属性
            $publishMessage->putProperty("a", "i");
            // 设置消息KEY
            $publishMessage->setMessageKey("MessageKey");
            $result = $this->producer->publishMessage($publishMessage);

            print "Send mq message success. msgId is:" . $result->getMessageId() . ", bodyMD5 is:" . $result->getMessageBodyMD5() . "\n";

        } catch (\Exception $e) {
            print_r($e->getMessage() . "\n");
        }
    }

}
