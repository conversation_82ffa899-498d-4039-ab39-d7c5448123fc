<?php
/**
 * Created by PhpStorm.
 * User: qzl
 * Date: 2019/8/25
 * Time: 8:46
 */

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Machine;
use App\Models\Order;
use App\Models\OrderExtend;
use App\Models\RepairStaff;
use App\Models\Warranty;
use Encore\Admin\Auth\Database\Administrator;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use Illuminate\Support\Facades\Input;
use App\Services\Admin\PostAllotService;
use Illuminate\Support\MessageBag;
use App\Models\OrderLog2;
use App\User;

//SN查询页面
class PostAllotController extends Controller
{

    protected $service;

    public function __construct(PostAllotService $service)
    {
        $this->service = $service;
    }

    public function index($id)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
            if (!empty($data['sn']) && !empty($data['check_man'])) {
                $order = Order::where('sn', '=', $data['sn'])->first();

                if($order->status < 410){
                    $error = new MessageBag([
                        'title'     => '错误',
                        'message'   => '未客服知会订单不能分派'
                    ]);
                    return back()->with(compact('error'))->withInput();
                }

                // $order->check_man = $data['check_man']; 
                // $order->save();

                // 记录检测人员
                Order::where('id', $order->id)->update(array('updated_at' => date('Y-m-d H:i:s') , 'check_man' => $data['check_man']));

                // 记录订单分派时间,分派人
                if($order->status == 410){
                    OrderExtend::where('sn' , $data['sn'])->update(array('allocation_time' => date('Y-m-d H:i:s') , 'allocation_uid' => Admin::user()->id));
                }
                //记录分派日志
                $check_man_name=User::where('id', '=', $data['check_man'])->value('name');
                $orderLogData = array(
                    'pr_sn' => $data['sn'],
                    'pr_status' => $order->status,
                    'log_status' => $order->status,
                    'log_from' => 'web',
                    'uid' => $order->uid,
                    'admin' => Admin::user()->id,
                    'title' => '订单分派，被分派人: '.$check_man_name,
                    'date' => date('Y-m-d H:i:s')
                );

                // 备用日志
                OrderLog2::insert($orderLogData);
                
                return back()->withInput();
            }
        }
        return Admin::content(function (Content $content) use ($id) {
            $content->header('订单分派');
            $staff = Administrator::find($id);
            $username = $staff->name;
            $content->description($username);
            $sn = Input::get('sn');
            $search_order = $this->service->search_order($sn);
            $user_order = $this->service->user_order($id);
//            dd($user_order[0]);
            $content->body(view('admin/post_allot/index', compact(array('id', 'username', 'sn', 'search_order', 'user_order'))));
        });
    }

    public function getMachineInformation()
    {
        return Admin::content(function (Content $content) {
            $content->header('SN查询页面');
            $content->description('SN查询页面');

            $barcode = Input::get('barcode');
            $machineInformation = $this->service->getMachineInfo($barcode);
            $content->body(view('admin/sn/machine', compact('machineInformation')));
        });
    }

    public function getMachinePurchaseRecordBySn()
    {
        $sn = Input::get('sn');
        $record = Warranty::where('barcode', '=', $sn)->orderBy('created_at', 'desc')->first();
        if ($record) {
            $record->model_id = Machine::where('name', '=', $record->model)->value('model_id');
            $record->buy_date = date('Y-m-d', strtotime($record->buy_date));
        }

        return $record;
    }

}