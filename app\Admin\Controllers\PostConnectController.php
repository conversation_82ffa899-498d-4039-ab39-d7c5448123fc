<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\Tools\Button;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\QuickPickTool;

use App\Models\ChinaArea;
use App\Models\Endpoint;
use App\Models\Order;
use App\Models\OrderRemark;
use App\Models\OrderExtend;
use App\Models\PostExpress;
use App\Models\PostRepairEndpoint;
use App\Models\RepairStaff;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;

use App\Http\Controllers\Controller;

use Encore\Admin\Controllers\ModelForm;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Request;

class PostConnectController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('联系用户');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('联系用户');
            $content->description('');

            $content->body($this->form()->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('联系用户--查看');
            $order = Order::where(['id' => $id])->first();

            $content->body(view('admin/post_check/view', compact('order')));
        });
    }


    public function cancel(Request $request)
    {
        foreach (Order::find(request()->get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    public function post_sign_in()
    {
        $ids = Request::get('ids');
        $id = Request::get('id');
        if (!empty($ids)) {
            foreach (Order::find($ids) as $post) {
                $post->status = Order::COME_SURE;
                $post->save();
            }
        }
        if (!empty($id)) {
            $post = Order::find($id);
            $post->status = Order::COME_SURE;
            $post->save();
        }
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOF
            $('input[name="come_exp_sn"]').focus();
            //询问框
            $('.post_sign_in').click(function(){
                var id = $(this).attr('value');
                layer.confirm('确定签收该订单？', {
                    btn: ['确认签收', '取消']
                }, function(){
                    $.get('/admin/post_sign_in?id='+id, function(result){
                        layer.msg('签收成功'+result, {time:500},function(){
                            $('.grid-refresh').click();
                        });
                    });
                }, function(){
                    layer.msg('取消签收');
                });
            });

            //拨打电话
            $('.call_phone').click(function(){
                var id = $(this).attr('value');
                var login_name = $(this).attr('login_name');
                var pass_word = $(this).attr('pass_word');
                layer.open({
                      type: 2,
                      title: '拨打电话',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['300px', '30%'],
                      content: '/packages/edb_bar/phoneBar/phonebar.html?loginType=sip&loginName='+login_name+'&password='+pass_word+'&callNumber='+id //iframe的url
                });
            });

            // 支付备注
            $('.pay_remark').click(function(e){
                sn = e.currentTarget.dataset.sn
                var html = '<div class="box box-info">'+
                            '<div class="box-header with-border">备注<\/div>'+
                            '<div class="box-body" style="min-height:300px;min-width:500px" >'+
                                '<table class="table">'+
                                    '<tr>'+
                                        '<th style="text-align: center;" >时间<\/th>'+
                                        '<th style="text-align: center;" >订单状态<\/th>'+
                                        '<th style="text-align: center;" >联系状态<\/th>'+
                                        '<th style="text-align: center;" >操作人<\/th>'+
                                        '<th style="text-align: center;" >备注<\/th>'+
                                    '</tr>'+
                                    '<tbody id="tbody"><\/tbody>'+
                                '<\/table>'+
                            '<\/div>'+
                            '<\/div>';
                html += '<div style="display:flex">'+
                            '<input style="margin:1%" name="remarkRadio" value="需要再次通知" type="radio"> 需要再次通知'+
                            '<input style="margin:1%" name="remarkRadio" value="已沟通未协商好" type="radio"> 已沟通未协商好'+
                            '<input style="margin:1%" name="remarkRadio" value="顾客有争议" type="radio"> 顾客有争议'+
                            '<input style="margin:1%" name="remarkRadio" value="终端有争议" type="radio"> 终端有争议'+
                            '<input style="margin:1%" name="remarkRadio" value="等待回复" type="radio"> 等待回复'+
                        '<\/div>';
                html += '<div style="display:flex">'+
                            '<textarea id="remarkTextarea" placeholder="请输入备注,不超过100字" cols="70">'+
                            '<\/textarea>'+
                            '<a class="btn btn-sm btn-primary" type="button" id="saveRemark" >确定<\/a>'+
                        '<\/div>';


                // 获取备注内容
                $.ajax({
                    url: '/admin/post_connect/getRemarkInfo',
                    data: {
                        sn: sn,
                        type:2
                    },
                    type: 'get',
                    dataType: 'json',
                    success: function(res){
                        console.log(res)
                        var tbodyHtml = '';
                        $('#tbody').html('');
                        $.each(res , function(i , v){
                            tbodyHtml +='<tr>'+
                                            '<td style="text-align: center;">'+v.created_at+'</td>'+
                                            '<td style="text-align: center;">'+v.status_name+'</td>'+
                                            '<td style="text-align: center;">'+v.connect_name+'</td>'+
                                            '<td style="text-align: center;">'+v.name+'</td>'+
                                            '<td style="text-align: center;">'+v.remark+'</td>'+
                                        '</tr>';
                        })

                        $('#tbody').append(tbodyHtml);
                    }
                })
                $.fancybox.open(html);

                $("input[name='remarkRadio']").change(function(){
                    $('#remarkTextarea').val($("input[name='remarkRadio']:checked").val());
                })

                $('#saveRemark').click(function(){
                    pay_remark = $('#remarkTextarea').val()
                    if(!pay_remark){
                        layer.msg('备注不能为空');
                        return false;
                    }
                    $.ajax({
                        url: '/admin/post_connect/setPayRemark',
                        data: {
                            sn: sn,
                            pay_remark: pay_remark
                        },
                        type: 'get',
                        dataType: 'json',
                        success: function(res){
                            layer.msg(res.info);
                            if(res.status == 1){
                                var newTbodyHtml = '<tr>'+
                                                        '<td style="text-align: center;color:#29db6f">'+res.data.created_at+'<\/td>'+
                                                        '<td style="text-align: center;color:#29db6f">'+res.data.status_name+'<\/td>'+
                                                        '<td style="text-align: center;color:#29db6f">'+res.data.connect_name+'<\/td>'+
                                                        '<td style="text-align: center;color:#29db6f">'+res.data.name+'<\/td>'+
                                                        '<td style="text-align: center;color:#29db6f">'+res.data.remark+'<\/td>'+
                                                    '<\/tr>';

                                $('#tbody').append(newTbodyHtml);
                                $('.grid-refresh').click();
                            }
                        },
                        error: function(res){
                            console.log('error');
                        }
                    })
                })
            })
            
EOF;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
            $agent_order_sn = Input::get('agent_order_sn');
            $grid->disableCreation();
            $grid->disableExport();
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('come_exp_sn', '寄来快递单号')->setPlaceHolder('光标移到此处扫码');
                $filter->like('sn', '维修订单号');
                $filter->like('barcode', 'S/N码')->setPlaceHolder('光标移到此处扫码');
                $filter->equal('connect', '联系状态')->select(Order::CONNECT);
                //$filter->between('updated_at', '订单支付日期')->datetime();
            });
            //快捷筛选条
            if ($agent_order_sn != null) {
                $option = [
                    -1 => [
                        'name' => '全部',
                        'param' => [['status', '=', Order::CHECK_FINISH_IS_TELL], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                    ],
                ];
                //筛选条数
                foreach ($option as $key => $value) {
                    $option[$key]['count'] = Order::where($value['param'])
                        ->rightjoin('agent_order_correlation as aoc', 'order.sn', '=', 'aoc.order_sn')
                        ->count();
                }
                $grid->tools(function ($tools) use ($option) {
                    //自定义状态快捷筛选按钮
                    $tools->append(new QuickPickTool($option));
                    $button = <<<EOF
                     <div class="btn-group" style="margin-right:-65%; margin-top: 10px;">
                      <a href ="agent_order" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 返回
                      </a >
                    </div >
EOF;
                    $tools->append($button);

                    $tools->batch(function ($batch) {
                        $batch->add('取消订单', new Cancel());
                    });
                });

                //根据自定义状态按钮搜索数据
                if (Request::get('quick_pick')) {
                    $quick_pick = Request::get('quick_pick');
                    $grid->model()
                        ->rightjoin('agent_order_correlation as aoc', 'order.sn', '=', 'aoc.order_sn')
                        ->where($option[$quick_pick]['param'])
                        ->orderBy('repeat_order', 'desc')
                        ->orderBy('id', 'desc');
                }
                $grid->model()
                    ->select('order.*')
                    ->rightjoin('agent_order_correlation as aoc', 'order.sn', '=', 'aoc.order_sn')
                    ->where($option[-1]['param'])
                    ->orderBy('updated_at', 'desc')
                    ->orderBy('repeat_order', 'desc')
                    ->orderBy('id', 'desc');

            } else {
                $option = [
                    -1 => [
                        'name' => '全部',
                        'param' => [['status', '=', Order::CHECK_FINISH_IS_TELL], ['order.type', '!=', '2']],
                    ],
                ];
                //筛选条数
                foreach ($option as $key => $value) {
                    $option[$key]['count'] = Order::where($value['param'])->count();
                }
                $grid->tools(function ($tools) use ($option) {
                    //自定义状态快捷筛选按钮
                    $tools->append(new QuickPickTool($option));
                    $tools->batch(function ($batch) {
                        $batch->add('取消订单', new Cancel());
                    });
                });

                //根据自定义状态按钮搜索数据
                if (Request::get('quick_pick')) {
                    $quick_pick = Request::get('quick_pick');
                    $grid->model()->where($option[$quick_pick]['param'])->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');
                }
                $grid->model()->where($option[-1]['param'])->orderBy('updated_at', 'desc')->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');

            }

            $grid->id('ID')->sortable();
            Order::order_priority_column($grid);
            $grid->sn('寄修单号');
            $grid->come_exp_sn('寄来快递单号');
            //$grid->barcode('S/N码');
            //$grid->model_name('机型');
            $grid->column('S/N码-机型')->display(function () {
                return $this->barcode . '<br/>' . $this->model_name;
            });
            $grid->name('联系人');
            $grid->address('寄件人地址')->display(function ($value) {
                return $this->province . $this->city . $this->district . $this->address;
            });
            $grid->pay_amount('待支付金额');
            $grid->accessory_cast('维修配件费用');
            $grid->optional_accessory_cast('自选配件费用')->display(function ($optional_accessory_cast) {
                $status_str = ' ';
                if ($this->optional_accessory_status != Order::OPTIONAL_ACCESSORY_STATUS_NORMAL &&
                    array_key_exists($this->optional_accessory_status, Order::OPTIONAL_ACCESSORY_STATUS)) {
                    $status_str = Order::OPTIONAL_ACCESSORY_STATUS[$this->optional_accessory_status];
                    $status_str = ' (' . $status_str . ')';
                }
                return '<span>' . $optional_accessory_cast . $status_str . '</span>';
            });
            $grid->staff_cast('快递费用');
            $grid->phone('电话拨打')->display(function ($value) {
                $user_id = Admin::user()->id;
                $staff = RepairStaff::where('user_id', $user_id)->first();
                if (!empty($staff) && !empty($staff->service_phone_username) && !empty($staff->service_phone_password)) {
                    $login_name = $staff->service_phone_username;
                    $pass_word = $staff->service_phone_password;
                    return '<a href="javascript:void(0);" class="call_phone" value="' . $value . '" login_name="' . $login_name . '" pass_word="' . $pass_word . '">' . $value . '</a>';
                } else {
                    return $value;
                }
            });
//            $grid->endpoint()->name('寄修售后点');
            $grid->updated_at('最后更新');
            $grid->status('订单状态')->display(function ($status) {
                $s = Order::STATUS;
                if (array_key_exists($status, $s)) {
                    return $s[$status];
                } else {
                    return "————";
                }
            });
            $grid->connect('联系状态')->editable('select', Order::CONNECT);

            $grid->column('order_extend.pay_remark', '支付备注')->display(function ($pay_remark) {
                $pay_remark = $pay_remark ? '<u>' . $pay_remark . '</u>' : '<u style="color:#dd1144">Empty</u>';
                return '<a href="JavaScript:void(0);" data-sn="' . $this->sn . '" class="pay_remark">' . $pay_remark . '</a>';
            });
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $status = $actions->row->status;
                $c = 'post_repair/view/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">查看</span></a>';
                $actions->append($html);
                if ($status == 300) {
                    $html = '<a href="javascript:void(0);" class="post_sign_in" value="' . $actions->row->id . ' target="_blank"><span style="color:orange">签收 </span></a>';
                    $actions->append($html);
                }
                if ($status == 400) {
                    $c = '';
                    $html = '<a href="' . $c . '" class="print"><span style="color:orange">打印 </span></a>';
                    $actions->append($html);
                }
            });
        });
    }

    /**
     * 获取备注信息
     */
    public function getRemarkInfo()
    {
        $sn = request()->input('sn');
        $type = request()->input('type');

        // 获取支付备注
        $orderRmarkInfo = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
            ->where('order_remark.sn', $sn)
            ->where('order_remark.type', $type)
            ->orderBy('order_remark.id', 'desc')
            ->select('au.name', 'order_remark.*')
            ->get();

        foreach ($orderRmarkInfo as $key => $value) {
            $orderRmarkInfo[$key]['status_name'] = Order::STATUS[$value['status']];
            $orderRmarkInfo[$key]['connect_name'] = Order::CONNECT[$value['connect']];
        }

        return $orderRmarkInfo;
    }

    /**
     * 保存备注信息
     */
    public function setPayRemark()
    {
        $sn = request()->input('sn');
        $pay_remark = request()->input('pay_remark');

        if ($orderExtendInfo = OrderExtend::where('sn', $sn)->first()) {
            $updateExtend = array(
                'pay_remark' => $pay_remark,
                'updated_at' => date('Y-m-d H:i:s'),
            );
            OrderExtend::where('sn', $sn)->update($updateExtend);
        } else {
            $insertExtend = array(
                'sn' => $sn,
                'pay_remark' => $pay_remark,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            );
            OrderExtend::insert($insertExtend);
        }

        // 获取订单信息
        $orderInfo = Order::where('sn', $sn)->first();

        $data = array(
            'sn' => $sn,
            'operator' => Admin::user()->id,
            'type' => 2,
            'status' => $orderInfo['status'],
            'connect' => $orderInfo['connect'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'remark' => $pay_remark
        );

        if (OrderRemark::insert($data)) {

            $data['status_name'] = Order::STATUS[$data['status']];
            $data['connect_name'] = Order::CONNECT[$data['connect']];
            $data['name'] = Admin::user()->name;

            return array('status' => 1, 'info' => '添加成功', 'data' => $data);
        } else {
            return array('status' => 0, 'info' => '添加失败');
        }
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Admin::form(Order::class, function (Form $form) {
//            $form->display('sn');
            $form->hidden('connect');
            $form->hidden('order_extend.pay_remark');
            $form->saving(function (Form $form) {
                if (!empty($form->order_extend['pay_remark'])) {
                    $sn = $form->model()->sn;

                    $order_extend = OrderExtend::where('sn', $sn)->count();
                    if ($order_extend == 0) {
                        $new_order_extend = new OrderExtend();
                        $new_order_extend->sn = $sn;
                        $new_order_extend->pay_remark = $form->order_extend['pay_remark'];
//                        dd($new_order_extend);
                        $new_order_extend->save();

                    }

                }

            });

            // 保存会生成一条id对应sn的关联数据的多余数据   暂无法修改bug  留着
            $form->saved(function (Form $form) {
                $id = $form->model()->id;
                OrderExtend::where('sn', $id)->delete();
            });
        });
    }
}
