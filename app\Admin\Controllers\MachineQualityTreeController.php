<?php

namespace App\Admin\Controllers;

use App\Models\MachineCategory;
use App\Models\MachineQualityTree;

use App\Models\MachineMalfunction;
use App\Models\Material;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Tree;
use Encore\Admin\Widgets\Box;
use Encore\Admin\Layout\Row;
use Encore\Admin\Layout\Column;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Symfony\Component\HttpFoundation\Request;

//机器品检管理
class MachineQualityTreeController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content){
            $machine_category_id = Input::get('machine_category_id');
            if (empty($machine_category_id)) {
                return back()->withInput();
            }
            $name = MachineCategory::find($machine_category_id)->name;
            $content->header($name.'品检列表');
            $content->description('');
            $content->row(function (Row $row) use ($machine_category_id) {
                $row->column(6, $this->treeView($machine_category_id)->render());

                $row->column(6, function (Column $column) use ($machine_category_id) {
                    $form = new \Encore\Admin\Widgets\Form();
                    $form->action(admin_url('machine_quality_tree_copy').'?machine_category_id='.$machine_category_id);
                    $form->select('copy_category_id', '复制品类')->options(MachineCategory::where('visible',1)->where('id','<>', $machine_category_id)->get()->pluck('name', 'id'));
                    $form->hidden('machine_category_id')->default($machine_category_id);
                    $column->append((new Box('复制品检(最多复制3层)', $form))->style('success'));

                    $form = new \Encore\Admin\Widgets\Form();
                    $form->action(admin_url('machine_quality_tree').'?machine_category_id='.$machine_category_id);
                    $form->select('parent_id', '父级')->options(MachineQualityTree::category_options($machine_category_id));
                    $form->text('title', '名称')->rules('required');
                    $form->textarea('description', '具体描述');
                    $form->hidden('machine_category_id')->default($machine_category_id);
                    $column->append((new Box('新增品检', $form))->style('success'));
                });
            });
        });
    }

    public function machine_quality_tree_copy(Request $request) {
        $data = $request->all();
//        dd($data);
        $machine_category_id = $data['machine_category_id'];
        $copy_mat = MachineQualityTree::category_tree($data['copy_category_id']);
//        dd($copy_mat);
        $now = date('Y-m-d H:i:s');
        if ($copy_mat) {
            foreach ($copy_mat as $v) {
                $deal = $v;
                $parent_id = $deal['parent_id'];
                $save = [
                    "machine_category_id" => $data['machine_category_id'],
                    "title" => $deal['title'],
                    "description" => $deal['description'],
                    "parent_id" => $parent_id,
                    "order" => $deal['order'],
                    "created_at" => $now,
                    "updated_at" => $now,
                ];
                $id = MachineQualityTree::insertGetId($save);
                if (!empty($deal['children'])) {
                    foreach ($deal['children'] as $vv) {
                        $parent_id = $id;
                        $deal = $vv;
                        $save = [
                            "machine_category_id" => $data['machine_category_id'],
                            "title" => $deal['title'],
                            "description" => $deal['description'],
                            "parent_id" => $parent_id,
                            "order" => $deal['order'],
                            "created_at" => $now,
                            "updated_at" => $now,
                        ];
                        $parent_id = MachineQualityTree::insertGetId($save);
                        if (!empty($deal['children'])) {
//                            $parent_id = $id;
                            foreach ($deal['children'] as $vvv) {
                                $deal = $vvv;
                                $save = [
                                    "machine_category_id" => $data['machine_category_id'],
                                    "title" => $deal['title'],
                                    "description" => $deal['description'],
                                    "parent_id" => $parent_id,
                                    "order" => $deal['order'],
                                    "created_at" => $now,
                                    "updated_at" => $now,
                                ];
                                MachineQualityTree::insert($save);
                            }
                        }
//                        dd($parent_id);
                    }
                }
            }
        }
        return redirect(admin_url('machine_quality_tree').'?machine_category_id='.$machine_category_id);
    }

    /**
     * @return \Encore\Admin\Tree
     */
    protected function treeView($id)
    {
        return MachineQualityTree::tree(function (Tree $tree) use ($id) {
            $tree->disableCreate();

            $tree->query(function ($model) use ($id) {
                return $model->where('machine_category_id', $id);
            });
            $tree->branch(function ($branch) {
                $payload =<<<EOT
<strong>{$branch['title']}</strong>
EOT;


                return $payload;
            });
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('品检');
            $content->description('品检');

            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('品检');
            $content->description('品检');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(MachineQualityTree::class, function (Grid $grid) {

            $grid->id('ID')->sortable();

            $grid->created_at();
            $grid->updated_at();
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id=null)
    {
        return Admin::form(MachineQualityTree::class, function (Form $form) use ($id) {
            $machine_category_id = Input::get('machine_category_id');
            if (empty($machine_category_id) && $id) {
                $machine_category_id = MachineQualityTree::find($id)->machine_category_id;
            }
            $form->select('parent_id', '父级')->options(MachineQualityTree::category_options($machine_category_id));
            $form->text('title', trans('品检名称'))->rules('required');
            $form->textarea('description', '具体描述');
            $form->hidden('machine_category_id', '机型品类id')->default($machine_category_id);
            $form->display('created_at', 'Created At');
            $form->display('updated_at', 'Updated At');
            $form->tools(function (Form\Tools $tools) {
                // 去掉`列表`按钮
                $tools->disableListButton();

                // 添加一个按钮, 参数可以是字符串, 或者实现了Renderable或Htmlable接口的对象实例
//                $tools->add('<a class="btn btn-sm btn-danger"><i class="fa fa-trash"></i>&nbsp;&nbsp;delete</a>');
            });
        });
    }

    public function getAccessoryByParentId(Request $request) {
        $parentId = $request->get('q');
        return MachineQualityTree::where('parent_id', $parentId)
            ->get([DB::raw('id as id'), DB::raw('title as text')]);
    }

    public function calculatePrice(){

        return Admin::content(function (Content $content) {

            $content->header('维修品检价格查询');
            $content->description('维修品检价格查询');

            $content->body(view('admin/accessory/calculatePrice'));
        });
    }
}
