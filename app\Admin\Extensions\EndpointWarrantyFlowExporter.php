<?php

namespace App\Admin\Extensions;

use App\Models\Warranty;
use App\Services\Admin\StatisticsService;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;

class EndpointWarrantyFlowExporter extends AbstractExporter
{
    protected $service;

    use ExcelExportTrait;

    public function export()
    {

        $genderDay = Input::get('gender');
        $createdAt = Input::get('buy_date');
        $time = StatisticsService::getWarrantyFlowStatisticsQueryTime($createdAt, $genderDay);
        $map = ['endpoint' => Input::get('endpoint_id')];
        $flow = Warranty::where($map)->whereBetween('buy_date', $time)->where('status', '=', 1)->get();

        $filename = "流水导出 [{$time[0]} - {$time[1]}]";

        // 根据上面的数据拼接出导出数据
        $titles = [
            '条码',
            '导购员',
            '用户姓名',
            '用户电话',
            '购机日期',
        ];

        $formatData = [];
        foreach ($flow as $key => $row) {
            $formatData[] = [
                ' ' . $row->barcode,
                $row->salesman,
                $row->customer_name,
                $row->customer_phone,
                $row->buy_date,
            ];
        }
        array_unshift($formatData, $titles);
        ExcelExportTrait::exportToExcel($filename, $formatData);
    }
}