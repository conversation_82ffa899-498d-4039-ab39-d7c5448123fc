<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\RepairOrderExporter;
use App\Admin\Extensions\Tools\Button;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\QuickPickTool;

use App\Models\AgentOrder;
use App\Models\ChinaArea;
use App\Models\Endpoint;
use App\Models\Machine;
use App\Models\MachineAccessoryTree;
use App\Models\MachineCategory;
use App\Models\MachineMalfunction;
use App\Models\Material;
use App\Models\Order;
use App\Models\OrderExtend;
use App\Models\OrderOldAddress;
use App\Models\PostExpress;
use App\Models\PostRepairEndpoint;
use App\Models\PostRepairMaterial;
use App\Models\PostRepairOptionalAccessory;
use App\Models\PostRepairUsedMaterial;

use Barryvdh\Debugbar\Facade as DebugBar;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use GuzzleHttp\Client;
use Illuminate\Support\MessageBag;
use function foo\func;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

class RepairOrderController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('维修订单');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('维修订单');
            $content->description('');

            $content->body($this->form($id)->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修订单::寄修订单维修');
            $order = Order::where(['id' => $id])->first();
            $order_old_address = OrderOldAddress::where(['sn' => $order->sn])->first();
            $order_extend = OrderExtend::where(['sn' => $order->sn])->first();

            if (!empty($order_extend)) {
                $order_extend->order_priority_span = Order::order_priority_span($order_extend->order_priority);
            }

            $pr_material = DB::table('pr_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_material.mat_id')
                ->leftjoin('machine_malfunction', 'machine_malfunction.id', '=', 'pr_material.malfunction_id')
                ->leftjoin('material', 'material.id', '=', 'pr_material.material_id')
                ->where('pr_material.pr_sn', $order['sn'])
                ->select('machine_accessory_tree.title as title', 'machine_malfunction.title as malfunction',
                    'pr_material.count as count', 'pr_material.charge_type', 'pr_material.price_in',
                    'material.price as price', 'material.price_first')
                ->get()
                ->toArray();
            $pr_used_material = DB::table('pr_used_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_used_material.mat_id')
                ->leftjoin('material', 'material.id', '=', 'pr_used_material.material_id')
                ->where('pr_used_material.pr_sn', $order['sn'])
                ->select('material.name as name', 'material.price as price', 'material.price_first', 'pr_used_material.count as count',
                    'material.code as code', 'material.old_code as old_code', 'material.specification as specification',
                    'material.from as from', 'pr_used_material.is_charge', 'pr_used_material.charge_type', 'pr_used_material.price_in')
                ->get()
                ->toArray();
            $pr_oa = PostRepairOptionalAccessory::getShowListBySn($order->sn);
            $agent_order = null;
            if ($order->type == 3) {
                $agent_order = AgentOrder::rightJoin('agent_order_correlation as aoc', 'agent_order.sn', '=', 'aoc.agent_order_sn')
                    ->where([['aoc.order_sn', $order->sn]])->select('agent_order.*')->first();
            }

            // 此页面可以记录维修时间
            $can_record_repair_time = 1;

            $content->body(view('admin/post_repair/view', compact('order', 'pr_material', 'pr_used_material',
                'pr_oa', 'order_old_address', 'order_extend', 'agent_order', 'can_record_repair_time')));

        });
    }

    public function express(Content $content, $id = null)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
            $data['custid'] = env('SF_CUSTID');
            $data['pay_method'] = 1;
            $id = $data['id'];
            unset($data['id']);
//            dd($data);
            $express = new Express();
            $result = $express->create_express_order($data);
            if ($result['Head'] == 'OK' && $result['Body']['OrderResponse']['@attributes']['filter_result'] < 3) {
                //更新快递信息
                PostExpress::where(['readboy_sn' => $result['Body']['OrderResponse']['@attributes']['orderid']])->update([
                    'status' => 1,
                    'exp_sn' => $result['Body']['OrderResponse']['@attributes']['mailno'],
                ]);
                //写入订单信息
                $order = Order::where(['id' => $id])->first();
//                dd($order);
                $order->status = Order::EXP_GO_SUCCESS;
                $order->rb_go_exp_sn = $result['Body']['OrderResponse']['@attributes']['orderid'];
                $order->go_exp_sn = $result['Body']['OrderResponse']['@attributes']['mailno'];
                $order->go_exp_com = '顺丰快递';
                $order->go_sure = 1;
                $order->save();
                return redirect('/admin/repair_order');
            } else {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => $result['ERROR'],
                ]);
                Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                return back()->withInput()->with(compact('error'));
            }

//            return redirect('/admin/post_check');
        }

        $order = Order::where(['id' => $id])->first();
        $endpoint = PostRepairEndpoint::where(['id' => $order->repair_endpoint])->first();
//        $endpoint = Endpoint::where(['name' => $repair_endpoint->name])->first();
        $province = ChinaArea::where(['region_id' => $endpoint->province])->first();
        $city = ChinaArea::where(['region_id' => $endpoint->city])->first();
        $district = ChinaArea::where(['region_id' => $endpoint->district])->first();
        $content->header('回寄-快递下单');

        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/repair_order/express');
        $form->hidden('id')->default($id);
        $form->hidden('sn')->default($order->sn);
        $form->text('j_contact', '终端-姓名')->default($endpoint->name)->rules('required');
        $form->text('j_tel', '终端-联系电话')->default($endpoint->phone)->rules('required');
        $form->text('j_province', '终端-省')->default($province->region_name)->rules('required');
        $form->text('j_city', '终端-市')->default($city->region_name)->rules('required');
        $form->text('j_county', '终端-区')->default($district->region_name)->rules('required');
        $form->text('j_address', '终端-地址')->default($endpoint->address)->rules('required');
        $form->divide();
        $form->text('d_contact', '用户-姓名')->default($order->name)->rules('required');
        $form->text('d_tel', '用户-联系电话')->default($order->phone)->rules('required');
        $form->text('d_province', '用户-省')->default($order->province)->rules('required');
        $form->text('d_city', '用户-市')->default($order->city)->rules('required');
        $form->text('d_county', '用户-区')->default($order->district)->rules('required');
        $form->text('d_address', '用户-地址')->default($order->address)->rules('required');
        $content->body($form);
        return $content;
    }

    public function cancel(Request $request)
    {
        foreach (Order::find(Request::get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    public function repair_abandon()
    {
        $id = Request::get('id');
        $order = Order::find($id);
        $order->status = Order::REPAIR_REFUSE;
        $order->save();
    }

    public function repair_sure()
    {
        $id = Request::get('id');
        $order = Order::find($id);
        $order->status = Order::REPAIR_FINISH;
        $order->save();
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOT
            $('.repair_abandon').click(function(){
                var id = $(this).attr('value');
                layer.confirm('确认完成弃修订单？',{btn:['确认','取消']},function(){
                    $.get('/admin/repair_abandon?id='+id, function(result){
                        layer.msg('弃修成功'+result, {time:500}, function(){
                            $('.grid-refresh').click();
                        });
                    });
                },function(){
                    layer.msg('取消弃修');
                });
            });

            $('.repair_sure').click(function(){
                var id = $(this).attr('value');
                console.log(id);
                layer.confirm('确认完成维修订单？',{'btn':['确认','取消']},function(){
                    $.get('/admin/repair_sure?id='+id, function(result){
                        layer.msg('维修成功'+result, {time:500}, function(){
                            $('.grid-refresh').click();
                        });
                    });
                },function(){
                    layer.msg('取消维修');
                });
            });
            $('.btn-material-export').click(function(){
                url = window.location.href;
                if (url.indexOf("&material_export=1&_export_=1") < 0) {
                    if (url.indexOf("?") < 0) {
                        url = url + "?&material_export=1&_export_=1";
                    } else {
                        url = url + "&material_export=1&_export_=1";
                    }
                }
                window.open(url);
            });
            $('.btn-material-export-async').click(function() {
                let start = $('#updated_at_start')[0].value;
                let end = $('#updated_at_end')[0].value;
                let content_ss = [
                    '在上方筛选栏的“订单支付日期”中选择时间',
                    '为空则默认导出今天 00:00-23:59',
                    '开始：' + start,
                    '结束：' + end,
                ];
                let content = content_ss.join('<br/>');
                let opt = {
                    title: '按支付时间导出维修物料',
                    content: content,
                    btn: ['确认','取消'],
                    shadeClose: true,
                };
                let yes_fn = function(index) {
                    let url = '/admin/repair_material_export_by_pay_time?start=' + 
                        encodeURIComponent(start) + '&end=' + encodeURIComponent(end);
                    $.get(url, function(result) {
                        console.log(result);
                        let msg_display = result?.msg ?? '空';
                        layer.msg('msg: ' + msg_display);
                    });
                    layer.close(index);
                };
                layer.confirm(opt.title, opt, yes_fn);
            });
            
            
            $('.btn-material-group-export').click(function(){
                 url = window.location.href;
                 var index = layer.open({              
                    content: '<div style="width: 520px;  margin-left:7px; margin-top:10px;">统计时间：'+
                    '<input  type="text" class="layui-input" id="time" style="width: 250px"/>'+
                    '<\/div>'+
                    '<div style="width: 520px;  margin-left:7px; margin-top:10px;">物料选择：'+
                    '<select id="select1"  style="width: 250px">'+
                    
                    '</select>'+
                    '<\/div>',
                    success: function(){
                        laydate.render({
                            "elem": "#time",
                            type: 'datetime',
                            'range': '~'
                        });
                        $.ajax({
                        type: "GET",
                        url: "/admin/material_group",
                        dataType: "json",
                        success: function (data) {
                            $("#select1").prepend("<option value='0'>请选择</option>");//添加第一个option值
　　　　                    for (var i = 0; i < data.length; i++) {
　　　　                        $("#select1").append("<option value=" +data[i]['group_code']+">"+data[i]['group_name']+"</option>");
                            }
                        }
                    });
                    },
                    btn:['确定','取消'],
                    btn1: function (index,layero) {
                        var time = $('#time').val();
                        var material_code = $('#select1').val();
                        if(time == ''){
                            layer.tips("请填写时间",$('#time'));
                        }else if(material_code == '0'){
                        layer.tips("请选择物料类型",$('#select1'));
                        }else{
                            
                            if (url.indexOf("&material_group_export=1&_export_=1") < 0) {
                                if (url.indexOf("?") < 0) {
                                    url = url + "?&time="+time+"&material_code="+material_code+"&material_group_export=1&_export_=1";
                                } else {
                                    url = url + "&time="+time+"&material_code="+material_code+"&material_group_export=1&_export_=1";
                                }
                            }
                             window.open(url); 
                        }  
                       layer.close(index);
                       
                    },
                    btn2: function (index,layero) {
<!--                        layer.close(index);-->
                    },
<!--                    layer.close(index);-->
                   
                 });
              
            });
EOT;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
            //快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => [['status', '>=', Order::PAY_FINISH], ['status', '<=', Order::REPAIR_FINISH]],
                ],
                1 => [
                    'name' => '未维修',
                    'param' => [['status', '=', Order::PAY_FINISH]],
                ],
                2 => [
                    'name' => '已维修',
                    'param' => [['status', '=', Order::REPAIR_FINISH]],
                ],
                3 => [
                    'name' => '已解冻',
                    'param' => [['status', '=', Order::PAY_FINISH], ['freeze', '=', 1]],
                ],
                4 => [
                    'name' => '已打回',
                    'param' => [['status', '=', Order::REPAIR_FINISH], ['quality', '=', 2]],
                ],
                5 => [
                    'name' => '已弃修',
                    'param' => [['status', '=', Order::PAY_FINISH], ['connect', '=', 3]],
                ],
            ];

            //角色判断
            if (Admin::user()->inRoles(['pr_repair'])) {
                foreach ($option as $key => $item) {
                    $option[$key]['param'] = array_merge($item['param'], [['check_man', '=', Admin::user()->id]]);
                }
            }
            //筛选条数
            foreach ($option as $key => $value) {
                $option[$key]['count'] = Order::where($value['param'])->count();
            }
            //根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');
            }
            $grid->model()->where($option[-1]['param'])->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');

            $grid->disableCreation();
            $grid->disableExport();
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('come_exp_sn', '快递单号');
                $filter->like('sn', '维修订单号');
                $filter->like('barcode', 'S/N码');
                $filter->equal('status', '订单状态')
                    ->select(Order::STATUS);
                $filter->between('updated_at', '订单支付日期')->datetime();
                $filter->like('type', '寄修类型')->select([1 => '用户寄修', 2 => '代理商寄修', 3 => '终端代寄']);
                $filter->equal('model_name', '产品型号')->select(
                    Machine::orderBy('name')->pluck('name', 'name')->all()
                );
                $filter->between('used_material.statistic_time', '统计时间')->datetime();
            });
            if (Request::get('material_export')) {
                $grid->exporter(new RepairOrderExporter());
            }
            if (Request::get('material_group_export')) {
                $grid->exporter(new RepairOrderExporter());
            }
            $grid->tools(function ($tools) use ($option) {
                //自定义状态快捷筛选按钮
                $tools->append(new QuickPickTool($option));

                $tools->batch(function ($batch) {
                    $batch->add('取消订单', new Cancel());
                });
                $html_print = <<<EOF
                <div class="btn-group" style=" margin-top: 10px;">
                    <a href ="" class="btn btn-sm btn-success btn-material-export" >
                        <i class="fa fa-print" ></i > 导出维修物料-按统计时间
                    </a >
                </div >
EOF;
                $tools->append($html_print);
                $html_print = <<<EOF
                <div class="btn-group" style=" margin-top: 10px;">
                    <div class="btn btn-sm btn-success btn-material-export-async" >
                        <i class="fa fa-print" ></i > 导出维修物料-按支付时间
                    </div >
                </div >
EOF;
                $tools->append($html_print);
                $html_print = <<<EOF
                <div class="btn-group" style=" margin-top: 10px;">
                    <a href ="" class="btn btn-sm btn-success btn-material-group-export" >
                        <i class="fa fa-print" ></i > 导出品类物料
                    </a >
                </div >
EOF;
                $tools->append($html_print);
            });

            $grid->id('ID')->sortable();
            Order::order_priority_column($grid);
            $grid->sn('维修订单号')->display(function ($sn) {
                $s = Order::STATUS;
                $status = $this->status;
                if (array_key_exists($status, $s)) {
                    $receive_time = $this->receive_time;

                    if ($receive_time) {
                        $receive_time = date('Y-m-d', strtotime($receive_time . " + 5 days"));
                        $now = date('Y-m-d');
                        if ($receive_time < $now && $status >= 600) {
                            return '<span style="color:red">' . $sn . '</span>';
                        }

                    }
                    return $sn;
                } else {
                    return $sn;
                }
            });
            //$grid->model_name('机型');
            //$grid->barcode('S/N码');
            $grid->column('S/N码-机型')->display(function () {
                return $this->barcode . '<br/>' . $this->model_name;
            });
            //$grid->name('联系人');
            //$grid->phone('用户联系方式');
            $grid->column('联系人')->display(function () {
                return $this->name . '<br/>' . $this->phone;
            });
            $grid->amount('总金额')->display(function ($amount) {
                return '￥' . $amount;
            });
            $grid->pay_amount('实际支付金额')->display(function ($amount) {
                return '￥' . $amount;
            });
            $grid->column('order_extend.pay_remark', '支付备注');
            $grid->repair_user()->name('维修人')->display(function ($name) {
                if ($name == '')
                    return '———';
                return $name;
            });
            //$grid->connect('联系状态')->editable('select', Order::CONNECT);
            $grid->status('订单状态')->display(function ($status) {
                if ($status == Order::PAY_FINISH) {
                    if ($this->freeze == 1) {
                        return '已解冻';
                    } elseif ($this->connect == 3) {
                        return '用户弃修';
                    }
                    return '待维修';
                }
                if ($status == Order::REPAIR_FINISH) {
                    if ($this->quality == 1) {
                        return '品检通过';
                    } elseif ($this->quality == 2) {
                        return '已打回';
                    } else {
                        return '维修成功';
                    }
                }
                return '';
            });
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $data = $actions->row;
                $status = $actions->row->status;
                $c = 'repair_order/view/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">查看</span></a>';
                $actions->append($html);
                //支付完成
                if ($status == Order::PAY_FINISH) {
                    //弃修
                    $c = 'repair_order/' . $actions->getKey() . '/edit';
                    if ($data->connect == 3) {
                        $html = '<a href="' . $c . '"><span style="color:orange"> 完成弃修</span></a>';
//                        $html = '<a href="javascript:void(0);" class="repair_abandon" value="'. $data->id.'"><span>完成弃修</span></a>';
                    } else {
                        $html = '<a href="' . $c . '"><span style="color:red"> 完成维修</span></a>';
//                        $html = '<a href="javascript:void(0);" class="repair_sure" value="'. $data->id.'"><span>确认</span></a>';
                    }
                    $actions->append($html);
                }
                if ($status == Order::REPAIR_FINISH) {
                    $c = 'repair_order/' . $actions->getKey() . '/edit';
                    $html = '<a href="' . $c . '"><span style="color:blue"> 编辑</span></a>';
                    $actions->append($html);
                }
                if ($status == Order::REPAIR_FINISH && $data->quality == 2) {
                    $c = 'repair_quality/quality_view/' . $actions->getKey();
                    $html = '<a href="' . $c . '"><span style="color:blue"> 【查看品检】</span></a>';
                    $actions->append($html);
                }
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id = null)
    {
        return Admin::form(Order::class, function (Form $form) use ($id) {
            $form->hidden('connect');
            $form->display('receive_case', '收到的配件');
            $form->text('deal_remark', '备注');
            $form->text('order_extend.backstage_remark', '后台备注');
            $form->display('order_extend.pay_remark', '支付备注');
            $form->select('repair_status', '维修状态')
                ->options(Order::repair_status);
            $form->divider();

            $html = <<<EOF
                    <span style="color:red;">以换代修关联保卡操作，谨慎操作！</span>&nbsp;
EOF;
            $form->html($html);
            // $form->switch('order_extend.warehouse_type', '旧机入仓')->states($states);
            $form->select('order_extend.is_exchange_for_repair', '是否以换代修')->options(Order::is_exchange_for_repair);
            $form->text('order_extend.new_barcode', '新机SN码');
            $form->select('order_extend.warehouse_type', '旧机入仓')->options(Order::warehouse_type);
            $form->divider();

            $type = Order::where('id', '=', $id)->value('type');
            $now = date('Y-m-d h:i:s');
            $form->hasMany('used_material', '已用维修配件列表',
                function (Form\NestedForm $form) use ($id, $type, $now) {
                    $modelId = Order::where('id', '=', $id)->value('model_id');
                    $pr_sn = Order::where('id', '=', $id)->value('sn');
                    $form->select('charge_type', '收费类型')->options(function ($value) use ($type) {
                        if ($type == 2) {
                            return Order::charge_type;
                        } else {
                            return [1 => '客户价格'];
                        }
                    });
                    $form->select('mat_id', '维修配件')->options(MachineAccessoryTree::model_options($modelId))->load('material_id', admin_url('repair_check_material'));
                    //$form->select('material_id', '维修物料')->options(function ($value) use ($pr_sn, $form){
                    //    $data = Material::where('id', $value)->get()->toArray();
                    //    $ret = array();
                    //    if (count($data) > 0) {
                    //        foreach ($data as $d) {
                    //            $from = $d['from'] == 1 ? '新仓库' : '旧仓库';
                    //            $ret[$d['id']] = $d['name'].$d['specification'].'|编码:'.$d['code'].'|旧编码:'.$d['old_code'].'|仓库:'.$from.'|库存:'.$d['quantity'].'|价格 : '.$d['price'];
                    //        }
                    //    }
                    //    return $ret;
                    //});
                    $form->select('material_id', '维修物料')->options(function ($value) use ($pr_sn, $form, $type) {
                        $data = Material::where('id', $value)->get()->toArray();
                        $ret = array();
                        $charge_type = PostRepairMaterial::where([['pr_sn', '=', $pr_sn], ['material_id', '=', $value]])->value('charge_type');
                        if (count($data) > 0) {
                            foreach ($data as $d) {
                                $from = $d['from'] == 1 ? '新仓库' : '旧仓库';
                                if ($charge_type == 2) {
                                    $ret[$d['id']] = $d['name'] . $d['specification'] . '|编码:' . $d['code'] . '|旧编码:' . $d['old_code'] .
                                        '|仓库:' . $from . '|库存:' . $d['quantity'] . '|价格 : ' . $d['price_first'];
                                } else {
                                    $ret[$d['id']] = $d['name'] . $d['specification'] . '|编码:' . $d['code'] . '|旧编码:' . $d['old_code'] .
                                        '|仓库:' . $from . '|库存:' . $d['quantity'] . '|价格 : ' . $d['price'];
                                }

                            }
                        }
                        return $ret;
                    });
                    $form->number('count', '数量')->default(1);
                    $form->select('is_charge', '是否收费')->options(function ($value) {
                        if ($value == 1) {
                            return [1 => '收费'];
                        } else if ($value == 2) {
                            return [2 => '仅弃修时收费'];
                        } else {
                            return [0 => '不收费'];
                        }
                    });
                    $form->hidden('mark', '物料标记')->value(1);
                    $form->hidden('created_at', '创建时间')->value($now);
                    $form->hidden('statistic_time', '统计时间')->value($now);
                });
            $form->text('order_extend.exchange_number', '更换主板后的序列号');

            $form->hidden('repair_man');
            $form->hidden('status');
            $form->hidden('sn');
            $form->hidden('serial');

            $form->saving(function (Form $form) {
                // 序列号检查
                $exchange_number_ban = '0123456789ABCDEF';
                $exchange_number = $form->order_extend['exchange_number'];
                $str_pos_ret = stripos($exchange_number, $exchange_number_ban);
                if (is_int($str_pos_ret) && $str_pos_ret >= 0) {
                    $error = new MessageBag([
                        "title" => '错误提示',
                        "message" => "更换主板后的序列号 禁止填写 " . $exchange_number_ban
                    ]);
                    return back()->withInput()->with(compact('error'));
                }

                // 以换代修
                if ($form->order_extend['is_exchange_for_repair'] == 1) {
                    if (empty($form->order_extend['new_barcode'])) {
                        $error = new MessageBag([
                            "title" => '错误提示',
                            "message" => "《以换代修》:新条码不能为空"
                        ]);
                        return back()->withInput()->with(compact('error'));
                    }
                    if (empty($form->order_extend['warehouse_type'])) {
                        $error = new MessageBag([
                            "title" => '错误提示',
                            "message" => "《以换代修》:请选择旧机入仓类型"
                        ]);
                        return back()->withInput()->with(compact('error'));
                    }
                    $data = DB::connection('mysql2')
                        ->table('warranty')
                        ->where([['barcode', '=', $form->barcode], ['status', '=', '1']])
                        ->select('buy_date')->first();
                    $mes_data = RepairCheckController::mes_device($form->order_extend['new_barcode'], "", "");
                    if (empty($mes_data)) {
                        $error = new MessageBag([
                            "title" => '错误提示',
                            "message" => "《以换代修》新条码（" . $form->order_extend['new_barcode'] . "）无效，请确认",
                        ]);
                        return back()->withInput()->with(compact('error'));
                    }
                    if ($data) {
                        // 有旧保卡执行换机操作
                        // 换机操作
                        $warranty_exchange = RepairCheckController::warranty_exchange($form->barcode, $form->order_extend['new_barcode']);
                        if ($warranty_exchange) {
                            if ($warranty_exchange['ok'] == 0) {
                                $error = new MessageBag([
                                    "title" => '错误提示',
                                    "message" => "《以换代修》换机操作失败：" . $warranty_exchange['msg'],
                                ]);
                                return back()->withInput()->with(compact('error'));
                            }
                        } else {
                            $error = new MessageBag([
                                "title" => '错误提示',
                                "message" => "《以换代修》换机操作失败：访问接口出错",
                            ]);
                            return back()->withInput()->with(compact('error'));
                        }
                    }
                }

                //角色判断
                if (Admin::user()->inRoles(['pr_repair'])) {
                    $form->repair_man = Admin::user()->id;
                }

                if ($form->repair_status == 1) {
                    if ($form->used_material) {
                        $modelId = $form->model()->model_id;
                        $category_id = Machine::where('model_id', '=', $modelId)->value('category_id');
                        $category = MachineCategory::where('id', '=', $category_id)->first();
                        $mat_tree = MachineAccessoryTree::model_options($modelId);
                        DebugBar::info(compact('form', 'category', 'mat_tree'));
                        foreach ($form->used_material as $material) {
                            if ($material['_remove_'] == 0 && $material['mat_id'] != 0) {
                                //检查标记字段
                                $accessory_mark = MachineAccessoryTree::where([["id", "=", $material['mat_id']]])->value("accessory_mark");
                                $accessory_check = $accessory_mark == 1;
                                //按配件名匹配
                                $mat_has = false;
                                $mat_id = $material['mat_id'];
                                if (array_key_exists($mat_id, $mat_tree)
                                    && (strstr($mat_tree[$mat_id], '主板')
                                        || strstr($mat_tree[$mat_id], '数据IC'))
                                    && (!strstr($mat_tree[$mat_id], '单词卡主板'))) {
                                    $mat_has = true;
                                }
                                //点读笔分类下不检查配件名匹配
                                if (strstr($category->name, '点读笔')) {
                                    $mat_has = false;
                                }
                                //换主板需填新序列号
                                DebugBar::info(compact('accessory_mark', 'accessory_check', 'mat_id', 'mat_has'));
                                if (($accessory_check || $mat_has) && !$form->order_extend['exchange_number']) {
                                    $error = new MessageBag([
                                        "title" => "错误提示",
                                        "message" => "配件中包含主板配件或闪存(数据IC), 请填写更换主板后的序列号",
                                    ]);
                                    return back()->withInput()->with(compact('error'));
                                }
                            }
                        }
                    }

                    $form->status = Order::REPAIR_FINISH;
                    //更换主板
                    if ($form->order_extend['exchange_number'] && $form->serial) {
                        $replace_mainboard = $this->replace_mainboard($form->sn, $form->serial, $form->order_extend['exchange_number']);
                        if (!$replace_mainboard[0]) {
                            $error = new MessageBag([
                                "title" => '错误提示',
                                "message" => "《更换主板》清除数据失败，请确认，提示（" . $replace_mainboard[1] . "）",
                            ]);
                            return back()->withInput()->with(compact('error'));
                        }
                    }

                } else if ($form->repair_status == 2) {
                    $form->status = Order::REPAIR_REFUSE;
                }
            });
            $form->saved(function (Form $form) use ($id) {

            });
        });
    }

    private function replace_mainboard($order_sn, $old_number, $new_number)
    {
        $data = DB::table('order_mainboard_record')->where([['sn', '=', $order_sn]])
            ->orderBy('created_at', 'desc')->limit(1)->select('old_number', 'exchange_number')->first();
        if ($data) {
            //如果要更换的序列号跟上一次更换填写的一样,不处理
            if ($data->exchange_number == $new_number) {
                return [true, ''];
            } else {
                $old_number = $data->exchange_number;
            }
        }
        $appId = 'parentsadmin';
        $time = time();
        $appSecret = '9b332c2653ce7189da101dac5a63fd4e';
        $sn = "00000000" . $time . md5($time . $appSecret . md5($appId)) . $appId;

        $param = [
            "sn" => $sn,
            "old_imei" => $old_number,
            "new_imei" => $new_number,

        ];
        $field = http_build_query($param);
        $client = new Client();
        $url = "https://parentadmin.readboy.com/v1/machine/replace_imei?" . $field;
        $response = $client->get($url);

        if ($response->getStatusCode() == '200') {
            $body = $response->getBody();
            //dd($body->getContents());
            $content = json_decode($body->getContents(), true);
            if ($content['status'] == 1) {
                DB::table('order_mainboard_record')->insert([
                    ['sn' => $order_sn, 'old_number' => $old_number, 'exchange_number' => $new_number],
                ]);
                return [true, ''];
            } else {
                return [false, $content['errmsg']];
            }
        } else {
            return [false, '接口访问失败'];
        }
    }

    public function material_group()
    {
        $data = DB::table('material_group')->where([['group_parentid', '!=', 0]])
            ->select(DB::raw('DISTINCT group_parentid'))->get()->toArray();
        $data = array_column($data, 'group_parentid');

        $data = DB::table('material_group')->where([['group_parentid', '!=', 0]])
            ->whereNotIn('group_id', $data)
            ->select(DB::raw('concat_ws(\'-\',group_code,group_name) as group_name, group_code'))
            ->orderBy('group_sort')
            ->get()->toArray();
        $data = array_map('get_object_vars', $data);
//        dd($data);
        return $data;
    }
}
