<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\MachineAccessoryExporter;
use App\Admin\Extensions\PayOrderExporter;
use App\Models\MachineAccessory;

use App\Models\Machine;
use App\Models\MachineCategory;
use App\Models\ModelCategory;
use App\Models\PayOrder;
use App\Models\PostRepairExpress;
use App\Services\Admin\ModelCategoryService;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Widgets\Table;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

class PayOrderController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('支付');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('');
            $content->description('');

            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('');
            $content->description('');

            $content->body($this->form());
        });
    }

    public function pay_notify(Request $request) {
        $readboy_sn = Request::get('readboy_sn');
        $routes = DB::table('pay_notify_log')->where('readboy_sn', $readboy_sn)->orwhere('pay_sn', $readboy_sn)->orderby('created_at', 'asc')->get()->toArray();
        foreach ($routes as $key => $route) {
//        dd(json_decode($route->detail, true));
            $route->detail = json_decode($route->detail, true);
        }
//        dd($routes);
        return view('admin/pay/notify', compact('routes'));
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOF
            //查看快递路由信息
            $('.pay_notify').click(function(){
                var id = $(this).attr('value');
                layer.open({
                      type: 2,
                      title: '',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['1000px', '60%'],
                      content: 'pay/notify?readboy_sn='+id //iframe的url
                });
            });
EOF;
        Admin::script($script);
        return Admin::grid(PayOrder::class, function (Grid $grid) {

            $grid->exporter(new PayOrderExporter());
            $grid->disableCreation();
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->where(function($query) {
                    $key = '%'.$this->input.'%';
                    $query->where('pr_sn', 'like', $key)->orwhere('readboy_sn', 'like', $key)->orwhere('pay_sn', 'like', $key);
                }, '单号搜索');
                $filter->is('com', '支付公司')->select([1 => '微信', 2 => '支付宝']);
                $filter->is('is_paid', '是否已支付')->select([1 => '已支付', 0 => '未支付']);
                $filter->between('updated_at', '支付时间')->datetime();
            });

            $grid->model()->orderBy('id', 'desc');

            $grid->id('ID')->sortable();

            $grid->pr_sn('寄修单号')->sortable();
            $grid->readboy_sn('内部单号')->sortable();
            $grid->com('支付公司')->sortable()->display(function ($value) {
                return PayOrder::COM[$value];
            });
            $grid->pay_sn('支付公司单号')->sortable();
            $grid->is_paid('是否支付')->sortable()->display(function ($value) {
                return PayOrder::IS_PAID[$value];
            });
            $grid->pay_amount('支付金额（元）');
            $grid->trade_state('推送状态');
            $grid->updated_at('最后更新');

            $grid->actions(function ($actions) {
                $actions->disableDelete();

            });
            $grid->tools(function ($tools) {
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                });
            });
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                //这个是主键id,不是模型id
                $key = $actions->row->readboy_sn;
                $html = <<<EOF
                    <a class='pay_notify' href='javascript:void(0);' value='$key' title='支付推送信息'>
                      <i class='fa fa-briefcase'> 支付推送信息</i>
                    </a>
EOF;
//                $actions->append($html);
//                $c = 'machine_accessory_tree';
//                $k = $actions->row->model_id;
//                $html = '|<a href="'.$c.'?model_id='.$k.'"><span style="color:red">【配件树】</span></a>';
                $actions->append($html);
            });

        });
    }

    /**
     * Make a form builder.
     * @param id
     * @return Form
     */
    protected function form($id = null)
    {
        return Admin::form(Machine::class, function (Form $form) use ($id) {
            $form->display('name', '机器型号');
            $categories = MachineCategory::where('visible', 1)->get()->pluck('name', 'id')->prepend('请选择', 0);
            $form->select('category_id', '型号品类')->options($categories);
//            $form->currency('company_price', '公司价格')->symbol('￥');
//            $form->currency('top_agency_price', '总代价格')->symbol('￥');
//            $form->currency('second_agency_price', '二代价格')->symbol('￥');
//            $form->currency('customer_price', '顾客价格')->symbol('￥');
            $states = [
                'on' => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ];
            $form->switch('visibility', '是否可见')->states($states);
            $form->display('updated_at', '更新时间');
        });
    }


}
