<?php


namespace App\Admin\Controllers;


use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\DeleteAll;
use App\Http\Controllers\Controller;
use App\Models\AgentOrder;
use App\Models\AgentOrderCorrelation;
use App\Models\Order;
use App\Models\OrderLog;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Form\Tools;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;
use function foo\func;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

class EndpointPostRepairListController extends Controller
{
    use ModelForm;

    /**
     *Index interface.
     *
     * @return content
     */
    public function index(){
        return Admin::content(function (Content $content){

            $content->header('终端代寄订单');
            $content->description('终端代寄大单列表');
            $content->body($this->grid());
        });
    }

    /**
     * @param $id
     * @return mixed
     */
    public function edit($id){
        return Admin::content(function (Content $content) use ($id){
            $content->header('代理商寄修');
            $content->description('代理商寄修编辑');
            $content->body($this->form($id)->edit($id));
        });
    }



    public function grid(){
        return Admin::grid(AgentOrder::class, function (Grid $grid){

            $grid->disableCreation();
            $grid->disableExport();
            $grid->tools(function ($tools){
                $button = <<<EOF
                     <div class="btn-group pull-right" style="margin-right:15px;">
                      <a href ="endpoint_order_post_check" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 审核总览
                      </a >
                    </div >
                    <div class="btn-group pull-right" style="margin-right:15px;">
                      <a href ="endpoint_repair_manager" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 终端代寄总览
                      </a >
                    </div >
EOF;
                $tools->append($button);
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                    $batch->add('取消订单', new Cancel());
                    $batch->add('删除', new DeleteAll());
                });
            });
            $grid->model()->where([['status', '=', '1'], ['type', '=', 2]])->orderBy('id', 'desc');
            $grid->filter(function ($filter){
                $filter->like('sn', '大单单号');
                $filter->where(function ($query){
                    $input = $this->input;
                    $query->whereHas('agent_order_correlation', function ($query) use ($input){
                        $query->whereHas('order', function ($query){
                            $query->where('sn', $this->input);
                        });
                    });
                }, '小单单号');
                $filter->like('name', '寄修人');
                $filter->like('phone', '手机号');
                $filter->where(function ($query){
                    $input = $this->input;
                    $query->whereHas('agent_order_correlation', function ($query) use ($input){
                        $query->whereHas('order', function ($query){
                            $query->where('barcode', $this->input);
                        });
                    });
                }, '条码');

                $filter->where(function ($query){
                    $input = $this->input;
                    $query->whereHas('agent_order_correlation', function ($query) use ($input){
                        $query->whereHas('order', function ($query){
                            $query->where('come_exp_sn', $this->input);
                        });
                    });
                }, '快递单号');
            });

            $grid->id('ID')->sortable();
            $grid->sn('寄修大单单号');
            $grid->name('联系人');
            $grid->phone('手机号');
            $grid->province('省份');
            $grid->city('城市');
            $grid->district('地区');
            $grid->address('详细地址');
            $grid->column('come_exp_sn', '寄来快递单号');
            $grid->column('小单单数')->display(function (){
//                dd($this->sn);
                $count = AgentOrderCorrelation::where([['agent_order_sn', '=', $this->sn]])
//                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
                return $count;
            });
            $grid->column('审核状态')->display(function (){
//                dd($this->sn);
                $count = Order::where([['order.status', '=', 100],['aoc.agent_order_sn', '=', $this->sn]])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
                if ($count == 0){
                    return '审核已完成';
                }
                return '<span style="color: red">审核未完成</span>';
            });
            $grid->actions(function ($actions){
                $actions->disableDelete();
                $c = 'endpoint_order_post_check';
                $k = $actions->row->sn;

                $html = '<a href="'.$c.'?agent_order_sn='.$k.'"><span style="color:red">【审核详情】</span></a>';
                $actions->append($html);
            });

        });
    }

    public function form($id = null){
        return Admin::form(AgentOrder::class, function (Form $form) use ($id){
//            dd(id);
            $form->display('id');
            $form->display('sn', '寄修单号');
            $form->text('name','联系人');
            $form->text('phone', '手机号');
            $form->text('province', '省份');
            $form->text('city', '城市');
            $form->text('district', '地区');
            $form->text('address', '详细地址');
        });
    }

    public function agent_order_bill(){
        return Admin::content(function (Content $content){
            $content->header('费用账单');
            $content->description('代理商寄修账单');
            $data = AgentOrder::getAgentOrderBill();
            $content->body(view('admin/agent_order_list/agent_order_bill', compact('data')));
        });

    }

    public function cancel(Request $request)
    {
        foreach (AgentOrder::find(request()->get('ids')) as $post) {
            $post->status = -1;
            DB::table('order as o')
                ->where('aoc.agent_order_sn','=', $post->sn)
                ->leftJoin('agent_order_correlation as aoc', 'aoc.order_sn', '=', 'o.sn')
                ->update(['o.status'=> -900]);

            $post->save();

        }
    }

    public function deleteAll(Request $request)
    {
        foreach (AgentOrder::find(request()->get('ids')) as $post) {
            $post->status = -1;
            DB::table('agent_order')->where('sn','=', $post->sn)->delete();
            DB::table('order as o')
                ->where('aoc.agent_order_sn','=', $post->sn)
                ->leftJoin('agent_order_correlation as aoc', 'aoc.order_sn', '=', 'o.sn')
                ->delete();
            DB::table('agent_order_correlation')->where('agent_order_sn','=', $post->sn)->delete();
        }
    }
}