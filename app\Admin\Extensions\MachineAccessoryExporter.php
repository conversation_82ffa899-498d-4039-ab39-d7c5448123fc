<?php

namespace App\Admin\Extensions;

use App\Models\Machine;
use App\Models\MachineAccessory;
use App\Models\MachineCategory;
use App\Models\MachineType;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;

class MachineAccessoryExporter extends AbstractExporter
{
    use ExcelExportTrait;

    public function export()
    {
        $filename = '机型以及配件列表';
        // 根据上面的数据拼接出导出数据
        $data = Machine::with('accessory')->get();
        //获取所有分类
        $categories = MachineCategory::all()->pluck('id', 'name');
        //获取所有配件
        $accessories = MachineAccessory::all()->pluck('title', 'id');

        //获取所有机型
        $models = Machine::all()->pluck('name', 'model_id');

        $machineCategoryData = [];
        foreach ($categories as $key => $categoryId) {

            foreach ($data as $k => $v) {
                if ($categoryId == $v->category_id) {
                    $machineCategoryData[$key][] = $v;
                }
            }
        }
        $formatData = [];
        foreach ($machineCategoryData as $key => $value) {

            $accessoryNameList = [];

            //获取该大分类下的机型所有配件名称
            foreach ($value as $k => $v) {
                $nameList = [];
                foreach ($v->accessory as $i => $j) {
                    $nameList[] = $accessories[$j->accessory_id];
                }
                $accessoryNameList = array_unique(array_merge($accessoryNameList, $nameList));
            }
            array_unshift($accessoryNameList, '机型');

            $formatData[] = $accessoryNameList;

            //获取该大分类下的机型对应的配件
            //由上面那个循环可以得到[0=>0,1=>'配件名称1',2=>'配件名称2' ...]这样子的格式
            //并且为了符合导出excel的数据格式,要根据配件名称定位值的键值
            foreach ($value as $k => $v) {
                $model = $models[$v->model_id];
                $items = [];
                foreach ($accessoryNameList as $i => $name) {
                    foreach ($v->accessory as $index => $item) {
                        if ($accessories[$item->accessory_id] == $name) {
                            $items[$i] = $item->price;
                        } elseif (!isset($items[$i])) {
                            $items[$i] = '';
                        }
                    }
                }
                $items[0] = $model;
                $formatData[] = $items;
            }
            $formatData[] = null;
        }

        //        dd($formatData);

        ExcelExportTrait::exportToExcel($filename, $formatData, null, null, 'B1');
    }
}