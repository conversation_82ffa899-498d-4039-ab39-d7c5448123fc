<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>an
 * Date: 2017/8/17
 * Time: 10:39
 */

namespace App\Api\Controllers;


use App\Models\MarketingCategory;

class MarketingCategoryController extends BaseController {

    public function index() {
        $marketingCategories = MarketingCategory::all()->toArray();
        $marketingCategories =  self::_generateTree($marketingCategories);
        return $this->returnArray($marketingCategories);
    }

    /**
     * 为了不涉及到修改MarketingCategory的model文件,直接在这里添加一个格式化输出树的方法
     * @param $data
     * @param int $parent_id
     * @return array
     */
    private static function _generateTree ($data, $parent_id = 0) {
        $tree = [];
        if ($data && is_array($data)) {
            foreach($data as $v) {
                if($v['parent_id'] == $parent_id) {
                    $tree[] = [
                        'id' => $v['id'],
                        'name' => $v['title'],
                        'pid' => $v['parent_id'],
                        'children' => self::_generateTree($data, $v['id']),
                    ];
                }
            }
        }
        return $tree;
    }
}