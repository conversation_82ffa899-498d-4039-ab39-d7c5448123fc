<?php

/**
 * <PERSON><PERSON>-admin - admin builder based on <PERSON><PERSON>.
 * <AUTHOR> <https://github.com/z-song>
 *
 * Bootstraper for Admin.
 *
 * Here you can remove builtin form field:
 * Encore\Admin\Form::forget(['map', 'editor']);
 *
 * Or extend custom form field:
 * Encore\Admin\Form::extend('php', PHPEditor::class);
 *
 * Or require js and css assets:
 * Admin::css('/packages/prettydocs/css/styles.css');
 * Admin::js('/packages/prettydocs/js/main.js');
 *
 */


use Encore\Admin\Form;
use Encore\Admin\Grid\Column;
use App\Admin\Extensions\Column\ExpandRow;
use App\Admin\Extensions\WangEditor;
use Encore\Admin\Grid\Exporter;
use App\Admin\Extensions\EndpointAccountExporter;
use App\Admin\Extensions\Tags;
use App\Admin\Extensions\Barcode;

Column::extend('expand', ExpandRow::class);
Exporter::extend('endpoint-account-exporter', EndpointAccountExporter::class);
Encore\Admin\Form::forget('editor');
Encore\Admin\Form::forget('map');
Form::extend('editor', WangEditor::class);
Form::extend('chtags', Tags::class);
Form::extend('barcode', Barcode::class);

app('translator')->addNamespace('admin', resource_path('lang/admin'));
Admin::js('/js/layer/layer.js');
Admin::js('/packages/admin/echarts/echarts.js');
Admin::js('/packages/admin/bootstrap-fileinput/js/plugins/piexif.min.js');
Admin::js('/packages/admin/fancybox/source/jquery.fancybox.js');
Admin::css('/packages/admin/fancybox/source/jquery.fancybox.css');
Admin::js('https://webapi.amap.com/loca?key=e87ed6d25c66b8c0714aff27dd6dd0eb');
Admin::js('/packages/JsBarcode/dist/JsBarcode.all.min.js');
Admin::js('/packages/jquery-loading-overlay/2.1.5/dist/loadingoverlay.min.js');
Admin::js('/js/LodopFuncs.js');
Admin::js('/js/laydate/laydate.js');

Column::extend('prependIcon', function ($value, $icon) {

    return "<span style='color: #999;'><i class='fa fa-$icon'></i>  $value</span>";

});

