<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Tools\Button;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Models\MachineAccessory;
use App\Models\MachineAccessoryRelation;
use App\Models\MachineMalfunction;
use App\Models\Order;

use App\Models\PostRepairAccessory;
use App\Models\PostRepairMalfunction;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Request;

class PostOrderController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修订单');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修订单检测');
            $content->description('检测');

            $content->body($this->form($id)->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修订单--查看');
            $order = Order::where(['id' => $id])->first();

            $content->body(view('admin/post_order/view', compact('order')));

        });
    }

    public function printf($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('打印检测报告');
            $order = Order::where(['id' => $id])->first();
            $post_malfunction = DB::table('pr_malfunction')
                ->join('machine_malfunction', 'machine_malfunction.id', '=', 'pr_malfunction.malfunction_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_malfunction.title')->toArray();
            $malfunction = implode('，', $post_malfunction);
            $post_accessory = DB::table('pr_accessory')
                ->join('machine_accessory_relation', 'machine_accessory_relation.id', '=', 'pr_accessory.mar_id')
                ->join('machine_accessory', 'machine_accessory.id', '=', 'machine_accessory_relation.accessory_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_accessory.title')->toArray();
            $accessory = implode('，', $post_accessory);
//            dd($accessory);
            $content->body(view('admin/post_order/print', compact('order', 'malfunction', 'accessory')));

        });
    }

    public function print_order($id){
        $order = Order::where(['id' => $id])->first();
        $post_malfunction = DB::table('pr_malfunction')
            ->join('machine_malfunction', 'machine_malfunction.id', '=', 'pr_malfunction.malfunction_id')
            ->where('pr_sn', '=', $order['sn'])->pluck('machine_malfunction.title')->toArray();
        $malfunction = implode('，', $post_malfunction);
        $post_accessory = DB::table('pr_accessory')
            ->join('machine_accessory_relation', 'machine_accessory_relation.id', '=', 'pr_accessory.mar_id')
            ->join('machine_accessory', 'machine_accessory.id', '=', 'machine_accessory_relation.accessory_id')
            ->where('pr_sn', '=', $order['sn'])->pluck('machine_accessory.title')->toArray();
        $accessory = implode('，', $post_accessory);
//            dd($accessory);
        return view('admin/post_order/print', compact('order', 'malfunction', 'accessory'));
    }

    public function cancel(Request $request)
    {
        foreach (Order::find($request->get('ids')) as $post)
        {
            $post->status = -900;
            $post->save();
        }
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOF
        $("input[name='sn']").focus();
        $('.print').click(function(){
            var id = $(this).attr('value');
            layer.open({
                  type: 2,
                  title: 'layer mobile页',
                  shadeClose: true,
                  shade: 0.8,
                  area: ['600px', '90%'],
                  content: 'post_order/print/'+id //iframe的url
            });
        });
//        $("input[name='come_exp_sn']").focus();
EOF;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
//            $grid->model()->where('status', '>=', 200);
            //快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => [],
                ],
                1 => [
                    'name' => '审核已通过',
                    'param' => [['status', '=', 200]],
                ],
                2 => [
                    'name' => '审核不通过',
                    'param' => [['status', '=', -200]],
                ],
            ];
            //筛选条数
            foreach($option as $key => $value) {
                $option[$key]['count'] = Order::where($value['param'])->count();
            }

            $grid->disableCreation();
            $grid->disableExport();
            $grid->tools(function ($tools) use($option) {
//                $url = "/admin/post_order?status=-900";
//                $icon = "";
//                $text = "已取消订单: " . Order::where(['status' => -900])->count();
//                $tools->append(new Button($url, $icon, $text));
//                $url = "/admin/post_order?status=400";
//                $icon = "";
//                $text = "未检测订单: " . Order::where(['status' => 400])->count();
//                $tools->append(new Button($url, $icon, $text));
//                $url = "/admin/post_order?status=500";
//                $icon = "";
//                $text = "已检测订单: " . Order::where(['status' => 500])->count();
//                $tools->append(new Button($url, $icon, $text));

                //自定义状态快捷筛选按钮
                $tools->append(new QuickPickTool($option));

                $tools->batch(function ($batch) {
                    $batch->add('取消订单', new Cancel());
                });
            });

            //根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param']);
            }

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('come_exp_sn', '快递单号')->setPlaceholder('光标移到此处扫码');
                $filter->like('sn', '维修订单号')->setPlaceholder('光标移到此处扫码');
                $filter->like('barcode', 'S/N码');
                $filter->like('status', '订单状态')
                    ->select(Order::STATUS);
                $filter->between('create_at', '订单提交日期')->datetime();
            });
            $grid->id('ID')->sortable();
            $grid->uid('UID')->sortable();
            $grid->sn('寄修订单编号');
            $grid->come_exp_sn('快递单号');
            $grid->barcode('S/N码');
            $grid->phone('用户联系方式');
            $grid->endpoint()->name('寄修售后点');
            $grid->created_at('订单提交时间');
            $grid->status('订单状态')->display(function ($status) {
                $s = Order::STATUS;
                if (array_key_exists($status, $s)) {
                    return $s[$status];
                } else {
                    return "————";
                }
            });
            $grid->check_user()->name('检测人')->display(function ($name) {
                if ($name == '')
                    return '———';
                return $name;
            });
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $status = $actions->row->status;

                if ($status < 0 || $status > 800) {
                    $actions->append('<span>检测 </span>');
                } else {
                    $c = 'post_order/' . $actions->getKey();
                    $html = '<a href="' . $c . '/edit"><span style="color:orange">检测 </span></a>';
                    $actions->append($html);
                }
                $c = 'post_order/view/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">查看 </span></a>';
                $actions->append($html);
                if ($status < 500) {
                    $actions->append('<span>打印</span>');
                } else {
                    $v =$actions->row->id;
                    $html = '<a href="javascript:void(0);" class="print" value="'.$v.'"><span style="color:blue">打印</span></a>';
                    $actions->append($html);
                }
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id = null)
    {
        $script = <<<EOF
            //别问我为什么不直接操作模板div,是因为用了DocumentFragment,操作不了,不是我用的!
            $(document).ready(function() {
              calculatePrice();
              function total(){
                var staff = Number($('#staff_cast').val());
                var accessory = Number($('#accessory_cast').val());
                $('#amount').val(staff + accessory);
                $('#pay_amount').val(staff + accessory);
              }
              $(document).on('change','.staff_cast',function(){
                total();
              });
              
                            
              //统计配件价格
              function calculatePrice(){
                let totalPrice = 0;
                $('.mar_id:visible').each(function(){
                  let optionText = $(this).find("option:selected").text();
                  let price = optionText.split('价格 : ')[1];
                  let count = $(this).parent().parent().next().find('#count').val();
                  totalPrice += price * count;
                });
                $('#accessory_cast').val(totalPrice);
                total()
              }
              
              //下拉框变化
              $(document).on('change','.mar_id',function(){
                calculatePrice();
              });
              
              //加减数量按钮点击
              $('.has-many-repair_accessory-forms').on('click','button',function(){
                calculatePrice();
              });
              //移除按钮点击
              $(document).on('click','.remove',function(){
                calculatePrice();
              });
            });      
EOF;

        Admin::script($script);
        return Admin::form(Order::class, function (Form $form) use ($id) {

            $form->display('name', '寄修人');
            $form->display('phone', '联系方式');
            $form->display('sn', '寄修订单号');
//            $form->barcode('sn', '寄修条码条码')->options(['width'=>1,]);
            $form->display('model_name', '机器型号');
            $form->display('in_period','保修状态')->with(function ($in_period){
                return Order::in_period[$in_period];
            });
            $form->hidden('sn', '寄修订单号');
            $form->divider('');
            $form->hasMany('repair_malfunction','产品故障选择',
                function (Form\NestedForm $form) {
                    $form->hidden('id');
                    $form->hidden('pr_sn');
                    $form->select('malfunction_parent', '故障位置')->options(
                        MachineMalfunction::where('parent_id', '=', 0)->pluck('title', 'id')->prepend('请选择', 0)
                    )->load('malfunction_id', '/admin/machine_malfunction/getMachineMalfunctionByParentId');
                    $form->select('malfunction_id', '故障类别')->options(function ($id) {
                        return MachineMalfunction::where('id', '=', $id)->pluck('title', 'id');
                    });
                });
            $form->divider('');
            $form->select('reason','损坏原因')->options(Order::reason)->value(function ($reason){return $reason;});
            $form->text('deal', '维修方式');
            $form->divider('');

            $options = [];
            if ($id) {
                $modelId = Order::where('id', '=', $id)->value('model_id');
                $accessoryRelations = MachineAccessoryRelation::with('accessory')
                    ->where('model_id', '=',$modelId)->get();
                $options[0] = '请选择';
                foreach ($accessoryRelations as $key => $relation) {
                    $options[$relation->id] = $relation->accessory->title . ' | 价格 : ' . $relation->price;
                }
            }
            $form->hasMany('repair_accessory', '维修配件列表',
                function (Form\NestedForm $form) use ($options) {
                    $form->select('mar_id', '维修配件')->options(
                        $options
                    );
                    $form->number('count', '数量')->default(1);
                });

            $form->currency('accessory_cast', '配件价格')->symbol('￥');
            $form->currency('staff_cast', '人工费用')->symbol('￥');
            $form->currency('amount', '总计')->symbol('￥');
            $form->currency('pay_amount', '待支付金额')->symbol('￥');

            $form->display('receive_case', '收到的配件');
            $form->text('deal_remark', '备注');
            $form->hidden('check_man');
            $form->hidden('status');
            $form->hidden('pay_amount');
            $form->saving(function (Form $form) {
                $form->check_man = Admin::user()->id;
                if ($form->status < 500)
                    $form->status = 500;
                if ($form->in_period == 1){
                    $form->pay_amount = 0;
                } else {
                    $form->pay_amount = $form->amount;
                }
            });
        });
    }
}
