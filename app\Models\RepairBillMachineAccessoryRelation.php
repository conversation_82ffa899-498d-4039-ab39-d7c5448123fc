<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class RepairBillMachineAccessoryRelation extends Model
{
    //
    protected $table = 'repair_bill_machine_accessory_relation';

    protected $fillable = ['machine_accessory_relation_id','amount'];

    public function machine_accessory_relation(){
        return $this->hasOne(MachineAccessory::class,'id','machine_accessory_relation_id');
    }

    protected static function boot()
    {
        parent::boot();

        self::saved(function(){
            DB::table('repair_bill_machine_accessory_relation')
                ->where('machine_accessory_relation_id',0)->delete();
        });
    }

}
