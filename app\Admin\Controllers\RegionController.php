<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\AgencyExporter;
use App\Models\Partition;
use App\Models\Region;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Tree;
use Symfony\Component\HttpFoundation\Request;
use Illuminate\Support\Facades\DB;

class RegionController extends Controller
{
    use ModelForm;




    public function city(Request $request)
    {
        $cityId = $request->get('q');
//        dd($cityId);
        return Region::where('parent_id', $cityId)
            ->get([DB::raw('region_id as id'), DB::raw('region_name as text')])
            ->prepend(['id' => 0, 'text' => '选择城市[可不选,代表只归省份]']);
    }
}
