<?php


namespace App\Admin\Controllers;


use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\DeleteAll;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Http\Controllers\Controller;
use App\Models\Order;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;
use Illuminate\Support\Facades\Request;

class AgentOrderGoConfirmController extends Controller
{
    use ModelForm;

    public function index(){
        return Admin::content(function (Content $content){
           $content->header('经销商回寄确认');
           $content->description('经销商回寄确认列表');
           $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id){
            $content->header('回寄确认');
            $content->description('回寄确认列表');
            $content->body($this->form()->edit($id));
        });
    }

    protected function grid(){
        return Admin::grid(Order::class, function (Grid $grid){
            $grid->disableExport();
            $grid->disableCreation();

            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => function($query){
                        $query->orwhere([['status', Order::REPAIR_FINISH ], ['quality', 1], ['order.type', '=', '2']])
                            ->orwhere([['status', '=', Order::REPAIR_REFUSE], ['order.type', '=', '2']]);
                    }

                ],
                1 => [
                    'name' => '未确认回寄',
//                    'param' => [['status', Order::REPAIR_FINISH ], ['quality', 1],['go_confirm', 0], ['order.type', '=', '2']]
                    'param' => function($query){
                $query->orwhere([['quality', '=', 1], ['status', '=', Order::REPAIR_FINISH], ['order.type', '=', '2'], ['go_confirm', '=', 0]])
                    ->orwhere([['status', '=', Order::REPAIR_REFUSE], ['order.type', '=', '2'], ['go_confirm', '=', 0]]);
            }
                ],
                2 => [
                    'name' => '已确认回寄',
//                    'param' => [['status', Order::REPAIR_FINISH ], ['quality', 1], ['go_confirm', 1], ['order.type', '=', '2']]
                    'param' => function($query){
                        $query->orwhere([['quality', '=', 1], ['status', '=', Order::REPAIR_FINISH], ['order.type', '=', '2'], ['go_confirm', '=', 1]])
                            ->orwhere([['status', '=', Order::REPAIR_REFUSE], ['order.type', '=', '2'], ['go_confirm', '=', 1]]);
                    }
                ]
            ];

            foreach ($option as $key => $value){
                $option[$key]['count'] = Order::where($value['param'])->count();
            }

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('sn', '寄修单号')->setPlaceHolder('光标移到此处扫码');
                $filter->like('phone', '手机号码');
                $filter->like('name', '联系人');
                $filter->equal('go_confirm', '回寄确认')->select([0 => '未确认', 1=>'已确认']);
            });
            //自定义状态快捷筛选按钮
            $grid->tools(function ($tools) use($option) {
                $tools->append(new QuickPickTool($option));
                $tools->batch(function ($batch) {
//                    $batch->add('取消订单', new Cancel());
                });
            });
            //根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])->orderBy('id', 'desc');
            }
            $grid->model()->where($option[-1]['param'])->orderBy('id', 'desc');

            $grid->id('ID')->sortable();
            $grid->column('agent_sn.agent_order_sn', '代理单号')->sortable();

            $grid->sn('维修订单号');
            $grid->barcode('S/N码');
            $grid->model_name('机型');
            $grid->name('联系人');
            $grid->phone('用户联系方式');
            $grid->status('订单状态')->display(function ($status) {
                $s = Order::STATUS;
                if (array_key_exists($status, $s)) {
                    return $s[$status];
                } else {
                    return "————";
                }
            });
            $grid->pay_amount('支付费用')->display(function ($amount) {
                return '￥' . $amount;
            });
            $grid->repair_user()->name('维修人')->display(function ($name) {
                if ($name == '')
                    return '———';
                return $name;
            });

            $grid->go_confirm('回寄确认')->color('#ffff00')->editable('select', [0 => '未确认', 1 => '已确认']);
            $grid->actions(function ($actions){
               $actions->disableEdit();
               $actions->disableDelete();
            });
            $grid->tools(function ($tools){
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                });
                $button = <<<EOF
                     <div class="btn-group pull-right" style="margin-right:15px; margin-top: 10px;">
                      <a href ="agent_order" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 返回
                      </a >
                    </div >
EOF;
                $tools->append($button);
            });
        });

    }


    protected function form(){
        return Admin::form(Order::class, function (Form $form){
//            $form->text('go_confirm', '回寄确认');
            $form->select('go_confirm', '回寄确认')->options();
        });
    }
}