<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Models\ChinaArea;
use App\Models\Damage;
use App\Models\Endpoint;
use App\Models\Machine;
use App\Models\MachineCategory;
use App\Models\MachineQualityTree;
use App\Models\Order;
use App\Models\OrderExtend;

use App\Models\PostExpress;
use App\Models\PostRepairEndpoint;
use App\Models\PostRepairQuality;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Widgets\Box;
use Encore\Admin\Widgets\Table;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\MessageBag;
use SimpleXMLElement;
use Symfony\Component\Console\Input\Input;

class RepairQualityController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修品检');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修品检');
            $content->description('编辑');

            $content->body($this->form()->edit($id));
        });
    }

    public function view(Content $content, $id = null)
    {
//        $order = Order::where(['id' => $id])->first();
//        $data = [
//            '机型名称' => $order->model_name,
//            'S/N码' => $order->barcode,
//            '保修状态' => Order::in_period[$order->in_period],
//            '受损状态' => Order::reason[$order->reason],
//            '故障类型' => $order->damage,
//        ];
//        $table = new Table([], $data);
//        $table->setStyle();
//        $content->header('寄修审核--查看');
//        $content->body((new Box('Table-2', $table))->style('info')->solid());
//        $content->body($table);
//        return $content;
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修品检--查看');
            $order = Order::where(['id' => $id])->first();

            $content->body(view('admin/post_check/view', compact('order')));

        });
    }

    public function quality_view(Content $content, $id = null)
    {
        $order = Order::where(['id' => $id])->first();
//        dd($order->machine_type['category_id']);
        $machine_category = MachineCategory::where('id', $order->machine_type['category_id'])->first();
//        dd(MachineQualityTree::category_tree($machine_category->id));
        $nodes = MachineQualityTree::category_tree($machine_category->id);
        $has_quality = PostRepairQuality::where('pr_sn', $order->sn)->get()->toArray();
        if ($has_quality) {
            $quality_history = array();
            foreach ($has_quality as $k => $v) {
                $quality_history[$v['mqt_id']] = $v['pass'];
            }
        } else {
            return $content->body('尚未品检');
        }
//        dd($quality_history);
        $content->header('品检结果');
        $content->row('<a href="javascript:history.back(-1)" class="btn btn-sm btn-default pull-right"><i class="fa fa-arrow-left"></i>&nbsp;返回</a>');
        $header = ['品检项目', '品检结果'];
        $row = array();
        foreach ($nodes as $v) {
            if (empty($v['children'])) {
//                $form->switch(strval($v['id']), strval($v['title']));
            } else {
//                $form->html(strval($v['title']));
                foreach ($v['children'] as $vv) {
                    if (empty($vv['children'])) {
//                        $form->switch(strval($v['id']), strval($v['title'].'&nbsp;&nbsp;>&nbsp;&nbsp;'.$vv['title']));
                    } else {
//                        $form->html(strval('&nbsp;&nbsp;&nbsp;&nbsp;'.$vv['title']));
                        foreach ($vv['children'] as $vvv) {
                            $row[] = [
                                strval($v['title'] . '&nbsp;&nbsp;>&nbsp;&nbsp;' . $vv['title'] . '&nbsp;&nbsp;>&nbsp;&nbsp;' . $vvv['title']),
                                empty($quality_history[$vvv['id']]) ? '未品检' : ($quality_history[$vvv['id']] == 1 ? '通过' : '<span style="color:red">不通过</span>')
                            ];
                        }

                    }
                }
            }
        }
        $table = new Table($header, $row);
        $content->body($table);
        return $content;
    }

    /**
     * 品检表单
     */
    public function quality_list(Content $content, $id = null)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
            // dd($data);
            foreach ($data as $k => $v) {
                if (intval($k) && intval($v) > 0) {
                    $save = [
                        'mqt_id' => intval($k),
                        'pass' => $v,
                        'pr_sn' => $data['sn'],
                        'user_id' => Admin::user()->id,
                    ];
                    PostRepairQuality::updateOrInsert(array('pr_sn' => $save['pr_sn'], 'mqt_id' => $save['mqt_id']), $save);
                }
            }

            // 记录品检时间,品检人
            OrderExtend::where('sn', $data['sn'])->update(array('quality_time' => date('Y-m-d H:i:s'), 'quality_uid' => Admin::user()->id));

            $order = Order::where('id', $data['id'])->first();
            $order->quality = $data['quality'];
            $order->save();
            return redirect('/admin/repair_quality');
        }

        $order = Order::where(['id' => $id])->first();
//        dd($order->machine_type['category_id']);
        $machine_category = MachineCategory::where('id', $order->machine_type['category_id'])->first();
//        dd(MachineQualityTree::category_tree($machine_category->id));
        $nodes = MachineQualityTree::category_tree($machine_category->id);
        $has_quality = PostRepairQuality::where('pr_sn', $order->sn)->get()->toArray();
        if ($has_quality) {
            $quality_history = array();
            foreach ($has_quality as $k => $v) {
                $quality_history[$v['mqt_id']] = $v['pass'];
            }
        }
//        dd($quality_history);
        $content->header('品检');
        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/repair_quality/quality_list');
        $form->hidden('id')->default($id);
        $form->hidden('sn')->default($order->sn);
        $states = [
            'on' => ['value' => 1, 'text' => '通过', 'color' => 'primary'],
            'off' => ['value' => 0, 'text' => '不通过', 'color' => 'default'],
        ];
        $options = [
            0 => '未品检',
            1 => '品检通过',
            2 => '品检打回',
        ];
        $form->display('description', '维修故障')->default(strval($order->description));
        foreach ($nodes as $v) {
            if (empty($v['children'])) {
                $form->switch(strval($v['id']), strval($v['title']));
            } else {
//                $form->html(strval($v['title']));
                foreach ($v['children'] as $vv) {
                    if (empty($vv['children'])) {
                        $form->switch(strval($v['id']), strval($v['title'] . '&nbsp;&nbsp;>&nbsp;&nbsp;' . $vv['title']));
                    } else {
//                        $form->html(strval('&nbsp;&nbsp;&nbsp;&nbsp;'.$vv['title']));
                        foreach ($vv['children'] as $vvv) {
                            $form->select(strval($vvv['id']), strval($v['title'] . '&nbsp;&nbsp;>&nbsp;&nbsp;' . $vv['title'] . '&nbsp;&nbsp;>&nbsp;&nbsp;' . $vvv['title']))
                                ->options($options)->setWidth(3, 7)->default($has_quality ? $quality_history[$vvv['id']] : 0);
                        }

                    }
                }
            }
        }
        $states = [
            'on' => ['value' => 1, 'text' => '通过', 'color' => 'primary'],
            'off' => ['value' => 2, 'text' => '打回', 'color' => 'default'],
        ];
        $form->select('quality', '品检评定')->options($options)->setWidth(3, 7)->default($order->quality);
        $content->body($form);
        return $content;
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOF
            //图片弹出层
            $('.img-thumbnail').click(
                 function(){
                     let src = $(this).attr('src');
                     let html = '<div class="message"><img style="max-width:900px;max-height:900px;" src="'+ src +'"><//div>'
                     $.fancybox.open(html);
                 }
             );

        // 品检时间记录
        $('.quality_time_begin').click(function(e){
            sn = e.currentTarget.dataset.sn
            layer.confirm('确认开始品检吗?', {
                btn: ['是', '否']
            }, function(){
                $.ajax({
                    url: '/admin/repair_check/saveRecordTime',
                    data: {
                        sn: sn,
                        type: 'quality_time'
                    },
                    dataType: 'json',
                    type: 'get',
                    success: function(res){
                        if(res.status == 1){
                            $('.grid-refresh').click();
                            layer.closeAll()
                        } else {
                            layer.msg(res.info);  
                        }
                    },
                    error:function(res){
                        console.log(res)
                    }
                })
            }, function(){
            });
        })
EOF;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
            $grid->disableCreation();
            $grid->disableExport();
            //快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => [['status', '=', Order::REPAIR_FINISH]],
                ],
                1 => [
                    'name' => '未品检',
                    'param' => [['status', '=', Order::REPAIR_FINISH], ['quality', '=', 0]],
                ],
                2 => [
                    'name' => '已品检',
                    'param' => [['status', '=', Order::REPAIR_FINISH], ['quality', '=', 1]],
                ],
                3 => [
                    'name' => '已打回',
                    'param' => [['status', '=', Order::REPAIR_FINISH], ['quality', '=', 2]],
                ],
            ];
            //筛选条数
            foreach ($option as $key => $value) {
                $option[$key]['count'] = Order::where($value['param'])->count();
            }
            //自定义状态快捷筛选按钮
            $grid->tools(function ($tools) use ($option) {
                $tools->append(new QuickPickTool($option));
                $tools->batch(function ($batch) {
//                    $batch->add('取消订单', new Cancel());
                });
            });
            //根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');
            }

            $grid->model()->where($option[-1]['param'])->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('sn', '寄修单号');
                $filter->like('barcode', '机器条码');
                $filter->equal('quality', '品检状态')->select([0 => '未品检', 1 => '品检通过', 2 => '品检打回']);
                $filter->equal('damage', '故障类型')->select(
                    Damage::pluck('title', 'title')->all()
                );
                $filter->equal('model_name', '产品型号')->select(
                    Machine::orderBy('name')->pluck('name', 'name')->all()
                );
                $filter->like('type', '寄修类型')->select(Order::post_repair_type);
                $data = DB::table('order as o')->join('admin_users as au', 'o.repair_man', '=', 'au.id')->select('o.repair_man', 'au.name')->distinct('repair_man')->get()->toArray();
                $map = array();
                foreach ($data as $key => $item) {
                    $map[$item->repair_man] = $item->name;
                }
                unset($data);
                $filter->equal('repair_man', '维修人')->select(
                    $map
                );

                $filter->between('order_extend.quality_time', '品检时间')->datetime();
            });

            $grid->id('ID')->sortable();
            Order::order_priority_column($grid);
            $grid->sn('寄修单号')->sortable();
            //$grid->model_name('产品型号');
            //$grid->barcode('SN');
            $grid->column('S/N码-机型')->display(function () {
                return $this->barcode . '<br/>' . $this->model_name;
            });
            $grid->damage('故障类型');
            $grid->description('故障详情描述');
            $grid->column('repair_user.name', '维修人员')->display(function ($value) {
                return $value;
            });
//            $grid->column('上传说明')->display(function ($value) {
//                $ret = '无';
//                $p = $this->upload_file;
//                if (is_array($p)) {
//                    $ret = '';
//                    foreach ($p as $key => $value) {
//                        $path = rtrim(config('admin.upload.host'), '/') . '/' . $value;
//                        $ret .= '<img src="' . $path . '" style="max-height:80px;max-width:80px" class="img img-thumbnail fancybox-thumbs" data=' . $this->id . ' i=' . $key . ' />';
//                        $ret .= '&nbsp;';
//                    }
//                }
//                return $ret;
//            });
//            $grid->phone('联系方式');
//            $grid->name('联系人');
//            $grid->come_exp_type('寄来方式')->display(function ($come_exp_type) {
//                return Order::come_exp_type[$come_exp_type];
//            });
            $grid->created_at('提交时间');
            $grid->quality('审批状态')->color('#ffff00')
                ->text(function ($audit_status) {
                    if ($audit_status == 1)
                        return '<span style="color:blue">品检通过</span>';
                    elseif ($audit_status == 2)
                        return '<span style="color:red">品检打回</span>';
                    else
                        return '未品检';
                })->editable('select', [0 => '未品检', 1 => '品检通过', 2 => '品检打回']);
            $grid->status('订单状态')->display(function ($value) {
                $s = Order::STATUS;
                if (array_key_exists($value, $s)) {
                    return $value . $s[$value];
                } else {
                    return "————";
                }
            });
            $grid->type('寄修类型')->display(function ($value) {
                return Order::post_repair_type[$value];
            });

            $grid->order_extend()->quality_time('品检时间')->display(function ($value) {
                if (!empty($value)) {
                    return $value . ' <button  class="btn btn-sm btn-danger quality_time_begin" data-sn=' . $this->sn . '>重新记录</button>';
                } else {
                    return '<button  class="btn btn-sm btn-primary quality_time_begin" data-sn=' . $this->sn . '>开始记录</button>';
                }
            });
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $status = $actions->row->status;
//                if ($actions->row->come_exp_type == 1 && $actions->row->audit_status == 1 && $status == Order::AUDIT_PASS) {
//                    $c = 'post_check/express/' . $actions->getKey();
//                    $html = '<a href="' . $c . '"><span style="color:blue">下单 </span></a>';
//                    $actions->append($html);
//                }
                $c = 'post_repair/view/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">查看 </span></a>';
                $actions->append($html);
                $c = 'repair_quality/quality_list/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">【品检】 </span></a>';
                $actions->append($html);
//                if ($status == Order::WAIT_AUDIT) {
//                    $c = 'post_check/' . $actions->getKey();
//                    $html = '<a href="' . $c . '/edit"><span style="color:orange">【审核】</span></a>';
//                    $actions->append($html);
//                } else {
//                    $c = 'post_check/' . $actions->getKey();
//                    $html = '<a href="' . $c . '/edit"><span style="color:green">【修改】</span></a>';
//                    $actions->append($html);
//                }
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $script = <<<EOF
            //图片弹出层
            $('.img-thumbnail').click(
                 function(){
                     let src = $(this).attr('src');
                     let html = '<div class="message"><img style="max-width:900px;max-height:900px;" src="'+ src +'"><//div>'
                     $.fancybox.open(html);
                 }
             );
EOF;
        Admin::script($script);
        return Admin::form(Order::class, function (Form $form) {
            $form->select('quality', '品检状态')->options();
        });
    }

}
