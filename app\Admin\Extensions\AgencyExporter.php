<?php

namespace App\Admin\Extensions;

use App\Models\Agency;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;

class AgencyExporter extends AbstractExporter
{
    use ExcelExportTrait;

    public function export()
    {
        $filename = '代理地区列表';
        // 根据上面的数据拼接出导出数据
        $titles = [
            '总代',
            '二代列表',
        ];
        $data = Agency::all()->toArray();
        $data = self::_generateTree($data);
        $formatData = [];
        if (!empty($data)) {
            foreach ($data as $key => $row) {
                $formatData[$key][] = $row['name'];
                foreach ($row['children'] as $k => $v) {
                    $formatData[$key][] = $v['name'];
                }
            }
        }
        array_unshift($formatData, $titles);
        ExcelExportTrait::exportToExcel($filename, $formatData);
    }

    //生成树
    private static function _generateTree($data, $pid = 0)
    {
        $tree = [];
        if ($data && is_array($data)) {
            foreach ($data as $v) {
                if ($v['pid'] == $pid) {
                    $tree[] = [
                        'id'       => $v['id'],
                        'name'     => $v['name'],
                        'pid'      => $v['pid'],
                        'children' => self::_generateTree($data, $v['id']),
                    ];
                }
            }
        }

        return $tree;
    }
}