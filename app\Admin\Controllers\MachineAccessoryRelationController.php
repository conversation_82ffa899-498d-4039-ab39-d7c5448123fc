<?php

namespace App\Admin\Controllers;

use App\Models\Agency;
use App\Models\Machine;
use App\Models\MachineAccessory;
use App\Models\MachineAccessoryRelation;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;

class MachineAccessoryRelationController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {
            $modelId = Input::get('modelId');
            $model = Machine::where('model_id', '=', $modelId)->value('name');
            $content->header("$model 配件列表");
            $content->description('机型配件列表');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form()->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('添加配件');
            $content->description('添加配件');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(MachineAccessoryRelation::class, function (Grid $grid) {
            $modelId = Input::get('modelId');
            $grid->disableFilter();
            $grid->model()->where('model_id', '=', $modelId);
            $grid->accessory()->title('配件名称');
            $grid->price('配件价格(元)')->editable();
            $grid->created_at('创建时间');
            $grid->updated_at('修改时间');

            $grid->actions(function ($actions) {
                $actions->disableEdit();
            });
            $grid->disableExport();
            $grid->disableCreation();
            $grid->tools(function ($tools) use ($modelId) {
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                });
                $exportImagesButton = <<<EOF
                    <div class="btn-group pull-right" style="margin-right:10px">
                      <a href ="machine_accessory_relation/create?modelId=$modelId" class="btn btn-sm btn-success" >
                        <i class="fa fa-print" ></i > 新增配件
                      </a >
                    </div >
                     <div class="btn-group pull-left" style="margin-left:5px">
                      <a href ="machine_type" class="btn btn-sm btn-success" >
                        <i class="fa fa-print" ></i > 返回
                      </a >
                    </div >
EOF;
                $tools->append($exportImagesButton);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Admin::form(MachineAccessoryRelation::class, function (Form $form) {
            $modelId = Input::get('modelId');
            $form->hidden('model_id')->value($modelId);
            $form->display('id', 'ID');
            $form->select('parent_id', '配件分类')->options(
                MachineAccessory::where('parent_id', '=', 0)->pluck('title', 'id')->prepend('请选择', '')
            )->load('accessory_id', '/admin/machine_accessory/getAccessoryByParentId');
            $form->select('accessory_id', '配件名称')->options(function ($id) {
                return MachineAccessory::options($id);
            })->rules('required');
            $form->text('price', '价格')->help('单位 : 元');
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '修改时间');
            $form->ignore(['parent_id']);

            $form->tools(function (Form\Tools $tools) {
                $tools->disableListButton();
            });
        });
    }

    public function getAccessoriesByModelId()
    {
        $modelId = Input::get('modelId');
        $accessory = MachineAccessoryRelation::where('model_id', '=', $modelId)->get();
        $accessory->map(function ($item) {
            $item->title = $item->accessory->title;

            return $item;
        });
        return $accessory;

    }


}
