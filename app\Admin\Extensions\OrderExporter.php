<?php

namespace App\Admin\Extensions;

use App\Models\Damage;
use App\Models\Endpoint;
use App\Models\Order;
use App\Models\OrderExtend;
use App\Models\OrderLog;
use App\Models\PayOrder;
use App\Traits\ExcelExportTrait;
use App\Traits\ExcelExport;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;
use App\Models\Agency;

class OrderExporter extends AbstractExporter
{
    public function export()
    {
        $filename = '寄修订单信息';
        $data = $this->getData();
        if (!empty($data)) {
            $titles = [
                'id',
                '寄修订单号',
                '订单状态',
                '机器条码',
                '上传的机器号',
                '机型名称',
                '颜色',
                '保内保外',
                '有没有保卡',
                '损坏原因',
                '受损类型',
                '实际故障类型',
                '联系人',
                '电话号码',
                '省',
                '市',
                '区',
//                '详细地址',
                '寄来快递类型',
                '公司内部寄来快递单号',
                '寄来快递单号',
                '寄来快递公司',
                '审核意见',
                '支付公司',
                '支付单号',
                '公司内部支付单号',
                '检查人',
                '维修处理',
                '配件费用',
                '快递费用',
                '总金额',
                '待付金额',
                '支付备注',
                '维修备注',
                '后台备注',
                '收到的配件',
                '寄去公司内部单号',
                '寄去快递单号',
                '寄去快递公司',
                '是否需要发票',
                '发票抬头',
                '发票税号',
                '发票接收邮箱',
                '提交订单时间',
                '回寄下单时间',
                '是否终端寄修',
                '是否二次维修',
                '签收时间',
                '故障导致原因分析',
                "市场拆修备注",
                '寄修类型'
            ];
            $formatData = $this->getFormatData($data);
            array_unshift($formatData, $titles);
            ExcelExportTrait::exportToExcel($filename, $formatData);
        }
    }

    private function getFormatData($datas)
    {
        foreach ($datas as $data) {
            $sign_time = OrderLog::where([['pr_sn', '=', $data['sn']], ['pr_status', '=', 400]])->pluck('date');
//            dd(count($sign_time));
            $order_extend = OrderExtend::where(['sn' => $data['sn']])
                ->select('pay_remark', 'backstage_remark', 'overhaul_remark')->first();
            $pay_remark = '';
            $backstage_remark = '';
            $overhaul_remark = '';
            if($order_extend){
                $pay_remark = $order_extend['pay_remark'];
                $backstage_remark = $order_extend['backstage_remark'];
                $overhaul_remark = implode(',', $order_extend['overhaul_remark']);
            }
            $damage_order = DB::table("damage as d")
                ->rightjoin("damage_order as `do`", "d.id", "=", "`do`.damage_id")
                ->where([['`do`.order_id', '=', $data['id']]])->pluck("d.title")->toArray();
            $damages = implode(',', $damage_order);
            $row = [
                $data['id'],
                $data['sn'],
                Order::STATUS[$data['status']],
                $data['barcode'].' ',
                $data['serial'].' ',
                $data['model_name'],
                $data['color'],
                Order::in_period[$data['in_period']],
                Order::has_warranty[$data['has_warranty']],
                Order::reason[$data['reason']],
                $data['damage'],
                $damages,
                $data['name'],
                $data['phone'].' ',
                $data['province'],
                $data['city'],
                $data['district'],
//                $data['address'],
                Order::come_exp_type[$data['come_exp_type']],
                $data['rb_come_exp_sn'],
                $data['come_exp_sn'].' ',
                $data['come_exp_com'],
                $data['audit_opinion'],
                PayOrder::COM[$data['pay_com']],
                $data['pay_sn'].' ',
                $data['rb_pay_sn'].' ',
                !empty($data['check_user']) ? $data['check_user']['name']: '无',
                $data['deal'],
                $data['accessory_cast'],
                $data['staff_cast'],
                $data['amount'],
                $data['pay_amount'],
                $pay_remark,
                $data['deal_remark'],
                $backstage_remark,
                $data['receive_case'],
                $data['rb_go_exp_sn'],
                $data['go_exp_sn'].' ',
                $data['go_exp_com'],
                $data['need_invoice'],
                $data['invoice_title'],
                $data['invoice_tax_id'],
                $data['invoice_email'],
                $data['created_at'],
                $data['updated_at_last'],
                Order::is_agency[$data['is_agency']],
                Order::repeat_repair[$data['repeat_order']],
                count($sign_time)>0?$sign_time[0]:'',
                implode(",", $data['repeat_remark']),
                $overhaul_remark,
                Order::post_repair_type[$data['type']],
            ];
            $formatData[] = $row;
//            dump($data['sn']);
//            dump($data['id']);
//            查询二次维修记录
            $repeat_order = Order::where('barcode', $data['barcode'])
                ->where([['id', '<>', $data['id']], ['status', '<>', -900], ['status', '<>', -200]])->where('created_at','<',$data['created_at'])->get();
//            dd(count($repeat_order));
            if (count($repeat_order)> 0){
                $row = ['二次维修记录'];
                $formatData[] = $row;
                foreach ($repeat_order as $data2){
//                dd($data2->check_user);
                    $sign_time2 = OrderLog::where([['pr_sn', '=', $data2->sn], ['pr_status', '=', 400]])->pluck('date');
//                    dd($sign_time2);
                    $order_extend = OrderExtend::where(['sn' => $data['sn']])
                        ->select('pay_remark', 'backstage_remark')->first();
                    $pay_remark = '';
                    $backstage_remark = '';
                    $overhaul_remark = '';
                    if($order_extend){
                        $pay_remark = $order_extend['pay_remark'];
                        $backstage_remark = $order_extend['backstage_remark'];
                        $overhaul_remark = implode(',', $order_extend['overhaul_remark']);
                    }
                    $damage_order = DB::table("damage as d")
                        ->rightjoin("damage_order as `do`", "d.id", "=", "`do`.damage_id")
                        ->where([['`do`.order_id', '=', $data['id']]])->pluck("d.title")->toArray();
                    $damages = implode(',', $damage_order);
                    $row = [
                        $data2->id,
                        $data2->sn,
                        Order::STATUS[$data2->status],
                        $data2->barcode.' ',
                        $data2->serial.' ',
                        $data2->model_name,
                        $data2->color,
                        Order::in_period[$data2->in_period],
                        Order::has_warranty[$data2->has_warranty],
                        Order::reason[$data2->reason],
                        $data2->damage,
                        $damages,
                        $data2->name,
                        $data2->phone.' ',
                        $data2->province,
                        $data2->city,
                        $data2->district,
//                        $data2->address,
                        Order::come_exp_type[$data2->come_exp_type],
                        $data2->rb_come_exp_sn,
                        $data2->come_exp_sn.' ',
                        $data2->come_exp_com,
                        $data2->audit_opinion,
                        PayOrder::COM[$data2->pay_com],
                        $data2->pay_sn.' ',
                        $data2->rb_pay_sn.' ',
                        !empty($data2->check_user) ? $data2->check_user->name: '无',
                        $data2->deal,
                        $data2->accessory_cast,
                        $data2->staff_cast,
                        $data2->amount,
                        $data2->pay_amount,
                        $pay_remark,
                        $data2->deal_remark,
                        $backstage_remark,
                        $data2->receive_case,
                        $data2->rb_go_exp_sn,
                        $data2->go_exp_sn.' ',
                        $data2->go_exp_com,
                        $data2->need_invoice,
                        $data2->invoice_title,
                        $data2->invoice_tax_id,
                        $data2->invoice_email,
                        $data2->created_at,
                        $data2->updated_at_last,
                        Order::is_agency[$data2->is_agency],
                        Order::repeat_repair[$data2->repeat_order],
                        count($sign_time2)>0?$sign_time2[0]:'',
                        implode(",", $data2->repeat_remark),
                        $overhaul_remark,
                    ];
                    $formatData[] = $row;

            }
                $row = [''];
                $formatData[] = $row;
            }

        }

        return $formatData;
    }


}
