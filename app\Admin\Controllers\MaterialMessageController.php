<?php

namespace App\Admin\Controllers;

use App\Models\MaterialMessage;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;

//物料留言管理
class MaterialMessageController extends Controller {
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index() {
        return Admin::content(function (Content $content) {

            $content->header('物料留言');
            $content->description('物料留言');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id) {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('物料留言编辑');
            $content->description('物料留言编辑');

            $content->body($this->form()->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create() {
        return Admin::content(function (Content $content) {

            $content->header('物料留言添加');
            $content->description('物料留言添加');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid() {
        return Admin::grid(MaterialMessage::class, function (Grid $grid) {
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('title', '留言标题');
            });
            $grid->disableExport();
            $grid->disableCreation();
            $grid->author('留言人');
            $grid->title('留言标题');
            $grid->created_at('留言时间');
            $states = [
                'on'  => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ];
            $grid->column('is_passed', '是否通过')->switch($states)->sortable();
            $grid->replied_at('回复时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form() {
        return Admin::form(MaterialMessage::class, function (Form $form) {

            $form->display('title', '留言标题');
            $form->textarea('content', '留言内容');
            $form->display('author', '留言人名称');
            $form->display('created_at', '留言时间');
            $form->textarea('reply_content','回复内容');
            $form->display('replied_at','回复时间');
            $states = [
                'on'  => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ];
            $form->switch('is_passed','是否通过')->states($states);
        });
    }
}
