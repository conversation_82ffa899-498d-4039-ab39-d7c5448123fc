<?php

namespace App\Admin\Extensions\Tools;

use Encore\Admin\Grid\Tools\BatchAction;

class ExpGoOneOrder extends BatchAction
{

    public function script()
    {
        return <<<EOT

$('{$this->getElementClass()}').on('click', function() {
    var res = confirm("确定要下同一个快递订单?");
    if (res == true)
    {
        /**
         * param 将要转为URL参数字符串的对象
         * key URL参数字符串的前缀
         * encode true/false 是否进行URL编码,默认为true
         *
         * return URL参数字符串
         */
        var urlEncode = function (param, key, encode) {
          if(param==null) return '';
          var paramStr = '';
          var t = typeof (param);
          if (t == 'string' || t == 'number' || t == 'boolean') {
            paramStr += '&' + key + '=' + ((encode==null||encode) ? encodeURIComponent(param) : param);
          } else {
            for (var i in param) {
              var k = key == null ? i : key + (param instanceof Array ? '[' + i + ']' : '.' + i);
              paramStr += urlEncode(param[i], k, encode);
            }
          }
          return paramStr;
        };
        var data = {
            _token:LA.token,
            ids: selectedRows()
        }
        var params = urlEncode(data);
        window.location.href = '{$this->resource}/express_one_order?'+ params;
//       $.ajax({
//        method: 'get',
//        url: '{$this->resource}/express_one_order',
//        data: {
//            _token:LA.token,
//            ids: selectedRows()
//        },
//        success: function () {
//            $.pjax.reload('#pjax-container');
//            toastr.success('操作成功');
//        }
//    });
    }
    else
    {
      alert("You pressed Cancel!");
    }
});

EOT;

    }
}