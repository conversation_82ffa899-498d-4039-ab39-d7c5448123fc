<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>an
 * Date: 2017/8/25
 * Time: 11:03
 */
namespace App\Api\Permission;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ApiPermission extends Model {
    function __construct() {
        $this->setTable(config('admin.database.users_table'));
        parent::__construct();
    }

    /**
     * A user has and belongs to many roles.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function roles() {
        $pivotTable = config('admin.database.role_users_table');

        $relatedModel = config('admin.database.roles_model');

        return $this->belongsToMany($relatedModel, $pivotTable, 'user_id', 'role_id');
    }

    /**
     * A User has and belongs to many permissions.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function permissions() {
        $pivotTable = config('admin.database.user_permissions_table');

        $relatedModel = config('admin.database.permissions_model');

        return $this->belongsToMany($relatedModel, $pivotTable, 'user_id', 'permission_id');
    }

    /**
     * Check if user has permission.
     *
     * @param $permission
     *
     * @return bool
     */
    public function can($permission) {
        if ($this->isAdministrator()) {
            return true;
        }

        //先判断该用户是否直接有该权限
        if ($this->permissions()->where('slug', $permission)->exists()) {
            return true;
        }

        //再判断角色是否有这权限
        foreach ($this->roles as $role) {
            //            if ($role->can($permission)) {
            //                return true;
            //            }
            $rolePermission = DB::table('admin_role_permissions')
                ->leftjoin('admin_permissions', 'permission_id', 'admin_permissions.id')
                ->where('slug', $permission)
                ->where('role_id', $role->id)
                ->exists();
            if ($rolePermission) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user has no permission.
     *
     * @param $permission
     *
     * @return bool
     */
    public function cannot($permission) {
        return !$this->can($permission);
    }

    /**
     * Check if user is administrator.
     *
     * @return mixed
     */
    public function isAdministrator() {
        return $this->isRole('administrator');
    }

    /**
     * Check if user is $role.
     *
     * @param string $role
     *
     * @return mixed
     */
    public function isRole($role) {
        return $this->roles()->where('slug', $role)->exists();
    }

    /**
     * Check if user in $roles.
     *
     * @param array $roles
     *
     * @return mixed
     */
    public function inRoles($roles = []) {
        return $this->roles()->whereIn('slug', (array)$roles)->exists();
    }
}
