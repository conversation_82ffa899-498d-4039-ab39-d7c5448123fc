<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\AfterSalesUserExporter;
use App\Models\PostRepairEndpoint;
use App\Models\Agency;
use App\Models\RepairStaff;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;

use Illuminate\Support\Facades\Input;

//寄修用户管理,这里的寄修用户也是新建的,和admin_users表通过pr_staff关联表关联起来
class RepairStaffController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修用户');
            $content->description('寄修用户');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修用户');
            $content->description('寄修用户');

            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修用户');
            $content->description('寄修用户');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(RepairStaff::class, function (Grid $grid) {
            $grid->exporter(new AfterSalesUserExporter());
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('admin_user.name', '寄修技师姓名');
            });
            $grid->id()->sortable();
            $grid->admin_user()->name('寄修技师姓名');
//            $grid->company_type('寄修技师类别');
            $grid->repair_endpoint()->name('寄修终端名称');

            $grid->admin_user()->phone('联系方式');
            $status = [
                'on' => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ];
            $grid->status('是否启用')->switch($status);
            $grid->repair_allot('是否启用维修分派')->switch($status);
            $grid->wechat_id('企业微信工号')->editable();
            $grid->actions(function ($action) {
                $action->disableDelete();
                $c = 'post_allot';
                $k = $this->row->admin_user['id'];
                $html = '<a href="'.$c.'/'.$k.'"><span style="color:red">【分派】</span></a>';
                $action->append($html);
            });
        });
    }

    /**
     * Make a form builder.
     * @param id
     * @return Form
     */
    protected function form($id = null)
    {
        //为了让选择一级代理的时候也能显示出终端列表,要注入js
        //这里使用name来定位元素是因为不会与已有组件点击事件冲突
        $script = <<<EOF
        $(document).on('change', "[name='top_agency'],[name='second_agency']", function () {
          var target = $(this).closest('.fields-group').find(".endpoint_id");
          if(this.value!="0"){
            $.get("/admin/after_sales_endpoint/getEndpointByAgencyId?q="+this.value, function (data) {
                target.find("option").remove();
                $(target).select2({
                    data: $.map(data, function (d) {
                        d.id = d.id;
                        d.text = d.text;
                        return d;
                    })
                }).trigger('change');
            });
          }
        });
EOF;
        Admin::script($script);

        return Admin::form(RepairStaff::class, function (Form $form) use ($id) {

            $endpointId = 0;
            $form->select('endpoint_id', '所属寄修终端')->options(function () {
                return PostRepairEndpoint::all()->pluck('name', 'id');
            })->default($endpointId)->rules('required');
            $form->switch('status', '是否启用')->default(1);
            $form->switch('repair_allot', '是否启用维修分派')->default(1);
            $form->display('admin_user.name', '寄修工程师姓名');
//            $form->select('work_type', '寄修工程师类型')->options(['编内' => '编内', '编外' => '编外']);
            $form->select('through_training', '是否通过培训')->options(['0' => '未通过', '1' => '通过']);
            $form->text('admin_user.phone', '联系方式');
            $form->text('identity_card', '身份证号码');
            $form->select('education', '学历')->options([
                '无' => '无',
                '小学' => '小学',
                '初中' => '初中',
                '高职' => '高职',
                '技校' => '技校',
                '高中' => '高中',
                '中职' => '中职',
                '中技' => '中技',
                '高技' => '高技',
                '中专' => '中专',
                '大专' => '大专',
                '本科' => '本科',
            ]);
            $form->date('entry_time', '入职时间');
            $form->textarea('remark', '备注');
            $form->text('service_phone_username', '客服电话用户名');
            $form->text('service_phone_password', '客服电话密码');
            $form->text('wechat_id', "企业微信工号");
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');

            //因为不是用关联关系直接创建,所以要在表单忽略这些,要不然会直接写入到user_after_sales表,导致出错
            $form->ignore([
                'admin_user.phone',
                'top_agency',
                'second_agency',
                'admin_user.username',
                'admin_user.name',
                'admin_user.password',
                'admin_user.password_confirmation',
            ]);
        });
    }


}
