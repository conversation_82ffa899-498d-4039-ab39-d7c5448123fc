<?php


namespace App\Models;


use App\User;
use Illuminate\Database\Eloquent\Model;

class OrderExtend extends Model
{
    protected $table = "order_extend";
    protected $fillable = ['sn', 'pay_remark', 'sign_in_pictures', 'sign_in_videos'];

    public function print_user()
    {
        return $this->hasOne(User::class, 'id', 'print_man');
    }

    public function setSignInPicturesAttribute($values)
    {
        if (is_array($values)) {
            $this->attributes['sign_in_pictures'] = json_encode($values);
        }
    }

    public function getSignInPicturesAttribute($values)
    {
        return json_decode($values, true);
    }

    public function setSignInVideosAttribute($values)
    {
        if (is_array($values)) {
            $this->attributes['sign_in_videos'] = json_encode($values);
        }
    }

    public function getSignInVideosAttribute($values)
    {
        return json_decode($values, true);
    }

    public function getOverhaulRemarkAttribute($value)
    {
        return explode(',', $value);
    }

    public function setOverhaulRemarkAttribute($value)
    {
        $this->attributes['overhaul_remark'] = implode(',', $value);
    }
}
