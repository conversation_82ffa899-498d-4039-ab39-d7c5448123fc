<?php


namespace App\Admin\Controllers;


use App\Http\Controllers\Controller;
use App\Models\PseudoCode;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;
use function foo\func;


class PseudoCodeController extends Controller
{
    use ModelForm;

    public function index(){
        return Admin::content(function (Content $content){
           $content->header('伪码记录');
           $content->description('伪码记录列表');
           $content->body($this->grid());
        });
    }

//    public function edit(){
//        return Admin::content(function (Content $content){
//            $content->header('伪码');
//        });
//    }
    public function grid(){
        return Admin::grid(PseudoCode::class, function (Grid $grid){
            $grid->disableExport();
            $grid->disableCreation();
            $grid->actions(function ($actions){
                $actions->disableDelete();
                $actions->disableEdit();
//                dd($actions->row->order_sn['id']);
                $c = 'post_repair/view/' . $actions->row->order_sn['id'];
                $html = '<a href="' . $c . '"><span style="color:blue">查看 </span></a>';
                $actions->append($html);
            });
            $grid->model()->where([['status', '=', '1']]);
            $grid->id('ID');
            $grid->model_name('寄修机型');
            $grid->barcode('伪码');
            $grid->column('order_sn.sn', '寄修单号');

        });
    }
}