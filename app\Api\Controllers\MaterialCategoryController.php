<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 2017/8/17
 * Time: 10:39
 */

namespace App\Api\Controllers;


use App\Models\MaterialCategory;
use App\Services\Admin\MaterialCategoryService;

class MaterialCategoryController extends BaseController {

    protected $service;

    public function __construct(MaterialCategoryService $service) {
        $this->service = $service;
    }

    public function index() {
        $materialCategories = $this->service::getTree();
        return $this->returnArray($materialCategories);
    }
}