<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Material extends Model
{
    protected $table = 'material';

    public static function getNameArray(array $ids)
    {
        return self::whereIn('id', $ids)->pluck('name', 'id');
    }

    public static function priceList()
    {
        $data = self::where('price', '>', '0')->get()->all();
        $ret = array();
        if ($data) {
            foreach ($data as $d) {
                $ret[$d->id] = $d->name . $d->specification . ' | 价格：' . $d->price . '【' . $d->quantity . '件】';
            }
        }
        return $ret;
    }
}
