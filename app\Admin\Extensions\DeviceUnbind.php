<?php

namespace App\Admin\Extensions;

use GuzzleHttp\Client;

/**
 * 设备解除绑定
 */
class DeviceUnbind
{
    /**
     * 平板解除绑定(家长助手)
     * @param $imei 对应平板序列号
     * 
     * @return void
     */
    public function unbindPad($imei){
        $url = 'parentadmin.readboy.com/v1/machine/cancel_bindings';

        // 接口签名 sn = uid + timestamp + md5(timestamp + appsec + checkstr) + appid 
        $appid = 'parentsadmin';
        $appsec = '9b332c2653ce7189da101dac5a63fd4e';
        $checkstr = md5($appid);
        $sn = '00000000' . time() . md5(time() . $appsec . $checkstr) . $appid;

        $data = array(
            'sn' => $sn,
            'imei' => $imei,
        );
        $param = http_build_query($data);
        $url = $url.'?'.$param;

        $client = new Client();
        $response = $client->request('GET', $url);

        $body = $response->getBody();
        $content = $body->getContents();
        $content = json_decode($content , 1);

        return $content;
    }

    /**
     * 手表解绑
     * @param $imei 对应平板序列号
     * 
     * @return void
     */
    public function unbindWatch($imei){
        $url = 'http://wearapi:<EMAIL>:8080/api/renovate?';

        $data = array(
            'imei' => $imei,
        );
        $param = http_build_query($data);
        $url = $url . $param;

        $client = new Client();
        $response = $client->request('GET', $url);

        $body = $response->getBody();
        $content = $body->getContents();
        $content = json_decode($content , 1);

        return $content;
    }

}
