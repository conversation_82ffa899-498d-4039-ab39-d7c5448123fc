<?php

namespace App\Admin\Controllers;

use App\Models\Material;
use App\Models\MaterialCategory;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Support\Facades\DB;
use Encore\Admin\Tree;
use Illuminate\Support\Facades\Storage;

//物料分类管理
class MaterialCategoryController extends Controller {
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index() {
        return Admin::content(function (Content $content) {

            $content->header('物料分类');
            $content->description('物料分类列表');

            $content->body($this->tree());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id) {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('物料分类');
            $content->description('物料分类编辑');

            $content->body($this->form()->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create() {
        return Admin::content(function (Content $content) {

            $content->header('物料分类');
            $content->description('物料分类创建');

            $content->body($this->form());
        });
    }

    /**
     * Make a form builder.
     */
    protected function tree() {
        return MaterialCategory::tree(function (Tree $tree) {

            $tree->branch(function ($branch) {
                return "{$branch['name']}";
            });
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid() {
        return Admin::grid(MaterialCategory::class, function (Grid $grid) {

            $grid->id('ID')->sortable();

            $grid->created_at();
            $grid->updated_at();
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form() {
        return Admin::form(MaterialCategory::class, function (Form $form) {

            $form->display('id', 'ID');
            $form->select('pid', '选择父分类')->options(MaterialCategory::selectOptions());
            $form->text('name', '名称')->rules('required');
            $form->text('level', '级别')->help('分类级别,一级填1,二级填2')->rules('required');
            $form->display('updated_at', '更新时间');
        });
    }
}
