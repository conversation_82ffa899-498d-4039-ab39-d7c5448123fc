<?php

namespace App\Admin\Controllers;

use App\Models\Damage;

use App\Models\MachineCategory;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Tree;

//机型品类管理,比如某个机型属于什么品类(学生平板.教育平板等等..)
class DamageController extends Controller {
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index() {
        return Admin::content(function (Content $content) {
           
            $content->header('机型品类故障');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id) {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('机型品类故障');
            $content->description('');

            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create() {
        return Admin::content(function (Content $content) {

            $content->header('机型品类故障');
            $content->description('');

            $content->body($this->form());
        });
    }

    /**
     * Make a form builder.
     *
     * @return Tree
     */
    protected function tree() {
        return Damage::tree(function (Tree $tree) {

            $tree->branch(function ($branch) {
                return "{$branch['name']}";
            });
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid() {
        return Admin::grid(Damage::class, function (Grid $grid) {
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('title', '名称');
                $filter->is('machine_category_id', '机型品类')->select(MachineCategory::all()->pluck('name', 'id'));
            });

            $grid->id('ID')->sortable();
            $grid->title('名称')->sortable()->editable();
            $grid->column('image_front', '正面样例图')->image(null, 50, 50);
            $grid->column('image_back', '背面样例图')->image(null, 50, 50);
            $grid->machine_category_id('机型品类')->display(function($value){
                if ($value) {
                    return MachineCategory::find($value)->name;
                }
                return '无';
            });
            $status = [
                'on' => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ];
            $grid->visible('寄修是否可见')->switch($status)->editable();
            $grid->self_test('自检是否可见')->switch($status)->editable();
            $grid->tools(function ($tools) {
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                });
            });
            $grid->actions(function ($actions) {
//                $actions->disableDelete();
//                //这个是主键id,不是模型id
//                $key = $actions->row->id;
//                $html = <<<EOF
//                |   <a href='accessory_price_offer?model_id=$key' title='故障管理'>
//                      <i class='fa fa-briefcase'> 故障表</i>
//                    </a>
//EOF;
//                $actions->append($html);
//                $c = 'machine_quality_tree';
//                $k = $actions->row->id;
//                $html = '|<a href="'.$c.'?machine_category_id='.$k.'"><span style="color:red">【品检树】</span></a>';
//                $actions->append($html);
            });

        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id=null) {
        return Admin::form(Damage::class, function (Form $form) {

            $form->display('id', 'ID');
            $form->text('title', '名称')->rules('required');
            $form->image('image_front', '正面样例图')->move('rbcare/image/damage');
            $form->image('image_back', '背面样例图')->move('rbcare/image/damage');
            $form->text('description', '描述');
            $form->select('machine_category_id', '机型品类')->options(MachineCategory::all()->pluck('name', 'id')->prepend('请选择', 0));
            $form->switch('visible', '寄修是否可见')->rules('required');
            $form->switch('self_test', '自检是否可见')->rules('required');
            $form->display('updated_at', '更新时间');
        });
    }
}
