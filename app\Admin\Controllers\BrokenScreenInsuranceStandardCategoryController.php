<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\MachineAccessoryExporter;
use App\Models\BrokenScreenInsuranceStandard;
use App\Models\MachineAccessory;

use App\Models\Machine;
use App\Models\MachineCategory;
use App\Models\ModelCategory;
use App\Services\Admin\ModelCategoryService;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

class BrokenScreenInsuranceStandardCategoryController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('投保类型');
            $content->description('投保类型列表');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('机型分类');
            $content->description('机型分类编辑');

            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('机型分类');
            $content->description('机型分类创建');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(Machine::class, function (Grid $grid) {

            //$grid->exporter(new MachineAccessoryExporter());
            $grid->disableCreation();
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('name', '机型名称');
                $filter->is('category_id', '机器品类')
                    ->select(MachineCategory::where('visible', 1)->pluck('name', 'id'));
                //  ->load('model_id', '/admin/machine_type/category');
                $filter->is('model_id', '机器型号')
                    ->select(Machine::where('visibility', 1)->orderBy('name', 'asc')->pluck('name', 'model_id'));
            });
            $grid->model()->where('visibility', 1)->orderBy('id', 'desc');

            $grid->id('ID')->sortable();
            $grid->model_id('机型id')->sortable();
            $grid->name('型号')->label('primary');
            $grid->category()->name('所属分类');
            $grid->column('full_name', '投保类型数')->display(function () {
                return BrokenScreenInsuranceStandard::where('model_id', '=', $this->model_id)->count();
            });

            $grid->tools(function ($tools) {
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                });
            });
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();

                $c = 'broken_screen_insurance_standard';
                $category_id = $actions->row->category_id;
                $model_id = $actions->row->model_id;
                $count = BrokenScreenInsuranceStandard::where('model_id', '=', $model_id)->count();
                if ($count > 0) {
                    $html = '<a href="' . $c . '?category_id=' . $category_id . '&model_id=' . $model_id .
                        '"><span style="color:dodgerblue">【投保标准表】</span></a>';
                } else {
                    $html = '<a href="' . $c . '?category_id=' . $category_id . '&model_id=' . $model_id .
                        '"><span style="color:#f56954">【新增标准表】</span></a>';
                }
                $actions->append($html);

            });

        });
    }

    /**
     * Make a form builder.
     * @param id
     * @return Form
     */
    protected function form($id = null)
    {
        return Admin::form(Machine::class, function (Form $form) use ($id) {
            $form->display('name', '机器型号');
            $categories = MachineCategory::where('visible', 1)->get()->pluck('name', 'id')->prepend('请选择', 0);
            $form->select('category_id', '型号品类')->options($categories);
//            $form->currency('company_price', '公司价格')->symbol('￥');
//            $form->currency('top_agency_price', '总代价格')->symbol('￥');
//            $form->currency('second_agency_price', '二代价格')->symbol('￥');
//            $form->currency('customer_price', '顾客价格')->symbol('￥');
            $states = [
                'on' => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ];
            $form->switch('visibility', '是否可见')->states($states);
            $form->display('updated_at', '更新时间');
        });
    }

    public function machine_category(Request $request)
    {
        $machine_category_id = request()->get('q');
        return Machine::where([['category_id', '=', $machine_category_id], ['visibility', '=', 1]])
            ->get(['model_id as id', 'name as text']);
    }
}
