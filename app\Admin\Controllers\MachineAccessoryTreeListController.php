<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\MachineAccessoryExporter;
use App\Models\MachineAccessory;

use App\Models\Machine;
use App\Models\MachineCategory;
use App\Models\MachineMalfunction;
use App\Models\ModelCategory;
use App\Services\Admin\ModelCategoryService;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;

use Illuminate\Support\Facades\DB;

class MachineAccessoryTreeListController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('配件关系');
            $content->description('机型列表');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('机型分类');
            $content->description('机型分类编辑');

            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('机型分类');
            $content->description('机型分类创建');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOF
        $('.batch_add_case').click(function(e){
            model_id = e.currentTarget.dataset.model_id
            var html =  '<div style="min-width:200px">'+
                            '<select id="caseSelect" name="caseSelect"><\/select>'+
                            '<br>'+
                            '<br>'+
                            '<br>'+
                            '<a class="btn btn-primary pull-right" type="button" id="save" >确定<\/a>'+
                        '<\/div>';

            $.fancybox.open(html);

            $.ajax({
                url: '/admin/machine_accessory_tree_list/get_malfunction_info',
                data: {
                },
                type: 'get',
                dataType: 'json',
                success: function(res){
                    var html = '<option value="0">请选择症状</option>';
                    $('#caseSelect').html('');
                    $.each(res , function(i , v){
                        html += '<option value="'+v.id+'">'+v.title+'</option>';
                    })

                    $('#caseSelect').append(html);
                }
            })

            $('#save').click(function(){
                if($("select[name='caseSelect']").val() == 0){
                    layer.msg('请选择症状');
                    return false;    
                }
                $.ajax({
                    url: '/admin/machine_accessory_tree_list/set_malfunction',
                    data: {
                        model_id: model_id,
                        malfunction_id: $("select[name='caseSelect']").val(),
                    },
                    type: 'get',
                    dataType: 'json',
                    success: function(res){
                        console.log(res)
                        layer.msg(res.info);
                        if(res.status == 1){
                            window.location.reload()
                        }
                    },
                    error:function(res){
                        console.log(res)
                    }
                })
            })
        })

EOF;
        Admin::script($script);
        return Admin::grid(Machine::class, function (Grid $grid) {

            $grid->exporter(new MachineAccessoryExporter());
            $grid->disableCreation();
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('name', '机器型号');
                $filter->is('category_id', '机器品类')->select(MachineCategory::all()->pluck('name', 'id'));
                $filter->is('visibility', '支持寄修（是|否')->select([0 => '否', 1 => '是']);
            });

            $grid->model_id('ID')->sortable();

            $grid->name('型号')->label('primary');
            $grid->actions(function ($actions) {
                $actions->disableDelete();

            });
            $grid->category()->name('所属分类');
//            $grid->customer_price('顾客价格')->sortable()->editable();
            $states = [
                'on'  => ['value' => 1, 'text' => '是', 'color' => 'primary'],
                'off' => ['value' => 0, 'text' => '否', 'color' => 'default'],
            ];
            $grid->visibility('支持寄修（是|否）')->display(function($value){
                if ($value)
                    return '是';
                else
                    return '否';
            });
            $grid->tools(function ($tools) {
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                });
            });
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                //这个是主键id,不是模型id
//                $key = $actions->row->model_id;
//                $html = <<<EOF
//                    <a href='accessory_price_offer?model_id=$key' title='配件报价管理'>
//                      <i class='fa fa-briefcase'> 配件报价</i>
//                    </a>
//EOF;
//                $actions->append($html);
                $c = 'machine_accessory_tree';
                $k = $actions->row->model_id;
                $html = '<a href="'.$c.'?model_id='.$k.'"><span style="color:red">【配件树】</span></a>';
                $actions->append($html);

                $html = '<a href="JavaScript:void(0);" class="batch_add_case" data-model_id="'.$actions->row->model_id.'" > 批量添加配件树症状 </a>';
                $actions->append($html);
            });

        });
    }

    /**
     * Make a form builder.
     * @param id
     * @return Form
     */
    protected function form($id = null)
    {
        return Admin::form(Machine::class, function (Form $form) use ($id) {
            $form->display('name', '机器型号');
            $categories = MachineCategory::where('visible', 1)->get()->pluck('name', 'id')->prepend('请选择', 0);
            $form->select('category_id', '型号品类')->options($categories);
//            $form->currency('company_price', '公司价格')->symbol('￥');
//            $form->currency('top_agency_price', '总代价格')->symbol('￥');
//            $form->currency('second_agency_price', '二代价格')->symbol('￥');
//            $form->currency('customer_price', '顾客价格')->symbol('￥');
            $states = [
                'on' => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ];
            $form->switch('visibility', '是否可见')->states($states);
            $form->display('updated_at', '更新时间');
        });
    }

    /**
     * 获取配件症状
     */
    public function getMalfunctionInfo(){

        $list = MachineMalfunction::get();
        return $list;
    }

    /**
     * 设置配件症状
     */
    public function setMalfunction(){
        $model_id = request()->input('model_id');
        $malfunction_id = request()->input('malfunction_id');

        // 获取该机型所有配件ID
        $accessoryIds = DB::table('machine_accessory_tree')->where('model_id', $model_id)->pluck('id')->toArray();

        foreach($accessoryIds as $key => $value){

            if(!DB::table('machine_accessory_malfunction_relation')->where('mat_id', $value)->where('malfunction_id' , $malfunction_id)->first()){
                DB::table('machine_accessory_malfunction_relation')->insert(array('mat_id' => $value , 'malfunction_id' => $malfunction_id));
            }
        }
        return array('status' => 1 , 'info' => '操作成功');
    }

}
