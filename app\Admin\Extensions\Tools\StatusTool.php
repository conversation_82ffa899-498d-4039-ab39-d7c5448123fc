<?php
/**
 * Created by PhpStorm.
 * User: 1
 * Date: 2017/5/15
 * Time: 14:02
 */

namespace App\Admin\Extensions\Tools;


use Encore\Admin\Grid\Tools\AbstractTool;
use Encore\Admin\Admin;
use Illuminate\Support\Facades\Request;

class StatusTool extends AbstractTool
{
    protected function script()
    {
        $url = Request::fullUrlWithQuery(['status' => '_status_']);

        return <<<EOT

$('input:radio.user-gender').change(function () {

    var url = "$url".replace('_status_', $(this).val());

    $.pjax({container:'#pjax-container', url: url });

});

EOT;
    }

    public function render()
    {
        Admin::script($this->script());

        $options = [
            0   => '全部',
            1     => '等待审核',
            2     => '大区审核通过',
            3     => '领导审核通过',
            4     => '活动已完成',
            5     => '核销通过',
            6     => '审核失败',
            7     => '活动未完成',
            8     => '核销失败',
        ];

        return view('admin.status', compact('options'));
    }
}