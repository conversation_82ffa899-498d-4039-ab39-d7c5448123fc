<?php

namespace App\Admin\Extensions;

use App\Models\Endpoint;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;
use App\Models\Agency;

class PaperExporter extends AbstractExporter
{
    public function export()
    {
        $filename = '试卷列表';
        $data = $this->getData();
        if (!empty($data)) {
            $titles = [
                '试卷id',
                '名称',
                '详情',
                '考试时长（分钟）',
                '出题数',
                '已有题目数',
                '开始时间',
                '结束时间',
                '更新时间',
            ];
            $formatData = $this->getFormatData($data);
            array_unshift($formatData, $titles);
            ExcelExportTrait::exportToExcel($filename, $formatData);
        }
    }

    private function getFormatData($data)
    {
        foreach ($data as $row) {
            $row = [
                $row['id'],
                $row['name'],
                $row['description'],
                $row['duration'],
                $row['number'],
                count($row['questions']),
                $row['start'],
                $row['end'],
                $row['updated_at'],
            ];
            $formatData[] = $row;
        }

        return $formatData;
    }


}