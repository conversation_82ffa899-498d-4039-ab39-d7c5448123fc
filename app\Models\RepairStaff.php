<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\User;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;


class RepairStaff extends Model
{
    //权限管理的角色Id
    const AFTER_SALES_ENGINEER = 17;

    protected $table = 'pr_staff';

    public static function boot()
    {
        parent::boot();
        static::saving(function (Model $model) {

            //判断密码修改的处理
//            if (Request::method() === "PUT") {
//                $user = User::find($model->user_id);
//                if ($model->admin_user->password != Input::get('admin_user.password')) {
//                    $user->password = bcrypt(Input::get('admin_user.password'));
//                }
//            } else {
//                //同时新建用户
//                $user = new User();
//                $user->password = bcrypt(Input::get('admin_user.password'));
//            }
//            $user->username = Input::get('admin_user.username');
//            $user->name = Input::get('admin_user.name');
//            $user->phone = Input::get('admin_user.phone');
//            $user->save();

//            $model->user_id = $user->id;
//            if (Request::method() !== "PUT") {
//                //赋角色给当前新增用户
//                DB::table('admin_role_users')->insert([
//                    'role_id' => self::AFTER_SALES_ENGINEER,
//                    'user_id' => $user->id,
//                ]);
//            }
        });

    }

    public function admin_user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function repair_endpoint()
    {
        return $this->hasOne(PostRepairEndpoint::class, 'id', 'endpoint_id');
    }
}
