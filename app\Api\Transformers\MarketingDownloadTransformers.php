<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>an
 * Date: 2017/8/17
 * Time: 10:22
 */

namespace App\Api\Transformers;

use League\Fractal\TransformerAbstract;
use App\Models\MarketingDownload;

class MarketingDownloadTransformers extends TransformerAbstract {
    private $hasValidatedToken;

    function __construct($hasValidatedToken) {
        $this->hasValidatedToken = $hasValidatedToken;
    }

    public function transform(MarketingDownload $marketingDownload) {

        $marketingDownload = $marketingDownload->toArray();

//        unset($marketingDownload['created_at']);
        unset($marketingDownload['updated_at']);
        unset($marketingDownload['status']);
        unset($marketingDownload['category']);

        $host = config('admin.upload.host');
        $marketingDownload['preview'] = array_map(function ($value) use ($host) {
            return $value ? $host . $value : '';
        }, (array)$marketingDownload['preview']);
        $marketingDownload['path'] = $marketingDownload['path'] ? $host . $marketingDownload['path'] : '';
        return $marketingDownload;
    }
}