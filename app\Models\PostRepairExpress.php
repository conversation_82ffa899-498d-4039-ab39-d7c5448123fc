<?php
/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 2019/7/30
 * Time: 13:50
 */

namespace App\Models;

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PostRepairExpress extends Model
{
    protected $table = "pr_express";

    const STATUS = [0 => '未成功', 1 => '下单成功'];
    const TYPE = [0 => '未知', 1 => '寄来', 2 => '寄走'];
    const ROUTE_STATUS = [0 => '待揽收', 1 => '已揽件', 2 => '运输中', 3 => '派送中', 4 => '已签收',
        5=>'异常件', 6=>'转寄件', 7=>'退回件', 8=>'签收失败'];

    public function routes() {
//        return $this->belongsToMany(Question::class, 'paper_question', 'paper_id', 'question_id');
        return $this->hasMany('express_route', 'readboy_sn', 'readboy_sn');
    }

    public function order() {
        return $this->hasOne(Order::class, 'sn', 'pr_sn');
    }
}
