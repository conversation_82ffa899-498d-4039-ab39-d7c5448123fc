<?php

namespace App\Api\Controllers;

use App\Api\Services\SummaryService;
use App\Models\Action;
use App\Models\Agency;
use App\Models\Endpoint;
use App\Models\Statistics;
use App\Models\Warranty;
use App\Models\WarrantyExchange;
use App\Models\WarrantyReturn;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Dingo\Api\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;

class SummaryController extends BaseController
{

    public function __construct()
    {
        $this->todayStartTime = Carbon::today()->toDateTimeString();
    }

    public function warranty(Request $request)
    {
        $topAgency = $request->topAgency;
        if ($topAgency) {
            $returnCount = WarrantyReturn::where('warranty_return.created_at', '>=', $this->todayStartTime)
                ->leftJoin('endpoint', 'warranty_return.endpoint', 'endpoint.id')
                ->where('endpoint.top_agency', $topAgency)
                ->count();
            $exchangeCount = WarrantyExchange::where('warranty_exchange.created_at', '>=', $this->todayStartTime)
                ->leftJoin('endpoint', 'warranty_exchange.endpoint', 'endpoint.id')
                ->where('endpoint.top_agency', $topAgency)
                ->count();
            $warrantyCount = Warranty::where('buy_date', '>=', $this->todayStartTime)
                ->leftJoin('endpoint', 'warranty.endpoint', 'endpoint.id')
                ->where('endpoint.top_agency', $topAgency)
                ->where('warranty.status', 1)
                ->count();
        } else {
            $returnCount = WarrantyReturn::where('created_at', '>=', $this->todayStartTime)
                ->count();
            $exchangeCount = WarrantyExchange::where('created_at', '>=', $this->todayStartTime)
                ->count();
            $warrantyCount = Warranty::where('buy_date', '>=', $this->todayStartTime)
                ->where('status', 1)
                ->count();
        }

        return [
            'return_count' => $returnCount,
            'exchange_count' => $exchangeCount,
            'warranty_count' => $warrantyCount,
        ];
    }

    public function warrantyFlow(Request $request)
    {
        $topAgency = $request->topAgency;
        $warranties = Warranty::with('endpoints')
            ->where('warranty.status', 1)
            ->where('buy_date', '>=', $this->todayStartTime)
            ->orderBy('buy_date', 'desc')
            ->limit(6);
        if ($topAgency) {
            $warranties = $warranties
                ->leftJoin('endpoint', 'warranty.endpoint', 'endpoint.id')
                ->where('endpoint.top_agency', $topAgency);
        }
        $warranties = $warranties->get()->toArray();
        foreach ($warranties as $key => $value) {
            Carbon::setLocale('zh');
            $warranties[$key]['buy_date'] = Carbon::parse($value['buy_date'])->diffForHumans();
        }

        return $warranties;
    }

    public function endpoint(Request $request)
    {
        $topAgency = $request->topAgency;
        if ($topAgency) {
            $warrantyCount = Cache::remember('api.summary.warranty_count' . $topAgency, 60,
                function () use ($topAgency) {
                    return Warranty::leftJoin('endpoint', 'warranty.endpoint', 'endpoint.id')
                        ->where('endpoint.top_agency', $topAgency)->where('endpoint.status', 1)->count();
                }
            );
            $endpointCount = Cache::remember('api.summary.endpoint_count' . $topAgency, 100,
                function () use ($topAgency) {
                    return Endpoint::where('top_agency', $topAgency)->where('status', 1)->count();
                }
            );
            $activeEndpointCount = Warranty::where('buy_date', '>=', $this->todayStartTime)
                ->leftJoin('endpoint', 'warranty.endpoint', 'endpoint.id')
                ->where('endpoint.top_agency', $topAgency)
                ->groupBy('endpoint')
                ->get()->count();
            //包含当天最近7天
            $time = Carbon::now()->subDay(6)->startOfDay();
            $activeEndpointCountWeek = Warranty::where('buy_date', '>=', $time)
                ->leftJoin('endpoint', 'warranty.endpoint', 'endpoint.id')
                ->where('endpoint.top_agency', $topAgency)
                ->groupBy('endpoint')
                ->get()->count();

            //包含当天最近30天
            $time = Carbon::now()->subDay(29)->startOfDay();
            $activeEndpointCountMonth = Warranty::where('buy_date', '>=', $time)
                ->leftJoin('endpoint', 'warranty.endpoint', 'endpoint.id')
                ->where('endpoint.top_agency', $topAgency)
                ->groupBy('endpoint')
                ->get()->count();
        } else {
            $warrantyCount = Cache::remember('api.summary.warranty_count', 60,
                function () {
                    return Warranty::where('status', 1)->count();
                }
            );
            $endpointCount = Cache::remember('api.summary.endpoint_count', 100,
                function () {
                    return Endpoint::where('status', 1)->count();
                }
            );
            $activeEndpointCount = Warranty::where('buy_date', '>=', $this->todayStartTime)
                ->groupBy('endpoint')
                ->get()->count();

            //包含当天最近7天
            $time = Carbon::now()->subDay(6)->startOfDay();
            $activeEndpointCountWeek = Cache::remember('api.summary.active_endpoint_count_weekly', 24 * 60,
                function () use ($time) {
                    return Warranty::where('buy_date', '>=', $time)
                        ->where('status', 1)
                        ->groupBy('endpoint')
                        ->get()->count();
                }
            );
            //包含当天最近30天
            $time = Carbon::now()->subDay(29)->startOfDay();
            $activeEndpointCountMonth = Cache::remember('api.summary.active_endpoint_count_monthly', 24 * 60,
                function () use ($time) {
                    return Warranty::where('buy_date', '>=', $time)
                        ->where('status', 1)
                        ->groupBy('endpoint')
                        ->get()->count();
                }
            );

        }

        return [
            'warranty_count' => $warrantyCount,
            'endpoint_count' => $endpointCount,
            'active_endpoint_count' => $activeEndpointCount,
            'active_endpoint_count_week' => $activeEndpointCountWeek,
            'active_endpoint_count_month' => $activeEndpointCountMonth,
        ];
    }

    public function weeklyWarrantyCount(Request $request)
    {
        $oneWeekAgeTime = Carbon::today()->subWeek()->toDateTimeString();
        $expireAt = Carbon::tomorrow();
        $rawSelectSql = "DATE_FORMAT(buy_date,'%m/%d') day,count(warranty.id) count";
        $topAgency = $request->topAgency;
        if ($topAgency) {
            $weeklyWarrantyCount = Cache::remember('api.summary.weekly_warranty_count' . $topAgency, $expireAt,
                function () use ($rawSelectSql, $oneWeekAgeTime, $topAgency) {
                    return Warranty::select(\DB::raw($rawSelectSql))
                        ->where('buy_date', '>=', $oneWeekAgeTime)
                        ->where('buy_date', '<', Carbon::today())
                        ->where('endpoint.top_agency', $topAgency)
                        ->where('warranty.status', 1)
                        ->leftJoin('endpoint', 'warranty.endpoint', 'endpoint.id')
                        ->groupBy('day')
                        ->get()->toArray();
                });
        } else {
            $weeklyWarrantyCount = Cache::remember('api.summary.weekly_warranty_count' . $topAgency, $expireAt,
                function () use ($rawSelectSql, $oneWeekAgeTime) {
                    return Warranty::select(\DB::raw($rawSelectSql))
                        ->where('buy_date', '>=', $oneWeekAgeTime)
                        ->where('buy_date', '<', Carbon::today())
                        ->where('status', 1)
                        ->groupBy('day')
                        ->get()->toArray();
                });
        }
        $weeklyWarrantyCount = $this->transformWeeklyWarrantyCount($weeklyWarrantyCount);

        return $weeklyWarrantyCount;
    }

    private function transformWeeklyWarrantyCount($weeklyWarrantyCount)
    {
        $result = [];
        foreach ($weeklyWarrantyCount as $key => $value) {
            $result['days'][] = $value['day'];
            $result['counts'][] = $value['count'];
        }

        return $result;

    }

    //机器型号占比
    public function modelWarranty(Request $request)
    {
        $topAgency = $request->topAgency;
        if ($topAgency) {
            $modelWarranty = Warranty::where('buy_date', '>=', $this->todayStartTime)
                ->select(\DB::raw('count(warranty.id) value,model as name'))
                ->where('endpoint.top_agency', $topAgency)
                ->leftJoin('endpoint', 'warranty.endpoint', 'endpoint.id')
                ->groupBy('model')
                ->orderBy('value', 'desc')
                ->limit(6)
                ->get()
                ->toArray();
        } else {
            $modelWarranty = Warranty::where('buy_date', '>=', $this->todayStartTime)
                ->select(\DB::raw('count(id) value,model as name'))
                ->groupBy('model')
                ->orderBy('value', 'desc')
                ->limit(6)
                ->get()
                ->toArray();
        }

        return $modelWarranty;
    }

    //活动数量和销售额统计
    public function action(Request $request)
    {
        $topAgency = $request->topAgency;
        $thisWeekStartTime = Carbon::now()->startOfWeek();
        $lastWeekStartTime = Carbon::now()->startOfWeek()->subWeek();
        $lastWeekEndTime = Carbon::now()->startOfWeek()->subWeek()->endOfWeek();
        $thisMonthStartTime = Carbon::now()->startOfMonth();
        $lastMonthStartTime = Carbon::now()->subMonth()->startOfMonth();
        $lastMonthEndTime = Carbon::now()->subMonth()->startOfMonth()->endOfMonth();
        $lastWeekActions = $this->getActionSummary($topAgency, $lastWeekStartTime, $lastWeekEndTime);
        $thisWeekActions = $this->getActionSummary($topAgency, $thisWeekStartTime, Carbon::now());
        $thisMonthActions = $this->getActionSummary($topAgency, $thisMonthStartTime, Carbon::now());
        $thisLastMonthActions = $this->getActionSummary($topAgency, $lastMonthStartTime, $lastMonthEndTime);

        return [
            array_merge($thisWeekActions, ['name' => '本周']),
            array_merge($lastWeekActions, ['name' => '上周']),
            array_merge($thisMonthActions, ['name' => '本月']),
            array_merge($thisLastMonthActions, ['name' => '上月']),
        ];
    }

    private function getActionSummary($topAgency, $beginTime, $endTime)
    {
        $beginTime = date('Y-m-d', strtotime($beginTime));
        $endTime = date('Y-m-d', strtotime($endTime));

        if ($topAgency) {
            $result = Action::whereBetween('date_start', [$beginTime, $endTime])
                ->select(\DB::raw('count(id) count,sum(total) all_total,sum(amount) all_amount'))
                ->where('top_agency_id', $topAgency)
                ->where('status', 5)
                ->first()->toArray();
            $count = Action::whereBetween('date_start', [$beginTime, $endTime])
                ->where('top_agency_id', $topAgency)
                ->count();
        } else {
            $result = Action::whereBetween('date_start', [$beginTime, $endTime])
                ->select(\DB::raw('count(id) count,sum(total) all_total,sum(amount) all_amount'))
                ->where('status', 5)
                ->first()->toArray();
            $count = Action::whereBetween('date_start', [$beginTime, $endTime])
                ->count();
        }
        $result['total'] = $result['amount'] = 0;
        $result['all_total'] = $result['all_total'] ?: 0;
        $result['all_amount'] = round(($result['all_amount'] / 10000), 2) ?: 0;

        if ($result['count']) {
            $result['total'] = round($result['all_total'] / $result['count'], 2);
            $result['amount'] = round($result['all_amount'] / $result['count'], 2);
        }
        $result['count'] = $count;

        return $result;
    }

    public function regionWarranty(Request $request)
    {
        $topAgency = $request->topAgency;
        if ($topAgency) {
            //获取echart所需区域位置对象
            $agencyPosition = Agency::rightJoin('endpoint', 'agency.id', 'endpoint.top_agency')
                ->select(['agency.name', 'lat', 'lng'])
                ->where('agency.pid', $topAgency)
                ->groupBy('agency.id')->get()
                ->toArray();
            $geoCoordMap = [];
            foreach ($agencyPosition as $key => $value) {
                $geoCoordMap[$value['name']] = [
                    round((float)$value['lng'], 2),
                    round((float)$value['lat'], 2),
                ];
            }
            //获取代理的保卡数量
            $agencyCount = DB::table('warranty')
                ->select(DB::raw('count(warranty.id) as count, agency.name,warranty.buy_date,endpoint.second_agency'))
                ->leftjoin('endpoint', 'warranty.endpoint', '=', 'endpoint.id')
                ->rightjoin('agency', 'endpoint.top_agency', '=', 'agency.id')
                ->where('buy_date', '>=', $this->todayStartTime)
                ->where('endpoint.top_agency', '=', $topAgency)
                ->groupBy('agency.name')
                ->get()
                ->pluck('count', 'name')
                ->toArray();
            $data = [];
            foreach ($agencyCount as $key => $value) {
                $data[] = ['name' => $key, 'value' => $value];
            }
        } else {
            //获取echart所需区域位置对象
            $agencyPosition = Agency::rightJoin('endpoint', 'agency.id', 'endpoint.top_agency')
                ->select(['agency.name', 'lat', 'lng'])
                ->where('agency.pid', 0)->groupBy('agency.id')->get()
                ->toArray();
            $geoCoordMap = [];
            foreach ($agencyPosition as $key => $value) {
                $geoCoordMap[$value['name']] = [
                    round((float)$value['lng'], 2),
                    round((float)$value['lat'], 2),
                ];
            }
            //获取代理的保卡数量
            $agencyCount = DB::table('warranty')
                ->select(DB::raw('count(warranty.id) as count, agency.name,warranty.buy_date,endpoint.second_agency'))
                ->leftjoin('endpoint', 'warranty.endpoint', '=', 'endpoint.id')
                ->rightjoin('agency', 'endpoint.top_agency', '=', 'agency.id')
                ->where('buy_date', '>=', $this->todayStartTime)
                ->groupBy('agency.name')
                ->get()
                ->pluck('count', 'name')
                ->toArray();
            $data = [];
            foreach ($agencyCount as $key => $value) {
                $data[] = ['name' => $key, 'value' => $value];
            }
        }

        return ['geoCoordMap' => $geoCoordMap, 'data' => $data];
    }

    public function agencyWarranty(SummaryService $service)
    {
        $topAgency = Input::get('topAgency');
        $start = Carbon::today()->toDateTimeString();
        $end = Carbon::now()->toDateTimeString();
        $createdAt = ['start' => $start, 'end' => $end];
        $agencyWarrantyStatistics = $service->getAgencyWarrantyStatistics($topAgency, $createdAt);

        return $agencyWarrantyStatistics;
    }

    public function weather()
    {
        return Cache::remember('api.summary.weather', 1800,
            function () {
                $ch = curl_init('http://wis.qq.com/weather/common?source=pc&weather_type=observe%7Cforecast_1h%7Cforecast_24h&province=%E5%B9%BF%E4%B8%9C%E7%9C%81&city=%E4%B8%AD%E5%B1%B1%E5%B8%82');
                curl_setopt($ch, CURLOPT_HEADER, 0);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);
                curl_setopt($ch, CURLOPT_TIMEOUT, 3);
                $res = curl_exec($ch);
                $info = curl_getinfo($ch);
                $weather = ['success' => false, 'forecast' => [], 'observe' => []];

                if ($info['http_code'] == 200) {
                    $data = json_decode($res, true)['data'];
                    $forecast = $data['forecast_1h'];

                    for ($i = 0; $i < 10; $i++) {
                        $weather['forecast'][] = [
                            'degree' => $forecast[$i]['degree'],
                            'update_time' => substr($forecast[$i]['update_time'], 8, 4),
                            'weather_code' => $forecast[$i]['weather_code'],
                        ];
                    }

                    $weather['observe']['degree'] = $data['observe']['degree'];
                    $weather['observe']['weather_code'] = $data['observe']['weather_code'];
                    $weather['success'] = true;
                }

                return $weather;
            }
        );

    }

    public function salesStar()
    {
        $start = Carbon::today()->toDateTimeString();
        $end = Carbon::now()->toDateTimeString();
        $createdAt = ['startTime' => $start, 'endTime' => $end];
        $salesStar = Statistics::getSalesStarRank($createdAt, true);

        return $salesStar;
    }

    public function endpointTypeRate(Request $request)
    {
        $query = Endpoint::select(DB::raw('count(id) as value,type as name'))
            ->where('status', 1)
            ->groupBy('type');
        if ($request->topAgency) {
            $query->where('endpoint.top_agency', $request->topAgency);
        }
        $endpointTypeRate = $query->get()->toArray();
        $res = [];
        foreach ($endpointTypeRate as $key => $value) {
            $res[Endpoint::ENDPOINT_TYPE[$value['name']]] = $value['value'];
        }
        $type = Input::get('type');
        if ($type == 'array') {
            $res = array_map(function ($value) {
                $value['name'] = Endpoint::ENDPOINT_TYPE[$value['name']];

                return $value;
            }, $endpointTypeRate);
        }

        return $res;
    }
}