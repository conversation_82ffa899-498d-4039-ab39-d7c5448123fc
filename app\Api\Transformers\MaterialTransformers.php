<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>an
 * Date: 2017/8/17
 * Time: 10:22
 */

namespace App\Api\Transformers;

use League\Fractal\TransformerAbstract;
use App\Models\Material;

class MaterialTransformers extends TransformerAbstract
{
    private $hasValidatedToken;

    function __construct($hasValidatedToken)
    {
        $this->hasValidatedToken = $hasValidatedToken;
    }

    public function transform(Material $material)
    {

        $material->category_name = $material->categories->name;
        $material = $material->toArray();

        $material['isLogin'] = 1;
        //如果没有正确的token验证过来,不返回价格和库存


        unset($material['created_at']);
        unset($material['updated_at']);
        unset($material['categories']);
        unset($material['order']);
        unset($material['like_count']);
        unset($material['dislike_count']);
        unset($material['pic_other']);

        $host = config('admin.upload.host');
        $material['pic'] = $material['pic'] ? $host . $material['pic'] : '';
        $material['thumbnail'] = $material['thumbnail'] ? $host . $material['thumbnail'] : '';
        $material['price'] = strlen($material['price']) < 10 ? sprintf('%0.2f',
            $material['price']) : $material['price'];

        //        if (!$this->hasValidatedToken) {
        //            $material['price'] = null;
        //            $material['store'] = null;
        //            $material['isLogin'] = 0;
        //        }
        return $material;
    }
}