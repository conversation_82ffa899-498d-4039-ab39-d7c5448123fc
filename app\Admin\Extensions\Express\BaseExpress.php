<?php

namespace App\Admin\Extensions\Express;

use App\Models\PostExpress;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use SimpleXMLElement;
use function GuzzleHttp\Psr7\str;

class BaseExpress
{
    public static function create_exp_sn($sn = null): string
    {
        //return '201908020903371884071774jxkd4159';
        if (!$sn) {
            $d = date('YmdHis');
            $r = random_int(100000, 999999);
            $sn = $d . strval($r);
        }
        $pre = 'jxkd';
        $rand = strval(random_int(1000, 9999));
        return $sn . $pre . $rand;
    }

    public static function store_fee($rb_exp_sn, $exp_sn, $fee)
    {
        return DB::table('pr_express')->where('readboy_sn', $rb_exp_sn)->where('exp_sn', $exp_sn)->update(['fee' => $fee]);
    }

    protected function store_express($company_name, $company_type, $data = null)
    {
        $sns = explode(',', $data['sn']);
        foreach ($sns as $sn) {
            $express = new PostExpress();
            $express->exp_sn = $data['exp_sn'];
            $express->pr_sn = $sn;
            $express->com = $company_name;
            $express->readboy_sn = $data['orderid'];
            $express->type = $data['type'] ?? 0;
            $express->pay_method = $data['pay_method'] ?? 0;
            $express->data = $data;
            $express->com_type = $company_type;
            $express->save();
        }
    }
}