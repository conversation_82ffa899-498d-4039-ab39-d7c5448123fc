<?php

namespace App\Api\Services;

use Illuminate\Support\Facades\DB;

class SummaryService
{
    public function getAgencyWarrantyStatistics($topAgency, $createdAt = [])
    {


        $time = [$createdAt['start'], $createdAt['end']];
        $topAgencyCount = [];
        //判断权限决定要显示的内容
        if ($topAgency) {
            //获取当前用户的总代id
            $sqlKeyWord = 'endpoint.second_agency';
            $where = [
                ['agency.pid', '=', $topAgency],
                ['warranty.status', '=', 1],
            ];
            //获取所有二级代理
            $agencies = DB::table('agency')->where('pid', '=', $topAgency)->pluck('name')->toArray();

            //计算出直属总代的保卡数量,为什么单独算,因为放在一起算,数据库查询会很慢
            $topAgencyCount = DB::table('warranty')
                ->select(DB::raw('count(warranty.id) as count, agency.name,warranty.buy_date,endpoint.second_agency'))
                ->leftjoin('endpoint', 'warranty.endpoint', '=', 'endpoint.id')
                ->rightjoin('agency', 'endpoint.top_agency', '=', 'agency.id')
                ->whereBetween('buy_date', $time)
                ->where([
                    ['agency.id', '=', $topAgency],
                    ['endpoint.second_agency', '=', 0],
                    ['warranty.status', '=', 1],
                ])
                ->get()
                ->pluck('count', 'name')
                ->toArray();
        } else {
            $sqlKeyWord = 'endpoint.top_agency';
            $where = [
                ['warranty.status', '=', 1],
            ];
            //获取所有一级代理
            $agencies = DB::table('agency')->where('level', '=', 1)->pluck('name')->toArray();
        }

        $result = DB::table('warranty')
            ->select(DB::raw('count(warranty.id) as count, agency.name,warranty.buy_date'))
            ->leftjoin('endpoint', 'warranty.endpoint', '=', 'endpoint.id')
            ->rightjoin('agency', $sqlKeyWord, '=', 'agency.id')
            ->whereBetween('buy_date', $time)
            ->where($where)
            ->groupBy('agency.id')
            ->orderBy('count', 'desc')
            ->get()
            ->pluck('count', 'name')
            ->toArray();
        $result = array_merge($result, $topAgencyCount);
        arsort($result);

        $data['countList'] = array_values($result);
        $data['agencyList'] = array_keys($result);
        foreach ($agencies as $key => $value) {
            if (!in_array($value, $data['agencyList'])) {
                array_push($data['agencyList'], $value);
                array_push($data['countList'], 0);
//                $data['agencyList'][] = $value;
//                $data['countList'][] = 0;
            }
        }

        return $data;
    }
}