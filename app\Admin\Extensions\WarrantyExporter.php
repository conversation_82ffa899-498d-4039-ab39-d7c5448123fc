<?php

namespace App\Admin\Extensions;

use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;

class WarrantyExporter extends AbstractExporter
{
    use ExcelExportTrait;

    public function export()
    {
        $filename = '保卡列表';
        $data = $this->getData();
        $titles = [
            '购机日期',
            '录入日期',
            '购机终端ID',
            '购机终端名称',
            '购机终端地址',
            '购机终端电话',
            '购机终端负责人',
            '导购员',
            '购机设备SN码',
            '购机设备机型',
            '购机用户姓名',
            //            '购机用户电话',
        ];
        $formatData = $this->formatData($data);
        array_unshift($formatData, $titles);
        ExcelExportTrait::exportToExcel($filename, $formatData);
    }

    protected function formatData($data)
    {
        $formatData = [];
        $data = $this->genData($data);
        foreach ($data as $value) {
            $row = [
                $value['buy_date'],
                $value['created_at'],
                $value['endpoints']['id'],
                $value['endpoints']['name'],
                $value['endpoints']['address'],
                $value['endpoints']['phone'],
                $value['endpoints']['manager'],
                $value['salesman'],
                ' ' . $value['barcode'],
                $value['model'],
                $value['customer_name'],
                //                $value['customer_phone'],
            ];
            $formatData[] = $row;
        }

        return $formatData;
    }

    function genData($data)
    {
        $n = count($data);
        for ($i = 0; $i < $n; $i++) {
            yield $data[$i];
        }
    }
}