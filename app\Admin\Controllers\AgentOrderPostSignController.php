<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\Tools\Button;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Models\ChinaArea;
use App\Models\Endpoint;
use App\Models\Order;

use App\Models\OrderExtend;
use App\Models\OrderLog;
use App\Models\OrderOldAddress;
use App\Models\PostExpress;
use App\Models\PostRepairEndpoint;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\DB;

class AgentOrderPostSignController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('签收订单');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('签收订单');
            $content->description('');

            $content->body($this->form()->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修订单--查看');
            $order = Order::where(['id' => $id])->first();
            $order_old_address = OrderOldAddress::where(['sn'=>$order->sn])->first();
            $content->body(view('admin/post_check/view', compact('order','order_old_address')));
        });
    }


    public function cancel(Request $request)
    {
        foreach (Order::find(request()->get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    public function post_sign_in()
    {
        $ids = Request::get('ids');
        $id = Request::get('id');
        $text = Request::get('text');
        if (!empty($ids)) {
            foreach (Order::find($ids) as $post) {
                $post->status = Order::COME_SURE;
                $post->receive_case = $text;
                $post->receive_time = date('Y-m-d H:i:s');
                $post->save();
            }
        }
        if (!empty($id)) {
            $post = Order::find($id);
            $post->status = Order::COME_SURE;
            $post->receive_case = $text;
            $post->receive_time = date('Y-m-d H:i:s');
            $post->save();
        }
    }

    public function set_come_sure()
    {
        $ids = Request::get('ids');
        $id = Request::get('id');
        $text = Request::get('text');
        if (!empty($ids)) {
            foreach (Order::find($ids) as $post) {
                $post->status = Order::EXP_COME_SUCCESS;
                $post->come_exp_sn = $text;
                $post->save();
            }
        }
        if (!empty($id)) {
            $post = Order::find($id);
            $post->status = Order::EXP_COME_SUCCESS;
            $post->come_exp_sn = $text;
            $post->save();
        }
    }

    public function post_sign_print() {
        $id = Request::get('id');
        $data = Order::where('order.id', $id)
            ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
            ->rightjoin('agent_order as ao','aoc.agent_order_sn', '=', 'ao.sn')
            ->select('order.*')
            ->first();
//        dd($data);
        $order_extend = OrderExtend::where('sn', $data['sn'])->first();
        if ($order_extend){
            $order_extend->print_time = date('Y-m-d H:i:s');
            $order_extend->print_man = Admin::user()->id;
            $order_extend->save();
        }else{
            $order_extend = new OrderExtend();
            $order_extend->sn = $data->sn;
            $order_extend->print_man = Admin::user()->id;
            $order_extend->print_time = date('Y-m-d H:i:s');
            $order_extend->save();
        }
        $data['time'] = date('Y-m-d H:i:s');
        return $data;
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOF
            $('input[name="come_exp_sn"]').bind('mouseover', function(){this.select();});
            $('input[name="come_exp_sn"]').bind('click', function(){this.select();});
            $('input[name="come_exp_sn"]').click();
            //打印便签
            $('.print').click(function(){
                var id = $(this).attr('value');
                $.get('/admin/agent_order_post_sign_print?id='+id, function(result){
                    if (result) {
//                        LODOP=getLodop(document.getElementById('LODOP1'),document.getElementById('LODOP_EM1'));
//                        LODOP.SET_PRINT_PAGESIZE(1,'40mm','80mm', '');//设定纸张方向和尺寸
//                        LODOP.SET_PRINTER_INDEXA('Deli DL-888D');//选择打印机
//                        LODOP.SET_PRINT_STYLE("FontSize",9);
//                        LODOP.SET_PRINT_STYLE("Bold",1);
//                        LODOP.ADD_PRINT_BARCODE('5mm','1mm','40mm','15mm',"128Auto",result.sn);
//                        LODOP.SET_PRINT_STYLEA(0,"AlignJustify",3);
//                        LODOP.SET_PRINT_STYLEA(0,"ShowBarText",0);
//                        LODOP.ADD_PRINT_TEXT('25mm','1mm','40mm','10mm',result.sn);
//                        LODOP.SET_PRINT_STYLEA(0,"FontSize",7);
//                        LODOP.ADD_PRINT_TEXT('35mm','1mm','40mm','10mm',"机型："+ result.model_name);
//                        LODOP.ADD_PRINT_TEXT('40mm','1mm','40mm','5mm',"SN："+ result.barcode);
//                        LODOP.ADD_PRINT_TEXT('45mm','1mm','40mm','5mm',"姓名："+ result.name);
//                        LODOP.ADD_PRINT_TEXT('50mm','1mm','40mm','5mm',"电话："+ result.phone);
//                        LODOP.ADD_PRINT_TEXT('55mm','1mm','40mm','15mm',"故障类型："+ result.damage);

                        //横版打印
                        LODOP=getLodop(document.getElementById('LODOP1'),document.getElementById('LODOP_EM1'));
                        LODOP.SET_PRINT_PAGESIZE(1,'60mm','90mm', '');//设定纸张方向和尺寸
                        LODOP.SET_PRINTER_INDEXA('Deli DL-888D');//选择打印机
                        LODOP.SET_PRINT_STYLE("FontSize",12);
                        LODOP.SET_PRINT_STYLE("Bold",1);
                        LODOP.ADD_PRINT_TEXT('5mm','18mm','35mm','6mm',"经销商寄修");
                        
                        LODOP.ADD_PRINT_BARCODE('12mm','1mm','55mm','10mm',"128Auto",result.sn);
                        LODOP.SET_PRINT_STYLEA(0,"AlignJustify",3);
                        LODOP.SET_PRINT_STYLEA(0,"ShowBarText",0);
                        LODOP.ADD_PRINT_TEXT('23mm','1mm','55mm','5mm',result.sn);
                        LODOP.SET_PRINT_STYLEA(0,"FontSize",12);
                        LODOP.ADD_PRINT_TEXT('28mm','1mm','55mm','5mm',"区域："+ result.agency);
                        LODOP.ADD_PRINT_TEXT('34mm','1mm','55mm','5mm',"机型："+ result.model_name);
                        LODOP.ADD_PRINT_TEXT('40mm','1mm','55mm','5mm',"SN："+ result.barcode);
                        LODOP.ADD_PRINT_TEXT('46mm','1mm','55mm','5mm',"姓名："+ result.name);
                        LODOP.ADD_PRINT_TEXT('52mm','1mm','55mm','5mm',"电话："+ result.phone);
                        LODOP.ADD_PRINT_TEXT('58mm','1mm','55mm','10mm',"打印时间："+ result.time);
                        LODOP.ADD_PRINT_TEXT('69mm','1mm','55mm','25mm',"故障类型："+ result.damage);
                        
                        

//                        LODOP.SET_PRINT_STYLEA(4,"TextFrame",2);
//                        LODOP.PREVIEW();
//                        LODOP.PRINT_DESIGN();
                        LODOP.PRINT();
                    }
                });
            });
            //询问框
            $('.post_sign_in').click(function(){
                var id = $(this).attr('value');
                layer.prompt({title: '请输入收到的配件，并确认',formType: 2}, function(text, index){
                    layer.close(index);
//                    layer.msg('您输入的配件为：'+ text );
                    $.ajax({
                        type: "GET",
                        url: "/admin/post_sign_in",
                        data: {
                            id: id,
                            text: text,
                        },
                        dataType: "json",
                        success: function (data) {
                            layer.msg('签收成功'+data, {time:500},function(){
                                $('.grid-refresh').click();
                            });
                        }
                    });
                    layer.msg("签收成功")
                    window.location.reload();
                });

//                layer.confirm('确定签收该订单？', {
//                    btn: ['确认签收', '取消']
//                }, function(){
//                    $.get('/admin/post_sign_in?id='+id, function(result){
//                        layer.msg('签收成功'+result, {time:500},function(){
//                            $('.grid-refresh').click();
//                        });
//                    });
//                }, function(){
//                    layer.msg('取消签收');
//                });
            });

            //标发货
            $('.set_come_sure').click(function(){
                var id = $(this).attr('value');
                layer.prompt({title: '请输入快递单号，并确认',formType: 2}, function(text, index){
                    layer.close(index);
//                    layer.msg('您输入的配件为：'+ text );
                    $.ajax({
                        type: "GET",
                        url: "/admin/set_come_sure",
                        data: {
                            id: id,
                            text: text,
                        },
                        dataType: "json",
                        success: function (data) {
                            layer.msg('标发货成功'+data, {time:500},function(){
                                $('.grid-refresh').click();
                            });
                        }
                    });
                    layer.msg("标发货成功")
                    window.location.reload();
                });
            });
EOF;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
            $grid->disableCreation();
            $grid->disableExport();
            $agent_order_sn = Input::get('agent_order_sn');
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('sn', '维修订单号');
                $filter->like('come_exp_sn', '寄来快递单号')->setPlaceHolder('光标移到此处扫码');
                $filter->like('barcode', 'S/N码')->setPlaceHolder('光标移到此处扫码');
                $filter->like('name', '寄件人');
//                $filter->equal('status', '订单状态')->select(Order::STATUS);
//                $filter->between('updated_at', '订单支付日期')->datetime();
            });
            //快捷筛选条

            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => function($query) use ($agent_order_sn){
                        $query->orwhere([['order.status', '>=', Order::EXP_COME_SUCCESS],
                            ['order.status', '<=',Order::COME_SURE_IS_TELL], ['order.type', '=', '2'],
                            ['aoc.agent_order_sn', '=', $agent_order_sn]])
                            ->orwhere([['order.come_exp_type', '=', 2], ['order.status', '=', Order::AUDIT_PASS],
                                ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]]);
                    }
                ],
                1 => [
                    'name' => '已签收',
                    'param' => [[DB::Raw('status in (' . Order::COME_SURE . ',' . Order::COME_SURE_IS_TELL . ')' ) , '1'], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
                2 => [
                    'name' => '未签收',
                    'param' => [['order.status', '=', Order::EXP_COME_SUCCESS], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
                3 => [
                    'name' => '已审核自主寄件',
                    'param' => [['order.come_exp_type', '=', 2], ['order.status', '=', Order::AUDIT_PASS],
                        ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
            ];
            //筛选条数
            foreach($option as $key => $value) {
                $option[$key]['count'] = Order::where($value['param'])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
            }

            $grid->tools(function ($tools) use($option){
                //自定义状态快捷筛选按钮
                $tools->batch(function ($batch) {
                    $batch->add('取消订单', new Cancel());
                });

                $tools->append(new QuickPickTool($option));
                $button = <<<EOF
                     <div class="btn-group pull-right" style="margin-right:0px; margin-top: 10px;">
                      <a href ="agent_order" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 返回
                      </a >
                    </div >
EOF;
                $tools->append($button);
            });

            //根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()
//                    ->select('order.*')
                    ->where($option[$quick_pick]['param'])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->orderBy('repeat_order', 'desc')
                    ->orderBy('id', 'desc');
            }
            $grid->model()
                ->select('order.*')
                ->where($option[-1]['param'])
                ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                ->orderBy('repeat_order', 'desc')
                ->orderBy('id', 'desc');

            $grid->id('ID')->sortable();
            $grid->sn('寄修单号');
            $grid->rb_come_exp_sn('内部寄来快递单号')->editable();
            $grid->come_exp_sn('寄来快递单号')->editable();
            $grid->barcode('S/N码');
            $grid->model_name('机型');
            $grid->name('寄件人');
            $grid->phone('用户联系方式');
            $grid->address('寄件人地址')->display(function($value){
                return $this->province . $this->city . $this->district . $this->address;
            });
//            $grid->endpoint()->name('寄修售后点');
            $grid->come_exp_type('寄来方式')->display(function ($come_exp_type) {
                return Order::come_exp_type[$come_exp_type];
            });
            $grid->status('订单状态')->display(function ($status) {
                $s = Order::STATUS;
                if (array_key_exists($status, $s)) {
                    return $s[$status];
                } else {
                    return "————";
                }
            });
            $grid->order_log('签收时间')->display(function ($value){
//                 $a =  OrderLog::where([['pr_sn','=',$value],['pr_status','=',400]])->pluck('date');
                if ($value){
                    foreach ($value as $item) {
                        if ($item['pr_status'] == 400){
                            return $item['date'];
                        }
                    }
                }
                return '';
            });

            $grid->actions(function ($actions) {
                $actions->disableDelete();
//                $actions->disableEdit();
                $status = $actions->row->status;
                $c = 'post_sign/view/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">查看 </span></a>';
                $actions->append($html);
                if ($status == Order::AUDIT_PASS) {
                    $html = '<a href="javascript:void(0);" class="set_come_sure" value="'. $actions->row->id . ' target="_blank"><span style="color:orange">【标发货】 </span></a>';
                    $actions->append($html);
                }
                if ($status == Order::EXP_COME_SUCCESS) {
                    $html = '<a href="javascript:void(0);" class="post_sign_in" value="'. $actions->row->id . ' target="_blank"><span style="color:orange">签收 </span></a>';
                    $actions->append($html);
                }
                if ($status == Order::COME_SURE || $status == Order::COME_SURE_IS_TELL) {
                    $html = '<a href="javascript:void(0);" class="print" value="'. $actions->row->id .'"><span style="color:orange">打印便签 </span></a>';
                    $actions->append($html);
                }
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Admin::form(Order::class, function (Form $form) {
            $form->text('rb_come_exp_sn', '内部寄来快递单号');
            $form->text('come_exp_sn', '寄来快递单号');
            $form->text('receive_case', '收到配件');
//            $form->select('repair_status', '维修状态')
//                ->options(Order::repair_status);
//            $form->hidden('repair_man');
//            $form->hidden('status');
            $form->saving(function (Form $form) {
                $form->no_log = 1;
            });
        });
    }
}
