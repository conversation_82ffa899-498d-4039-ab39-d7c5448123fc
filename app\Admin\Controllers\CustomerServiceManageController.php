<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Sms;
use App\Admin\Extensions\Tools\AccessoryLackNoticeTool;
use App\Admin\Extensions\Tools\AgingTestNoticeTool;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Admin\Extensions\MessagePush;

use App\Http\Controllers\Controller;

use App\Models\AdminUsersYkf;
use App\Models\AgentOrder;
use App\Models\Machine;
use App\Models\Order;
use App\Models\OrderExtend;
use App\Models\OrderRemark;
use App\Models\OrderOldAddress;
use App\Models\OrderLog;
use App\Models\OrderLog2;
use App\Models\PostRepairOptionalAccessory;

use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;
use Encore\Admin\Grid\Tools\BatchAction;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Database\Eloquent\Collection;

class CustomerServiceManageController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修客户管理');
            $content->description('寄修客户管理列表');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修客服管理');
            // $content->description('检测');

            $content->body($this->form($id)->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修客服管理 -- 订单详情');
            $order = Order::where(['id' => $id])->first();
            $order_old_address = OrderOldAddress::where(['sn' => $order->sn])->first();
            $order_extend = OrderExtend::where(['sn' => $order->sn])->first();

            if (!empty($order_extend)) {
                $order_extend->order_priority_span = Order::order_priority_span($order_extend->order_priority);
            }

            $pr_material = DB::table('pr_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_material.mat_id')
                ->leftjoin('machine_malfunction', 'machine_malfunction.id', '=', 'pr_material.malfunction_id')
                ->leftjoin('material', 'material.id', '=', 'pr_material.material_id')
                ->where('pr_material.pr_sn', $order['sn'])
                ->select('machine_accessory_tree.title as title', 'machine_malfunction.title as malfunction', 'material.price as price', 'pr_material.count as count')
                ->get()
                ->toArray();
            $pr_used_material = DB::table('pr_used_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_used_material.mat_id')
                ->leftjoin('material', 'material.id', '=', 'pr_used_material.material_id')
                ->where('pr_used_material.pr_sn', $order['sn'])
                ->select('material.name as name', 'material.price as price', 'pr_used_material.count as count',
                    'material.code as code', 'material.old_code as old_code', 'material.specification as specification', 'material.from as from', 'pr_used_material.is_charge')
                ->get()
                ->toArray();
            $pr_oa = PostRepairOptionalAccessory::getShowListBySn($order->sn);
            $agent_order = null;
            if ($order->type == 3) {
                $agent_order = AgentOrder::rightJoin('agent_order_correlation as aoc', 'agent_order.sn', '=', 'aoc.agent_order_sn')
                    ->where([['aoc.order_sn', $order->sn]])->select('agent_order.*')->first();
            }

            // 获取通话记录
            $callLog1 = DB::table('call_log')
                ->join('admin_users_ykf as auy', 'call_log.agent', 'auy.seat_number')
                ->join('admin_users as au', 'auy.uid', 'au.id')
                ->where('call_log.bill_id', $order->sn)
                ->groupBy('call_log.call_sheet_id')
                ->orderBy('call_log.id', 'desc')
                ->get();

            // 获取最新的客服知会记录
            $orderRemarkInfo1 = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
                ->where('order_remark.sn', $order->sn)
                ->where('order_remark.type', 1)
                ->orderBy('order_remark.id', 'desc')
                ->select('au.name', 'order_remark.created_at', 'order_remark.remark', 'order_remark.status')
                ->first();

            if (!empty($orderRemarkInfo1)) {
                $orderRemarkInfo1['status_name'] = Order::STATUS[$orderRemarkInfo1['status']];
            }

            // 获取所有客服备注
            $allOrderRmarkInfo1 = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
                ->where('order_remark.type', 1)
                ->where('order_remark.sn', $order->sn)
                ->orderBy('order_remark.id', 'desc')
                ->select('au.name', 'order_remark.*')
                ->get();

            foreach ($allOrderRmarkInfo1 as $key => $value) {
                $allOrderRmarkInfo1[$key]['status_name'] = Order::STATUS[$value['status']];
                $allOrderRmarkInfo1[$key]['connect_name'] = Order::CONNECT[$value['connect']];
            }

            // 获取云客服账号密码
            $usersYkfInfo = AdminUsersYkf::where('uid', Admin::user()->id)->first();

            // 此页面可以拨打电话
            $is_ykf_400 = 1;

            // 获取通话记录
            $callLog = DB::table('call_log')
                ->join('admin_users_ykf as auy', 'call_log.agent', 'auy.seat_number')
                ->join('admin_users as au', 'auy.uid', 'au.id')
                ->where('call_log.bill_id', $order->sn)
                ->groupBy('call_log.call_sheet_id')
                ->orderBy('call_log.id', 'desc')
                ->get();

            // 获取所有维修知会备注
            $allOrderRmarkInfo = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
                ->where('order_remark.type', 3)
                ->where('order_remark.sn', $order->sn)
                ->orderBy('order_remark.id', 'desc')
                ->select('au.name', 'order_remark.*')
                ->get();

            foreach ($allOrderRmarkInfo as $key => $value) {
                if ($value['status'] == 410) {
                    $allOrderRmarkInfo[$key]['status_name'] = '未检测,未知会';
                } else {
                    $allOrderRmarkInfo[$key]['status_name'] = Order::STATUS[$value['status']];
                }
                $allOrderRmarkInfo[$key]['connect_name'] = Order::CONNECT[$value['connect']];
            }

            $content->body(view('admin/post_repair/view', compact('order', 'pr_material', 'pr_used_material',
                'pr_oa', 'order_old_address', 'order_extend', 'agent_order',
                'orderRemarkInfo1', 'callLog1', 'callLog', 'orderRemarkInfo', 'allOrderRmarkInfo',
                'is_ykf_400', 'usersYkfInfo', 'allOrderRmarkInfo1')));

        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $action_style = <<<css
    .detail_view:hover, .push_msg_to_app:hover, .log_view:hover, .order_mark:hover,
    .pay_notice:hover, .aging_test_notice:hover, .accessory_lack_notice:hover {
        text-decoration-line: underline;
    }
css;
        $action_style_str = base64_encode($action_style);

        $script = <<<js

    // 设置操作链接-设置样式
    const action_style = decodeURIComponent(escape(atob('$action_style_str')));
    $('head').append('<style type="text/css">' + action_style + '</style>');

    // 设置操作链接-监听点击事件-发送短信通知
    $('.pay_notice').on('click', function() {
        var val = $(this).attr('value');
        var phone = val.split(',')[0];
        var id = val.split(',')[1];
        var msg = "是否对联系人 " + phone + " 发送短信通知 支付 ？";
        var res = confirm(msg);
        if (res == true) {
            $.ajax({
                method: 'post',
                url: 'customer_service_manage/pay_notice',
                data: {
                    _token: LA.token,
                    id: id,
                },
                success: function (data) {
                    //$.pjax.reload('#pjax-container');
                    if(data == "success") {
                        toastr.success('操作成功');
                    } else {
                        toastr.error('发送失败');
                    }
                }
            });
        }
    });
    $('.aging_test_notice').on('click', function() {
        var val = $(this).attr('value');
        var phone = val.split(',')[0];
        var id = val.split(',')[1];
        var msg = "是否对联系人 " + phone + " 发送短信通知 老化测试 ？";
        var res = confirm(msg);
        if (res == true) {
            $.ajax({
                method: 'post',
                url: 'customer_service_manage/aging_test_notice',
                data: {
                    _token: LA.token,
                    id: id,
                },
                success: function (data) {
                    //$.pjax.reload('#pjax-container');
                    if(data == "success") {
                        toastr.success('操作成功');
                    } else {
                        toastr.error('发送失败');
                    }
                }
            });
        }
    });
    $('.accessory_lack_notice').on('click', function() {
        var val = $(this).attr('value');
        var phone = val.split(',')[0];
        var id = val.split(',')[1];
        var msg = "是否对联系人 " + phone + " 发送短信通知 缺少配件 ？";
        var res = confirm(msg);
        if (res == true) {
            $.ajax({
                method: 'post',
                url: 'customer_service_manage/accessory_lack_notice',
                data: {
                    _token: LA.token,
                    id: id,
                },
                success: function (data) {
                    //$.pjax.reload('#pjax-container');
                    if(data == "success") {
                        toastr.success('操作成功');
                    } else {
                        toastr.error('发送失败');
                    }
                }
            });    
        }
    });
    
    // 订单标注
    $('.order_mark').click(function(){
        var param = $(this).attr('value');
        $.ajax({
            url: 'repair_check/order_mark?' + param,
            method: 'get',
            success: function () {
                $.pjax.reload('#pjax-container');
                toastr.success('操作成功');
            }
        });
    });

    // 查看订单日志
    $('.log_view').click(function(){
        var id = $(this).attr('value');
        layer.open({
            type: 2,
            title: '日志',
            shadeClose: true,
            shade: 0.8,
            area: ['1000px', '60%'],
            content: 'post_repair_manage/log/'+id //iframe的url
        });
    });

    // 订单备注
    $('.pay_remark').click(function(e){
        sn = e.currentTarget.dataset.sn
        var html = '<div class="box box-info">'+
                    '<div class="box-header with-border">备注<\/div>'+
                    '<div class="box-body" style="min-height:300px;min-width:500px" >'+
                        '<table class="table">'+
                            '<tr>'+
                                '<th style="text-align: center;" >时间<\/th>'+
                                '<th style="text-align: center;" >订单状态<\/th>'+
                                '<th style="text-align: center;" >联系状态<\/th>'+
                                '<th style="text-align: center;" >操作人<\/th>'+
                                '<th style="text-align: center;" >备注<\/th>'+
                            '</tr>'+
                            '<tbody id="tbody"><\/tbody>'+
                        '<\/table>'+
                    '<\/div>'+
                    '<\/div>';
        html += '<div style="display:flex">'+
                    '<input id="remarkInput" placeholder="请输入备注,不超过100字" size="70">'+
                    '<a class="btn btn-sm btn-primary" type="button" id="saveRemark" >确定<\/a>'+
                '<\/div>';


        // 获取备注内容
        $.ajax({
            url: '/admin/post_connect/getRemarkInfo',
            data: {
                sn: sn,
                type: 2
            },
            type: 'get',
            dataType: 'json',
            success: function(res){
                var tbodyHtml = '';
                $('#tbody').html('');
                $.each(res , function(i , v){
                    tbodyHtml +='<tr>'+
                                    '<td style="text-align: center;">'+v.created_at+'</td>'+
                                    '<td style="text-align: center;">'+v.status_name+'</td>'+
                                    '<td style="text-align: center;">'+v.connect_name+'</td>'+
                                    '<td style="text-align: center;">'+v.name+'</td>'+
                                    '<td style="text-align: center;">'+v.remark+'</td>'+
                                '</tr>';
                })

                $('#tbody').append(tbodyHtml);
            }
        })
        $.fancybox.open(html);

        $('#saveRemark').click(function(){
            pay_remark = $('#remarkInput').val()
            if(!pay_remark){
                layer.msg('备注不能为空');
                return false;
            }
            $.ajax({
                url: '/admin/post_connect/setPayRemark',
                data: {
                    sn: sn,
                    pay_remark: pay_remark
                },
                type: 'get',
                dataType: 'json',
                success: function(res){
                    layer.msg(res.info);
                    if(res.status == 1){
                        var newTbodyHtml = '<tr>'+
                                                '<td style="text-align: center;color:#29db6f">'+res.data.created_at+'<\/td>'+
                                                '<td style="text-align: center;color:#29db6f">'+res.data.status_name+'<\/td>'+
                                                '<td style="text-align: center;color:#29db6f">'+res.data.connect_name+'<\/td>'+
                                                '<td style="text-align: center;color:#29db6f">'+res.data.name+'<\/td>'+
                                                '<td style="text-align: center;color:#29db6f">'+res.data.remark+'<\/td>'+
                                            '<\/tr>';

                        $('#tbody').append(newTbodyHtml);
                        $('.grid-refresh').click();
                    }
                }
            })
        })
    })

    // 客服备注
    $('.cloud_cunstomer_remark').click(function(e){
        sn = e.currentTarget.dataset.sn
        var html = '<div class="box box-info">'+
                    '<div class="box-header with-border">客服备注<\/div>'+
                    '<div class="box-body" style="min-height:300px;min-width:500px" >'+
                        '<table class="table">'+
                            '<tr>'+
                                '<th style="text-align: center;" >时间<\/th>'+
                                '<th style="text-align: center;" >订单状态<\/th>'+
                                '<th style="text-align: center;" >操作人<\/th>'+
                                '<th style="text-align: center;" >备注<\/th>'+
                            '</tr>'+
                            '<tbody id="tbody"><\/tbody>'+
                        '<\/table>'+
                    '<\/div>'+
                    '<\/div>'+
                    '<div style="display:flex">'+
                        '<input style="margin:1%" name="remarkRadio400s" value="需要再次通知" type="radio"> 需要再次通知'+
                        '<input style="margin:1%" name="remarkRadio400s" value="已沟通未协商好" type="radio"> 已沟通未协商好'+
                        '<input style="margin:1%" name="remarkRadio400s" value="顾客有争议" type="radio"> 顾客有争议'+
                        '<input style="margin:1%" name="remarkRadio400s" value="终端有争议" type="radio"> 终端有争议'+
                        '<input style="margin:1%" name="remarkRadio400s" value="等待回复" type="radio"> 等待回复'+
                    '<\/div>';
        html += '<div style="display:flex">'+
                    '<input id="remarkInput" placeholder="请输入备注,不超过100字" size="70">'+
                    '<a class="btn btn-sm btn-primary" type="button" id="saveRemark" >确定<\/a>'+
                '<\/div>';


        // 获取备注内容
        $.ajax({
            url: '/admin/post_connect/getRemarkInfo',
            data: {
                sn: sn,
                type: 1
            },
            type: 'get',
            dataType: 'json',
            success: function(res){
                var tbodyHtml = '';
                $('#tbody').html('');
                $.each(res , function(i , v){
                    tbodyHtml +='<tr>'+
                                    '<td style="text-align: center;">'+v.created_at+'</td>'+
                                    '<td style="text-align: center;">'+v.status_name+'</td>'+
                                    '<td style="text-align: center;">'+v.name+'</td>'+
                                    '<td style="text-align: center;">'+v.remark+'</td>'+
                                '</tr>';
                })

                $('#tbody').append(tbodyHtml);
            }
        })
        $.fancybox.open(html);

        $("input[name='remarkRadio400s']").change(function(){
            $('#remarkInput').val($("input[name='remarkRadio400s']:checked").val());
        })

        $('#saveRemark').click(function(){
            remark = $('#remarkInput').val()
            if(!remark){
                layer.msg('备注不能为空');
                return false;
            }
            $.ajax({
                url: '/admin/customer_service_manage/set400Remark',
                data: {
                    sn: sn,
                    remark: remark
                },
                type: 'get',
                dataType: 'json',
                success: function(res){
                    // layer.msg(res.info);
                    if(res.status == 1){
                        // var newTbodyHtml = '<tr>'+
                        //                         '<td style="text-align: center;color:#29db6f">'+res.data.created_at+'<\/td>'+
                        //                         '<td style="text-align: center;color:#29db6f">'+res.data.status_name+'<\/td>'+
                        //                         '<td style="text-align: center;color:#29db6f">'+res.data.name+'<\/td>'+
                        //                         '<td style="text-align: center;color:#29db6f">'+res.data.remark+'<\/td>'+
                        //                     '<\/tr>';

                        // $('#tbody').append(newTbodyHtml);
                        parent.$.fancybox.close();
                        $('.grid-refresh').click();
                    }
                }
            })
        })
    })

    // 重新申请寄修
    $('.renewOrder').click(function(e){
        id = e.currentTarget.dataset.id

        layer.confirm('该订单是否确认重新申请寄修?', {
            btn: ['是', '否']
            }, function(){
                $.ajax({
                    url: '/admin/customer_service_manage/postRepairAgain',
                    data: {
                        id: id,
                        _token: LA.token,
                    },
                    dataType: 'json',
                    method: 'post',
                    success: function(res){
                        console.log(res)
                        layer.msg(res.info)
                        if(res.status == 1){
                            // $.pjax.reload('#pjax-container');
                            toastr.success(res.info);
                            setTimeout(function(){
                                window.location.reload();
                            },1000)
                        } else {
                            toastr.error(res.info);
                        }
                    },error:function(res){
                        console.log(res)    
                    }
                })
            }, function(){
        });
    })

    // 知会推送
    $('.push_msg_to_app').on('click', function(){
        var id = $(this).attr('value');
        var msg = "是否向用户推送知会备注信息?";
        var res = confirm(msg);
        if (res == true){
            $.ajax({
                method: 'get',
                url: '/admin/customer_service_manage/push_msg_to_app',
                data: {
                    id: id,
                    type: 1
                },
                success: function (res) {
                    layer.msg(res.info)
                    if(res.status == 1){
                        toastr.success(res.info);
                        setTimeout(function(){
                            window.location.reload();
                        },1000)
                    } else {
                        toastr.error(res.info);
                    }
                }
            });    
        }
    });
js;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
            // $grid->model()->where('status', '>=', 200);
            // 快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => [],
                ],
                1 => [
                    'name' => '已收货,未知会',
                    'param' => [['status', '=', Order::COME_SURE]],
                ],
                2 => [
                    'name' => '已收货,已知会',
                    'param' => [['status', '=', Order::COME_SURE_IS_TELL]],
                ],
                3 => [
                    'name' => '未检测',
                    'param' => [['status', '=', Order::COME_SURE_IS_TELL]],
                ],
                4 => [
                    'name' => '待支付',
                    'param' => [['status', '=', Order::CHECK_FINISH_IS_TELL]],
                ],
            ];
            //角色判断
            if (Admin::user()->inRoles(['pr_check'])) {
                foreach ($option as $key => $item) {
                    $option[$key]['param'] = array_merge($item['param'], [['check_man', '=', Admin::user()->id]]);
                }
            }
            // 筛选条数
            foreach ($option as $key => $value) {
                $option[$key]['count'] = Order::where($value['param'])->count();
            }

            $grid->disableCreation();
            $grid->disableExport();
            $grid->tools(function ($tools) use ($option) {

                // 批量操作按钮
                if (Admin::user()->can('batch_cloud_customer')) {
                    $tools->batch(function ($batch) {
                        $batch->add('批量客服知会', new BatchReplicate(1));
                    });
                }

                // 自定义状态快捷筛选按钮
                $tools->append(new QuickPickTool($option));

                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                    // $batch->add('取消订单', new Cancel());
                    // $batch->add('短信通知（老化测试）', new AgingTestNoticeTool());
                    // $batch->add('短信通知（配件缺少）', new AccessoryLackNoticeTool());
                });
            });

            // 根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])
                    ->orderBy('repeat_order', 'desc')
                    ->orderBy('id', 'desc');
            }
            $grid->model()->where($option[-1]['param'])
                ->orderBy('repeat_order', 'desc')
                ->orderBy('id', 'desc');

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('come_exp_sn', '寄来快递单号')->setPlaceholder('光标移到此处扫码');
                $filter->like('sn', '维修订单号')->setPlaceholder('光标移到此处扫码');
                $filter->like('barcode', 'S/N码');
                $filter->equal('status', '订单状态')->select(Order::STATUS);
                $filter->equal('model_name', '产品型号')->select(
                    Machine::orderBy('name')->pluck('name', 'name')->all()
                );
                // $data = DB::table('order as o')->join('admin_users as au', 'o.check_man', '=', 'au.id')->select('o.check_man','au.name')->distinct('check_man')->get()->toArray();
                // $map = array();
                // foreach ($data as $key => $item){
                //     $map[$item->check_man] = $item->name;
                // }
                // unset($data);
                // $filter->equal('check_man', '检测员')->select(
                //     $map
                // );
                $filter->between('create_at', '订单提交日期')->datetime();
                $filter->like('type', '寄修类型')->select(Order::post_repair_type);
                $filter->equal('order_extend.order_mark', '订单标注')->select(Order::ORDER_MARK);
                $filter->like("phone", "联系人电话");
                $filter->equal('in_period', '保内保外')->select(Order::in_period);
                $filter->equal('reason', '损坏原因')->select(Order::reason);

            });

            $grid->id('ID')->sortable();
            $grid->uid('UID')->sortable();
            Order::order_priority_column($grid);
            $grid->sn('寄修订单编号');
            $grid->come_exp_sn('寄来快递单号');
            //$grid->model_name('机型');
            //$grid->barcode('S/N码');
            $grid->column('S/N码-机型')->display(function () {
                return $this->barcode . '<br/>' . $this->model_name;
            });
            //$grid->name('联系人');
            //$grid->phone('用户联系方式');
            $grid->column('联系人')->display(function () {
                return $this->name . '<br/>' . $this->phone;
            });
            $grid->receive_time('签收时间')->display(function ($time) {
                $receive_time = date('Y-m-d', strtotime($time . " + 7 days"));
                $now = date('Y-m-d');
                if (date('Y-m-d', strtotime($time . " + 7 days")) < $now &&
                    $now < date('Y-m-d', strtotime($time . " + 15 days"))) {
                    return '<span style="color:#e5cda4">' . $time . '</span>';
                } elseif ($now > date('Y-m-d', strtotime($time . " + 15 days"))) {
                    return '<span style="color:#ff0000">' . $time . '</span>';
                }
                return '<span style="color:#a1ec96">' . $time . '</span>';
            })->sortable();
            $grid->updated_at('最后操作时间')->sortable();;
            $grid->status('订单状态')->display(function ($status) {

                // 获取是否知会状态
                $isTell = OrderExtend::where('sn', $this->sn)->value("is_tell");
                if ($isTell == 1) {
                    $isTellName = "（需要知会）";
                } else {
                    $isTellName = "（无需知会）";
                }

                $s = Order::STATUS;
                if ($status == Order::CHECK_FINISH && $this->connect == 3) {
                    return '<span style="color:red">已弃修</span><a class="btn btn-sm btn-danger renewOrder" href="JavaScript:void(0);" data-id="' . $this->id . '">重新申请寄修</a>';
                }
                if (array_key_exists($status, $s)) {

                    if ($status == Order::REPAIR_REFUSE) {
                        return $s[$status] . '<a class="btn btn-sm btn-danger renewOrder" href="JavaScript:void(0);" data-id="' . $this->id . '">重新申请寄修</a>';
                    }

                    if ($status == Order::COME_SURE) {
                        return $s[$status] . $isTellName;
                    } else {
                        return $s[$status];
                    }

                } else {
                    return "————";
                }
            });
            $grid->cloud_cunstomer_remark('客服备注')->display(function () {
                $remark = DB::table('order_remark')->where('sn', $this->sn)->where('type', 1)->orderBy('created_at', 'desc')->value('remark');
                $remark = $remark ? '<u>' . $remark . '</u>' : '<u style="color:#dd1144">Empty</u>';
                return '<a href="JavaScript:void(0);" data-sn="' . $this->sn . '" class="cloud_cunstomer_remark">' . $remark . '</a>';
            });
            $grid->check_user()->name('检测人')->display(function ($name) {
                if ($name == '')
                    return '———';
                return $name;
            });
            $grid->type('寄修类型')->display(function ($value) {
                return Order::post_repair_type[$value];
            });

            $grid->order_extend()->order_mark('订单标注')->display(function ($order_mark) {
                $str = '未知';
                if (array_key_exists($order_mark, Order::ORDER_MARK)) {
                    $str = Order::ORDER_MARK[$order_mark];
                }
                switch ($order_mark) {
                    case Order::ORDER_MARK_NORMAL:
                        return '<span>' . $str . '</span>';
                    case Order::ORDER_MARK_MISSING_ACCESSORY:
                        // 获取所需配件
                        $prMaterialArr = DB::table('pr_material')
                            ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_material.mat_id')
                            ->where('pr_sn', $this->sn)
                            ->pluck('machine_accessory_tree.title')
                            ->toArray();
                        $prMaterialStr = implode(',', $prMaterialArr);
                        return '<span  style="color: red">' . $str . ': ' . $prMaterialStr . '</span>';
                    default:
                        return '<span  style="color: red">' . $str . '</span>';
                }
            });

            $grid->connect('联系状态')->editable('select', [0 => '未联系', 1 => '联系成功', 2 => '联系失败']);
            $grid->column('order_extend.pay_remark', '订单备注')->display(function ($pay_remark) {
                $pay_remark = $pay_remark ? '<u>' . $pay_remark . '</u>' : '<u style="color:#dd1144">Empty</u>';
                return '<a href="JavaScript:void(0);" data-sn="' . $this->sn . '" class="pay_remark">' . $pay_remark . '</a>';
            });

            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $status = $actions->row->status;

                $c = 'customer_service_manage/view/' . $actions->getKey();
                $html = '<a href="' . $c . '" class="detail_view"><span style="color:black"> 查看 </span></a>';
                $actions->append($html);

                $id = $actions->row->id;
                $html = '<a href="javascript:void(0);" class="push_msg_to_app" value="' . $id . '"> 知会推送 </a>';
                $actions->append($html);

                $v = $actions->row->phone;
                $id = $actions->row->id;
                $html = '<a href="javascript:void(0);" class="pay_notice" value="' . $v . ',' . $id . '"><span style="color:black">短信通知（支付）</span></a>';
                $actions->append($html);
                $html = '<a href="javascript:void(0);" class="accessory_lack_notice" value="' . $v . ',' . $id . '"><span style="color:black">短信通知（配件缺少）</span></a>';
                $actions->append($html);
                $html = '<a href="javascript:void(0);" class="aging_test_notice" value="' . $v . ',' . $id . '"><span style="color:black">短信通知（老化测试）</span></a>';
                $actions->append($html);

                $sn = $actions->row->sn;
                $order_mark = $actions->row->order_extend['order_mark'];
                if ($order_mark != Order::ORDER_MARK_INFORM_ABNORMAL) {
                    $param = 'sn=' . $sn . '&order_mark=' . Order::ORDER_MARK_INFORM_ABNORMAL;
                    $html_inform_abnormal = '<a href="javascript:void(0);" class="order_mark" value="' . $param . '"><span style="color:orange"> 【知会异常】 </span></a>';
                } else {
                    $param = 'sn=' . $sn . '&order_mark=' . Order::ORDER_MARK_NORMAL;
                    $html_inform_abnormal = '<a href="javascript:void(0);" class="order_mark" value="' . $param . '"><span style="color:blue"> 【知会正常】 </span></a>';
                }
                if ($order_mark != Order::ORDER_MARK_PAY_ABNORMAL) {
                    $param = 'sn=' . $sn . '&order_mark=' . Order::ORDER_MARK_PAY_ABNORMAL;
                    $html_pay_abnormal = '<a href="javascript:void(0);" class="order_mark" value="' . $param . '"><span style="color:orange"> 【支付异常】 </span></a>';
                } else {
                    $param = 'sn=' . $sn . '&order_mark=' . Order::ORDER_MARK_NORMAL;
                    $html_pay_abnormal = '<a href="javascript:void(0);" class="order_mark" value="' . $param . '"><span style="color:blue"> 【支付正常】 </span></a>';
                }
                if (Admin::user()->inRoles(['administrator', 'manager', 'repair_service', 'customer_service_team_leader'])) {
                    $actions->append($html_inform_abnormal);
                    $actions->append($html_pay_abnormal);
                }

                $data = $actions->row;
                $c = 'post_repair_manage/log/' . $data->sn;
                $html = '<a href="javascript:void(0);" class="log_view" value="' . $data->sn . '"><span style="color:green">日志 </span></a>';
                $actions->append($html);
            });
        });
    }


    protected function form($id = null)
    {
        return Admin::form(Order::class, function (Form $form) {
            $form->text('connect');
            $form->text('order_extend.pay_remark');
            $form->saving(function (Form $form) {
            });
        });
    }

    public function pay_notice(Request $request)
    {
        $id = request()->get('id');
        $sms = new Sms();
        $data = Order::where('id', $id)->select('model_name', 'phone')->first();

        $phone = $data->phone;
        $template = '1544493';
        $templateParam = ['model' => $data->model_name];
        $ret = $sms->send($phone, $template, $templateParam);
        if ($ret) {
            return "success";
        }
        return "error";
    }

    public function aging_test_notice(Request $request)
    {
//        $phone = request()->get('phone');
        $id = request()->get('id');
        $sms = new Sms();
        $data = Order::where('id', $id)->select('model_name', 'phone')->first();

        $phone = $data->phone;
        $template = 'SMS_215344127';
        $templateParam = ['model' => $data->model_name];
        $ret = $sms->send($phone, $template, $templateParam);
        if ($ret) {
            return "success";
        }
        return "error";
//          $ret = $sms->query_info($phone);
    }

    public function accessory_lack_notice(Request $request)
    {
        $id = request()->get('id');
        $sms = new Sms();
        $data = Order::where('id', $id)->select('model_name', 'phone')->first();

        $phone = $data->phone;
        $template = 'SMS_215339152';
        $templateParam = ['model' => $data->model_name];
        $ret = $sms->send($phone, $template, $templateParam);
        if ($ret) {
            return "success";
        }
        return "error";
    }

    /**
     * 变更状态为已收货已知会
     *
     * @param $sn 维修单号
     * @param $order_remark 客服备注
     */
    public function setOrderRemark()
    {

        $sn = request()->input('sn');
        $order_remark = request()->input('order_remark');

        $newStatus = Order::COME_SURE_IS_TELL;

        // 获取订单信息
        $orderInfo = Order::where('sn', $sn)->first();

        // 更新订单状态
        $orderData = array(
            'status' => $newStatus,
            'updated_at' => date('Y-m-d H:i:s')
        );

        if (!Order::where('sn', $sn)->update($orderData)) {
            return array('status' => 0, 'info' => '操作失败');
        }

        // 添加知会日志
        $orderRemarkData = array(
            'sn' => $sn,
            'operator' => Admin::user()->id,
            'status' => $newStatus,
            'connect' => $orderInfo['connect'],
            'remark' => $order_remark,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'type' => 1
        );

        if (!OrderRemark::insert($orderRemarkData)) {
            return array('status' => 0, 'info' => '操作失败');
        }

        // // 消息推送到APP(无需知会才推送)
        // $orderExtendIsTell = OrderExtend::where('sn' , $sn)->value('is_tell');
        // if(!empty($order_remark) && $orderExtendIsTell != 1 && Admin::user()->id != 1){
        //     $msgPush = new  MessagePush();

        //     if($orderInfo->type == 3){ // 终端代寄
        //         $msgPush->pushRemarkEndpoint($orderInfo->endpoint , $order_remark);
        //         // $msgPush->pushRemarkEndpoint(2 , $order_remark);
        //     }else { // 正常寄修 代理商寄修
        //         $account_center_id = DB::table('admin_users')->where('id' , $orderInfo->uid)->value('account_center_id'); // 账户中心ID
        //         $msgPush->pushRemark($account_center_id , $order_remark);
        //         // $msgPush->pushRemark(5317926 , $order_remark);
        //     }
        // }   

        // 添加订单日志
        $orderLogData = array(
            'pr_sn' => $sn,
            'pr_status' => $newStatus,
            'log_status' => $newStatus,
            'log_from' => 'web',
            'uid' => $orderInfo['uid'],
            'admin' => Admin::user()->id,
            'title' => Order::title[$newStatus],
            'date' => date('Y-m-d H:i:s')
        );

        if (!OrderLog::updateOrInsert(array('pr_sn' => $sn, 'log_status' => $orderLogData['log_status']), $orderLogData)) {
            return array('status' => 0, 'info' => '操作失败');
        }

        // 备用日志
        OrderLog2::Insert($orderLogData);

        return array('status' => 1, 'info' => '操作成功');
    }

    /**
     * 保存备注信息
     */
    public function set400Remark()
    {
        $sn = request()->input('sn');
        $remark = request()->input('remark');

        // 获取订单信息
        $orderInfo = Order::where('sn', $sn)->first();

        $data = array(
            'sn' => $sn,
            'operator' => Admin::user()->id,
            'type' => 1,
            'status' => $orderInfo['status'],
            'connect' => $orderInfo['connect'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'remark' => $remark
        );

        // // 消息推送到APP(无需知会才推送)
        // $orderExtendIsTell = OrderExtend::where('sn' , $sn)->value('is_tell');
        // if(!empty($remark) && $orderExtendIsTell != 1 && Admin::user()->id != 1){
        //     $msgPush = new  MessagePush();

        //     if($orderInfo->type == 3){ // 终端代寄
        //         $msgPush->pushRemarkEndpoint($orderInfo->endpoint , $remark);
        //         // $msgPush->pushRemarkEndpoint(2 , $remark);
        //     }else { // 正常寄修 代理商寄修
        //         $account_center_id = DB::table('admin_users')->where('id' , $orderInfo->uid)->value('account_center_id'); // 账户中心ID
        //         $msgPush->pushRemark($account_center_id , $remark);
        //         // $msgPush->pushRemark(5317926 , $remark);
        //     }
        // }

        if (OrderRemark::insert($data)) {

            $data['status_name'] = Order::STATUS[$data['status']];
            $data['connect_name'] = Order::CONNECT[$data['connect']];
            $data['name'] = Admin::user()->name;

            return array('status' => 1, 'info' => '添加成功', 'data' => $data);
        } else {
            return array('status' => 0, 'info' => '添加失败');
        }
    }

    /**
     * 客服知会批处理
     */
    public function batchIsTell()
    {
        $ids = request()->input('ids');
        $order_remark = request()->input('order_remark');
        $status = request()->input('status', 0);

        if (empty($ids)) {
            return array('status' => 0, 'info' => '请至少选择一个订单');
        }

        // 获取订单号码
        $orderList = Order::whereIn('id', $ids)->select('sn', 'uid', 'connect', 'status', 'type', 'endpoint')->get();

        if ($status == 1) {

            $newStatus = Order::COME_SURE_IS_TELL;

            // 知会备注
            $remarkData = array(
                'operator' => Admin::user()->id,
                'status' => $newStatus,
                'remark' => $order_remark,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => 1
            );

            // 操作日志
            $logData = array(
                'pr_status' => $newStatus,
                'log_status' => $newStatus,
                'log_from' => 'web',
                'admin' => Admin::user()->id,
                'title' => Order::title[$newStatus],
                'date' => date('Y-m-d H:i:s')
            );

            $remarkDataAll = $logDataAll = array();
            foreach ($orderList as $key => $value) {
                if ($value->status != Order::COME_SURE) {
                    return array('status' => 0, 'info' => '订单: ' . $value->sn . ' 状态不为“已收货”无法操作');
                }
                $remarkData['sn'] = $value->sn;
                $remarkData['connect'] = $value->connect;

                $remarkDataAll[] = $remarkData;

                // // 备注推送到APP(无需知会才推送)
                // $orderExtendIsTell = OrderExtend::where('sn' , $value->sn)->value('is_tell');
                // if(!empty($order_remark) && $orderExtendIsTell != 1 && Admin::user()->id != 1){
                //     $msgPush = new  MessagePush();

                //     if($value->type == 3){ // 终端代寄
                //         $msgPush->pushRemarkEndpoint($value->endpoint , $order_remark);
                //         // $msgPush->pushRemarkEndpoint(2 , $order_remark);
                //     }else { // 正常寄修 代理商寄修
                //         $account_center_id = DB::table('admin_users')->where('id' , $value->uid)->value('account_center_id'); // 账户中心ID
                //         $msgPush->pushRemark($account_center_id , $order_remark);
                //         // $msgPush->pushRemark(5317926 , $order_remark);
                //     }
                // } 

                $logData['pr_sn'] = $value->sn;
                $logData['uid'] = $value->uid;

                $logDataAll[] = $logData;

            }

            // 更新订单状态
            $orderData = array(
                'status' => $newStatus,
                'updated_at' => date('Y-m-d H:i:s')
            );
            if (!Order::whereIn('id', $ids)->update($orderData)) {
                return array('status' => 0, 'info' => '操作失败');
            }

            // 添加知会记录
            if (!OrderRemark::insert($remarkDataAll)) {
                return array('status' => 0, 'info' => '操作失败');
            }

            // 添加订单日志
            OrderLog::Insert($logDataAll);

            // 备用日志
            OrderLog2::Insert($logDataAll);
        } else {

            // 知会备注
            $remarkData = array(
                'operator' => Admin::user()->id,
                'remark' => $order_remark,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => 1
            );

            $remarkDataAll = array();
            foreach ($orderList as $key => $value) {
                $remarkData['sn'] = $value->sn;
                $remarkData['connect'] = $value->connect;
                $remarkData['status'] = $value->status;

                // // 备注推送到APP(无需知会才推送)
                // $orderExtendIsTell = OrderExtend::where('sn' , $value->sn)->value('is_tell');
                // if(!empty($order_remark) && $orderExtendIsTell != 1){
                //     $msgPush = new  MessagePush();

                //     if($value->type == 3){ // 终端代寄
                //         // $msgPush->pushRemarkEndpoint($value->endpoint , $order_remark);
                //         $msgPush->pushRemarkEndpoint(2 , $order_remark);
                //     }else { // 正常寄修 代理商寄修
                //         $account_center_id = DB::table('admin_users')->where('id' , $value->uid)->value('account_center_id'); // 账户中心ID
                //         // $msgPush->pushRemark($account_center_id , $order_remark);
                //         $msgPush->pushRemark(5317926 , $order_remark);
                //     }
                // }
                $remarkDataAll[] = $remarkData;
            }

            // 添加知会记录
            if (!OrderRemark::insert($remarkDataAll)) {
                return array('status' => 0, 'info' => '操作失败');
            }
        }


        return array('status' => 1, 'info' => '操作成功');
    }

    /**
     * 重新下单
     */
    public function postRepairAgain()
    {

        $id = request()->input('id');

        // 获取订单信息
        $orderInfo = Order::where('id', $id)->first();
        $orderExtendInfo = OrderExtend::where('sn', $orderInfo->sn)->first();

        // 查看机器是否正在寄修
        if (Order::where('barcode', $orderInfo->barcode)->where('uid', $orderInfo->uid)->whereIn('status', [100, 200, 300, -300, 400, 410, 490, 500, 600, 700, 800, -800])->first()) {
            return array('status' => 0, 'info' => '该设备寄修中!');
        }

        // 添加订单
        $orderData = [
            'barcode' => $orderInfo->barcode,
            'model_name' => $orderInfo->model_name,
            'imei' => $orderInfo->imei,
            'color' => $orderInfo->color,
            'model_id' => $orderInfo->model_id,
            'serial' => $orderInfo->serial,
            'in_period' => $orderInfo->in_period,
            'has_warranty' => $orderInfo->has_warranty,
            'in_si_period' => $orderInfo->in_si_period,
            'has_screen_insurance' => $orderInfo->has_screen_insurance,
            'reason' => $orderInfo->reason,
            'damage' => $orderInfo->damage,
            'period_file' => json_encode($orderInfo->period_file),
            'upload_file' => json_encode($orderInfo->upload_file),
            'video_file' => json_encode($orderInfo->video_file),
            'description' => $orderInfo->description,
            'name' => $orderInfo->name,
            'phone' => $orderInfo->phone,
            'province' => $orderInfo->province,
            'city' => $orderInfo->city,
            'district' => $orderInfo->district,
            'address' => $orderInfo->address,
            'come_exp_type' => $orderInfo->come_exp_type,
            'repair_endpoint' => $orderInfo->repair_endpoint,
            'uid' => $orderInfo->uid,
            'created_at' => date('Y-m-d H:i:s'),
            'sn' => date('YmdHis') . mt_rand(100000, 999999),
            'status' => 100,
            'repeat_order' => $orderInfo->repeat_order,
            'need_invoice' => $orderInfo->need_invoice,
            'invoice_type' => $orderInfo->invoice_type,
            'invoice_title' => $orderInfo->invoice_title,
            'invoice_tax_id' => $orderInfo->invoice_tax_id,
            'invoice_email' => $orderInfo->invoice_email,
        ];
        Order::insert($orderData);

        // 添加订单扩展信息
        $orderExtendData = array(
            'sn' => $orderData['sn'],
            'created_at' => $orderData['created_at'],
            'external_fault' => $orderExtendInfo->external_fault,
            'internal_fault' => $orderExtendInfo->internal_fault,
            'is_tell' => $orderExtendInfo->is_tell,
        );
        OrderExtend::insert($orderExtendData);

        // 添加日志数据
        $logData = array(
            'pr_sn' => $orderData['sn'],
            'pr_status' => 100,
            'log_status' => 100,
            'log_from' => 'web',
            'relation_key' => $orderInfo->sn,
            'operation' => 'created order',
            'uid' => $orderInfo->uid,
            'admin' => Admin::user()->id,
            'title' => '寄修订单提交，寄修服务中心等待接收中',
            'remark' => '',
            'date' => $orderData['created_at'],
        );
        OrderLog::insert($logData);
        OrderLog2::insert($logData);

        return array('status' => 1, 'info' => '操作成功!');
    }

    /**
     * 推送消息到app
     */
    public function PushMsgToApp()
    {
        $id = request()->input('id');
        $type = request()->input('type');

        // 获取订单信息
        $orderInfo = Order::where('id', $id)->first();

        // 获取最新客服知会备注
        $remark = OrderRemark::where('sn', $orderInfo->sn)->where('type', $type)->orderBy('id')->value('remark');

        // 消息推送到APP(无需知会才推送)
        $orderExtendIsTell = OrderExtend::where('sn', $orderInfo->sn)->value('is_tell');
        if (!empty($remark) && $orderExtendIsTell != 1) {
            $msgPush = new  MessagePush();

            if ($orderInfo->type == 3) { // 终端代寄
                $msgPush->pushRemarkEndpoint($orderInfo->endpoint, $remark);
                // $msgPush->pushRemarkEndpoint(2 , $remark);
            } else { // 正常寄修 代理商寄修
                $account_center_id = DB::table('admin_users')->where('id', $orderInfo->uid)->value('account_center_id'); // 账户中心ID
                $msgPush->pushRemark($account_center_id, $remark);
                // $msgPush->pushRemark(5317926 , $remark);
            }

            return array('status' => 1, 'info' => '推送成功');

        } else {
            return array('status' => 0, 'info' => '推送失败');
        }

    }
}

/**
 * 批量处理客服知会
 */
class BatchReplicate extends BatchAction
{
    protected $action;

    public function __construct($action = 1)
    {
        $this->action = $action;
    }

    public function script()
    {
        if ($this->action == 1) {
            return <<<EOT
            $('{$this->getElementClass()}').on('click', function() {
                var html = '<div class="box box-info">'+
                                '<div class="box-header with-border">客服知会批处理 <span style="color:orange">(勾选“已知会”订单状态修改为 “已收货已知会”)<\/span><\/div>'+
                                '<div class="box-body" style="max-width:750px">'+
                                    '<div class="form-group">'+
                                        '<label class="control-label">客服知会<\/label>'+
                                        '<input type="checkbox" class="status la_checkbox" name="status" value="1" ><label class="control-label">已知会<\/label>'+
                                    '<\/div>'+
                                    '<div class="form-group">'+
                                        '<label for="order_remark" class="control-label">客服备注<\/label>'+
                                        '<div style="display:flex">'+
                                            '<input style="margin:1%" name="remarkRadio400" value="需要再次通知" type="radio"> 需要再次通知'+
                                            '<input style="margin:1%" name="remarkRadio400" value="已沟通未协商好" type="radio"> 已沟通未协商好'+
                                            '<input style="margin:1%" name="remarkRadio400" value="顾客有争议" type="radio"> 顾客有争议'+
                                            '<input style="margin:1%" name="remarkRadio400" value="终端有争议" type="radio"> 终端有争议'+
                                            '<input style="margin:1%" name="remarkRadio400" value="等待回复" type="radio"> 等待回复'+
                                            '<input style="margin:1%" name="remarkRadio400" value="已成功知会,分派订单" type="radio"> 已成功知会,分派订单'+
                                            '<input style="margin:1%" name="remarkRadio400" value="无其他异常,直接分派" type="radio"> 无其他异常,直接分派'+
                                        '<\/div>'+
                                        '<div class="input-group">'+
                                            '<span class="input-group-addon"><i class="fa fa-pencil"><\/i><\/span>'+
                                            '<input type="text" id="order_remark" name="order_remark" value="" class="form-control">'+
                                        '<\/div>'+
                                    '<\/div>'+
                                    '<button class="btn btn-sm btn-primary" id="save" >提交<\/button>'+
                                '<\/div>'+
                            '<\/div>';
                $.fancybox.open(html);

                $("input[name='remarkRadio400']").change(function(){
                    $('#order_remark').val($("input[name='remarkRadio400']:checked").val());
                })
                
                // 确定
                $('#save').click(function(){

                    status = $("input[name='status']:checked").val();
                    order_remark = $('#order_remark').val();
                    if(!order_remark){
                        layer.msg('备注不能为空');
                        return false;
                    }

                    $.ajax({
                        method: 'get',
                        url: '/admin/customer_service_manage/batchIsTell',
                        data: {
                            ids: selectedRows(),
                            order_remark: order_remark,
                            status: status
                        },
                        success: function (res) {
                            // console.log(res)
                            if(res.status == 1){
                                // $.pjax.reload('#pjax-container');
                                toastr.success(res.info);
                                $('.grid-refresh').click();
                            } else {
                                toastr.error(res.info);
                            }
                        }, 
                        error:function(res){
                            toastr.error('操作失败');
                            console.log(res)
                        }
                            
                    });
                });
                
            });
EOT;
        }

    }
}
