<?php
namespace App\Admin\Extensions;

use App\Models\Order;
use App\Models\PayOrder;
use App\Models\PostRepairUsedMaterial;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;

/**
 * 寄修统计导出
 */
class StatisticsExporter extends AbstractExporter
{
    public function export()
    {
        $year = '2021';
        $filename = $year . '年寄修耗时时长分布';
        $titles = [
            '月份',
            '耗时1天',
            '耗时2天',
            '耗时3天',
            '耗时4天',
            '耗时5天',
            '耗时6天',
            '耗时7天',
            '耗时8-14天',
            '耗时15天以上',
            '维修机器总数',
            '维修总耗时',
            '平均耗时',
        ];
        $formatData = $this->formatData($year);
        array_unshift($formatData, $titles);

        ExcelExportTrait::exportToExcel($filename, $formatData);
    }

    /**
     * 导出
     */
    private function formatData($year)
    {
        $formatData = [];
        for($i = 1; $i <= 12 ; $i++){
            $month = $i;
            if(strlen($month) < 2){
                $month = '0' . $month;
            }
            $start = $year .'-'. $month . '-01 00:00:00';
            $end = date('Y-m-t 23:59:59' , strtotime($start));

            $data = DB::table('order')
            ->whereBetween('updated_at_last' , [$start, $end])
            ->where('status' , '!=' , -900)
            ->whereNotNull('receive_time')
            ->select('updated_at_last', 'receive_time')
            ->get();
            $sum_day = $one = $two = $three = $four = $five = $six = $seven = $eight_fourteen_day = $more_days = $count = 0;
            foreach ($data as $key => $value) {
                $count ++ ;
                $timeDifference = intval((strtotime($value->updated_at_last) - strtotime($value->receive_time)) / 86400);
                if($timeDifference == 0){
                    $timeDifference = 1;
                }
                $sum_day += $timeDifference;
                if($timeDifference == 1){
                    $one ++;
                }
                if($timeDifference == 2){
                    $two ++;
                }
                if($timeDifference == 3){
                    $three ++;
                }
                if($timeDifference == 4){
                    $four ++;
                }
                if($timeDifference == 5){
                    $five ++;
                }
                if($timeDifference == 6){
                    $six ++;
                }
                if($timeDifference == 7){
                    $seven ++;
                }
                if($timeDifference > 7 && $timeDifference < 15){ // 8-14天
                    $eight_fourteen_day ++;
                }
                if($timeDifference > 14 ){ // 15天以及15天以上
                    $more_days ++;
                }
            }

            $row = [
                $i . '月',                                                                       
                $one . '台',  
                $two . '台',
                $three . '台',
                $four . '台', 
                $five . '台', 
                $six . '台',
                $seven . '台', 
                $eight_fourteen_day . '台', 
                $more_days . '台', 
                $count . '台',
                $sum_day . '天',
                $count == 0 ? 0 : round($sum_day/$count , 2) .'天',
            ];
            $formatData[] = $row;
        }
        return $formatData;
    }

}
