<?php
/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 2019/7/8
 * Time: 20:12
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class PostRepairMaterial extends Model
{
    protected $table = "pr_material";
    protected $fillable = ['mat_id', 'material_id', 'malfunction_id', 'count', 'pr_sn',
        'is_charge', 'charge_type', 'price_in'];

    protected static function boot()
    {
        parent::boot();

        self::saved(function () {
            DB::table('pr_material')
                ->where('material_id', 0)->delete();
        });
    }

    public static function check_charge_type_price($data)
    {
        if (!empty($data)) {
            $exist_price = property_exists($data, 'price');
            $exist_price_first = property_exists($data, 'price_first');
            $exist_charge_type = property_exists($data, 'charge_type');
            if ($exist_price && $exist_price_first && $exist_charge_type) {
                if ($data->charge_type == 2) {
                    $data->price = $data->price_first;
                }
            }
        }
    }
}
