<?php
/**
 * Created by PhpStorm.
 * User: qzl
 * Date: 2019/8/8
 * Time: 20:02
 */

namespace App\Admin\Extensions\Tools;


use Encore\Admin\Grid\Tools\AbstractTool;
use Encore\Admin\Admin;
use Illuminate\Support\Facades\Request;

class QuickPickTool extends AbstractTool
{
    public $option;

    public function __construct($option)
    {
        $this->option = $option;
    }

    protected function script()
    {
        $url = Request::fullUrlWithQuery(['quick_pick' => '_status_']);

        return <<<EOT

$('input:radio.user-gender').change(function () {

    var url = "$url".replace('_status_', $(this).val());

    $.pjax({container:'#pjax-container', url: url });

});

EOT;
    }

    public function render()
    {
        Admin::script($this->script());

        $options = $this->option;

        return view('admin.quick_pick', compact('options'));
    }
}