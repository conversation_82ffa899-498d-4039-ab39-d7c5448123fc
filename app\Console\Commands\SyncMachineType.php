<?php

namespace App\Console\Commands;

use App\Models\Machine;
use Illuminate\Console\Command;

class SyncMachineType extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:machine_type';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $authKey = $this->getAuthKey();

        $data = $this->getData($authKey);

        $this->syncMachineType($data);


    }

    private function getData($authKey)
    {
        $lastId = Machine::min('model_id');
        $client = new \GuzzleHttp\Client();
        $url = "http://api-mes.readboy.com/index.php?s=/Api/DeviceType/last/authKey/$authKey/lastid/$lastId";

        $response = $client->get($url);
        $body = $response->getBody();

        return json_decode($body->getContents(), true);
    }

    private function getAuthKey()
    {
        $appId = 'Web';
        $time = time();
        $appSecret = 'M4S8tUB8OBBvIUN7';
        $md5Str = md5("$appId-$time-$appSecret");

        return "$appId-$time-$md5Str";
    }

    private function syncMachineType($data)
    {
        if ($data) {
            if (!$data['errcode']) {
                foreach ($data['data'] as $key => $value) {
                    $machineType = Machine::where('model_id', $value['id'])->first();
                    if (!$machineType) {
                        $machineType = new Machine();
                    }
                    $machineType->model_id = $value['id'];
                    $machineType->name = $value['name'];
                    $machineType->save();
                }
            }
        }

    }
}
