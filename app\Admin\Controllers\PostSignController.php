<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\Tools\Button;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Models\AgentOrder;
use App\Models\ChinaArea;
use App\Models\Endpoint;
use App\Models\Order;

use App\Models\OrderExtend;
use App\Models\OrderLog;
use App\Models\OrderOldAddress;
use App\Models\PostExpress;
use App\Models\PostRepairEndpoint;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Http\Request as Request2;
use Tests\Models\User;

use function GuzzleHttp\json_decode;

class PostSignController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('签收订单');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('签收订单');
            $content->description('');

            $content->body($this->form()->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('签收订单--详情');
            $order = Order::where(['id' => $id])->first();
            $order_old_address = OrderOldAddress::where(['sn' => $order->sn])->first();
            $agent_order = null;
            if ($order->type == 3) {
                $agent_order = AgentOrder::rightJoin('agent_order_correlation as aoc', 'agent_order.sn', '=', 'aoc.agent_order_sn')
                    ->where([['aoc.order_sn', $order->sn]])->select('agent_order.*')->first();
            }

            // 获取扩展信息
            $orderExtendInfo = OrderExtend::where('sn', $order->sn)->first();
            $sign_in_pictures = $orderExtendInfo->sign_in_pictures;
            $sign_in_videos = $orderExtendInfo->sign_in_videos;
            //$sign_in_pictures = '';
            //if (!empty($orderExtendInfo->sign_in_pictures)) {
            //    $sign_in_pictures = json_decode($orderExtendInfo->sign_in_pictures, true);
            //}

            $sign_in_status_name = Order::SIGN_IN_STATUS[$orderExtendInfo->sign_in_status];

            $content->body(view('admin/post_check/view', compact(
                'order', 'order_old_address', 'agent_order', 'sign_in_pictures', 'sign_in_videos', 'sign_in_status_name')));
        });
    }


    public function cancel(Request $request)
    {
        foreach (Order::find(request()->get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    /**
     * 签收
     */
    public function post_sign_in()
    {
        $ids = Request::get('ids');
        $id = Request::get('id');
        $text = Request::get('text');

        if (!empty($ids)) {
            foreach (Order::find($ids) as $post) {
                $post->status = Order::COME_SURE;
                $post->receive_case = $text;
                $post->receive_time = date('Y-m-d H:i:s');
                $post->save();
            }
        }
        if (!empty($id)) {
            $post = Order::find($id);
            $post->status = Order::COME_SURE;
            $post->receive_case = $text;
            $post->receive_time = date('Y-m-d H:i:s');
            $post->save();

            // 代理商寄修跳过客服知会
            $order = Order::find($id);
            if ($order->type == 2) {
                $order->status = Order::COME_SURE_IS_TELL;
                $order->save();
            }
        }
    }

    /**
     * 签收 post
     */
    public function post_sign_in_new(Request2 $request)
    {
        $id = Request::input('id');
        $receive_case = Request::input('receive_case');
        $sign_in_status = Request::input('sign_in_status');

        $pathList = [];
        if (!empty($request->file('sign_in_pictures'))) {
            foreach ($request->file('sign_in_pictures') as $key => $value) {
                $pathList[] = $value->store('rbcare/repair/sign_in_picture', 'oss');
                // $pathList[] = config('admin.upload.host') . $path;
            }
        }

        if (!empty($id)) {
            $post = Order::find($id);
            $post->status = Order::COME_SURE;
            $post->receive_case = $receive_case;
            $post->receive_time = date('Y-m-d H:i:s');
            $post->save();

            // 记录签收图片,签收状态
            $sn = $post->sn;
            if ($orderExtendInfo = OrderExtend::where('sn', $sn)->first()) {

                $updateData = array(
                    'receive_time' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'sign_in_pictures' => json_encode($pathList),
                    'sign_in_status' => $sign_in_status
                );

                OrderExtend::where('id', $orderExtendInfo->id)->update($updateData);
            } else {
                $insertData = array(
                    'receive_time' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'created_at' => date('Y-m-d H:i:s'),
                    'sn' => $sn,
                    'sign_in_pictures' => json_encode($pathList),
                    'sign_in_status' => $sign_in_status
                );

                OrderExtend::insert($insertData);
            }

        }
    }

    /**
     * 签收 post
     */
    public function change_status_or_remark_post(Request2 $request)
    {
        $sn = Request::input('sn');
        $receive_case = Request::input('receive_case');
        $sign_in_status = Request::input('sign_in_status');

        $pathList = [];
        if (!empty($request->file('sign_in_pictures'))) {
            foreach ($request->file('sign_in_pictures') as $key => $value) {
                $pathList[] = $value->store('rbcare/repair/sign_in_picture', 'oss');
            }
        }

        Order::where('sn', $sn)->update(array('receive_case' => $receive_case));

        $updateData = array(
            'sign_in_pictures' => json_encode($pathList),
            'sign_in_status' => $sign_in_status
        );

        OrderExtend::where('sn', $sn)->update($updateData);

    }

    /**
     * 修改签收状态或备注或图片
     */
    public function change_status_or_remark(Request2 $request)
    {
        $sn = Request::input('sn');
        $receive_case = Request::input('receive_case');
        $sign_in_status = Request::input('sign_in_status');

        if (!empty($receive_case)) {
            Order::where('sn', $sn)->update(array('receive_case' => $receive_case));
        }
        if (!empty($sign_in_status)) {
            OrderExtend::where('sn', $sn)->update(array('sign_in_status' => $sign_in_status));
        }

        return array('status' => 1, 'info' => '操作成功');
    }

    public function set_come_sure()
    {
        $ids = Request::get('ids');
        $id = Request::get('id');
        $text = Request::get('text');
        if (!empty($ids)) {
            foreach (Order::find($ids) as $post) {
                $post->status = Order::EXP_COME_SUCCESS;
                $post->come_exp_sn = $text;
                $post->save();
            }
        }
        if (!empty($id)) {
            $post = Order::find($id);
            $post->status = Order::EXP_COME_SUCCESS;
            $post->come_exp_sn = $text;
            $post->save();
        }
    }

    public function post_sign_print()
    {
        $id = Request::get('id');
        $data = Order::where('id', $id)->first();
        $order_extend = OrderExtend::where('sn', $data['sn'])->first();

        if ($order_extend) {
            $order_extend->print_time = date('Y-m-d H:i:s');
            $order_extend->print_man = Admin::user()->id;
            $order_extend->save();
        } else {
            $order_extend = new OrderExtend();
            $order_extend->sn = $data->sn;
            $order_extend->print_time = date('Y-m-d H:i:s');
            $order_extend->print_man = Admin::user()->id;
            $order_extend->save();
        }

        $data['time'] = date('Y-m-d H:i:s');
        $data['in_period_text'] = Order::in_period[$data['in_period'] ?? Order::in_period_null];
        return $data;
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $in_period_in = Order::in_period_in;
        $in_period_out = Order::in_period_out;

        $script = <<<js

            $('input[name="come_exp_sn"]').bind('mouseover', function(){this.select();});
            $('input[name="come_exp_sn"]').bind('click', function(){this.select();});
            $('input[name="come_exp_sn"]').click();
            //打印便签
            $('.print').click(function() {
                var id = $(this).attr('value');
                $.get('/admin/post_sign_print?id='+id, function(result) {
                    if (result) {
                        var in_period_icon = '';
                        if (result.in_period == $in_period_in) {
                            in_period_icon = '◉';
                        } else if (result.in_period == $in_period_out) {
                            in_period_icon = '◌';
                        }

                        //LODOP=getLodop(document.getElementById('LODOP1'),document.getElementById('LODOP_EM1'));
                        //LODOP.SET_PRINT_PAGESIZE(1,'40mm','80mm', '');//设定纸张方向和尺寸
                        //LODOP.SET_PRINTER_INDEXA('Deli DL-888D');//选择打印机
                        //LODOP.SET_PRINT_STYLE("FontSize",9);
                        //LODOP.SET_PRINT_STYLE("Bold",1);
                        //LODOP.ADD_PRINT_BARCODE('5mm','1mm','40mm','15mm',"128Auto",result.sn);
                        //LODOP.SET_PRINT_STYLEA(0,"AlignJustify",3);
                        //LODOP.SET_PRINT_STYLEA(0,"ShowBarText",0);
                        //LODOP.ADD_PRINT_TEXT('25mm','1mm','40mm','10mm',result.sn);
                        //LODOP.SET_PRINT_STYLEA(0,"FontSize",7);
                        //LODOP.ADD_PRINT_TEXT('35mm','1mm','40mm','10mm',"机型："+ result.model_name);
                        //LODOP.ADD_PRINT_TEXT('40mm','1mm','40mm','5mm',"SN："+ result.barcode);
                        //LODOP.ADD_PRINT_TEXT('45mm','1mm','40mm','5mm',"姓名："+ result.name);
                        //LODOP.ADD_PRINT_TEXT('50mm','1mm','40mm','5mm',"电话："+ result.phone);
                        //LODOP.ADD_PRINT_TEXT('55mm','1mm','40mm','15mm',"故障类型："+ result.damage);

                        //横版打印
                        LODOP=getLodop(document.getElementById('LODOP1'),document.getElementById('LODOP_EM1'));
                        LODOP.SET_PRINT_PAGESIZE(2, '80mm', '40mm', '');//设定纸张方向和尺寸
                        LODOP.SET_PRINTER_INDEXA('Deli DL-888D');//选择打印机
                        LODOP.SET_PRINT_STYLE("FontSize", 10);
                        LODOP.SET_PRINT_STYLE("Bold", 1);
                        LODOP.ADD_PRINT_BARCODE('2mm', '2mm', '40mm', '8mm', "128Auto", result.sn);
                        LODOP.SET_PRINT_STYLEA(0, "AlignJustify", 3);
                        LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 0);
                        LODOP.ADD_PRINT_TEXT('13mm', '3mm', '40mm', '5mm', result.sn);
                        LODOP.SET_PRINT_STYLEA(0, "FontSize", 9);
                        LODOP.ADD_PRINT_TEXT('18mm', '3mm', '39mm', '10mm', "机型：" + result.model_name);
                        LODOP.ADD_PRINT_TEXT('28mm', '3mm', '39mm', '5mm', "保修：" + result.in_period_text + in_period_icon);
                        LODOP.ADD_PRINT_TEXT('33mm', '3mm', '39mm', '5mm', "SN：" + result.barcode);
                        LODOP.ADD_PRINT_TEXT('38mm', '3mm', '39mm', '5mm', "姓名：" + result.name);
                        LODOP.ADD_PRINT_TEXT('43mm', '3mm', '39mm', '5mm', "电话：" + result.phone);
                        LODOP.ADD_PRINT_TEXT('48mm', '3mm', '39mm', '9mm', "打印时间：" + result.time);
                        LODOP.ADD_PRINT_TEXT('57mm', '3mm', '39mm', '25mm', "故障类型：" + result.damage);


                        //LODOP.SET_PRINT_STYLEA(4,"TextFrame",2);
                        //LODOP.PREVIEW();
                        //LODOP.PRINT_DESIGN();
                        LODOP.PRINT();
                    }
                });
            });

            //询问框
            $('.post_sign_in').click(function(e) {
                // var id = $(this).attr('value');
                // layer.prompt({title: '请输入收到的配件，并确认',formType: 2}, function(text, index){
                //     layer.close(index);
                //     layer.msg('您输入的配件为：'+ text );
                //     $.ajax({
                //         type: "GET",
                //         url: "/admin/post_sign_in",
                //         data: {
                //             id: id,
                //             text: text,
                //         },
                //         dataType: "json",
                //         success: function (data) {
                //             layer.msg('签收成功'+data, {time:500},function(){
                //                 $('.grid-refresh').click();
                //             });
                //         }
                //     });
                //     layer.msg("签收成功")
                //     window.location.reload();
                // });
                var id = e.currentTarget.dataset.id
                var html = '<div class="box box-info">'+
                                '<div class="box-header with-border">签收确认<\/div>'+
                                '<div class="box-body" >'+
                                    '<div>'+
                                        '<input style="margin:1%" name="sign_in_status" value="1" type="radio" checked> 正常<br>'+
                                        '<input style="margin:1%" name="sign_in_status" value="2" type="radio"> 外观有瑕疵轻微<br>'+
                                        '<input style="margin:1%" name="sign_in_status" value="3" type="radio"> 外观有瑕疵严重<br>'+
                                        '<input style="margin:1%" name="sign_in_status" value="3" type="radio"> 外观有损坏，与描述符合<br>'+
                                        '<input style="margin:1%" name="sign_in_status" value="3" type="radio"> 外观有损坏，与描述不符合<br>'+
                                        '<input style="margin:1%" name="sign_in_status" value="3" type="radio"> 条码不一致已核实'+
                                    '<\/div>'+
                                    '<textarea id="receive_case" placeholder="请输入收到的配件(签收备注),并确认" cols="70" rows="5"><\/textarea>'+
                                '<\/div>'+

                                '<div class="form-group" style="display:flex">'+
                                    '<label for="repair_image" class="control-label">签收图片: <\/label>'+
                                    '<div>'+
                                        '<input type="file" class="sign_in_picture" placeholder="上传图片" name="sign_in_pictures[]" multiple >'+
                                    '<\/div>'+
                                '<\/div>'+
                            '<\/div>';
                    html += '<div>'+
                                '<a class="btn btn-sm btn-primary" type="button" id="saveSign" >确定<\/a>'+
                            '<\/div>';

                $.fancybox.open(html);

                $('#saveSign').click(function(){
                    receive_case = $('#receive_case').val();
                    sign_in_status = $("input[name='sign_in_status']:checked").val();
                    if(!receive_case){
                        layer.msg("请输入收到的配件");
                        return false;
                    }

                    var formData = new FormData();
                    fileList = $("input[name='sign_in_pictures[]']")[0].files;
                    for (let i = 0; i < fileList.length; i++) {
                        formData.append('sign_in_pictures[]', fileList[i])
                    }
                    formData.append("id",id);
                    formData.append("receive_case",receive_case);
                    formData.append("sign_in_status",sign_in_status);
                    formData.append("_token",LA.token);
                    formData.append("service",'App.Passion.UploadFile');
                    $.ajax({
                        url: "/admin/post_sign_in_new",
                        method: 'post',
                        data: formData,
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            layer.msg('签收成功'+data, {time:1000},function(){
                                // $('.grid-refresh').click();
                            });

                            setTimeout(function(){
                                window.location.reload();
                            },1000)
                        },
                        error:function(error){
                            console.log(error)    
                        }
                    });
                })


                // layer.confirm('确定签收该订单？', {
                //     btn: ['确认签收', '取消']
                // }, function(){
                //     $.get('/admin/post_sign_in?id='+id, function(result){
                //         layer.msg('签收成功'+result, {time:500},function(){
                //             $('.grid-refresh').click();
                //         });
                //     });
                // }, function(){
                //     layer.msg('取消签收');
                // });
            });

            //标发货
            $('.set_come_sure').click(function(){
                var id = $(this).attr('value');
                layer.prompt({title: '请输入快递单号，并确认',formType: 2}, function(text, index){
                    layer.close(index);
//                    layer.msg('您输入的配件为：'+ text );
                    $.ajax({
                        type: "GET",
                        url: "/admin/set_come_sure",
                        data: {
                            id: id,
                            text: text,
                        },
                        dataType: "json",
                        success: function (data) {
                            layer.msg('标发货成功'+data, {time:500},function(){
                                $('.grid-refresh').click();
                            });
                        }
                    });
                    layer.msg("标发货成功")
                    window.location.reload();
                });
            });
js;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
            $grid->disableCreation();
            $grid->disableExport();
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('sn', '维修订单号');
                $filter->like('come_exp_sn', '寄来快递单号')->setPlaceHolder('光标移到此处扫码');
                $filter->like('barcode', 'S/N码')->setPlaceHolder('光标移到此处扫码');
                $filter->like('name', '寄件人');
                //$filter->equal('status', '订单状态')->select(Order::STATUS);
                $filter->between('receive_time', '签收时间')->datetime();
                $filter->between('order_extend.print_time', '打印时间')->datetime();
                $filter->equal('order_extend.sign_in_status', '签收状态')->select(Order::SIGN_IN_STATUS);
                $filter->equal('order_extend.print_man', '打印人员')->select(DB::table('order_extend as oe')->join('admin_users as au', 'oe.print_man', '=', 'au.id')->pluck('au.name', 'oe.print_man'));
            });
            //快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => function ($query) {
                        $query->orwhere([['status', '>=', Order::EXP_COME_SUCCESS], ['status', '<=', Order::COME_SURE], ['type', '!=', '2']])
                            ->orwhere([['come_exp_type', '=', 2], ['status', '=', Order::AUDIT_PASS], ['type', '!=', '2']]);
                    }
                ],
                1 => [
                    'name' => '已签收',
                    'param' => [['status', '=', Order::COME_SURE], ['type', '!=', '2']],
                ],
                2 => [
                    'name' => '未签收',
                    'param' => [['status', '=', Order::EXP_COME_SUCCESS], ['type', '!=', '2']],
                ],
                3 => [
                    'name' => '已审核自主寄件',
                    'param' => [['come_exp_type', '=', 2], ['status', '=', Order::AUDIT_PASS], ['type', '!=', '2']],
                ],
            ];
            //筛选条数
            foreach ($option as $key => $value) {
                $option[$key]['count'] = Order::where($value['param'])->count();
            }
            $grid->tools(function ($tools) use ($option) {
                //自定义状态快捷筛选按钮
                $tools->append(new QuickPickTool($option));
//                $tools->d
                $tools->batch(function ($batch) {
                    $batch->add('取消订单', new Cancel());
                });
            });

            //根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');
            }
            $grid->model()->where($option[-1]['param'])->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');

            $grid->id('ID')->sortable();
            Order::order_priority_column($grid);
            $grid->sn('寄修单号');
            $grid->rb_come_exp_sn('内部寄来快递单号')->editable();
            $grid->come_exp_sn('寄来快递单号')->editable();
            //$grid->barcode('S/N码');
            //$grid->model_name('机型');
            $grid->column('S/N码-机型')->display(function () {
                return $this->barcode . '<br/>' . $this->model_name;
            });
            //$grid->name('寄件人');
            //$grid->phone('用户联系方式');
            $grid->column('联系人')->display(function () {
                return $this->name . '<br/>' . $this->phone;
            });
            $grid->address('寄件人地址')->display(function ($value) {
                return $this->province . $this->city . $this->district . $this->address;
            });
//            $grid->endpoint()->name('寄修售后点');
            $grid->come_exp_type('寄来方式')->display(function ($come_exp_type) {
                return Order::come_exp_type[$come_exp_type];
            });

            $grid->sign_in_status('签收状态')->display(function () {
                $sign_in_status = OrderExtend::where('sn', $this->sn)->value('sign_in_status');
                return Order::SIGN_IN_STATUS[$sign_in_status];
            });
            $grid->status('订单状态')->display(function ($status) {
                $s = Order::STATUS;
                if (array_key_exists($status, $s)) {
                    return $s[$status];
                } else {
                    return "————";
                }
            });
            $grid->audit_opinion('审核备注');
            $grid->receive_time('签收时间');
            $grid->column('order_extend.print_time', '打印时间');
            $grid->column('order_extend.print_man', '打印人员')->display(function ($user_id) {
                if ($user_id) {
                    return DB::table('admin_users')->where('id', $user_id)->value('name');
                }
                return '---';
            });
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                //$actions->disableEdit();
                $status = $actions->row->status;
                $c = 'post_sign/view/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">查看 </span></a>';
                $actions->append($html);
                if ($status == Order::AUDIT_PASS) {
                    $html = '<a href="javascript:void(0);" class="set_come_sure" value="' . $actions->row->id . '"><span style="color:orange">【标发货】 </span></a>';
                    $actions->append($html);
                }
                if ($status == Order::EXP_COME_SUCCESS) {
                    $html = '<a href="javascript:void(0);" class="post_sign_in" data-id="' . $actions->row->id . '" value="' . $actions->row->id . '"><span style="color:orange">签收 </span></a>';
                    $actions->append($html);
                }
                if ($status == Order::COME_SURE) {
                    $html = '<a href="javascript:void(0);" class="print" value="' . $actions->row->id . '"><span style="color:orange">打印便签 </span></a>';
                    $actions->append($html);
                }
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Admin::form(Order::class, function (Form $form) {
            $form->display('sn', '寄修单号');
            $form->text('rb_come_exp_sn', '内部寄来快递单号');
            $form->text('come_exp_sn', '寄来快递单号');
            $form->radio('order_extend.sign_in_status', '签收状态')->options(Order::SIGN_IN_STATUS);
            $form->text('receive_case', '签收备注(收到配件)');
//            $form->select('repair_status', '维修状态')
//                ->options(Order::repair_status);
//            $form->hidden('repair_man');
//            $form->hidden('status');
            $form->saving(function (Form $form) {
                $form->no_log = 1;
                $order_log = PostExpress::where([['exp_sn', '=', $form->come_exp_sn]])->value('readboy_sn');
//                dd($order_log);
                $form->rb_come_exp_sn = $order_log;
            });
        });
    }
}
