<?php

namespace App\Admin\Extensions;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use function PHPSTORM_META\type;
use function Symfony\Component\VarDumper\Tests\Fixtures\bar;

/**
 * 消息推送
 */
class MessagePush
{
    const MESSAGE_PUSH_URL = 'https://api1-yx.readboy.com'; // 终端服务APP推送地址(正式)
    // const MESSAGE_PUSH_URL = 'http://api1-yxtest.readboy.com'; // 终端服务APP推送地址(测试)
    const APP_ID = 'repair.readboy.com';
    const APP_KEY = '31879b82704954028802c9325bbcd55c';

    /**
     * 碎屏保推送
     */
    public function push($sn, $barcode, $endpoint){
        $client = new Client();
        $url = self::MESSAGE_PUSH_URL . '/tp/v1/notification/push';
        $now = strtotime(date('Y-m-d H:i:s'));

        $data = array('app_id'=> self::APP_ID, 'timestamp'=>$now);

        self::ksort_recursion($data);
        // 生成查询字符串并哈希

        $sign = md5(http_build_query($data) . self::APP_KEY);
        $data['sign'] = $sign;
        $param = http_build_query($data);
        $url = $url.'?'.$param;
        $h5_url = [
            "name"  =>  "碎屏保",
            "url"   =>  "http://h5-yx.readboy.com/brokenScreen/record",
            'icon'  =>  'http://dt.readboy.com/rbcare/h5/icon/碎屏保.png',
            'index' =>  'http://h5-yx.readboy.com/brokenScreen/',
            'key'   =>  'screen_insurance'
        ];
        $param_form = [
            'form_params' => [
                'platform' => ['ios','android'],
                'audience' => [
                    'endpoints' => [$endpoint]
                ],
                'title' => '碎屏保审核结果通知',
                'content' => '单号:'.$sn.',条码'.$barcode.'的订单已经通过审核，请尽快支付，有效期1小时。',
                'slug' => 'broken_screen_insurance',
                'action' => 'forward',
                'url' => 'h5://'.json_encode($h5_url),
                // 'popup' => 1,
            ]
        ];
        $response = $client->request('POST', $url, $param_form);

        $body = $response->getBody();
        $content = $body->getContents();
        $content = json_decode($content);
        // dd($content);
        // dump($url);
        // $param1 = [
        //     'platform' => ['ios','android'],
        //     'audience' => ['tags' => ['终端版']],
        //     'title' => '碎屏保审核结果通知',
        //     'content' => '碎屏保',
        //     'slug' => 'broken_screen_insurance',
        //     'action' => 'forward',
        //     'url' => 'http://h5-yxtest.readboy.com/brokenScreen/record',
        //     'popup' => 1,
        // ];
        // $param1 = json_encode($param1);
        // dump($param1);
        // $curl = curl_init();
        // curl_setopt($curl, CURLOPT_URL, $url);
        // curl_setopt($curl, CURLOPT_HEADER, false);
        // curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
        // curl_setopt($curl, CURLOPT_POST, true);
        // curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        // curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        // curl_setopt($curl,CURLOPT_POSTFIELDS, $param1);
        // $output = curl_exec($curl);
        // dd($output);
        // curl_close($curl);
    }

    /**
     * 知会备注推送(终端代寄) 按终端ID推送  推送消息到终端服务APP
     * @return void
     */
    public function pushRemarkEndpoint($endpoint , $content){
        $client = new Client();
        $url = self::MESSAGE_PUSH_URL . '/tp/v1/notification/push';
        $now = strtotime(date('Y-m-d H:i:s'));

        $data = array('app_id'=> self::APP_ID, 'timestamp'=>$now);

        self::ksort_recursion($data);
        // 生成查询字符串并哈希

        $sign = md5(http_build_query($data) . self::APP_KEY);
        $data['sign'] = $sign;
        $param = http_build_query($data);
        $url = $url.'?'.$param;
        $param_form = [
            'form_params' => [
                'platform' => ['ios','android'],
                'audience' => [
                    'endpoints' => [$endpoint]
                ],
                'title' => '寄修知会通知',
                'content' => $content,
                'slug' => 'post_repair_remark',
                'action' => 'none',
            ]
        ];
        $response = $client->request('POST', $url, $param_form);
        $body = $response->getBody();
        $responseContent = $body->getContents();
        $responseContent = json_decode($responseContent , 1);
        $code = $responseContent['code'];

        // 记录推送日志
        $messageLogData = array(
            'endpoint_id' => $endpoint,
            'content' => $content,
            'status' => $code == 0 ? 1 : 2,
            'created_at' => date('Y-m-d H:i:s')
        );
        DB::table('message_push_log')->insert($messageLogData);
    }

    /**
     * 知会备注推送(正常寄修,代理商寄修) 按账户中心ID推送  推送消息到家长助手APP
     * @return void
     */
    public function pushRemark($targetId , $description){
        $client = new Client();
        $url = 'https://api-aide.readboy.com/api/save_push_message';

        $data = array(
            'target_ids' => $targetId,
            'title' => '寄修知会通知',
            'description' => $description,
            'data' => '{"type":30}'
        );
        $param = http_build_query($data);
        $url = $url.'?'.$param;
        
        $response = $client->request('GET', $url);
        $body = $response->getBody();
        $content = $body->getContents();
        $content = json_decode($content , 1);
        $errno = $content['errno'];

        // 记录推送日志
        $messageLogData = array(
            'uid' => $targetId,
            'content' => $description,
            'status' => $errno == 0 ? 1 : 2,
            'created_at' => date('Y-m-d H:i:s')
        );
        DB::table('message_push_log')->insert($messageLogData);
    }

    /**
     * 生成查询字符串并哈希
     * @return void
     */
    public static function ksort_recursion(array &$array, $sort_flags = null)
    {
        foreach ($array as &$item) {
            if (is_array($item)) {
                self::ksort_recursion($item, $sort_flags);
            }
        }
        ksort($array);
    }
}
