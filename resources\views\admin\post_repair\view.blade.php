@if(!empty($ext_param))
    <style>
        .content-wrapper {
            margin: 0;
            padding: 0 !important;
        }

        .main-header {
            display: none;
        }

        .main-sidebar {
            display: none;
        }

        .main-footer {
            display: none;
        }
    </style>
@endif

<div class="btn-group pull-right" style="margin-right: 10px">
    <a href="javascript:history.back(-1)" class="btn btn-sm btn-default">
        <i class="fa fa-list "></i>&nbsp;返回（返回后如果进入检测页面请按F5刷新）
    </a>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                {{--<h3 class="box-title">查询</h3>--}}
            </div>
            <div class="box-body" style="min-height:650px">
                <div class="col-md-3">
                </div>
                <div class="col-md-6" style="float: left">
                    <table class="table">
                        <caption align="center" style="text-align: center; font-size:20px;">用户信息</caption>
                        @if($order_old_address)
                            <tr>
                                <th align="center" width="33%"><span style="color:red">旧联系人信息:</span></th>
                                <td>
                                    {{$order_old_address->name}}<br>
                                    {{$order_old_address->phone}}<br>
                                    {{$order_old_address->province}} {{$order_old_address->city}} {{$order_old_address->district}} {{$order_old_address->address}}
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <th align="center" width="33%">寄修单号:</th>
                            <td>{{$order->sn}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">寄修人:</th>
                            <td>{{$order->name}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">联系方式:</th>
                            <td>
                                @if((!empty($is_ykf_400) || !empty($is_ykf_repair)) && $order->status >= 400)
                                    <a id="call" title="点击拨打: {{$order->phone}}"
                                       href="JavaScript:void(0);">{{$order->phone}}</a>
                                @else
                                    {{$order->phone}}
                                @endif
                            </td>
                            @if(!empty($is_ykf_400) && $order->status == 400)
                                <td>
                                    <button class="btn btn-sm btn-danger" id="edit_is_tell_400">
                                        <i class="fa fa-edit"></i>&nbsp;变更状态为已知会
                                    </button>
                                </td>
                            @endif

                            @if(!empty($is_ykf_repair) && ($order->status == 410 || $order->status == 490))
                                <td>
                                    <button class="btn btn-sm btn-danger" id="edit_is_tell_repair">
                                        <i class="fa fa-edit"></i>&nbsp;变更状态为已知会
                                    </button>
                                </td>
                            @endif
                        </tr>

                        <!-- 客服知会以及备注 -->
                        @if(!empty($orderRemarkInfo1))
                            <tr>
                                <th align="center" width="33%">客服知会:</th>
                                <td>
                                    <a href="JavaScript:void(0);" id="openContent1">
                                        {{$orderRemarkInfo1->status_name}}
                                    </a>
                                    <br>
                                    <span>{{$orderRemarkInfo1->name}}  {{$orderRemarkInfo1->created_at}}</span>
                                </td>
                            </tr>

                            <tr>
                                <th align="center" width="33%">客服备注:</th>
                                <td>
                                    <a href="JavaScript:void(0);" id="400Remark">
                                        {{$orderRemarkInfo1->remark}}
                                    </a>({{$orderRemarkInfo1->created_at}}
                                    )
                                </td>
                            </tr>
                        @endif

                        @if(!empty($allOrderRemarkInfo[0]))
                            <tr>
                                <th align="center" width="33%">维修知会:</th>
                                <td>
                                    <a href="JavaScript:void(0);"
                                       id="openContent">{{$allOrderRemarkInfo[0]['status_name']}}</a>
                                    <br>
                                    <span>{{$allOrderRemarkInfo[0]['name']}} {{$allOrderRemarkInfo[0]['created_at']}}</span>
                                </td>
                            <!--
                            @if($allOrderRemarkInfo[0]['status'] == 500)
                                <td>
                                    <button class="btn btn-sm btn-primary" id="sendMsgBtn" >发送支付推送信息</button>
                                </td>
                            @endif -->
                            </tr>
                            <tr>
                                <th align="center" width="33%">维修知会备注:</th>
                                <td>
                                    <a href="JavaScript:void(0);" id="repairRemark">
                                        {{$allOrderRemarkInfo[0]['remark']}}
                                    </a>({{$allOrderRemarkInfo[0]['created_at']}}
                                    )
                                </td>
                            </tr>
                        @endif

                        <tr>
                            <th align="center" width="33%">寄修地址:</th>
                            <td>{{$order->province}} {{$order->city}} {{$order->district}} {{$order->address}}</td>
                        </tr>

                        @if($agent_order)
                            <tr>
                                <th align="center" width="33%"><span style="color: burlywood;">代理人信息:</span></th>
                                <td>
                                    {{$agent_order->name}}<br>
                                    {{$agent_order->phone}}<br>
                                    {{$agent_order->province}} {{$agent_order->city}} {{$agent_order->district}} {{$agent_order->address}}
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <th align="center" width="33%">设备型号:</th>
                            <td>{{$order->model_name}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">S/N码:</th>
                            <td>{{$order->barcode}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%" style="color: red">更换主板序列号:</th>
                            <td>{{$order_extend->exchange_number}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">寄修方式:</th>
                            <td>{{$order->come_exp_type==1?'上门取件':($order->come_exp_type==2?'自主寄件':'无')}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%" style="color: red">附件信息:</th>
                            <td>{{$order->attachment}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">保修状态:</th>
                            <td>{{$order->in_period==1?'保修期内':($order->in_period==2?'保修期外':'无保修信息')}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">受损状态:</th>
                            {{--<td>{{$order->reason==1?'人为损坏':($order->reason==2?'元件损坏':'未知原因')}}</td>--}}
                            <td>{{\App\Models\Order::get_reason($order->reason)}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">客诉故障类型:</th>
                            <td>{{$order->damage}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">细节描述:</th>
                            <td>{{$order->description}}</td>
                        </tr>

                        <tr>
                            <th align="center" width="33%">保修材料:</th>
                            <td>
                        @if (is_array($order->period_file))
                            @foreach($order->period_file as $value)
                                <tr>
                                    <th></th>
                                    <td><img class="img" src="{{config('admin.upload.host').$value}}" height="200"></td>
                                </tr>
                                @endforeach
                                @endif
                                </td>
                                </tr>
                                <tr>
                                    <th align="center" width="33%">描述材料:</th>
                                    <td>
                                        @if (is_array($order->upload_file))
                                            @foreach($order->upload_file as $value)
                                                <img class="img" src="{{config('admin.upload.host').$value}}"
                                                     height="200">
                                            @endforeach
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th align="center" width="33%">视频:</th>
                                    <td>
                                        @if (is_array($order->video_file))
                                            @foreach($order->video_file as $value)
                                                <video class="video" src="{{config('admin.upload.host').$value}}"
                                                       controls="controls" height="200" width="400px"></video>
                                            @endforeach
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th align="left" width="33%">是否开具发票:</th>
                                    <td align="left" width="">{{$order->need_invoice?'是':'否'}}</td>
                                </tr>
                                <tr>
                                    <th align="left" width="33%">发票抬头:</th>
                                    <td align="left" width="">{{$order->invoice_title}}</td>
                                </tr>
                                <tr>
                                    <th align="left" width="33%">发票税号:</th>
                                    <td align="left" width="">{{$order->invoice_tax_id}}</td>
                                </tr>
                                <tr>
                                    <th align="left" width="33%">发票邮箱:</th>
                                    <td align="left" width="">{{$order->invoice_email}}</td>
                                </tr>
                    </table>
                    <table class="table">
                        <caption align="center" style="text-align: center; font-size:20px;">后台信息</caption>
                        <tbody>
                        <tr>
                            <th align="center" width="33%">审核备注:</th>
                            <td>{{$order->audit_opinion}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">订单优先级:</th>
                            <td>{!!$order_extend->order_priority_span!!}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">是否二次维修:</th>
                            <td>
                                <span>{{$order->repeat_order ? '是' : '否'}}</span>
                                <span>{{$order->repeat_order ? $order->repeat_order_count . '次' : ''}}</span>
                            </td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">该条码之前弃修订单数:</th>
                            @if($order->ar_repeat > 0)
                            <td style="color: red;">{{$order->ar_repeat}}</td>
                            @else
                            <td>{{$order->ar_repeat}}</td>
                            @endif
                        </tr>
                        <tr>
                            <th align="center" width="33%">是否终端寄修:</th>
                            <td>{{$order->is_agency? '是':'否'}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">签收配件:</th>
                            <td>{{$order->receive_case}}</td>
                        </tr>

                        @if(!empty($order_extend->sign_in_status_name))
                            <tr>
                                <th align="center" width="33%">签收状态:</th>
                                <td>{{$order_extend->sign_in_status_name}}</td>
                            </tr>
                        @endif

                        @if(!empty($order_extend->sign_in_pictures))
                            <tr>
                                <th align="center" width="33%">签收图片:</th>
                                {{--@php
                                    $order_extend->sign_in_pictures = json_decode($order_extend->sign_in_pictures , 1 );
                                @endphp--}}
                                <td>
                                    @foreach($order_extend->sign_in_pictures as $value)
                                        <img class="img" src="{{config('admin.upload.host').$value}}" height="200">
                                    @endforeach
                                </td>
                            </tr>
                        @endif

                        @if(!empty($order_extend->sign_in_videos))
                            <tr>
                                <th align="center" width="33%">签收视频:</th>
                                <td>
                                    @foreach($order_extend->sign_in_videos as $value)
                                        <video src="{{config('admin.upload.host').$value}}" height="200" controls>
                                    @endforeach
                                </td>
                            </tr>
                        @endif

                        <tr>
                            <th align="center" width="33%">维修备注:</th>
                            <td>{{$order->deal_remark}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">支付备注:</th>
                            @if($order_extend)
                                <td>{{$order_extend->pay_remark}}</td>
                            @endif
                        </tr>
                        <tr>
                            <th align="center" width="33%">后台备注:</th>
                            @if($order_extend)
                                <td>{{$order_extend->backstage_remark}}</td>
                            @endif
                        </tr>
                        <tr>
                            <th align="center" width="33%">维修图片:</th>
                            <td>
                                @if (is_array($order->repair_image))
                                    @foreach($order->repair_image as $value)
                                        @php
                                            // 获取文件扩展名
                                            $extension = pathinfo($value, PATHINFO_EXTENSION);
                                        @endphp

                                        @if (in_array($extension, ['jpg', 'jpeg', 'png']))
                                            <!-- 如果是图片格式 -->
                                                <img class="img" src="{{ config('admin.upload.host') . $value }}" height="200">
                                        @elseif (in_array($extension, ['mp4', 'avi']))
                                            <!-- 如果是视频格式 -->
                                                <video width="320" height="240" controls muted>
                                                    <source src="{{ config('admin.upload.host') . $value }}" type="video/{{ $extension }}">
                                                </video>
                                        @endif
                                    @endforeach
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">检测人:</th>
                            <td>{{$order->check_user? $order->check_user->name:''}}</td>
                        </tr>
                        <tr>
                            <th>自选配件:</th>
                        </tr>
                        @if (isset($pr_oa) && $pr_oa)
                            <tr>
                                <td align="left" width="25%">配件分类</td>
                                <td align="left" width="25%">配件名称</td>
                                <td align="left" width="25%">物料编码</td>
                                <td align="left" width="25%">购买数量</td>
                                <td align="left" width="25%">购买单价</td>
                                <td align="left" width="25%">合计</td>
                                <td align="left" width="25%">状态</td>
                            </tr>
                            @foreach($pr_oa as $oa)
                                <tr>
                                    <td>{{$oa->oac_name}}</td>
                                    <td>{{$oa->oa_name}}</td>
                                    <td>{{$oa->m_code}}</td>
                                    <td>{{$oa->count}}</td>
                                    <td>{{$oa->price}}</td>
                                    <td>{{$oa->price_sum}}</td>
                                    <td>{{\App\Models\PostRepairOptionalAccessory::STATUS[$oa->status]}}</td>
                                </tr>
                            @endforeach
                        @else
                            <td>无</td>
                        @endif
                        <tr>
                            <th align="left" width="">更换配件:</th>
                            {{--<td align="left" width="">{{$accessory}}</td>--}}
                        </tr>
                        @if ($pr_material)
                            <tr>
                                <td align="left" width="25%">配件</td>
                                <td align="left" width="25%">更换故障</td>
                                <td align="left" width="25%">价格（元）</td>
                                <td align="left" width="25%">数量</td>
                            </tr>
                            @foreach($pr_material as $p)
                                <tr>
                                    <td>{{$p->title}}</td>
                                    <td>{{$p->malfunction}}</td>
                                    <td>{{(property_exists($p, 'charge_type') && property_exists($p, 'price_first') && ($p->charge_type == 2))
                                            ? $p->price_first : $p->price
                                        }}</td>
                                    <td>{{$p->count}}</td>
                                </tr>
                            @endforeach
                        @else
                            <td>无</td>
                        @endif
                        <tr>
                            <th align="left" width="">更换物料:</th>
                            {{--<td align="left" width="">{{$accessory}}</td>--}}
                        </tr>
                        @if ($pr_used_material)
                            <tr>
                                <td align="left" width="">物料名称规格</td>
                                {{--<td align="left" width="">物料规格</td>--}}
                                <td align="left" width="">物料编码</td>
                                <td align="left" width="">旧编码</td>
                                <td align="left" width="">仓库</td>
                                <td align="left" width="">价格（元）</td>
                                <td align="left" width="">数量</td>
                                <td align="left" width="">是否收费</td>
                                <td align="left" width="">实收价格</td>
                            </tr>
                            @foreach($pr_used_material as $p)
                                <tr>
                                    <td>{{$p->name.$p->specification}}</td>
                                    {{--<td>{{$p->specification}}</td>--}}
                                    <td>{{$p->code}}</td>
                                    <td>{{$p->old_code}}</td>
                                    <td>{{$p->from == 1?'新':'旧'}}</td>
                                    <td>{{(property_exists($p, 'charge_type') && property_exists($p, 'price_first') && ($p->charge_type == 2))
                                            ? $p->price_first : $p->price
                                        }}</td>
                                    <td>{{$p->count}}</td>
                                    <td>{{\App\Models\Order::is_charge[$p->is_charge]}}</td>
                                    <td>
                                        @if (property_exists($p, 'price_in') && $p->price_in > 0)
                                            {{ $p->price_in }}
                                        @else
                                            {{ (property_exists($p, 'charge_type') && property_exists($p, 'price_first') && ($p->charge_type == 2))
                                                ? $p->price_first : $p->price }}
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            <td>无</td>
                        @endif
                        <tr>
                            <th align="center" width="33%">自选购配件费用:</th>
                            <td>{{$order->optional_accessory_cast}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">维修的配件费用:</th>
                            <td>{{$order->accessory_cast}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">弃修时配件费用:</th>
                            <td>{{$order->accessory_in_ar}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">快递费用:</th>
                            <td>{{$order->staff_cast}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">总费用:</th>
                            <td>{{$order->amount}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">维修时需支付金额:</th>
                            <td>{{$order->pay_amount}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">弃修时需支付金额:</th>
                            <td>{{$order->amount_in_ar}}</td>
                        </tr>

                        <tr>
                            <th align="center" width="33%">维修记录时间:</th>

                            @if(!empty($order_extend->repair_record_times))
                                <td>{{$order_extend->repair_record_times}}</td>
                            @endif

                            @if(!empty($can_record_repair_time))
                                <td>
                                    <button class="btn btn-sm btn-primary" id="save_repair_record_time">
                                        开始维修（添加维修记录时间）
                                    </button>
                                </td>
                            @endif

                        </tr>

                        </tbody>
                    </table>
                </div>
                <div class="col-md-3">
                </div>
                <div class="col-md-12">
                </div>
            </div>
            <div class="box-footer">
            </div>
        </div>
    </div>
</div>

<input hidden id="bill_id" type="text" value="{{$order->sn}}">
<input hidden id="phoneNumber" type="text" value="{{$order->phone}}">
@if(!empty($usersYkfInfo))
    <input hidden id="ykf_account" type="text" value="{{$usersYkfInfo->ykf_account}}">
    <input hidden id="ykf_password" type="text" value="{{$usersYkfInfo->ykf_password}}">
@endif
<!-- 变更状态为已收货已知会 -->
<div id="edit_is_tell_400_div" hidden class="box box-info">
    <div class="box-header with-border">
        客服知会
    </div>
    <div class="box-body" style="max-width:750px">

        <div class="form-group">
            <label class="control-label">客服知会</label>
            <input type="checkbox" class="status la_checkbox" name="status" value="1"/><label
                    class="control-label">已知会</label>
        </div>

        <div class="form-group">

            <label for="order_remark" class="control-label">客服备注</label>

            <div style="display:flex">
                <input style="margin:1%" name="remarkRadio400s" value="需要再次通知" type="radio"> 需要再次通知
                <input style="margin:1%" name="remarkRadio400s" value="已沟通未协商好" type="radio"> 已沟通未协商好
                <input style="margin:1%" name="remarkRadio400s" value="顾客有争议" type="radio"> 顾客有争议
                <input style="margin:1%" name="remarkRadio400s" value="终端有争议" type="radio"> 终端有争议
                <input style="margin:1%" name="remarkRadio400s" value="等待回复" type="radio"> 等待回复
                <input style="margin:1%" name="remarkRadio400s" value="已成功知会,分派订单" type="radio"> 已成功知会,分派订单
                <input style="margin:1%" name="remarkRadio400s" value="无其他异常,直接分派" type="radio"> 无其他异常,直接分派
            </div>

            <div class="input-group">
                <span class="input-group-addon"><i class="fa fa-pencil"></i></span>
                <input type="text" id="order_remark" name="order_remark" value="" class="form-control"/>
            </div>
        </div>

        <button class="btn btn-sm btn-primary" id="is_tell_400_save">提交</button>
    </div>
</div>

<!-- 变更状态为未检测已知会/已检测已知会 -->
<div id="edit_is_tell_repair_div" hidden class="box box-info">
    <div class="box-header with-border">
        维修知会
    </div>
    <div class="box-body" style="max-width:750px">

        <div class="form-group">
            <label class="control-label">维修知会</label>
            <input type="checkbox" class="status la_checkbox" name="repair_status" value="1"/><label
                    class="control-label">已知会</label>
        </div>

        <div class="form-group">
            <label for="order_remark_repair" class="control-label">知会备注</label>
            <div style="display:flex">
                <input style="margin:1%" name="remarkRadioRepairs" value="需要再次通知" type="radio"> 需要再次通知
                <input style="margin:1%" name="remarkRadioRepairs" value="已沟通未协商好" type="radio"> 已沟通未协商好
                <input style="margin:1%" name="remarkRadioRepairs" value="顾客有争议" type="radio"> 顾客有争议
                <input style="margin:1%" name="remarkRadioRepairs" value="终端有争议" type="radio"> 终端有争议
                <input style="margin:1%" name="remarkRadioRepairs" value="等待回复" type="radio"> 等待回复
                <input style="margin:1%" name="remarkRadioRepairs" value="未打电话" type="radio"> 未打电话
                <input style="margin:1%" name="remarkRadioRepairs" value="已打电话" type="radio"> 已打电话
            </div>
            <div class="input-group">
                <span class="input-group-addon"><i class="fa fa-pencil"></i></span>
                <input type="text" id="order_remark_repair" name="order_remark_repair" value="" class="form-control"/>
            </div>
        </div>

        <button class="btn btn-primary" id="is_tell_repair_save">提交</button>
    </div>
</div>

<!-- 通话记录 -->
<div id="phoneRecord1" hidden class="box box-info">
    <div class="box-header with-border">
        客服沟通
    </div>
    <div class="box-body" style="min-height:300px">
        <table class="table">
            <tr>
                <th style="text-align: center;">序号</th>
                <th style="text-align: center;">主叫号码</th>
                <th style="text-align: center;">被叫号码</th>
                <th style="text-align: center;">客服</th>
                <th style="text-align: center;">通话接通时间</th>
                <th style="text-align: center;">通话结束时间</th>
                <th style="text-align: center;">通话录音</th>
            </tr>

            @if(!empty($callLog1))
                @foreach($callLog1 as $key => $value)
                    <tr>
                        <td style="text-align: center;">{{$key+1}}</td>
                        <td style="text-align: center;">{{$value->call_no}}</td>
                        <td style="text-align: center;">{{$value->called_no}}</td>
                        <td style="text-align: center;">{{$value->name}}</td>
                        <td style="text-align: center;">{{$value->begin}}</td>
                        <td style="text-align: center;">{{$value->end}}</td>
                        <td style="text-align: center;">
                            @if(empty($value->record_file))
                                无录音文件
                            @endif

                            @if(!empty($value->record_file) && empty($value->sound_url))
                                <audio controls style="height: 20px;">
                                    <source src="{{$value->file_server}}/{{$value->record_file}}" type="audio/mpeg">
                                </audio>
                            @endif

                            @if(!empty($value->sound_url))
                                <audio controls style="height: 20px;">
                                    <source src="{{$value->sound_url}}" type="audio/mpeg">
                                </audio>
                            @endif
                        </td>
                    </tr>
                @endforeach
            @endif
        </table>

    </div>
</div>
<!-- 通话记录  end-->

<!-- 通话记录 -->
<div id="phoneRecord" hidden class="box box-info">
    <div class="box-header with-border">
        维修知会
    </div>
    <div class="box-body" style="min-height:300px">
        <table class="table">
            <tr>
                <th style="text-align: center;">序号</th>
                <th style="text-align: center;">主叫号码</th>
                <th style="text-align: center;">被叫号码</th>
                <th style="text-align: center;">客服</th>
                <th style="text-align: center;">通话接通时间</th>
                <th style="text-align: center;">通话结束时间</th>
                <th style="text-align: center;">通话录音</th>
            </tr>

            @if(!empty($callLog))
                @foreach($callLog as $key => $value)
                    <tr>
                        <td style="text-align: center;">{{$key+1}}</td>
                        <td style="text-align: center;">{{$value->call_no}}</td>
                        <td style="text-align: center;">{{$value->called_no}}</td>
                        <td style="text-align: center;">{{$value->name}}</td>
                        <td style="text-align: center;">{{$value->begin}}</td>
                        <td style="text-align: center;">{{$value->end}}</td>
                        <td style="text-align: center;">
                            @if(empty($value->record_file))
                                无录音文件
                            @endif

                            @if(!empty($value->record_file) && empty($value->sound_url))
                                <audio controls style="height: 20px;">
                                    <source src="{{$value->file_server}}/{{$value->record_file}}" type="audio/mpeg">
                                </audio>
                            @endif

                            @if(!empty($value->sound_url))
                                <audio controls style="height: 20px;">
                                    <source src="{{$value->sound_url}}" type="audio/mpeg">
                                </audio>
                            @endif
                        </td>
                    </tr>
                @endforeach
            @endif
        </table>
    </div>
</div>
<!-- 通话记录  end-->

<!-- 添加客服备注 -->
<div class="box box-info" hidden id="400RemarkDiv">
    <div class="box-header with-border">备注</div>
    <div class="box-body" style="min-height:300px;min-width:500px">
        <table class="table">
            <tr>
                <th style="text-align: center;">时间</th>
                <th style="text-align: center;">订单状态</th>
                <!-- <th style="text-align: center;" >联系状态</th> -->
                <th style="text-align: center;">操作人</th>
                <th style="text-align: center;">备注</th>
            </tr>
            <tbody id="tbody400">
            @if(!empty($allOrderRemarkInfo1))
                @foreach($allOrderRemarkInfo1 as $key => $value)
                    <tr>
                        <td style="text-align: center;">{{$value->created_at}}</td>
                        <td style="text-align: center;">{{$value->status_name}}</td>
                    <!-- <td style="text-align: center;">{{$value->connect_name}}</td> -->
                        <td style="text-align: center;">{{$value->name}}</td>
                        <td style="text-align: center;">{{$value->remark}}</td>
                    </tr>
                @endforeach
            @endif
            </tbody>
        </table>
    </div>

    <div style="display:flex">
        <input style="margin:1%" name="remarkRadio400" value="需要再次通知" type="radio"> 需要再次通知
        <input style="margin:1%" name="remarkRadio400" value="已沟通未协商好" type="radio"> 已沟通未协商好
        <input style="margin:1%" name="remarkRadio400" value="顾客有争议" type="radio"> 顾客有争议
        <input style="margin:1%" name="remarkRadio400" value="终端有争议" type="radio"> 终端有争议
        <input style="margin:1%" name="remarkRadio400" value="等待回复" type="radio"> 等待回复
    </div>
    <div style="display:flex">
        <textarea id="remarkTextarea400" placeholder="请输入备注,不超过100字" cols="70"></textarea>
        <a class="btn btn-sm btn-primary" type="button" id="saveRemark400">确定</a>
    </div>
</div>

<!-- 添加维修备注 -->
<div class="box box-info" hidden id="RepairRemarkDiv">
    <div class="box-header with-border">备注</div>
    <div class="box-body" style="min-height:300px;min-width:500px">
        <table class="table">
            <tr>
                <th style="text-align: center;">时间</th>
                <th style="text-align: center;">订单状态</th>
                <!-- <th style="text-align: center;" >联系状态</th> -->
                <th style="text-align: center;">操作人</th>
                <th style="text-align: center;">备注</th>
            </tr>
            <tbody id="tbodyRepair">

            @if(!empty($allOrderRemarkInfo))
                @foreach($allOrderRemarkInfo as $key => $value)
                    <tr>
                        <td style="text-align: center;">{{$value->created_at}}</td>
                        <td style="text-align: center;">{{$value->status_name}}</td>
                    <!-- <td style="text-align: center;">{{$value->connect_name}}</td> -->
                        <td style="text-align: center;">{{$value->name}}</td>
                        <td style="text-align: center;">{{$value->remark}}</td>
                    </tr>
                @endforeach
            @endif
            </tbody>
        </table>
    </div>

    <div style="display:flex">
        <input style="margin:1%" name="remarkRadioRepair" value="需要再次通知" type="radio"> 需要再次通知
        <input style="margin:1%" name="remarkRadioRepair" value="已沟通未协商好" type="radio"> 已沟通未协商好
        <input style="margin:1%" name="remarkRadioRepair" value="顾客有争议" type="radio"> 顾客有争议
        <input style="margin:1%" name="remarkRadioRepair" value="终端有争议" type="radio"> 终端有争议
        <input style="margin:1%" name="remarkRadioRepair" value="等待回复" type="radio"> 等待回复
        <input style="margin:1%" name="remarkRadioRepair" value="未打电话" type="radio"> 未打电话
        <input style="margin:1%" name="remarkRadioRepair" value="已打电话" type="radio"> 已打电话
    </div>
    <div style="display:flex">
        <textarea id="remarkTextareaRepair" placeholder="请输入备注,不超过100字" cols="90"></textarea>
        <a class="btn btn-primary" type="button" id="saveRemarkRepair">确定</a>
    </div>
</div>

<div id="softphone-bar" hidden class="box box-info">
    <!-- 电话条标题栏 -->
    <div class="phone-bar-header">
        <span class="phone-bar-title">云客服电话</span>
        <div class="phone-bar-controls">
            <button class="phone-bar-minimize" title="最小化">−</button>
            <button class="phone-bar-close" title="关闭">×</button>
        </div>
    </div>
    <div id="phone_bar" class="clearFix">
        <div class="btn-group f-l">
            <button id="HoldEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.hold()"> 保持 </button >
            <button id="HoldGetEnable" type="button" class="btn btn-primary margin-r5" onclick="holly.unHold()" style="display:none"> 恢复 </button>
            <button id="MuteEnable" type="button" class="btn btn-primary margin-r5" onclick="holly.mute()" style="display:none"> 静音 </button>
            <button id="UnMuteEnable" type="button" class="btn btn-primary margin-r5" onclick="holly.unMute()" style="display:none"> 恢复 </button>
            <button id="TransferEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.openTransferOrConsult('softphone_transfer')"> 转接 </button>
            <button id="ConsultTransferEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.transfer('9123456', 'number')"> 转接 </button>
            <button id="ConsultEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.openTransferOrConsult('softphone_consult')"> 咨询 </button>
            <button id="InvestigateEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.investigate()"> 邀评 </button>
            <button id="ValidateEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.validate()"> 转验证 </button>
            <button id="IVRMenuEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.openToMenuPage('softphone_transfer')"> 转菜单 </button>
            <button id="SmsChatPortalEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.openChatPortalPage('softphone_transfer')"> 转在线 </button>
            <button id="ThreeWayCallEnable" class="btn btn-primary margin-r5" style="display:none"> 三方 </button>
            <button id="ConsultThreeWayCallEnable" type="button" class="btn btn-primary margin-r5" style="display: none" onclick="holly.threeWayCall('9123456')"> 三方 </button>
            <button id="StopConsultEnable" type="button" class="btn btn-danger margin-r5" style="display:none" onclick="holly.stopConsult()"> 结束咨询 </button>
            <button id="CallVideoEnable" type="button" class="btn btn-danger margin-r5" style="display:none" onclick="holly.openVideoRoom()"> 视频 </button>
            <button id="HangupEnable" type="button" class="btn btn-danger margin-r5" onclick="holly.hangup()" style="display:none"> 挂机 </button>
        </div>
        <div class="f-l">
            <input id="dialout_input" class="span2" type="text" value="" placeholder="请输入电话号码" style="display: none;" onkeydown="if(event.keyCode==13){holly.dialout($('#dialout_input').val())}">
            <button id="DialEnable" class="btn btn-primary margin-r5" type="button" onclick="holly.dialout($('#dialout_input').val())" style="display:none"> 呼叫 </button>
        </div>
        <div class="state_group f-l clearFix">
            <div id="softphone_phonestate" class="f-l state">未签入</div>
            <div id="softphone_dropdown" class="f-l state_dropdown"><b id="softphone_dropdown_caret" class="caret"></b> </div>
            <div id="softphone_timer" class="f-l state_time">00:00:00</div>
            <div id="softphone_otherstate" class="customer_db"></div>
        </div>
        <div class="f-l">
            <button id="softPhoneBarKick" type="button" class="btn btn-primary margin-r5" style="display:none"> 签出 </button>
            <button id="softPhoneBarPick" type="button" class="btn btn-primary margin-r5" style="display:none"> 签入 </button>
        </div>
        <div class="f-l margin-t5" id="softWaitCountTotalDiv" style="display:none">
            排队数：<span id="softWaitCountTotal">0 </span>
        </div>
        <!--以下代码仅使用webrtc时，需要引入-->
        <div id="AcceptBellingEnable" style="display:none;" onclick="holly.webRtc.processWebRTCButton('accept')">
            <img src="http://a6.7x24cc.com/softPhone/img/webrtc-incoming.png" alt="" style="width: 13px;vertical-align: -2px;background: #0066cc">
        </div>
        <div id="RefuseBellingEnable" style="display:none;margin-left: 5px" onclick="holly.webRtc.processWebRTCButton('reject')">
            <img src="http://a6.7x24cc.com/softPhone/img/hangup.png" alt="" style="vertical-align: -2px">
        </div>
        <!--webrtc拨号盘，可选-->
        <div class="btn-group-dial" id="DialPlateBtnEnable" style="margin-top: 2px; display:none">
            <button id="" type="button" class="btn btn-primary" onclick="holly.webRtc.showNum()">拨号盘</button>
            <!--拨号盘-->
            <div class="dial-wrap" id="dial_plate" style="display:none;">
                <div class="dial-phone-num">
                    <span class="pushed1" onclick="holly.webRtc.processWebRTCButton('dtmf', 1)">1</span>
                    <span class="pushed2" onclick="holly.webRtc.processWebRTCButton('dtmf', 2)">2</span>
                    <span class="pushed3" onclick="holly.webRtc.processWebRTCButton('dtmf', 3)">3</span>
                    <span class="pushed4" onclick="holly.webRtc.processWebRTCButton('dtmf', 4)">4</span>
                    <span class="pushed5" onclick="holly.webRtc.processWebRTCButton('dtmf', 5)">5</span>
                    <span class="pushed6" onclick="holly.webRtc.processWebRTCButton('dtmf', 6)">6</span>
                    <span class="pushed7" onclick="holly.webRtc.processWebRTCButton('dtmf', 7)">7</span>
                    <span class="pushed8" onclick="holly.webRtc.processWebRTCButton('dtmf', 8)">8</span>
                    <span class="pushed9" onclick="holly.webRtc.processWebRTCButton('dtmf', 9)">9</span>
                    <span class="" onclick="holly.webRtc.processWebRTCButton('dtmf', '*')">*</span>
                    <span class="pushed0" onclick="holly.webRtc.processWebRTCButton('dtmf', 0)">0</span>
                    <span class="" onclick="holly.webRtc.processWebRTCButton('dtmf', '#')">#</span>
                </div>
            </div>
        </div>
        <!--拨号盘结束-->
        <!--webrtc结束-->
    </div>
    <div id="softphone-bar-bgdiv" class="softphone-transfer-bg-div"></div>
    <div id="softphone_consult" class="softphone-transfer-div"></div>
    <div id="softphone_transfer" class="softphone-transfer-div"></div>
    <div id="icc_event"></div>
</div>

<script type="text/javascript" src="https://a6.7x24cc.com/softPhone/javascripts/jquery-1.7.2.min.js"></script>
<script type="text/javascript" src="https://a6.7x24cc.com/softPhone/javascripts/md5.min.js"></script>
<script type="text/javascript" src="https://a6.7x24cc.com/softPhone/javascripts/phone.min.js"></script>
<script type="text/javascript" src="https://a6.7x24cc.com/softPhone/javascripts/aes.js"></script>
<script type="text/javascript" src="https://a6.7x24cc.com/softPhone/javascripts/sip.js"></script>
<link rel="stylesheet" type="text/css" href="https://a6.7x24cc.com/softPhone/stylesheets/global.css">

<!-- 电话条弹窗样式 -->
<style>
    /* 覆盖云客服CSS中阻止滚动的样式 */
    body {
        overflow-y: auto !important;
        overflow-x: hidden !important;
    }

    /* 电话条弹窗样式 */
    #softphone-bar {
        position: fixed !important;
        top: 20% !important;
        right: 20px !important;
        width: 400px !important;
        max-width: calc(100vw - 40px) !important;
        z-index: 9999 !important;
        background-color: #ffffff !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 8px !important;
        padding: 15px !important;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
        font-size: 12px !important;
        max-height: 60vh !important;
        overflow-y: auto !important;
    }

    /* 电话条标题栏 */
    .phone-bar-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 10px !important;
        padding-bottom: 8px !important;
        border-bottom: 1px solid #f0f0f0 !important;
        cursor: move !important;
        user-select: none !important;
    }

    .phone-bar-title {
        font-weight: bold !important;
        font-size: 14px !important;
        color: #333 !important;
    }

    .phone-bar-controls {
        display: flex !important;
        gap: 5px !important;
    }

    .phone-bar-minimize,
    .phone-bar-close {
        width: 20px !important;
        height: 20px !important;
        border: none !important;
        border-radius: 50% !important;
        background-color: #f5f5f5 !important;
        color: #666 !important;
        font-size: 14px !important;
        line-height: 1 !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: all 0.2s ease !important;
    }

    .phone-bar-minimize:hover {
        background-color: #e0e0e0 !important;
        color: #333 !important;
    }

    .phone-bar-close:hover {
        background-color: #ff4757 !important;
        color: #fff !important;
    }

    /* 电话条内部布局优化 */
    #softphone-bar .btn-group {
        margin: 5px 5px 5px 0;
        display: inline-block;
    }

    #softphone-bar .btn {
        font-size: 11px !important;
        padding: 3px 8px !important;
        margin: 2px !important;
    }

    #softphone-bar .state_group {
        margin: 5px 0;
        display: inline-block;
        font-size: 11px;
    }

    #softphone-bar .f-l {
        float: none !important;
        display: inline-block;
        margin: 2px 5px 2px 0;
    }

    #softphone-bar #dialout_input {
        width: 120px !important;
        height: 24px !important;
        font-size: 11px !important;
        margin: 2px !important;
    }

    /* 拖拽功能 */
    #softphone-bar {
        cursor: move;
    }

    #softphone-bar .btn,
    #softphone-bar input,
    #softphone-bar select {
        cursor: pointer;
    }

    /* 确保弹窗层级正确 */
    .softphone-transfer-div,
    .softphone-consult-div {
        z-index: 10000 !important;
    }

    .softphone-transfer-bg-div {
        z-index: 9998 !important;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        #softphone-bar {
            width: calc(100vw - 20px) !important;
            right: 10px !important;
            left: 10px !important;
            top: 10% !important;
        }
    }

    /* 最小化状态 */
    #softphone-bar.minimized {
        height: 50px !important;
        overflow: hidden !important;
    }

    #softphone-bar.minimized .phone-bar-header {
        margin-bottom: 0 !important;
        border-bottom: none !important;
    }

    #softphone-bar.minimized #phone_bar {
        display: none !important;
    }

    #softphone-bar.minimized .phone-bar-minimize {
        transform: rotate(180deg) !important;
    }
</style>
<script>
    $(function () {
        $('#{{\Illuminate\Support\Facades\Input::get('type')}}').attr("selected", true);
        $('.img').click(
            function () {
                let src = $(this).attr('src');
                if (src) {
                    let html = '<div class="message"><img style="max-width:900px;max-height:900px;" src="' + src + '"><//div>'
                    $.fancybox.open(html);
                }
            }
        );
    });
    // 拨打电话
    $('#call').click(function (){
        var phoneNum = $('#phoneNumber').val()
        var billID = $('#bill_id').val()
        var ykf_account = $('#ykf_account').val()
        var ykf_password = $('#ykf_password').val()
        interfaceData = {
            bill_id:billID
        }

        // 显示电话条弹窗
        $('#softphone-bar').show();
        initPhoneBarDrag();

        holly.loginPhoneBar(ykf_account,ykf_password,"sip")
    });

    // 初始化电话条拖拽功能
    function initPhoneBarDrag() {
        var $phoneBar = $('#softphone-bar');
        var $header = $('.phone-bar-header');
        var isDragging = false;
        var startX, startY, startLeft, startTop;

        // 鼠标按下事件（只在标题栏区域）
        $header.on('mousedown', function(e) {
            // 如果点击的是按钮，不启动拖拽
            if ($(e.target).is('button, .phone-bar-minimize, .phone-bar-close')) {
                return;
            }

            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            startLeft = parseInt($phoneBar.css('left')) || 0;
            startTop = parseInt($phoneBar.css('top')) || 0;

            $header.css('cursor', 'grabbing');
            e.preventDefault();
        });

        // 鼠标移动事件
        $(document).on('mousemove', function(e) {
            if (!isDragging) return;

            var deltaX = e.clientX - startX;
            var deltaY = e.clientY - startY;
            var newLeft = startLeft + deltaX;
            var newTop = startTop + deltaY;

            // 限制在窗口范围内
            var maxLeft = $(window).width() - $phoneBar.outerWidth();
            var maxTop = $(window).height() - $phoneBar.outerHeight();

            newLeft = Math.max(0, Math.min(newLeft, maxLeft));
            newTop = Math.max(0, Math.min(newTop, maxTop));

            $phoneBar.css({
                left: newLeft + 'px',
                top: newTop + 'px',
                right: 'auto'
            });
        });

        // 鼠标释放事件
        $(document).on('mouseup', function() {
            if (isDragging) {
                isDragging = false;
                $header.css('cursor', 'move');
            }
        });
    }

    // 关闭电话条弹窗
    $(document).on('click', '.phone-bar-close', function(e) {
        e.stopPropagation();
        $('#softphone-bar').hide();
        // 如果有签出功能，也执行签出
        if (typeof holly !== 'undefined' && holly.logout) {
            holly.logout();
        }
    });

    // 最小化/还原电话条
    $(document).on('click', '.phone-bar-minimize', function(e) {
        e.stopPropagation();
        $('#softphone-bar').toggleClass('minimized');
    });

    // 双击标题栏最小化/还原
    $(document).on('dblclick', '.phone-bar-header', function(e) {
        $('#softphone-bar').toggleClass('minimized');
    });

    // 监听电话条内的签出按钮
    $(document).on('click', '#softPhoneBarKick', function() {
        setTimeout(function() {
            $('#softphone-bar').hide();
        }, 500);
    });

    // 页面加载完成后的初始化
    $(document).ready(function() {
        // 确保电话条初始状态为隐藏
        $('#softphone-bar').hide();

        // 如果电话条已经显示，初始化拖拽功能
        setTimeout(function() {
            if ($('#softphone-bar').is(':visible')) {
                initPhoneBarDrag();
            }
        }, 1000);
    });

    // 拨打电话
    $('#call123').click(function () {
        layer.confirm('你确定拨打电话: ' + $('#phoneNumber').val() + '吗?', {
            btn: ['确认', '取消']
        }, function () {

            var ykf_account = $('#ykf_account').val()
            var ykf_password = $('#ykf_password').val()
            layer.open({
                type: 2,
                title: '电话外呼',
                // shadeClose: true,
                shade: 0,
                area: ['400px', '18%'],
                // maxmin: true,
                content: '/edb_bar/edb1112/edb/html/main.html?loginType=sip&agentStatus=0&loginName=' + ykf_account + '&password=' + encodeURIComponent(ykf_password)
                // content: '/edb_bar/edb1112/edb/html/main.html?loginName=8000@dusl&password='+ encodeURIComponent('1qaz!DUSL8000') +'&loginType=sip&agentStatus=0&callNumber=' + $('#phoneNumber').val()
            });
            layer.msg('正在登录云客服...');

            setTimeout(function () {

                $.ajax({
                    url: '/admin/repair_check/call',
                    data: {
                        phone: $('#phoneNumber').val(),
                        bill_id: $('#bill_id').val(),
                    },
                    dataType: 'json',
                    type: 'get',
                    sync: false,
                    success: function (res) {

                        if (res.Succeed == true) {
                            layer.msg('电话接通中,请稍后...');


                        } else {
                            layer.msg('拨通失败,请核查');
                        }
                        console.log(res)
                    }
                })

            }, 2000)

        }, function () {
        });
    })

    // 发信息
    $('#sendMsgBtn').click(function () {

        layer.confirm('是否对联系人: ' + $('#phoneNumber').val() + '发送短信通知?', {
            btn: ['是', '否']
        }, function () {
            $.ajax({
                url: '/admin/repair_check/sendMsg',
                data: {
                    phone: $('#phoneNumber').val(),
                },
                dataType: 'json',
                type: 'get',
                success: function (res) {
                    console.log(res)
                    layer.msg(res.info);
                }
            })
        }, function () {
        });
    })

    // 维修时间打卡
    $('#save_repair_record_time').click(function () {

        layer.confirm('是否开始维修?', {
            btn: ['是', '否']
        }, function () {
            $.ajax({
                url: '/admin/repair_check/saveRecordTime',
                data: {
                    sn: $('#bill_id').val(),
                    type: 'repair_time'
                },
                dataType: 'json',
                type: 'get',
                success: function (res) {
                    window.location.reload()
                }
            })
        }, function () {
        });
    })


    // 维修知会通话记录
    $('#openContent').click(function () {

        $.fancybox.open($('#phoneRecord'));
    })

    // 客服知会通话记录
    $('#openContent1').click(function () {
        $.fancybox.open($('#phoneRecord1'));
    })

    // 填写客服备注
    $('#edit_is_tell_400').click(function () {
        $.fancybox.open($('#edit_is_tell_400_div'));
    })

    // 填写维修知会
    $('#edit_is_tell_repair').click(function () {
        $.fancybox.open($('#edit_is_tell_repair_div'));
    })

    // 变更状态为已收货,已知会
    $('#is_tell_400_save').click(function () {
        sn = $('#bill_id').val();
        order_remark = $('#order_remark').val();
        status = $("input[name='status']:checked").val();

        if (!order_remark) {
            layer.msg('备注不能为空');
            return false;
        }

        if (status == 1) {

            $.ajax({
                url: '/admin/customer_service_manage/setOrderRemark',
                data: {
                    sn: sn,
                    order_remark: order_remark,
                },
                dataType: 'json',
                type: 'get',
                success: function (res) {
                    layer.msg(res.info)
                    if (res.status == 1) {
                        window.location.reload()
                    }
                }
            })
        } else {
            $.ajax({
                url: '/admin/customer_service_manage/set400Remark',
                data: {
                    sn: sn,
                    remark: order_remark
                },
                type: 'get',
                dataType: 'json',
                success: function (res) {
                    layer.msg(res.info);
                    if (res.status == 1) {
                        window.location.reload()
                    }
                },

            })
        }
    })

    // 变更状态为未检测已知会/已检测已知会
    $('#is_tell_repair_save').click(function () {
        sn = $('#bill_id').val();
        order_remark = $('#order_remark_repair').val();
        status = $("input[name='repair_status']:checked").val();

        if (!order_remark) {
            layer.msg('备注不能为空');
            return false;
        }
        if (status == 1) {
            $.ajax({
                url: '/admin/repair_check/setOrderRemark',
                data: {
                    sn: sn,
                    order_remark: order_remark,
                },
                dataType: 'json',
                type: 'get',
                success: function (res) {
                    layer.msg(res.info)
                    if (res.status == 1) {
                        window.location.reload()
                    }
                }
            })
        } else {
            $.ajax({
                url: '/admin/repair_check/setRepairRemark',
                data: {
                    sn: sn,
                    remark: order_remark
                },
                type: 'get',
                dataType: 'json',
                success: function (res) {
                    layer.msg(res.info);
                    if (res.status == 1) {
                        window.location.reload()
                    }
                },
            })
        }

    })

    // 单独添加客服备注
    $('#400Remark').click(function () {
        $.fancybox.open($('#400RemarkDiv'));
    })

    $("input[name='remarkRadio400']").change(function () {
        $('#remarkTextarea400').val($("input[name='remarkRadio400']:checked").val());
    })

    $("input[name='remarkRadio400s']").change(function () {
        $('#order_remark').val($("input[name='remarkRadio400s']:checked").val());
    })

    $('#saveRemark400').click(function () {
        remark = $('#remarkTextarea400').val()
        if (!remark) {
            layer.msg('备注不能为空');
            return false;
        }
        $.ajax({
            url: '/admin/customer_service_manage/set400Remark',
            data: {
                sn: $('#bill_id').val(),
                remark: remark
            },
            type: 'get',
            dataType: 'json',
            success: function (res) {
                layer.msg(res.info);
                if (res.status == 1) {
                    var newTbodyHtml = '<tr>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.created_at + '<\/td>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.status_name + '<\/td>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.name + '<\/td>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.remark + '<\/td>' +
                        '<\/tr>';

                    $('#tbody400').append(newTbodyHtml);

                    setTimeout(function () {
                        window.location.reload()
                    }, 1000)
                }
            },
            error: function (res) {
                console.log('error');
            }
        })
    })

    // 单独添加维修知会备注
    $('#repairRemark').click(function () {
        $.fancybox.open($('#RepairRemarkDiv'));
    })

    $("input[name='remarkRadioRepair']").change(function () {
        $('#remarkTextareaRepair').val($("input[name='remarkRadioRepair']:checked").val());
    })

    $("input[name='remarkRadioRepairs']").change(function () {
        $('#order_remark_repair').val($("input[name='remarkRadioRepairs']:checked").val());
    })

    $('#saveRemarkRepair').click(function () {
        remark = $('#remarkTextareaRepair').val()
        if (!remark) {
            layer.msg('备注不能为空');
            return false;
        }
        $.ajax({
            url: '/admin/repair_check/setRepairRemark',
            data: {
                sn: $('#bill_id').val(),
                remark: remark
            },
            type: 'get',
            dataType: 'json',
            success: function (res) {
                layer.msg(res.info);
                if (res.status == 1) {
                    var newTbodyHtml = '<tr>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.created_at + '<\/td>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.status_name + '<\/td>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.name + '<\/td>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.remark + '<\/td>' +
                        '<\/tr>';

                    $('#tbodyRepair').append(newTbodyHtml);

                    setTimeout(function () {
                        window.location.reload()
                    }, 1000)
                }
            },
            error: function (res) {
                console.log('error');
            }
        })
    })

    var hollyglobal = {
        loginSuccessCallback: function (peer) { // 软电话条登录成功回调函数
            console.log(peer);
        },
        loginFailureCallback: function (peer) { // 软电话条登录失败回调函数
            console.log(peer);
        },
        ringEvent: function (peer) { // 响铃回调函数，可用于实现弹屏。参数请参考“软电话条-》事件API-》坐席振铃事件”
            console.log(peer);
        },
        talkingEvent: function (peer) { // 接通事件回调函数。参数请参考“软电话条-》事件API-》坐席接通事件”
            console.log(peer);
        },
        hangupEvent: function (peer) { // 挂机事件回调函数。参数请参考“软电话条-》事件API-》坐席挂断事件”
            console.log(peer);
        },
        queueWaitCountEvent: function (peer) { // 技能组排队数事件回调函数。参数请参考“软电话条-》事件API-》技能组排队数事件”
            console.dir(peer);
        },
        setBusyEvent: function(peer){ // 坐席切换状态回调
        },
        queueInfo: function(peer){ // 技能组发生变化回调
        },
        registered: function(peer){ // 电话条注册成功、通话功能可正常使用回调
        },
        isDisplayInvestigate: false, // 是否开启转满意功能
        isDisplayConsult: false, // 是否开启咨询功能
        isDisplayTransfer: false, // 是否开启转接
        isDisplayValidate: true, // 是否开启转验证
        isHiddenNumber: false, // 是否开启隐藏号码功能
        listenSelfEvent: 2,//开启监控权限时，需要去掉此参数
        monitor: 0, //是否开启监控权限，1表示开启、不配置或者其他值表示不开启，开启后将会收到所有的坐席事件并处理
        loginBusyType: "0", //坐席登录后状态。0是空闲，1是忙碌，2是小休，注：该参数不传的情况下，默认为0
        videoEnable: false //是否对接视频, true为开启视频对接
    };

</script>
