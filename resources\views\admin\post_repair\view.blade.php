@if(!empty($ext_param))
    <style>
        .content-wrapper {
            margin: 0;
            padding: 0 !important;
        }

        .main-header {
            display: none;
        }

        .main-sidebar {
            display: none;
        }

        .main-footer {
            display: none;
        }
    </style>
@endif

<div class="btn-group pull-right" style="margin-right: 10px">
    <a href="javascript:history.back(-1)" class="btn btn-sm btn-default">
        <i class="fa fa-list "></i>&nbsp;返回（返回后如果进入检测页面请按F5刷新）
    </a>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                {{--<h3 class="box-title">查询</h3>--}}
            </div>
            <div class="box-body" style="min-height:650px">
                <div class="col-md-3">
                </div>
                <div class="col-md-6" style="float: left">
                    <table class="table">
                        <caption align="center" style="text-align: center; font-size:20px;">用户信息</caption>
                        @if($order_old_address)
                            <tr>
                                <th align="center" width="33%"><span style="color:red">旧联系人信息:</span></th>
                                <td>
                                    {{$order_old_address->name}}<br>
                                    {{$order_old_address->phone}}<br>
                                    {{$order_old_address->province}} {{$order_old_address->city}} {{$order_old_address->district}} {{$order_old_address->address}}
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <th align="center" width="33%">寄修单号:</th>
                            <td>{{$order->sn}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">寄修人:</th>
                            <td>{{$order->name}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">联系方式:</th>
                            <td>
                                @if((!empty($is_ykf_400) || !empty($is_ykf_repair)) && $order->status >= 400)
                                    <a id="call" title="点击拨打: {{$order->phone}}"
                                       href="JavaScript:void(0);">{{$order->phone}}</a>
                                @else
                                    {{$order->phone}}
                                @endif
                            </td>
                            @if(!empty($is_ykf_400) && $order->status == 400)
                                <td>
                                    <button class="btn btn-sm btn-danger" id="edit_is_tell_400">
                                        <i class="fa fa-edit"></i>&nbsp;变更状态为已知会
                                    </button>
                                </td>
                            @endif

                            @if(!empty($is_ykf_repair) && ($order->status == 410 || $order->status == 490))
                                <td>
                                    <button class="btn btn-sm btn-danger" id="edit_is_tell_repair">
                                        <i class="fa fa-edit"></i>&nbsp;变更状态为已知会
                                    </button>
                                </td>
                            @endif
                        </tr>

                        <!-- 客服知会以及备注 -->
                        @if(!empty($orderRemarkInfo1))
                            <tr>
                                <th align="center" width="33%">客服知会:</th>
                                <td>
                                    <a href="JavaScript:void(0);" id="openContent1">
                                        {{$orderRemarkInfo1->status_name}}
                                    </a>
                                    <br>
                                    <span>{{$orderRemarkInfo1->name}}  {{$orderRemarkInfo1->created_at}}</span>
                                </td>
                            </tr>

                            <tr>
                                <th align="center" width="33%">客服备注:</th>
                                <td>
                                    <a href="JavaScript:void(0);" id="400Remark">
                                        {{$orderRemarkInfo1->remark}}
                                    </a>({{$orderRemarkInfo1->created_at}}
                                    )
                                </td>
                            </tr>
                        @endif

                        @if(!empty($allOrderRemarkInfo[0]))
                            <tr>
                                <th align="center" width="33%">维修知会:</th>
                                <td>
                                    <a href="JavaScript:void(0);"
                                       id="openContent">{{$allOrderRemarkInfo[0]['status_name']}}</a>
                                    <br>
                                    <span>{{$allOrderRemarkInfo[0]['name']}} {{$allOrderRemarkInfo[0]['created_at']}}</span>
                                </td>
                            <!--
                            @if($allOrderRemarkInfo[0]['status'] == 500)
                                <td>
                                    <button class="btn btn-sm btn-primary" id="sendMsgBtn" >发送支付推送信息</button>
                                </td>
                            @endif -->
                            </tr>
                            <tr>
                                <th align="center" width="33%">维修知会备注:</th>
                                <td>
                                    <a href="JavaScript:void(0);" id="repairRemark">
                                        {{$allOrderRemarkInfo[0]['remark']}}
                                    </a>({{$allOrderRemarkInfo[0]['created_at']}}
                                    )
                                </td>
                            </tr>
                        @endif

                        <tr>
                            <th align="center" width="33%">寄修地址:</th>
                            <td>{{$order->province}} {{$order->city}} {{$order->district}} {{$order->address}}</td>
                        </tr>

                        @if($agent_order)
                            <tr>
                                <th align="center" width="33%"><span style="color: burlywood;">代理人信息:</span></th>
                                <td>
                                    {{$agent_order->name}}<br>
                                    {{$agent_order->phone}}<br>
                                    {{$agent_order->province}} {{$agent_order->city}} {{$agent_order->district}} {{$agent_order->address}}
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <th align="center" width="33%">设备型号:</th>
                            <td>{{$order->model_name}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">S/N码:</th>
                            <td>{{$order->barcode}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%" style="color: red">更换主板序列号:</th>
                            <td>{{$order_extend->exchange_number}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">寄修方式:</th>
                            <td>{{$order->come_exp_type==1?'上门取件':($order->come_exp_type==2?'自主寄件':'无')}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%" style="color: red">附件信息:</th>
                            <td>{{$order->attachment}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">保修状态:</th>
                            <td>{{$order->in_period==1?'保修期内':($order->in_period==2?'保修期外':'无保修信息')}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">受损状态:</th>
                            {{--<td>{{$order->reason==1?'人为损坏':($order->reason==2?'元件损坏':'未知原因')}}</td>--}}
                            <td>{{\App\Models\Order::get_reason($order->reason)}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">客诉故障类型:</th>
                            <td>{{$order->damage}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">细节描述:</th>
                            <td>{{$order->description}}</td>
                        </tr>

                        <tr>
                            <th align="center" width="33%">保修材料:</th>
                            <td>
                        @if (is_array($order->period_file))
                            @foreach($order->period_file as $value)
                                <tr>
                                    <th></th>
                                    <td><img class="img" src="{{config('admin.upload.host').$value}}" height="200"></td>
                                </tr>
                                @endforeach
                                @endif
                                </td>
                                </tr>
                                <tr>
                                    <th align="center" width="33%">描述材料:</th>
                                    <td>
                                        @if (is_array($order->upload_file))
                                            @foreach($order->upload_file as $value)
                                                <img class="img" src="{{config('admin.upload.host').$value}}"
                                                     height="200">
                                            @endforeach
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th align="center" width="33%">视频:</th>
                                    <td>
                                        @if (is_array($order->video_file))
                                            @foreach($order->video_file as $value)
                                                <video class="video" src="{{config('admin.upload.host').$value}}"
                                                       controls="controls" height="200" width="400px"></video>
                                            @endforeach
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th align="left" width="33%">是否开具发票:</th>
                                    <td align="left" width="">{{$order->need_invoice?'是':'否'}}</td>
                                </tr>
                                <tr>
                                    <th align="left" width="33%">发票抬头:</th>
                                    <td align="left" width="">{{$order->invoice_title}}</td>
                                </tr>
                                <tr>
                                    <th align="left" width="33%">发票税号:</th>
                                    <td align="left" width="">{{$order->invoice_tax_id}}</td>
                                </tr>
                                <tr>
                                    <th align="left" width="33%">发票邮箱:</th>
                                    <td align="left" width="">{{$order->invoice_email}}</td>
                                </tr>
                    </table>
                    <table class="table">
                        <caption align="center" style="text-align: center; font-size:20px;">后台信息</caption>
                        <tbody>
                        <tr>
                            <th align="center" width="33%">审核备注:</th>
                            <td>{{$order->audit_opinion}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">订单优先级:</th>
                            <td>{!!$order_extend->order_priority_span!!}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">是否二次维修:</th>
                            <td>
                                <span>{{$order->repeat_order ? '是' : '否'}}</span>
                                <span>{{$order->repeat_order ? $order->repeat_order_count . '次' : ''}}</span>
                            </td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">该条码之前弃修订单数:</th>
                            @if($order->ar_repeat > 0)
                            <td style="color: red;">{{$order->ar_repeat}}</td>
                            @else
                            <td>{{$order->ar_repeat}}</td>
                            @endif
                        </tr>
                        <tr>
                            <th align="center" width="33%">是否终端寄修:</th>
                            <td>{{$order->is_agency? '是':'否'}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">签收配件:</th>
                            <td>{{$order->receive_case}}</td>
                        </tr>

                        @if(!empty($order_extend->sign_in_status_name))
                            <tr>
                                <th align="center" width="33%">签收状态:</th>
                                <td>{{$order_extend->sign_in_status_name}}</td>
                            </tr>
                        @endif

                        @if(!empty($order_extend->sign_in_pictures))
                            <tr>
                                <th align="center" width="33%">签收图片:</th>
                                {{--@php
                                    $order_extend->sign_in_pictures = json_decode($order_extend->sign_in_pictures , 1 );
                                @endphp--}}
                                <td>
                                    @foreach($order_extend->sign_in_pictures as $value)
                                        <img class="img" src="{{config('admin.upload.host').$value}}" height="200">
                                    @endforeach
                                </td>
                            </tr>
                        @endif

                        @if(!empty($order_extend->sign_in_videos))
                            <tr>
                                <th align="center" width="33%">签收视频:</th>
                                <td>
                                    @foreach($order_extend->sign_in_videos as $value)
                                        <video src="{{config('admin.upload.host').$value}}" height="200" controls>
                                    @endforeach
                                </td>
                            </tr>
                        @endif

                        <tr>
                            <th align="center" width="33%">维修备注:</th>
                            <td>{{$order->deal_remark}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">支付备注:</th>
                            @if($order_extend)
                                <td>{{$order_extend->pay_remark}}</td>
                            @endif
                        </tr>
                        <tr>
                            <th align="center" width="33%">后台备注:</th>
                            @if($order_extend)
                                <td>{{$order_extend->backstage_remark}}</td>
                            @endif
                        </tr>
                        <tr>
                            <th align="center" width="33%">维修图片:</th>
                            <td>
                                @if (is_array($order->repair_image))
                                    @foreach($order->repair_image as $value)
                                        @php
                                            // 获取文件扩展名
                                            $extension = pathinfo($value, PATHINFO_EXTENSION);
                                        @endphp

                                        @if (in_array($extension, ['jpg', 'jpeg', 'png']))
                                            <!-- 如果是图片格式 -->
                                                <img class="img" src="{{ config('admin.upload.host') . $value }}" height="200">
                                        @elseif (in_array($extension, ['mp4', 'avi']))
                                            <!-- 如果是视频格式 -->
                                                <video width="320" height="240" controls muted>
                                                    <source src="{{ config('admin.upload.host') . $value }}" type="video/{{ $extension }}">
                                                </video>
                                        @endif
                                    @endforeach
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">检测人:</th>
                            <td>{{$order->check_user? $order->check_user->name:''}}</td>
                        </tr>
                        <tr>
                            <th>自选配件:</th>
                        </tr>
                        @if (isset($pr_oa) && $pr_oa)
                            <tr>
                                <td align="left" width="25%">配件分类</td>
                                <td align="left" width="25%">配件名称</td>
                                <td align="left" width="25%">物料编码</td>
                                <td align="left" width="25%">购买数量</td>
                                <td align="left" width="25%">购买单价</td>
                                <td align="left" width="25%">合计</td>
                                <td align="left" width="25%">状态</td>
                            </tr>
                            @foreach($pr_oa as $oa)
                                <tr>
                                    <td>{{$oa->oac_name}}</td>
                                    <td>{{$oa->oa_name}}</td>
                                    <td>{{$oa->m_code}}</td>
                                    <td>{{$oa->count}}</td>
                                    <td>{{$oa->price}}</td>
                                    <td>{{$oa->price_sum}}</td>
                                    <td>{{\App\Models\PostRepairOptionalAccessory::STATUS[$oa->status]}}</td>
                                </tr>
                            @endforeach
                        @else
                            <td>无</td>
                        @endif
                        <tr>
                            <th align="left" width="">更换配件:</th>
                            {{--<td align="left" width="">{{$accessory}}</td>--}}
                        </tr>
                        @if ($pr_material)
                            <tr>
                                <td align="left" width="25%">配件</td>
                                <td align="left" width="25%">更换故障</td>
                                <td align="left" width="25%">价格（元）</td>
                                <td align="left" width="25%">数量</td>
                            </tr>
                            @foreach($pr_material as $p)
                                <tr>
                                    <td>{{$p->title}}</td>
                                    <td>{{$p->malfunction}}</td>
                                    <td>{{(property_exists($p, 'charge_type') && property_exists($p, 'price_first') && ($p->charge_type == 2))
                                            ? $p->price_first : $p->price
                                        }}</td>
                                    <td>{{$p->count}}</td>
                                </tr>
                            @endforeach
                        @else
                            <td>无</td>
                        @endif
                        <tr>
                            <th align="left" width="">更换物料:</th>
                            {{--<td align="left" width="">{{$accessory}}</td>--}}
                        </tr>
                        @if ($pr_used_material)
                            <tr>
                                <td align="left" width="">物料名称规格</td>
                                {{--<td align="left" width="">物料规格</td>--}}
                                <td align="left" width="">物料编码</td>
                                <td align="left" width="">旧编码</td>
                                <td align="left" width="">仓库</td>
                                <td align="left" width="">价格（元）</td>
                                <td align="left" width="">数量</td>
                                <td align="left" width="">是否收费</td>
                                <td align="left" width="">实收价格</td>
                            </tr>
                            @foreach($pr_used_material as $p)
                                <tr>
                                    <td>{{$p->name.$p->specification}}</td>
                                    {{--<td>{{$p->specification}}</td>--}}
                                    <td>{{$p->code}}</td>
                                    <td>{{$p->old_code}}</td>
                                    <td>{{$p->from == 1?'新':'旧'}}</td>
                                    <td>{{(property_exists($p, 'charge_type') && property_exists($p, 'price_first') && ($p->charge_type == 2))
                                            ? $p->price_first : $p->price
                                        }}</td>
                                    <td>{{$p->count}}</td>
                                    <td>{{\App\Models\Order::is_charge[$p->is_charge]}}</td>
                                    <td>
                                        @if (property_exists($p, 'price_in') && $p->price_in > 0)
                                            {{ $p->price_in }}
                                        @else
                                            {{ (property_exists($p, 'charge_type') && property_exists($p, 'price_first') && ($p->charge_type == 2))
                                                ? $p->price_first : $p->price }}
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            <td>无</td>
                        @endif
                        <tr>
                            <th align="center" width="33%">自选购配件费用:</th>
                            <td>{{$order->optional_accessory_cast}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">维修的配件费用:</th>
                            <td>{{$order->accessory_cast}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">弃修时配件费用:</th>
                            <td>{{$order->accessory_in_ar}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">快递费用:</th>
                            <td>{{$order->staff_cast}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">总费用:</th>
                            <td>{{$order->amount}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">维修时需支付金额:</th>
                            <td>{{$order->pay_amount}}</td>
                        </tr>
                        <tr>
                            <th align="center" width="33%">弃修时需支付金额:</th>
                            <td>{{$order->amount_in_ar}}</td>
                        </tr>

                        <tr>
                            <th align="center" width="33%">维修记录时间:</th>

                            @if(!empty($order_extend->repair_record_times))
                                <td>{{$order_extend->repair_record_times}}</td>
                            @endif

                            @if(!empty($can_record_repair_time))
                                <td>
                                    <button class="btn btn-sm btn-primary" id="save_repair_record_time">
                                        开始维修（添加维修记录时间）
                                    </button>
                                </td>
                            @endif

                        </tr>

                        </tbody>
                    </table>
                </div>
                <div class="col-md-3">
                </div>
                <div class="col-md-12">
                </div>
            </div>
            <div class="box-footer">
            </div>
        </div>
    </div>
</div>

<input hidden id="bill_id" type="text" value="{{$order->sn}}">
<input hidden id="phoneNumber" type="text" value="{{$order->phone}}">
@if(!empty($usersYkfInfo))
    <input hidden id="ykf_account" type="text" value="{{$usersYkfInfo->ykf_account}}">
    <input hidden id="ykf_password" type="text" value="{{$usersYkfInfo->ykf_password}}">
@endif
<!-- 变更状态为已收货已知会 -->
<div id="edit_is_tell_400_div" hidden class="box box-info">
    <div class="box-header with-border">
        客服知会
    </div>
    <div class="box-body" style="max-width:750px">

        <div class="form-group">
            <label class="control-label">客服知会</label>
            <input type="checkbox" class="status la_checkbox" name="status" value="1"/><label
                    class="control-label">已知会</label>
        </div>

        <div class="form-group">

            <label for="order_remark" class="control-label">客服备注</label>

            <div style="display:flex">
                <input style="margin:1%" name="remarkRadio400s" value="需要再次通知" type="radio"> 需要再次通知
                <input style="margin:1%" name="remarkRadio400s" value="已沟通未协商好" type="radio"> 已沟通未协商好
                <input style="margin:1%" name="remarkRadio400s" value="顾客有争议" type="radio"> 顾客有争议
                <input style="margin:1%" name="remarkRadio400s" value="终端有争议" type="radio"> 终端有争议
                <input style="margin:1%" name="remarkRadio400s" value="等待回复" type="radio"> 等待回复
                <input style="margin:1%" name="remarkRadio400s" value="已成功知会,分派订单" type="radio"> 已成功知会,分派订单
                <input style="margin:1%" name="remarkRadio400s" value="无其他异常,直接分派" type="radio"> 无其他异常,直接分派
            </div>

            <div class="input-group">
                <span class="input-group-addon"><i class="fa fa-pencil"></i></span>
                <input type="text" id="order_remark" name="order_remark" value="" class="form-control"/>
            </div>
        </div>

        <button class="btn btn-sm btn-primary" id="is_tell_400_save">提交</button>
    </div>
</div>

<!-- 变更状态为未检测已知会/已检测已知会 -->
<div id="edit_is_tell_repair_div" hidden class="box box-info">
    <div class="box-header with-border">
        维修知会
    </div>
    <div class="box-body" style="max-width:750px">

        <div class="form-group">
            <label class="control-label">维修知会</label>
            <input type="checkbox" class="status la_checkbox" name="repair_status" value="1"/><label
                    class="control-label">已知会</label>
        </div>

        <div class="form-group">
            <label for="order_remark_repair" class="control-label">知会备注</label>
            <div style="display:flex">
                <input style="margin:1%" name="remarkRadioRepairs" value="需要再次通知" type="radio"> 需要再次通知
                <input style="margin:1%" name="remarkRadioRepairs" value="已沟通未协商好" type="radio"> 已沟通未协商好
                <input style="margin:1%" name="remarkRadioRepairs" value="顾客有争议" type="radio"> 顾客有争议
                <input style="margin:1%" name="remarkRadioRepairs" value="终端有争议" type="radio"> 终端有争议
                <input style="margin:1%" name="remarkRadioRepairs" value="等待回复" type="radio"> 等待回复
                <input style="margin:1%" name="remarkRadioRepairs" value="未打电话" type="radio"> 未打电话
                <input style="margin:1%" name="remarkRadioRepairs" value="已打电话" type="radio"> 已打电话
            </div>
            <div class="input-group">
                <span class="input-group-addon"><i class="fa fa-pencil"></i></span>
                <input type="text" id="order_remark_repair" name="order_remark_repair" value="" class="form-control"/>
            </div>
        </div>

        <button class="btn btn-primary" id="is_tell_repair_save">提交</button>
    </div>
</div>

<!-- 通话记录 -->
<div id="phoneRecord1" hidden class="box box-info">
    <div class="box-header with-border">
        客服沟通
    </div>
    <div class="box-body" style="min-height:300px">
        <table class="table">
            <tr>
                <th style="text-align: center;">序号</th>
                <th style="text-align: center;">主叫号码</th>
                <th style="text-align: center;">被叫号码</th>
                <th style="text-align: center;">客服</th>
                <th style="text-align: center;">通话接通时间</th>
                <th style="text-align: center;">通话结束时间</th>
                <th style="text-align: center;">通话录音</th>
            </tr>

            @if(!empty($callLog1))
                @foreach($callLog1 as $key => $value)
                    <tr>
                        <td style="text-align: center;">{{$key+1}}</td>
                        <td style="text-align: center;">{{$value->call_no}}</td>
                        <td style="text-align: center;">{{$value->called_no}}</td>
                        <td style="text-align: center;">{{$value->name}}</td>
                        <td style="text-align: center;">{{$value->begin}}</td>
                        <td style="text-align: center;">{{$value->end}}</td>
                        <td style="text-align: center;">
                            @if(empty($value->record_file))
                                无录音文件
                            @endif

                            @if(!empty($value->record_file) && empty($value->sound_url))
                                <audio controls style="height: 20px;">
                                    <source src="{{$value->file_server}}/{{$value->record_file}}" type="audio/mpeg">
                                </audio>
                            @endif

                            @if(!empty($value->sound_url))
                                <audio controls style="height: 20px;">
                                    <source src="{{$value->sound_url}}" type="audio/mpeg">
                                </audio>
                            @endif
                        </td>
                    </tr>
                @endforeach
            @endif
        </table>

    </div>
</div>
<!-- 通话记录  end-->

<!-- 通话记录 -->
<div id="phoneRecord" hidden class="box box-info">
    <div class="box-header with-border">
        维修知会
    </div>
    <div class="box-body" style="min-height:300px">
        <table class="table">
            <tr>
                <th style="text-align: center;">序号</th>
                <th style="text-align: center;">主叫号码</th>
                <th style="text-align: center;">被叫号码</th>
                <th style="text-align: center;">客服</th>
                <th style="text-align: center;">通话接通时间</th>
                <th style="text-align: center;">通话结束时间</th>
                <th style="text-align: center;">通话录音</th>
            </tr>

            @if(!empty($callLog))
                @foreach($callLog as $key => $value)
                    <tr>
                        <td style="text-align: center;">{{$key+1}}</td>
                        <td style="text-align: center;">{{$value->call_no}}</td>
                        <td style="text-align: center;">{{$value->called_no}}</td>
                        <td style="text-align: center;">{{$value->name}}</td>
                        <td style="text-align: center;">{{$value->begin}}</td>
                        <td style="text-align: center;">{{$value->end}}</td>
                        <td style="text-align: center;">
                            @if(empty($value->record_file))
                                无录音文件
                            @endif

                            @if(!empty($value->record_file) && empty($value->sound_url))
                                <audio controls style="height: 20px;">
                                    <source src="{{$value->file_server}}/{{$value->record_file}}" type="audio/mpeg">
                                </audio>
                            @endif

                            @if(!empty($value->sound_url))
                                <audio controls style="height: 20px;">
                                    <source src="{{$value->sound_url}}" type="audio/mpeg">
                                </audio>
                            @endif
                        </td>
                    </tr>
                @endforeach
            @endif
        </table>
    </div>
</div>
<!-- 通话记录  end-->

<!-- 添加客服备注 -->
<div class="box box-info" hidden id="400RemarkDiv">
    <div class="box-header with-border">备注</div>
    <div class="box-body" style="min-height:300px;min-width:500px">
        <table class="table">
            <tr>
                <th style="text-align: center;">时间</th>
                <th style="text-align: center;">订单状态</th>
                <!-- <th style="text-align: center;" >联系状态</th> -->
                <th style="text-align: center;">操作人</th>
                <th style="text-align: center;">备注</th>
            </tr>
            <tbody id="tbody400">
            @if(!empty($allOrderRemarkInfo1))
                @foreach($allOrderRemarkInfo1 as $key => $value)
                    <tr>
                        <td style="text-align: center;">{{$value->created_at}}</td>
                        <td style="text-align: center;">{{$value->status_name}}</td>
                    <!-- <td style="text-align: center;">{{$value->connect_name}}</td> -->
                        <td style="text-align: center;">{{$value->name}}</td>
                        <td style="text-align: center;">{{$value->remark}}</td>
                    </tr>
                @endforeach
            @endif
            </tbody>
        </table>
    </div>

    <div style="display:flex">
        <input style="margin:1%" name="remarkRadio400" value="需要再次通知" type="radio"> 需要再次通知
        <input style="margin:1%" name="remarkRadio400" value="已沟通未协商好" type="radio"> 已沟通未协商好
        <input style="margin:1%" name="remarkRadio400" value="顾客有争议" type="radio"> 顾客有争议
        <input style="margin:1%" name="remarkRadio400" value="终端有争议" type="radio"> 终端有争议
        <input style="margin:1%" name="remarkRadio400" value="等待回复" type="radio"> 等待回复
    </div>
    <div style="display:flex">
        <textarea id="remarkTextarea400" placeholder="请输入备注,不超过100字" cols="70"></textarea>
        <a class="btn btn-sm btn-primary" type="button" id="saveRemark400">确定</a>
    </div>
</div>

<!-- 添加维修备注 -->
<div class="box box-info" hidden id="RepairRemarkDiv">
    <div class="box-header with-border">备注</div>
    <div class="box-body" style="min-height:300px;min-width:500px">
        <table class="table">
            <tr>
                <th style="text-align: center;">时间</th>
                <th style="text-align: center;">订单状态</th>
                <!-- <th style="text-align: center;" >联系状态</th> -->
                <th style="text-align: center;">操作人</th>
                <th style="text-align: center;">备注</th>
            </tr>
            <tbody id="tbodyRepair">

            @if(!empty($allOrderRemarkInfo))
                @foreach($allOrderRemarkInfo as $key => $value)
                    <tr>
                        <td style="text-align: center;">{{$value->created_at}}</td>
                        <td style="text-align: center;">{{$value->status_name}}</td>
                    <!-- <td style="text-align: center;">{{$value->connect_name}}</td> -->
                        <td style="text-align: center;">{{$value->name}}</td>
                        <td style="text-align: center;">{{$value->remark}}</td>
                    </tr>
                @endforeach
            @endif
            </tbody>
        </table>
    </div>

    <div style="display:flex">
        <input style="margin:1%" name="remarkRadioRepair" value="需要再次通知" type="radio"> 需要再次通知
        <input style="margin:1%" name="remarkRadioRepair" value="已沟通未协商好" type="radio"> 已沟通未协商好
        <input style="margin:1%" name="remarkRadioRepair" value="顾客有争议" type="radio"> 顾客有争议
        <input style="margin:1%" name="remarkRadioRepair" value="终端有争议" type="radio"> 终端有争议
        <input style="margin:1%" name="remarkRadioRepair" value="等待回复" type="radio"> 等待回复
        <input style="margin:1%" name="remarkRadioRepair" value="未打电话" type="radio"> 未打电话
        <input style="margin:1%" name="remarkRadioRepair" value="已打电话" type="radio"> 已打电话
    </div>
    <div style="display:flex">
        <textarea id="remarkTextareaRepair" placeholder="请输入备注,不超过100字" cols="90"></textarea>
        <a class="btn btn-primary" type="button" id="saveRemarkRepair">确定</a>
    </div>
</div>

<div id="softphone-bar" hidden class="box box-info">
    <!-- 电话条标题栏 -->
    <div class="phone-bar-header">
        <span class="phone-bar-title">云客服电话</span>
        <div class="phone-bar-controls">
            <button class="phone-bar-minimize" title="最小化">−</button>
            <button class="phone-bar-close" title="关闭">×</button>
        </div>
    </div>
    <div id="phone_bar" class="clearFix">
        <div class="btn-group f-l">
            <button id="HoldEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.hold()"> 保持 </button >
            <button id="HoldGetEnable" type="button" class="btn btn-primary margin-r5" onclick="holly.unHold()" style="display:none"> 恢复 </button>
            <button id="MuteEnable" type="button" class="btn btn-primary margin-r5" onclick="holly.mute()" style="display:none"> 静音 </button>
            <button id="UnMuteEnable" type="button" class="btn btn-primary margin-r5" onclick="holly.unMute()" style="display:none"> 恢复 </button>
            <button id="TransferEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.openTransferOrConsult('softphone_transfer')"> 转接 </button>
            <button id="ConsultTransferEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.transfer('9123456', 'number')"> 转接 </button>
            <button id="ConsultEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.openTransferOrConsult('softphone_consult')"> 咨询 </button>
            <button id="InvestigateEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.investigate()"> 邀评 </button>
            <button id="ValidateEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.validate()"> 转验证 </button>
            <button id="IVRMenuEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.openToMenuPage('softphone_transfer')"> 转菜单 </button>
            <button id="SmsChatPortalEnable" type="button" class="btn btn-primary margin-r5" style="display:none" onclick="holly.openChatPortalPage('softphone_transfer')"> 转在线 </button>
            <button id="ThreeWayCallEnable" class="btn btn-primary margin-r5" style="display:none"> 三方 </button>
            <button id="ConsultThreeWayCallEnable" type="button" class="btn btn-primary margin-r5" style="display: none" onclick="holly.threeWayCall('9123456')"> 三方 </button>
            <button id="StopConsultEnable" type="button" class="btn btn-danger margin-r5" style="display:none" onclick="holly.stopConsult()"> 结束咨询 </button>
            <button id="CallVideoEnable" type="button" class="btn btn-danger margin-r5" style="display:none" onclick="holly.openVideoRoom()"> 视频 </button>
            <button id="HangupEnable" type="button" class="btn btn-danger margin-r5" onclick="holly.hangup()" style="display:none"> 挂机 </button>
        </div>
        <div class="f-l">
            <input id="dialout_input" class="span2" type="text" value="" placeholder="请输入电话号码" style="display: none;" onkeydown="if(event.keyCode==13){holly.dialout($('#dialout_input').val())}">
            <button id="DialEnable" class="btn btn-primary margin-r5" type="button" onclick="holly.dialout($('#dialout_input').val())" style="display:none"> 呼叫 </button>
        </div>
        <div class="state_group f-l clearFix">
            <div id="softphone_phonestate" class="f-l state">未签入</div>
            <div id="softphone_dropdown" class="f-l state_dropdown"><b id="softphone_dropdown_caret" class="caret"></b> </div>
            <div id="softphone_timer" class="f-l state_time">00:00:00</div>
            <div id="softphone_otherstate" class="customer_db"></div>
        </div>
        <div class="f-l">
            <button id="softPhoneBarKick" type="button" class="btn btn-primary margin-r5" style="display:none"> 签出 </button>
            <button id="softPhoneBarPick" type="button" class="btn btn-primary margin-r5" style="display:none"> 签入 </button>
        </div>
        <div class="f-l margin-t5" id="softWaitCountTotalDiv" style="display:none">
            排队数：<span id="softWaitCountTotal">0 </span>
        </div>
        <!--以下代码仅使用webrtc时，需要引入-->
        <div id="AcceptBellingEnable" style="display:none;" onclick="holly.webRtc.processWebRTCButton('accept')">
            <img src="http://a6.7x24cc.com/softPhone/img/webrtc-incoming.png" alt="" style="width: 13px;vertical-align: -2px;background: #0066cc">
        </div>
        <div id="RefuseBellingEnable" style="display:none;margin-left: 5px" onclick="holly.webRtc.processWebRTCButton('reject')">
            <img src="http://a6.7x24cc.com/softPhone/img/hangup.png" alt="" style="vertical-align: -2px">
        </div>
        <!--webrtc拨号盘，可选-->
        <div class="btn-group-dial" id="DialPlateBtnEnable" style="margin-top: 2px; display:none">
            <button id="" type="button" class="btn btn-primary" onclick="holly.webRtc.showNum()">拨号盘</button>
            <!--拨号盘-->
            <div class="dial-wrap" id="dial_plate" style="display:none;">
                <div class="dial-phone-num">
                    <span class="pushed1" onclick="holly.webRtc.processWebRTCButton('dtmf', 1)">1</span>
                    <span class="pushed2" onclick="holly.webRtc.processWebRTCButton('dtmf', 2)">2</span>
                    <span class="pushed3" onclick="holly.webRtc.processWebRTCButton('dtmf', 3)">3</span>
                    <span class="pushed4" onclick="holly.webRtc.processWebRTCButton('dtmf', 4)">4</span>
                    <span class="pushed5" onclick="holly.webRtc.processWebRTCButton('dtmf', 5)">5</span>
                    <span class="pushed6" onclick="holly.webRtc.processWebRTCButton('dtmf', 6)">6</span>
                    <span class="pushed7" onclick="holly.webRtc.processWebRTCButton('dtmf', 7)">7</span>
                    <span class="pushed8" onclick="holly.webRtc.processWebRTCButton('dtmf', 8)">8</span>
                    <span class="pushed9" onclick="holly.webRtc.processWebRTCButton('dtmf', 9)">9</span>
                    <span class="" onclick="holly.webRtc.processWebRTCButton('dtmf', '*')">*</span>
                    <span class="pushed0" onclick="holly.webRtc.processWebRTCButton('dtmf', 0)">0</span>
                    <span class="" onclick="holly.webRtc.processWebRTCButton('dtmf', '#')">#</span>
                </div>
            </div>
        </div>
        <!--拨号盘结束-->
        <!--webrtc结束-->
    </div>
    <div id="softphone-bar-bgdiv" class="softphone-transfer-bg-div"></div>
    <div id="softphone_consult" class="softphone-transfer-div"></div>
    <div id="softphone_transfer" class="softphone-transfer-div"></div>
    <div id="icc_event"></div>
</div>

<script type="text/javascript" src="https://a6.7x24cc.com/softPhone/javascripts/jquery-1.7.2.min.js"></script>
<script type="text/javascript" src="https://a6.7x24cc.com/softPhone/javascripts/md5.min.js"></script>
<script type="text/javascript" src="https://a6.7x24cc.com/softPhone/javascripts/phone.min.js"></script>
<script type="text/javascript" src="https://a6.7x24cc.com/softPhone/javascripts/aes.js"></script>
<script type="text/javascript" src="https://a6.7x24cc.com/softPhone/javascripts/sip.js"></script>
<link rel="stylesheet" type="text/css" href="https://a6.7x24cc.com/softPhone/stylesheets/global.css">

<!-- 电话条弹窗样式 -->
<style>
    /* 覆盖云客服CSS中阻止滚动的样式，但不影响侧边栏 */
    body:not(.sidebar-open) {
        overflow-y: auto !important;
        overflow-x: hidden !important;
    }

    /* 确保侧边栏可以正常工作 */
    body.sidebar-open {
        overflow: visible !important;
    }

    /* 修复可能的层级问题 */
    .main-sidebar {
        z-index: 1040 !important;
    }

    .sidebar-toggle {
        z-index: 1041 !important;
    }

    /* 确保内容区域不会阻挡侧边栏 */
    .content-wrapper {
        position: relative !important;
        z-index: 1000 !important;
    }

    /* 强制覆盖云客服CSS对body的影响 */
    body.holly {
        overflow-y: auto !important;
        overflow-x: hidden !important;
    }

    /* 确保AdminLTE的侧边栏样式不被覆盖 */
    .main-sidebar .sidebar {
        overflow-y: auto !important;
    }

    /* 修复可能的点击事件问题 */
    .sidebar-toggle {
        pointer-events: auto !important;
        position: relative !important;
    }

    /* 确保侧边栏背景遮罩正常工作 */
    .sidebar-open .content-wrapper,
    .sidebar-open .right-side,
    .sidebar-open .main-footer {
        -webkit-transform: translate(230px, 0) !important;
        -ms-transform: translate(230px, 0) !important;
        -o-transform: translate(230px, 0) !important;
        transform: translate(230px, 0) !important;
    }

    /* 电话条弹窗样式 */
    #softphone-bar {
        position: fixed !important;
        top: 20% !important;
        right: 20px !important;
        width: 420px !important;
        min-width: 380px !important;
        max-width: calc(100vw - 40px) !important;
        z-index: 9999 !important;
        background-color: #ffffff !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 8px !important;
        padding: 15px !important;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
        font-size: 12px !important;
        max-height: 70vh !important;
        overflow: hidden !important;
        resize: none !important;
    }

    /* 电话条内容区域 */
    #softphone-bar #phone_bar {
        max-height: calc(70vh - 80px) !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
        padding-right: 5px !important;
    }

    /* 自定义滚动条 */
    #softphone-bar #phone_bar::-webkit-scrollbar {
        width: 6px !important;
    }

    #softphone-bar #phone_bar::-webkit-scrollbar-track {
        background: #f1f1f1 !important;
        border-radius: 3px !important;
    }

    #softphone-bar #phone_bar::-webkit-scrollbar-thumb {
        background: #c1c1c1 !important;
        border-radius: 3px !important;
    }

    #softphone-bar #phone_bar::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8 !important;
    }

    /* 电话条标题栏 */
    .phone-bar-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 10px !important;
        padding-bottom: 8px !important;
        border-bottom: 1px solid #f0f0f0 !important;
        cursor: move !important;
        user-select: none !important;
    }

    .phone-bar-title {
        font-weight: bold !important;
        font-size: 14px !important;
        color: #333 !important;
    }

    .phone-bar-controls {
        display: flex !important;
        gap: 5px !important;
    }

    .phone-bar-minimize,
    .phone-bar-close {
        width: 20px !important;
        height: 20px !important;
        border: none !important;
        border-radius: 50% !important;
        background-color: #f5f5f5 !important;
        color: #666 !important;
        font-size: 14px !important;
        line-height: 1 !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: all 0.2s ease !important;
    }

    .phone-bar-minimize:hover {
        background-color: #e0e0e0 !important;
        color: #333 !important;
    }

    .phone-bar-close:hover {
        background-color: #ff4757 !important;
        color: #fff !important;
    }

    /* 电话条内部布局优化 */
    #softphone-bar .btn-group {
        margin: 3px 3px 3px 0 !important;
        display: inline-block !important;
        vertical-align: top !important;
    }

    #softphone-bar .btn {
        font-size: 10px !important;
        padding: 2px 6px !important;
        margin: 1px !important;
        line-height: 1.2 !important;
        border-radius: 3px !important;
        white-space: nowrap !important;
    }

    #softphone-bar .state_group {
        margin: 3px 5px 3px 0 !important;
        display: inline-block !important;
        font-size: 10px !important;
        vertical-align: top !important;
        max-width: 150px !important;
    }

    #softphone-bar .f-l {
        float: none !important;
        display: inline-block !important;
        margin: 2px 3px 2px 0 !important;
        vertical-align: top !important;
    }

    #softphone-bar #dialout_input {
        width: 110px !important;
        height: 22px !important;
        font-size: 10px !important;
        margin: 2px !important;
        padding: 2px 5px !important;
        border-radius: 3px !important;
        border: 1px solid #ddd !important;
    }

    /* 电话条内容区域布局 */
    #softphone-bar #phone_bar > div {
        margin-bottom: 5px !important;
    }

    #softphone-bar #phone_bar > div:last-child {
        margin-bottom: 0 !important;
    }

    /* 状态显示区域优化 */
    #softphone-bar .state {
        font-size: 10px !important;
        max-width: 80px !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
    }

    #softphone-bar .state_time {
        font-size: 10px !important;
        margin-top: 2px !important;
        padding-left: 5px !important;
    }

    /* 拖拽功能 */
    #softphone-bar {
        cursor: move;
    }

    #softphone-bar .btn,
    #softphone-bar input,
    #softphone-bar select {
        cursor: pointer;
    }

    /* 优化云客服的二次弹窗 */
    .softphone-transfer-div,
    .softphone-consult-div {
        z-index: 10000 !important;
        background-color: #ffffff !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 6px !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
        font-size: 11px !important;
        max-width: 300px !important;
    }

    .softphone-transfer-bg-div {
        z-index: 9998 !important;
        background-color: rgba(0,0,0,0.3) !important;
    }

    /* 隐藏不必要的云客服弹窗元素 */
    #softphone-bar .customer_db {
        max-width: 200px !important;
        font-size: 10px !important;
        border-radius: 4px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
    }

    #softphone-bar .customer_div {
        padding: 5px 10px !important;
        font-size: 10px !important;
        line-height: 1.3 !important;
        height: auto !important;
    }

    /* 拨号盘优化 */
    .dial-wrap {
        background-color: #ffffff !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 6px !important;
        box-shadow: 0 4px 15px rgba(0,0,0,0.15) !important;
        padding: 10px !important;
        width: 200px !important;
        left: -150px !important;
        z-index: 10002 !important;
        position: absolute !important;
        top: 100% !important;
        margin-top: 5px !important;
    }

    /* 拨号盘容器 */
    .dial-container {
        position: relative !important;
        z-index: 10002 !important;
    }

    /* 拨号盘背景遮罩 */
    .dial-bg {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background-color: rgba(0,0,0,0.3) !important;
        z-index: 10001 !important;
        display: none !important;
    }

    .dial-phone-num span {
        width: 40px !important;
        height: 40px !important;
        line-height: 40px !important;
        font-size: 16px !important;
        margin: 3px !important;
        border: 1px solid #ddd !important;
        color: #333 !important;
    }

    .dial-phone-num span:hover {
        background-color: #f0f0f0 !important;
        border-color: #007bff !important;
    }

    /* 确保拨号盘相关元素的层级和显示 */
    #softphone-bar .dial-wrap,
    #softphone-bar .dial-container,
    .dial-wrap,
    .dial-container {
        z-index: 10002 !important;
        position: absolute !important;
    }

    /* 拨号盘按钮样式 */
    .dial-phone-num {
        text-align: center !important;
        padding: 5px !important;
    }

    .dial-phone-num span {
        display: inline-block !important;
        text-align: center !important;
        cursor: pointer !important;
        user-select: none !important;
        background-color: #f8f9fa !important;
        border-radius: 50% !important;
        transition: all 0.2s ease !important;
    }

    .dial-phone-num span:active {
        background-color: #007bff !important;
        color: #fff !important;
        transform: scale(0.95) !important;
    }

    /* 拨号盘输入显示区域 */
    .dial-input-display {
        background-color: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
        border-radius: 4px !important;
        padding: 8px !important;
        margin-bottom: 10px !important;
        text-align: center !important;
        font-size: 14px !important;
        font-family: monospace !important;
    }

    /* 拨号盘控制按钮 */
    .dial-controls {
        text-align: center !important;
        margin-top: 10px !important;
    }

    .dial-controls button {
        margin: 0 5px !important;
        padding: 5px 10px !important;
        font-size: 11px !important;
        border-radius: 3px !important;
    }

    /* 进一步优化云客服弹窗 */
    .softphone-transfer-div .transfer-title,
    .softphone-consult-div .consult-title {
        background-color: #f8f9fa !important;
        padding: 8px 12px !important;
        border-bottom: 1px solid #e9ecef !important;
        font-weight: bold !important;
        font-size: 12px !important;
    }

    .softphone-transfer-div .transfer-content,
    .softphone-consult-div .consult-content {
        padding: 10px 12px !important;
        font-size: 11px !important;
    }

    /* 优化客户信息弹窗 */
    #softphone-bar .customer_db {
        border: 1px solid #e0e0e0 !important;
        background-color: #ffffff !important;
        border-radius: 4px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
        padding: 8px !important;
        max-height: 200px !important;
        overflow-y: auto !important;
    }

    #softphone-bar .customer_div {
        border-bottom: 1px solid #f0f0f0 !important;
        padding: 5px 0 !important;
        margin-bottom: 5px !important;
    }

    #softphone-bar .customer_div:last-child {
        border-bottom: none !important;
        margin-bottom: 0 !important;
    }

    /* 隐藏一些不必要的云客服元素 */
    #softphone-bar .holly-logo,
    #softphone-bar .holly-watermark {
        display: none !important;
    }

    /* 优化通话状态显示 */
    #softphone-bar .call-status {
        background-color: #e8f5e8 !important;
        border: 1px solid #c3e6c3 !important;
        border-radius: 3px !important;
        padding: 2px 6px !important;
        font-size: 10px !important;
        color: #2d5a2d !important;
    }

    #softphone-bar .call-status.calling {
        background-color: #fff3cd !important;
        border-color: #ffeaa7 !important;
        color: #856404 !important;
    }

    #softphone-bar .call-status.connected {
        background-color: #d4edda !important;
        border-color: #c3e6cb !important;
        color: #155724 !important;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        #softphone-bar {
            width: calc(100vw - 20px) !important;
            right: 10px !important;
            left: 10px !important;
            top: 10% !important;
        }
    }

    /* 最小化状态 */
    #softphone-bar.minimized {
        height: 50px !important;
        overflow: hidden !important;
    }

    #softphone-bar.minimized .phone-bar-header {
        margin-bottom: 0 !important;
        border-bottom: none !important;
    }

    #softphone-bar.minimized #phone_bar {
        display: none !important;
    }

    #softphone-bar.minimized .phone-bar-minimize {
        transform: rotate(180deg) !important;
    }
</style>
<script>
    $(function () {
        $('#{{\Illuminate\Support\Facades\Input::get('type')}}').attr("selected", true);
        $('.img').click(
            function () {
                let src = $(this).attr('src');
                if (src) {
                    let html = '<div class="message"><img style="max-width:900px;max-height:900px;" src="' + src + '"><//div>'
                    $.fancybox.open(html);
                }
            }
        );
    });
    // 电话条登录状态
    var phoneBarLoggedIn = false;
    var phoneBarLoginData = null;

    // 云客服配置
    var cloudServiceConfig = {
        accountSuffix: 'holly', // 账户名称后缀，如果账号没有@符号会自动添加
        loginType: 'sip',     // 登录方式：Local(直线)、sip(软电话)、gateway(语音网关)
        loginDelay: 3000        // 登录后延迟拨打时间(毫秒)
    };

    // 拨打电话
    $('#call').click(function (){
        var phoneNum = $('#phoneNumber').val()
        var billID = $('#bill_id').val()
        var ykf_account = $('#ykf_account').val()
        var ykf_password = $('#ykf_password').val()

        // 构建接口数据
        interfaceData = {
            bill_id: billID
        }

        // 检查是否已登录，如果没有登录或账号信息变化则重新登录
        if (!isPhoneBarLoggedIn() ||
            !phoneBarLoginData ) {
            console.log('电话条登录中...账号:', ykf_account, '登录方式:', cloudServiceConfig.loginType);
            // 使用配置的登录方式
            holly.loginPhoneBar(ykf_account, ykf_password, cloudServiceConfig.loginType);
            $('#softphone-bar').show();
            initPhoneBarDrag();
            optimizePhoneBarLayout();

            // 保存登录信息
            phoneBarLoginData = {
                account: ykf_account,
                password: ykf_password,
                loginType: cloudServiceConfig.loginType
            };
            phoneBarLoggedIn = true;

            // 等待登录成功后拨打电话
            waitForLoginSuccess(phoneNum, interfaceData);
        } else {
            console.log('电话条已登录，直接拨打电话:', phoneNum);
            $('#softphone-bar').show();
            holly.dialout(phoneNum, interfaceData);
        }
    });

    // 初始化电话条拖拽功能
    function initPhoneBarDrag() {
        var $phoneBar = $('#softphone-bar');
        var $header = $('.phone-bar-header');
        var isDragging = false;
        var startX, startY, startLeft, startTop;

        // 鼠标按下事件（只在标题栏区域）
        $header.on('mousedown', function(e) {
            // 如果点击的是按钮，不启动拖拽
            if ($(e.target).is('button, .phone-bar-minimize, .phone-bar-close')) {
                return;
            }

            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            startLeft = parseInt($phoneBar.css('left')) || 0;
            startTop = parseInt($phoneBar.css('top')) || 0;

            $header.css('cursor', 'grabbing');
            e.preventDefault();
        });

        // 鼠标移动事件
        $(document).on('mousemove', function(e) {
            if (!isDragging) return;

            var deltaX = e.clientX - startX;
            var deltaY = e.clientY - startY;
            var newLeft = startLeft + deltaX;
            var newTop = startTop + deltaY;

            // 限制在窗口范围内
            var maxLeft = $(window).width() - $phoneBar.outerWidth();
            var maxTop = $(window).height() - $phoneBar.outerHeight();

            newLeft = Math.max(0, Math.min(newLeft, maxLeft));
            newTop = Math.max(0, Math.min(newTop, maxTop));

            $phoneBar.css({
                left: newLeft + 'px',
                top: newTop + 'px',
                right: 'auto'
            });
        });

        // 鼠标释放事件
        $(document).on('mouseup', function() {
            if (isDragging) {
                isDragging = false;
                $header.css('cursor', 'move');
            }
        });
    }

    // 关闭电话条弹窗
    $(document).on('click', '.phone-bar-close', function(e) {
        e.stopPropagation();
        $('#softphone-bar').hide();
        console.log('电话条关闭，重置登录状态');
    });

    // 最小化/还原电话条
    $(document).on('click', '.phone-bar-minimize', function(e) {
        e.stopPropagation();
        $('#softphone-bar').toggleClass('minimized');
    });

    // 双击标题栏最小化/还原
    $(document).on('dblclick', '.phone-bar-header', function(e) {
        $('#softphone-bar').toggleClass('minimized');
    });

    // 监听电话条内的签出按钮
    $(document).on('click', '#softPhoneBarKick', function() {
        console.log('用户手动签出，重置登录状态');
        phoneBarLoggedIn = false;
        phoneBarLoginData = null;
        setTimeout(function() {
            $('#softphone-bar').hide();
        }, 500);
    });

    // 页面加载完成后的初始化
    $(document).ready(function() {
        // 确保电话条初始状态为隐藏
        $('#softphone-bar').hide();

        // 如果电话条已经显示，初始化拖拽功能
        setTimeout(function() {
            if ($('#softphone-bar').is(':visible')) {
                initPhoneBarDrag();
            }
        }, 1000);

        // 确保侧边栏功能正常工作
        fixSidebarFunctionality();
    });

    // 修复侧边栏功能
    function fixSidebarFunctionality() {
        // 监听侧边栏切换事件
        $(document).on('click', '.sidebar-toggle', function(e) {
            // 确保事件不被阻止
            e.stopPropagation();

            // 延迟检查body类，确保AdminLTE的逻辑先执行
            setTimeout(function() {
                if ($('body').hasClass('sidebar-open')) {
                    $('body').css('overflow', 'visible');
                } else {
                    $('body').css('overflow-y', 'auto').css('overflow-x', 'hidden');
                }
            }, 100);
        });

        // 监听body类的变化
        if (typeof MutationObserver !== 'undefined') {
            var bodyObserver = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        var body = mutation.target;
                        if (body.classList.contains('sidebar-open')) {
                            body.style.overflow = 'visible';
                        } else {
                            body.style.overflowY = 'auto';
                            body.style.overflowX = 'hidden';
                        }
                    }
                });
            });

            bodyObserver.observe(document.body, {
                attributes: true,
                attributeFilter: ['class']
            });
        }

        // 确保初始状态正确
        if ($('body').hasClass('sidebar-open')) {
            $('body').css('overflow', 'visible');
        }

        // 强制重置可能被云客服CSS影响的样式
        resetCloudServiceCSS();
    }

    // 重置云客服CSS的影响
    function resetCloudServiceCSS() {
        // 延迟执行，确保云客服CSS已加载
        setTimeout(function() {
            // 强制重置body的overflow
            $('body').css({
                'overflow-y': 'auto',
                'overflow-x': 'hidden'
            });

            // 确保侧边栏切换按钮可点击
            $('.sidebar-toggle').css({
                'pointer-events': 'auto',
                'z-index': '1041'
            });

            // 确保侧边栏本身的样式正确
            $('.main-sidebar').css({
                'z-index': '1040',
                'position': 'fixed'
            });

            // 如果侧边栏是打开状态，确保overflow正确
            if ($('body').hasClass('sidebar-open')) {
                $('body').css('overflow', 'visible');
            }
        }, 500);

        // 再次延迟执行，确保完全覆盖
        setTimeout(function() {
            $('body').removeClass('holly');
            if (!$('body').hasClass('sidebar-open')) {
                $('body').css({
                    'overflow-y': 'auto',
                    'overflow-x': 'hidden'
                });
            }
        }, 1500);
    }

    // 优化电话条布局
    function optimizePhoneBarLayout() {
        // 延迟执行，等待云客服内容加载
        setTimeout(function() {
            // 优化输入框宽度
            $('#softphone-bar input[type="text"]').each(function() {
                var $input = $(this);
                if ($input.attr('id') === 'dialout_input') {
                    $input.css({
                        'width': '110px',
                        'max-width': '110px',
                        'box-sizing': 'border-box'
                    });
                } else {
                    $input.css({
                        'max-width': '100px',
                        'box-sizing': 'border-box'
                    });
                }
            });

            // 优化按钮组布局
            $('#softphone-bar .btn-group').each(function() {
                $(this).css({
                    'display': 'inline-block',
                    'vertical-align': 'top',
                    'margin': '3px 3px 3px 0'
                });
            });

            // 优化状态显示
            $('#softphone-bar .state_group').each(function() {
                $(this).css({
                    'display': 'inline-block',
                    'vertical-align': 'top',
                    'max-width': '150px',
                    'overflow': 'hidden'
                });
            });

            // 隐藏或优化不必要的元素
            $('#softphone-bar .customer_db').css({
                'position': 'absolute',
                'max-width': '200px',
                'z-index': '10001'
            });

            // 监听输入框变化，防止弹窗被撑开
            $('#softphone-bar input').on('input focus blur', function() {
                setTimeout(function() {
                    $('#softphone-bar').css({
                        'width': '420px',
                        'max-width': 'calc(100vw - 40px)'
                    });
                }, 100);
            });

            // 特殊处理拨号盘相关元素
            setTimeout(function() {
                // 确保拨号盘按钮有正确的样式和事件
                $('#softphone-bar .dial-btn, #softphone-bar [class*="dial"]').each(function() {
                    $(this).css({
                        'position': 'relative',
                        'z-index': '1001'
                    });
                });

                // 监听拨号盘的显示
                $('#softphone-bar').on('click', '.dial-btn, [class*="dial"]', function(e) {
                    console.log('拨号相关按钮被点击');
                    setTimeout(function() {
                        // 查找并优化拨号盘
                        $('.dial-wrap, #softphone-bar .dial-wrap').each(function() {
                            $(this).css({
                                'position': 'absolute',
                                'z-index': '10002',
                                'background-color': '#ffffff',
                                'border': '1px solid #e0e0e0',
                                'border-radius': '6px',
                                'box-shadow': '0 4px 15px rgba(0,0,0,0.15)',
                                'padding': '10px',
                                'display': 'block',
                                'top': '100%',
                                'left': '0',
                                'margin-top': '5px'
                            });
                        });
                    }, 200);
                });
            }, 2000);

        }, 1000);

        // 持续监听和优化
        setInterval(function() {
            // 确保弹窗尺寸不被撑开
            if ($('#softphone-bar').is(':visible')) {
                var currentWidth = $('#softphone-bar').width();
                if (currentWidth > 450) {
                    $('#softphone-bar').css('width', '420px');
                }

                // 检查并修复拨号盘显示问题
                $('.dial-wrap').each(function() {
                    var $dialWrap = $(this);
                    if ($dialWrap.is(':visible')) {
                        // 确保拨号盘有正确的层级和定位
                        if (parseInt($dialWrap.css('z-index')) < 10000) {
                            $dialWrap.css({
                                'z-index': '10002',
                                'position': 'absolute',
                                'background-color': '#ffffff',
                                'border': '1px solid #e0e0e0',
                                'box-shadow': '0 4px 15px rgba(0,0,0,0.15)'
                            });
                            console.log('修复了拨号盘的显示层级');
                        }
                    }
                });
            }
        }, 2000);
    }

    // 检查电话条实际登录状态
    function checkPhoneBarLoginStatus() {
        // 检查电话条是否显示了登录后的界面
        if ($('#softphone-bar').is(':visible')) {
            // 检查是否有拨号按钮或其他登录后才有的元素
            var hasDialButton = $('#softphone-bar .btn').length > 0;
            var hasStateGroup = $('#softphone-bar .state_group').length > 0;

            if (hasDialButton || hasStateGroup) {
                return true;
            }
        }
        return false;
    }

    // 智能登录检查（结合本地状态和实际界面状态）
    function isPhoneBarLoggedIn() {
        var actuallyLoggedIn = checkPhoneBarLoginStatus();

        // 如果实际界面显示未登录，但本地状态显示已登录，重置本地状态
        if (phoneBarLoggedIn && !actuallyLoggedIn) {
            console.log('检测到登录状态不一致，重置本地状态');
            phoneBarLoggedIn = false;
            phoneBarLoginData = null;
        }

        return phoneBarLoggedIn && actuallyLoggedIn;
    }

    // 定期检查登录状态
    setInterval(function() {
        if ($('#softphone-bar').is(':visible')) {
            isPhoneBarLoggedIn();
        }
    }, 5000);

    // 优化拨号盘显示
    function optimizeDialPad() {
        // 监听拨号盘按钮点击
        $(document).on('click', '#softphone-bar .dial-btn, .dial-btn', function(e) {
            e.stopPropagation();
            console.log('拨号盘按钮被点击');

            // 确保拨号盘容器有正确的样式
            setTimeout(function() {
                $('.dial-wrap').css({
                    'z-index': '10002',
                    'position': 'absolute',
                    'background-color': '#ffffff',
                    'border': '1px solid #e0e0e0',
                    'border-radius': '6px',
                    'box-shadow': '0 4px 15px rgba(0,0,0,0.15)',
                    'display': 'block'
                });
            }, 100);
        });

        // 监听拨号盘数字按钮
        $(document).on('click', '.dial-phone-num span', function(e) {
            e.stopPropagation();
            console.log('拨号盘数字被点击:', $(this).text());
        });

        // 点击其他地方关闭拨号盘
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.dial-wrap, .dial-btn').length) {
                $('.dial-wrap').hide();
            }
        });

        // 确保拨号盘在电话条内部时有正确的定位
        $(document).on('DOMNodeInserted', '#softphone-bar', function(e) {
            if ($(e.target).hasClass('dial-wrap') || $(e.target).find('.dial-wrap').length) {
                setTimeout(function() {
                    $('#softphone-bar .dial-wrap').css({
                        'position': 'absolute',
                        'z-index': '10002',
                        'top': '100%',
                        'left': '0',
                        'margin-top': '5px'
                    });
                }, 100);
            }
        });
    }

    // 等待登录成功的多种方案
    function waitForLoginSuccess(phoneNum, interfaceData) {
        console.log('开始等待登录成功...');

        // 方案1：监听DOM变化（推荐）
        waitForLoginByDOMChange(phoneNum, interfaceData);

        // 方案2：作为备用的超时机制
        setTimeout(function() {
            console.log('备用超时机制：强制拨打电话');
            holly.dialout(phoneNum, interfaceData);
        }, cloudServiceConfig.loginDelay);
    }

    // 方案1：监听DOM变化确认登录成功
    function waitForLoginByDOMChange(phoneNum, interfaceData) {
        var maxAttempts = 30; // 最多尝试30次
        var attempts = 0;
        var checkInterval = 200; // 每200ms检查一次

        var loginChecker = setInterval(function() {
            attempts++;

            // 检查登录成功的标志
            var loginSuccess = checkLoginSuccessIndicators();

            if (loginSuccess) {
                console.log('检测到登录成功，开始拨打电话');
                clearInterval(loginChecker);
                holly.dialout(phoneNum, interfaceData);
                return;
            }

            // 超过最大尝试次数
            if (attempts >= maxAttempts) {
                console.log('登录检测超时，强制拨打电话');
                clearInterval(loginChecker);
                holly.dialout(phoneNum, interfaceData);
                return;
            }

            console.log('等待登录中... 尝试次数:', attempts);
        }, checkInterval);
    }

    // 检查登录成功的多个指标
    function checkLoginSuccessIndicators() {
        // 指标1：检查是否有拨号按钮
        var hasDialButton = $('#softphone-bar .btn').length > 0;

        // 指标2：检查是否有状态组
        var hasStateGroup = $('#softphone-bar .state_group').length > 0;

        // 指标3：检查是否有拨号输入框
        var hasDialInput = $('#softphone-bar #dialout_input').length > 0;

        // 指标4：检查电话条内容是否已加载
        var hasPhoneBarContent = $('#softphone-bar #phone_bar').children().length > 0;

        // 指标5：检查是否有登录后的特定元素
        var hasLoginElements = $('#softphone-bar .f-l').length > 0;

        // 指标6：检查是否没有登录错误信息
        var noLoginError = $('#softphone-bar').text().indexOf('登录失败') === -1 &&
                          $('#softphone-bar').text().indexOf('连接失败') === -1;

        // 至少满足2个指标才认为登录成功
        var successCount = 0;
        if (hasDialButton) successCount++;
        if (hasStateGroup) successCount++;
        if (hasDialInput) successCount++;
        if (hasPhoneBarContent) successCount++;
        if (hasLoginElements) successCount++;
        if (noLoginError) successCount++;

        console.log('登录指标检查:', {
            hasDialButton: hasDialButton,
            hasStateGroup: hasStateGroup,
            hasDialInput: hasDialInput,
            hasPhoneBarContent: hasPhoneBarContent,
            hasLoginElements: hasLoginElements,
            noLoginError: noLoginError,
            successCount: successCount
        });

        return successCount >= 2;
    }

    // 方案2：使用MutationObserver监听DOM变化
    function waitForLoginByMutationObserver(phoneNum, interfaceData) {
        var loginDetected = false;
        var timeoutId = setTimeout(function() {
            if (!loginDetected) {
                console.log('MutationObserver超时，强制拨打电话');
                holly.dialout(phoneNum, interfaceData);
            }
        }, cloudServiceConfig.loginDelay);

        if (typeof MutationObserver !== 'undefined') {
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // 检查是否添加了登录后的元素
                        for (var i = 0; i < mutation.addedNodes.length; i++) {
                            var node = mutation.addedNodes[i];
                            if (node.nodeType === 1) { // 元素节点
                                var $node = $(node);
                                if ($node.hasClass('btn') || $node.hasClass('state_group') ||
                                    $node.find('.btn').length > 0 || $node.find('.state_group').length > 0) {
                                    console.log('MutationObserver检测到登录成功');
                                    loginDetected = true;
                                    clearTimeout(timeoutId);
                                    observer.disconnect();
                                    holly.dialout(phoneNum, interfaceData);
                                    return;
                                }
                            }
                        }
                    }
                });
            });

            observer.observe(document.getElementById('softphone-bar'), {
                childList: true,
                subtree: true
            });
        } else {
            // 不支持MutationObserver，使用延迟方案
            setTimeout(function() {
                holly.dialout(phoneNum, interfaceData);
            }, cloudServiceConfig.loginDelay);
        }
    }

    // 方案3：Promise-based 登录等待
    function waitForLoginPromise(phoneNum, interfaceData) {
        return new Promise(function(resolve, reject) {
            var maxWaitTime = cloudServiceConfig.loginDelay;
            var checkInterval = 200;
            var startTime = Date.now();

            var checker = setInterval(function() {
                if (checkLoginSuccessIndicators()) {
                    clearInterval(checker);
                    resolve();
                } else if (Date.now() - startTime > maxWaitTime) {
                    clearInterval(checker);
                    reject(new Error('登录超时'));
                }
            }, checkInterval);
        }).then(function() {
            console.log('Promise: 登录成功，开始拨打电话');
            holly.dialout(phoneNum, interfaceData);
        }).catch(function(error) {
            console.log('Promise: 登录等待失败，强制拨打电话', error);
            holly.dialout(phoneNum, interfaceData);
        });
    }

    // 初始化拨号盘优化
    $(document).ready(function() {
        optimizeDialPad();
    });

    // 拨打电话
    $('#call123').click(function () {
        layer.confirm('你确定拨打电话: ' + $('#phoneNumber').val() + '吗?', {
            btn: ['确认', '取消']
        }, function () {

            var ykf_account = $('#ykf_account').val()
            var ykf_password = $('#ykf_password').val()
            layer.open({
                type: 2,
                title: '电话外呼',
                // shadeClose: true,
                shade: 0,
                area: ['400px', '18%'],
                // maxmin: true,
                content: '/edb_bar/edb1112/edb/html/main.html?loginType=sip&agentStatus=0&loginName=' + ykf_account + '&password=' + encodeURIComponent(ykf_password)
                // content: '/edb_bar/edb1112/edb/html/main.html?loginName=8000@dusl&password='+ encodeURIComponent('1qaz!DUSL8000') +'&loginType=sip&agentStatus=0&callNumber=' + $('#phoneNumber').val()
            });
            layer.msg('正在登录云客服...');

            setTimeout(function () {

                $.ajax({
                    url: '/admin/repair_check/call',
                    data: {
                        phone: $('#phoneNumber').val(),
                        bill_id: $('#bill_id').val(),
                    },
                    dataType: 'json',
                    type: 'get',
                    sync: false,
                    success: function (res) {

                        if (res.Succeed == true) {
                            layer.msg('电话接通中,请稍后...');


                        } else {
                            layer.msg('拨通失败,请核查');
                        }
                        console.log(res)
                    }
                })

            }, 2000)

        }, function () {
        });
    })

    // 发信息
    $('#sendMsgBtn').click(function () {

        layer.confirm('是否对联系人: ' + $('#phoneNumber').val() + '发送短信通知?', {
            btn: ['是', '否']
        }, function () {
            $.ajax({
                url: '/admin/repair_check/sendMsg',
                data: {
                    phone: $('#phoneNumber').val(),
                },
                dataType: 'json',
                type: 'get',
                success: function (res) {
                    console.log(res)
                    layer.msg(res.info);
                }
            })
        }, function () {
        });
    })

    // 维修时间打卡
    $('#save_repair_record_time').click(function () {

        layer.confirm('是否开始维修?', {
            btn: ['是', '否']
        }, function () {
            $.ajax({
                url: '/admin/repair_check/saveRecordTime',
                data: {
                    sn: $('#bill_id').val(),
                    type: 'repair_time'
                },
                dataType: 'json',
                type: 'get',
                success: function (res) {
                    window.location.reload()
                }
            })
        }, function () {
        });
    })


    // 维修知会通话记录
    $('#openContent').click(function () {

        $.fancybox.open($('#phoneRecord'));
    })

    // 客服知会通话记录
    $('#openContent1').click(function () {
        $.fancybox.open($('#phoneRecord1'));
    })

    // 填写客服备注
    $('#edit_is_tell_400').click(function () {
        $.fancybox.open($('#edit_is_tell_400_div'));
    })

    // 填写维修知会
    $('#edit_is_tell_repair').click(function () {
        $.fancybox.open($('#edit_is_tell_repair_div'));
    })

    // 变更状态为已收货,已知会
    $('#is_tell_400_save').click(function () {
        sn = $('#bill_id').val();
        order_remark = $('#order_remark').val();
        status = $("input[name='status']:checked").val();

        if (!order_remark) {
            layer.msg('备注不能为空');
            return false;
        }

        if (status == 1) {

            $.ajax({
                url: '/admin/customer_service_manage/setOrderRemark',
                data: {
                    sn: sn,
                    order_remark: order_remark,
                },
                dataType: 'json',
                type: 'get',
                success: function (res) {
                    layer.msg(res.info)
                    if (res.status == 1) {
                        window.location.reload()
                    }
                }
            })
        } else {
            $.ajax({
                url: '/admin/customer_service_manage/set400Remark',
                data: {
                    sn: sn,
                    remark: order_remark
                },
                type: 'get',
                dataType: 'json',
                success: function (res) {
                    layer.msg(res.info);
                    if (res.status == 1) {
                        window.location.reload()
                    }
                },

            })
        }
    })

    // 变更状态为未检测已知会/已检测已知会
    $('#is_tell_repair_save').click(function () {
        sn = $('#bill_id').val();
        order_remark = $('#order_remark_repair').val();
        status = $("input[name='repair_status']:checked").val();

        if (!order_remark) {
            layer.msg('备注不能为空');
            return false;
        }
        if (status == 1) {
            $.ajax({
                url: '/admin/repair_check/setOrderRemark',
                data: {
                    sn: sn,
                    order_remark: order_remark,
                },
                dataType: 'json',
                type: 'get',
                success: function (res) {
                    layer.msg(res.info)
                    if (res.status == 1) {
                        window.location.reload()
                    }
                }
            })
        } else {
            $.ajax({
                url: '/admin/repair_check/setRepairRemark',
                data: {
                    sn: sn,
                    remark: order_remark
                },
                type: 'get',
                dataType: 'json',
                success: function (res) {
                    layer.msg(res.info);
                    if (res.status == 1) {
                        window.location.reload()
                    }
                },
            })
        }

    })

    // 单独添加客服备注
    $('#400Remark').click(function () {
        $.fancybox.open($('#400RemarkDiv'));
    })

    $("input[name='remarkRadio400']").change(function () {
        $('#remarkTextarea400').val($("input[name='remarkRadio400']:checked").val());
    })

    $("input[name='remarkRadio400s']").change(function () {
        $('#order_remark').val($("input[name='remarkRadio400s']:checked").val());
    })

    $('#saveRemark400').click(function () {
        remark = $('#remarkTextarea400').val()
        if (!remark) {
            layer.msg('备注不能为空');
            return false;
        }
        $.ajax({
            url: '/admin/customer_service_manage/set400Remark',
            data: {
                sn: $('#bill_id').val(),
                remark: remark
            },
            type: 'get',
            dataType: 'json',
            success: function (res) {
                layer.msg(res.info);
                if (res.status == 1) {
                    var newTbodyHtml = '<tr>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.created_at + '<\/td>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.status_name + '<\/td>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.name + '<\/td>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.remark + '<\/td>' +
                        '<\/tr>';

                    $('#tbody400').append(newTbodyHtml);

                    setTimeout(function () {
                        window.location.reload()
                    }, 1000)
                }
            },
            error: function (res) {
                console.log('error');
            }
        })
    })

    // 单独添加维修知会备注
    $('#repairRemark').click(function () {
        $.fancybox.open($('#RepairRemarkDiv'));
    })

    $("input[name='remarkRadioRepair']").change(function () {
        $('#remarkTextareaRepair').val($("input[name='remarkRadioRepair']:checked").val());
    })

    $("input[name='remarkRadioRepairs']").change(function () {
        $('#order_remark_repair').val($("input[name='remarkRadioRepairs']:checked").val());
    })

    $('#saveRemarkRepair').click(function () {
        remark = $('#remarkTextareaRepair').val()
        if (!remark) {
            layer.msg('备注不能为空');
            return false;
        }
        $.ajax({
            url: '/admin/repair_check/setRepairRemark',
            data: {
                sn: $('#bill_id').val(),
                remark: remark
            },
            type: 'get',
            dataType: 'json',
            success: function (res) {
                layer.msg(res.info);
                if (res.status == 1) {
                    var newTbodyHtml = '<tr>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.created_at + '<\/td>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.status_name + '<\/td>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.name + '<\/td>' +
                        '<td style="text-align: center;color:#29db6f">' + res.data.remark + '<\/td>' +
                        '<\/tr>';

                    $('#tbodyRepair').append(newTbodyHtml);

                    setTimeout(function () {
                        window.location.reload()
                    }, 1000)
                }
            },
            error: function (res) {
                console.log('error');
            }
        })
    })

    var hollyglobal = {
        loginSuccessCallback: function (peer) { // 软电话条登录成功回调函数
            console.log(peer);
        },
        loginFailureCallback: function (peer) { // 软电话条登录失败回调函数
            console.log(peer);
        },
        ringEvent: function (peer) { // 响铃回调函数，可用于实现弹屏。参数请参考“软电话条-》事件API-》坐席振铃事件”
            console.log(peer);
        },
        talkingEvent: function (peer) { // 接通事件回调函数。参数请参考“软电话条-》事件API-》坐席接通事件”
            console.log(peer);
        },
        hangupEvent: function (peer) { // 挂机事件回调函数。参数请参考“软电话条-》事件API-》坐席挂断事件”
            console.log(peer);
        },
        queueWaitCountEvent: function (peer) { // 技能组排队数事件回调函数。参数请参考“软电话条-》事件API-》技能组排队数事件”
            console.dir(peer);
        },
        setBusyEvent: function(peer){ // 坐席切换状态回调
        },
        queueInfo: function(peer){ // 技能组发生变化回调
        },
        registered: function(peer){ // 电话条注册成功、通话功能可正常使用回调
            console.log("======");
        },
        isDisplayInvestigate: false, // 是否开启转满意功能
        isDisplayConsult: false, // 是否开启咨询功能
        isDisplayTransfer: false, // 是否开启转接
        isDisplayValidate: true, // 是否开启转验证
        isHiddenNumber: false, // 是否开启隐藏号码功能
        listenSelfEvent: 2,//开启监控权限时，需要去掉此参数
        monitor: 0, //是否开启监控权限，1表示开启、不配置或者其他值表示不开启，开启后将会收到所有的坐席事件并处理
        loginBusyType: "0", //坐席登录后状态。0是空闲，1是忙碌，2是小休，注：该参数不传的情况下，默认为0
        videoEnable: false //是否对接视频, true为开启视频对接
    };

</script>
