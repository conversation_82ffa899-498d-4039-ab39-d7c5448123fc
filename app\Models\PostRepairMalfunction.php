<?php
/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 2019/7/8
 * Time: 20:12
 */

namespace App\Models;


use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class PostRepairMalfunction extends Model
{
    protected $table = "pr_malfunction";
    protected $fillable = ['malfunction_parent', 'malfunction_id'];

    protected static function boot()
    {
        parent::boot();

        self::saved(function () {
            DB::table('pr_malfunction')
                ->where('malfunction_id', 0)->delete();
        });
    }
}
