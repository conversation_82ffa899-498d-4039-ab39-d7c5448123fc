<?php
/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 2019/7/22
 * Time: 10:37
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PostExpress extends Model
{
    protected $table = "pr_express";

    public const EXPRESS_COM_SF = 1; // 顺丰
    public const EXPRESS_COM_YT = 2; // 圆通
    public const EXPRESS_COM_YZ = 3; // 邮政
    public const EXPRESS_COM = [
        self::EXPRESS_COM_SF => '顺丰',
        self::EXPRESS_COM_YT => '圆通',
        self::EXPRESS_COM_YZ => '邮政',
    ];
    public const GO_EXP_COM = [
        self::EXPRESS_COM_SF => '顺丰快递',
        self::EXPRESS_COM_YT => '圆通快递',
        self::EXPRESS_COM_YZ => '邮政快递',
    ];

    public const PAY_METHOD_CONSIGN = 1; // 寄付
    public const PAY_METHOD_COLLECT = 2; // 到付
    public const PAY_METHOD = [
        self::PAY_METHOD_CONSIGN => '寄付',
        self::PAY_METHOD_COLLECT => '到付',
    ];

    public function setDataAttribute($value)
    {
        if (is_array($value)) {
            $this->attributes['data'] = json_encode($value, JSON_UNESCAPED_UNICODE);
        }
    }

    public function getDataAttribute($value)
    {
        return json_decode($value, true);
    }
}