<?php
/**
 * Created by PhpStorm.
 * User: qzl
 * Date: 2019/4/11
 * Time: 21:15
 */

namespace App\Admin\Extensions;


use Encore\Admin\Form\Field;

class Barcode extends Field
{
    protected $view = 'admin.barcode';

//    protected static $css = [
//        'packages/bootstrap-tagsinput/dist/bootstrap-tagsinput.css',
//    ];


    protected static $js = [
        'packages/JsBarcode/dist/JsBarcode.all.min.js',
    ];

//    public function fill($data)
//    {
//        $this->value = array_get($data, $this->column);
//
//        if (is_string($this->value)) {
//            $this->value = explode(',', $this->value);
//        }
//
//        $this->value = array_filter((array) $this->value);
//    }

    public function render()
    {
        $op = json_encode($this->options);
        $this->script = <<<EOT
        JsBarcode('#{$this->id}', '$this->value', $op);
EOT;
;
        return parent::render();
    }
}