<?php

namespace App\Admin\Controllers;

use App\Models\Partition;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use App\Models\Agency;

//大区管理,主要是给代理用户或者代理加上大区信息的
class PartitionController extends Controller {
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index() {
        return Admin::content(function (Content $content) {

            $content->header('大区管理');
            $content->description('大区列表');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id) {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('大区管理');
            $content->description('大区编辑');

            $content->body($this->form()->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create() {
        return Admin::content(function (Content $content) {

            $content->header('大区管理');
            $content->description('大区添加');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid() {
        return Admin::grid(Partition::class, function (Grid $grid) {
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('name', '大区名字');
            });
            $grid->disableRowSelector();
            $grid->disableExport();
            $grid->id('ID')->sortable();
            $grid->name('大区名称');
            $grid->created_at('创建时间')->prependIcon('calendar-times-o');
            $grid->updated_at('更新时间')->prependIcon('calendar-times-o');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form() {
        return Admin::form(Partition::class, function (Form $form) {

            $form->display('id', 'ID');
            $form->text('name', '大区名字');
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
