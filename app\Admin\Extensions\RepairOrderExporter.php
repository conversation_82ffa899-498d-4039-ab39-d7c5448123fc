<?php

namespace App\Admin\Extensions;

use App\Models\BrokenScreenInsurance;
use App\Models\Order;
use App\Models\PayOrder;
use App\Models\PostRepairExpense;
use App\Models\PostRepairUsedMaterial;
use App\Models\RefundOrder;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

/**
 * 维修订单导出物料
 */
class RepairOrderExporter extends AbstractExporter
{
    public function export()
    {
        $start = date('Y-m-d 00:00:00');
        $end = date('Y-m-d 23:59:59');
        $statistic_time = Request::get('used_material');
        $time = Request::get('time');
        $material_code = Request::get('material_code');
        if ($statistic_time) {
            $start = $statistic_time['statistic_time']['start'];
            $end = $statistic_time['statistic_time']['end'];
        }
        $type = 1;
        if ($time) {
            $time = explode(" ~ ", $time);
            $start = $time[0];
            $end = $time[1];
            $type = 2;
        }
        $statistic_time_start_str = date('Y-m-d', strtotime($start));
        $statistic_time_end_str = date('Y-m-d', strtotime($end));

        $filename = '寄修物料导出' . $statistic_time_start_str . '-' . $statistic_time_end_str;
        $titles = [
            '支付方式',
            'id',
            '寄修单号',
            'SN码',
            '订单状态',
            '联系状态',
            '保修状态',
            '有无碎屏保',
            '是否使用碎屏保',
            '是否直营（碎屏保）',
            '是否二次维修',
            '故障导致原因分析',
            '付款时间',
            '统计时间',
            '细节描述',
            '配件故障',
            '物料名称',
            '新物料编码',
            '旧物料编码',
            '数量',
            '单价',
            '是否收费',
            '收费类型',
            '配件折扣',
            '订单是1否0打折',
            '配件是1否0打折',
            '配件折后价',
            '总费用',
            '快递费用',
            '配件实际收费(总)',
            '实际费用（需要支付的费用）',
            '机型',
            '寄修渠道',
            '物料添加类型',
            '是否已导出',
        ];
        $titles2 = [
            '支付方式',
            'id',
            '寄修单号',
            'SN码',
            '订单状态',
            '联系状态',
            '付款时间',
            '统计时间',
            '细节描述',
            'group_code',
            '配件故障',
            '物料名称',
            '新物料编码',
            '旧物料编码',
            '数量',
            '单价',
            '是否收费',
            '收费类型',
            '配件折扣',
            '订单是1否0打折',
            '配件是1否0打折',
            '配件折后价',
            '总费用',
            '快递费用',
            '配件实际收费(总)',
            '实际费用（需要支付的费用）',
            '机型',
            '寄修渠道',
            '物料添加类型',
        ];
        if ($type == 1) {
            $formatData = $this->formatData($start, $end);
            array_unshift($formatData, $titles);
        } else {
            $formatData = $this->formatData2($start, $end, $material_code);
            array_unshift($formatData, $titles2);
        }

        ExcelExportTrait::exportToExcel($filename, $formatData, null, null, 'G2');
    }

    /**
     * 导出物料1
     */
    private function formatData($start, $end): array
    {
        $formatData = [];

        $data = DB::table('pr_used_material as pum')
            ->leftjoin('machine_accessory_tree as mat', 'mat.id', '=', 'pum.mat_id')
            ->leftjoin('material as m', 'm.id', '=', 'pum.material_id')
            ->leftjoin('order as o', 'pum.pr_sn', '=', 'o.sn')
            ->leftjoin('order_extend as oe', 'o.sn', '=', 'oe.sn')
            ->leftjoin('broken_screen_insurance as bsi', function ($j) {
                $j->on('bsi.barcode', '=', 'o.barcode')->whereIn('bsi.status', [300, 400]);
                //    ->Orwhere('bsi.status', '=', 400);
            })
            // ->where([['o.sn', '=', '20210420102232519012']])
            ->whereBetween('pum.statistic_time', [$start, $end])
            ->whereIn('o.status', [600, 700, 800, -800, 900])
            ->orderby('o.id')
            ->orderby('pum.is_charge', 'desc')
            ->select('o.pay_com', 'o.id', 'o.sn', 'o.barcode', 'o.model_name', 'o.status', 'o.in_period',
                'o.repeat_order', 'o.repeat_remark', 'o.pay_time',
                'o.accessory_amount', 'o.accessory_cast', 'o.staff_cast', 'o.amount', 'o.pay_amount',
                'o.updated_at_last', 'o.staff_cast', 'o.type', 'o.description',
                'o.amount', 'o.pay_amount', 'o.connect', 'oe.discount as oe_discount', 'm.name', 'm.price_first',
                'bsi.is_direct_sales', 'bsi.status as bsi_status', 'mat.discount as mat_discount',
                'm.price', 'pum.count', 'm.code', 'm.old_code', 'm.from', 'pum.id as pum_id', 'pum.is_charge',
                'pum.charge_type', 'pum.created_at', 'pum.mark', 'pum.statistic_time', 'pum.export_status', 'pum.material_id'
            )
            ->get()
            ->toArray();

        $sns1 = array();
        foreach ($data as $d1) {
            $sns1[] = $d1->sn;
        }

        // 获取时间段内的退款
        $refundArr = array();
        $refundOrderList = DB::table('refund_order')->whereBetween('created_at', [$start, $end])
            ->where('is_refund', 1)->select('pr_sn', 'refund_amount')->get();
        foreach ($refundOrderList as $key => $value) {
            $refundArr[$value->pr_sn] = $value->refund_amount;
        }

        // 获取物料对应的故障数组
        $materialCaseList = DB::table('pr_material as pm')
            ->leftjoin('machine_malfunction as mm', 'mm.id', '=', 'pm.malfunction_id')
            ->whereIn('pm.pr_sn', $sns1)
            ->get();
        $materialCaseArr = array();
        foreach ($materialCaseList as $key => $value) {
            $newKey = $value->pr_sn . '_' . $value->material_id;
            $materialCaseArr[$newKey] = $value->title;
        }

        $sn = "";
        $export_status = [0 => "未导出", 1 => "已导出"];
        $mark = [0 => "检测添加", 1 => "维修添加"];
        $i = 1;
        $id = -1;
        $buy_date = null;
        $sns = $snToOldCodeArr = array();
        foreach ($data as $d) {
            $sns[] = $d->sn;
            $pay_com = $d->pay_com;
            $amount = $d->amount;
            $pay_amount = $d->pay_amount;
            $accessory_cast = $d->accessory_cast;
            $staff_cast = $d->staff_cast;
            if ($id != $d->id) {
                $id = $d->id;
                $buy_date = Order::get_buy_date($d->barcode);
                if ($d->connect == 3 || $d->connect == 6) {
                    $pay_com = DB::table('pay_order as po')->where('po.pr_sn', $d->sn)
                            ->where('po.is_paid', 1)->orderBy('po.id', 'desc')->value('com') ?? 0;
                }
                // 单列快递费到一行
                $row = [
                    PayOrder::COM[$pay_com], //0
                    $d->id,  //1
                    $d->sn . ' ', //2
                    $d->barcode . ' ', //3
                    Order::STATUS[$d->status], //4
                    Order::CONNECT[$d->connect], //5
                    Order::in_period[$d->in_period],
                    Order::has_screen_insurance[$d->bsi_status == 300 || $d->bsi_status == 400 ? 1 : 0],// 有无碎屏保 //6
                    Order::used_screen_insurance[$d->bsi_status == 400 ? 1 : 0],// 是否使用碎屏保 //7
                    $d->bsi_status == 300 || $d->bsi_status == 400 ? Order::is_direct_sales[$d->is_direct_sales] : '无碎屏保',//8
                    Order::repeat_repair[$d->repeat_order],
                    $d->repeat_remark,
                    $d->pay_time, //9
                    $d->statistic_time, //10
                    $this->filter(trim($d->description)), //11
                    '', //12
                    '快递费用', //13
                    '97004.00040 ', //14
                    '', //15
                    1, //16
                    $staff_cast, //17
                    '快递费用', //18
                    '', //19
                    '', //20
                    $d->oe_discount, //21
                    0, //22
                    $staff_cast, //23
                    '', //24
                    $staff_cast, //25
                    '', //26
                    '', //27
                    $d->model_name, // 28
                    Order::post_repair_type[$d->type], //29
                    '', //30
                    '', //31
                ];
                $formatData[] = $row;
            } else {
                $amount = '';

                $pay_amount = '';

                $accessory_cast = '';
            }

            if ($d->export_status == 0) {
                PostRepairUsedMaterial::where('id', $d->pum_id)->update(['export_status' => 1]);
            }
            $price = $d->price;
            if ($d->charge_type == 2) {
                $price = $d->price_first;
            }
            $discount_value = Order::get_discount_value($buy_date, $d->statistic_time);

            // 取出旧物料编码数据
            if (strlen($d->code) < 11) {
                $snToOldCodeArr[$d->sn]['code'][] = $d->code;
                $snToOldCodeArr[$d->sn]['title'][] = !empty($materialCaseArr[$d->sn . '_' . $d->material_id]) ? $materialCaseArr[$d->sn . '_' . $d->material_id] : '';
                $snToOldCodeArr[$d->sn]['price'][] = $price;
                $snToOldCodeArr[$d->sn]['is_charge'][] = Order::is_charge[$d->is_charge];
                $snToOldCodeArr[$d->sn]['charge_type'][] = Order::charge_type[$d->charge_type];
                $snToOldCodeArr[$d->sn]['amount'][] = $amount;
                $snToOldCodeArr[$d->sn]['staff_cast'][] = $staff_cast;
                $snToOldCodeArr[$d->sn]['accessory_cast'][] = $accessory_cast;
                $snToOldCodeArr[$d->sn]['pay_amount'][] = !empty($refundArr[$d->sn]) && $pay_amount > 0 ? (floatval($pay_amount) - floatval($refundArr[$d->sn])) : $pay_amount;
            }


            $row = [
                PayOrder::COM[$pay_com], //0
                $d->id,  //1
                $d->sn . ' ', //2
                $d->barcode . ' ', //3
                Order::STATUS[$d->status], //4
                Order::CONNECT[$d->connect], //5
                Order::in_period[$d->in_period],
                Order::has_screen_insurance[$d->bsi_status == 300 || $d->bsi_status == 400 ? 1 : 0],// 有无碎屏保 //6
                Order::used_screen_insurance[$d->bsi_status == 400 ? 1 : 0],// 是否使用碎屏保 //7
                $d->bsi_status == 300 || $d->bsi_status == 400 ? Order::is_direct_sales[$d->is_direct_sales] : '无碎屏保',//8
                Order::repeat_repair[$d->repeat_order],
                $d->repeat_remark,
                $d->pay_time, //9
                $d->statistic_time, //10
                $this->filter(trim($d->description)), //11
                !empty($materialCaseArr[$d->sn . '_' . $d->material_id]) ? $materialCaseArr[$d->sn . '_' . $d->material_id] : '', //12
                $d->name, //13
                $d->code . ' ', //14
                $d->old_code . ' ', //15
                $d->count, //16
                $price, //17
                Order::is_charge[$d->is_charge], //18
                Order::charge_type[$d->charge_type], //19
                $discount_value, //20
                $d->oe_discount, //21
                $d->mat_discount, //22
                ($d->oe_discount == 1 && $d->mat_discount == 1) ? $discount_value * $price : $price, //23
                $amount, //24
                $staff_cast, //25
                $accessory_cast, //26
                !empty($refundArr[$d->sn]) && $pay_amount > 0 ? (floatval($pay_amount) - floatval($refundArr[$d->sn])) : $pay_amount, //27
                $d->model_name, // 28
                Order::post_repair_type[$d->type], //29
                $mark[$d->mark], //30
                $export_status[$d->export_status], //31
            ];
            $formatData[] = $row;
            $i++;
        }

        // 旧编码统一转换 97004.00040 维修服务费
        foreach ($formatData as $key => $value) {
            if (strlen(trim($value[14])) < 11) {
                $sn = trim($value[2]);
                if (!empty($snToOldCodeArr[$sn])) {
                    $formatData[$key][13] = '维修服务费';
                    $formatData[$key][15] = $value[14];
                    $formatData[$key][14] = '97004.00040 ';
                }
                /* if (!empty($snToOldCodeArr[$sn])) {

                    // 价格叠加
                    $price = 0;
                    foreach ($snToOldCodeArr[$sn]['price'] as $k => $v) {
                        $price += $v;
                    }
                    $row = [
                        $value[0],
                        $value[1],
                        $value[2],
                        $value[3],
                        $value[4],
                        $value[5],
                        $value[6],
                        $value[7],
                        $value[8],
                        $value[9],
                        $value[10],
                        $value[11],
                        implode(',', $snToOldCodeArr[$sn]['title']),
                        '维修服务费',
                        '97004.00040 ',
                        implode(',', $snToOldCodeArr[$sn]['code']) . ' ',
                        $value[16],
                        $price,
                        implode('/', array_unique($snToOldCodeArr[$sn]['is_charge'])),
                        implode('/', array_unique($snToOldCodeArr[$sn]['charge_type'])),
                        '',
                        $value[21],
                        $value[22],
                        $price,
                        implode('/', array_filter($snToOldCodeArr[$sn]['amount'])),
                        implode('/', array_unique($snToOldCodeArr[$sn]['staff_cast'])),
                        implode('/', array_filter($snToOldCodeArr[$sn]['accessory_cast'])),
                        implode('/', array_filter($snToOldCodeArr[$sn]['pay_amount'])),
                        $value[28],
                        $value[29],
                        $value[30],
                        $value[31],
                    ];
                    $formatData[$key] = $row;
                    unset($snToOldCodeArr[$sn]);
                } else {
                    unset($formatData[$key]);
                } */
            }
        }

        // 补充(没有使用物料) 以换代修 快递费 物料编码也统一转换 97004.00040 维修服务费
        $data2 = DB::table('pay_order as po')->leftjoin('order as o', 'o.sn', '=', 'po.pr_sn')
            ->leftjoin('broken_screen_insurance as bsi', function ($j) {
                $j->on('bsi.barcode', '=', 'o.barcode')->whereIn('bsi.status', [300, 400]);
            })
            ->whereBetween('po.updated_at', [$start, $end])
            ->whereNotIn('po.pr_sn', $sns)
            ->where('po.is_paid', 1)
            ->orderby('o.id')
            ->select('po.com', 'o.id', 'o.sn', 'o.barcode', 'o.model_name', 'o.status', 'o.in_period',
                'o.repeat_order', 'o.repeat_remark', 'po.updated_at as pay_time',
                'o.accessory_amount', 'o.accessory_cast', 'o.staff_cast', 'o.pay_amount as amount', 'po.pay_amount as pay_amount',
                'o.updated_at_last', 'o.staff_cast', 'o.type', 'o.description', 'o.connect',
                'bsi.is_direct_sales', 'bsi.status as bsi_status'
            )
            ->get()
            ->toArray();
        foreach ($data2 as $key => $value) {
            if (!isset($value->sn)) {
                continue;
            }
            $sns[] = $value->sn;

            //检查是否只收了快递费
            $is_staff = $value->pay_amount == $value->amount && $value->amount == $value->staff_cast;

            $row = [
                PayOrder::COM[$value->com], //0 支付方式
                $value->id,  //1 订单ID
                $value->sn . ' ', //2 维修单号
                $value->barcode . ' ', //3 条码
                Order::STATUS[$value->status], //4 订单状态
                Order::CONNECT[$value->connect], //5 联系状态
                Order::in_period[$d->in_period],
                Order::has_screen_insurance[$value->bsi_status == 300 || $value->bsi_status == 400 ? 1 : 0],//6 有无碎屏保
                Order::used_screen_insurance[$value->bsi_status == 400 ? 1 : 0],//7 是否使用碎屏保
                $value->bsi_status == 300 || $value->bsi_status == 400 ? Order::is_direct_sales[$value->is_direct_sales] : '无碎屏保',//8 是否直营（碎屏保）
                Order::repeat_repair[$value->repeat_order],
                $value->repeat_remark,
                $value->pay_time, //9 付款时间
                $value->pay_time, //10 统计时间
                $this->filter(trim($value->description)), //11 细节描述
                '', // 12 配件故障
                $is_staff ? '快递费用' : '维修服务费', //13 物料名称
                '97004.00040 ', //14 新物料编码
                '', //15 旧物料编码
                1, //16 数量
                $value->pay_amount, //17 单价
                $is_staff ? '快递费用' : '收费', //18 是否收费
                '客服价格', //19 收费类型
                '', //20 配件折扣
                '', //21 订单是否打折
                '', //22 配件是否打折
                $value->pay_amount, //23 配件折后价
                $value->amount, //24 总费用
                $value->staff_cast, //25 快递费
                $value->accessory_cast, //26 配件收取费用
                !empty($refundArr[$value->sn]) && $value->pay_amount > 0 ? (floatval($value->pay_amount) - floatval($refundArr[$value->sn])) : $value->pay_amount, //27 实际付费
                $value->model_name, //28 机型
                Order::post_repair_type[$value->type], // 29 寄修渠道
                '检测添加', //30 物料添加类型
                '已导出', //31 是否已导出
            ];
            $formatData[] = $row;
        }

        return $formatData;
    }

    /**
     * 导出物料2
     */
    private function formatData2($start, $end, $material_code)
    {
        $formatData = [];
        $data = DB::table('pr_used_material as pum')
            ->leftjoin('machine_accessory_tree as mat', 'mat.id', '=', 'pum.mat_id')
            ->leftjoin('material as m', 'm.id', '=', 'pum.material_id')
            ->leftjoin('order as o', 'pum.pr_sn', '=', 'o.sn')
            ->leftjoin('order_extend as oe', 'o.sn', '=', 'oe.sn')
            // ->leftjoin('material_group as mg', 'm.group', '=', 'mg.group_code')
            // ->where([['o.sn', '=', '20210420102232519012']])
            ->whereBetween('pum.statistic_time', [$start, $end])
            ->where([['o.status', '>=', 600], ['m.group', '=', $material_code]])
            ->orderby('o.id')
            ->select('o.pay_com', 'o.id', 'o.sn', 'o.barcode', 'o.model_name', 'o.pay_time',
                'o.accessory_amount', 'o.accessory_cast', 'o.staff_cast', 'o.amount', 'o.pay_amount',
                'o.updated_at_last', 'o.staff_cast', 'o.type', 'o.description', 'o.connect',
                'o.amount', 'o.pay_amount', 'oe.discount as oe_discount',
                'mat.discount as mat_discount', 'm.name', 'm.price_first',
                'm.price', 'pum.count', 'm.code', 'm.old_code', 'm.from', 'pum.id as pum_id', 'pum.is_charge',
                'pum.charge_type', 'pum.created_at', 'pum.mark', 'pum.statistic_time'
            // 'mm.title'
            )
            ->get()
            ->toArray();

        $sns1 = array();
        foreach ($data as $d1) {
            $sns1[] = $d1->sn;
        }

        // 获取物料对应的故障数组
        $materialCaseList = DB::table('pr_material as pm')
            ->leftjoin('machine_malfunction as mm', 'mm.id', '=', 'pm.malfunction_id')
            ->whereIn('pm.pr_sn', $sns1)
            ->get();

        $materialCaseArr = array();
        foreach ($materialCaseList as $key => $value) {
            $newKey = $value->pr_sn . '_' . $value->material_id;
            $materialCaseArr[$newKey] = $value->title;
        }

        $mark = [0 => "检测添加", 1 => "维修添加"];
        $i = 1;
        $id = -1;
        $buy_date = null;
        $snToOldCodeArr = array();
        foreach ($data as $d) {

            $amount = $d->amount;
            $pay_amount = $d->pay_amount;
            $accessory_cast = $d->accessory_cast;
            $staff_cast = $d->staff_cast;
            if ($id != $d->id) {
                $id = $d->id;
                $buy_date = Order::get_buy_date($d->barcode);
            } else {
                $amount = '';

                $pay_amount = '';

                $accessory_cast = '';
            }
            $price = $d->price;
            if ($d->charge_type == 2) {
                $price = $d->price_first;
            }
            $discount_value = Order::get_discount_value($buy_date, $d->statistic_time);

            // 取出旧物料编码数据
            if (strlen($d->code) < 11) {
                $snToOldCodeArr[$d->sn]['code'][] = $d->code;
                $snToOldCodeArr[$d->sn]['title'][] = !empty($materialCaseArr[$d->sn . '_' . $d->material_id]) ? $materialCaseArr[$d->sn . '_' . $d->material_id] : '';
                $snToOldCodeArr[$d->sn]['price'][] = $price;
                $snToOldCodeArr[$d->sn]['is_charge'][] = Order::is_charge[$d->is_charge];
                $snToOldCodeArr[$d->sn]['charge_type'][] = Order::charge_type[$d->charge_type];
                $snToOldCodeArr[$d->sn]['amount'][] = $amount;
                $snToOldCodeArr[$d->sn]['staff_cast'][] = $staff_cast;
                $snToOldCodeArr[$d->sn]['accessory_cast'][] = $accessory_cast;
                $snToOldCodeArr[$d->sn]['pay_amount'][] = $pay_amount;
            }

            $row = [

                PayOrder::COM[$d->pay_com], //0
                $d->id,//1
                $d->sn . ' ',//2
                $d->barcode . ' ',//3
                Order::STATUS[$d->status],//4
                Order::CONNECT[$d->connect],//5
                $d->pay_time,//6
                $d->statistic_time,//7
                $this->filter(trim($d->description)), //8 细节描述
                $material_code,//9
                !empty($materialCaseArr[$d->sn . '_' . $d->material_id]) ? $materialCaseArr[$d->sn . '_' . $d->material_id] : '', //10
                $d->name,//11
                $d->code . ' ',//12
                $d->old_code . ' ',//13
                $d->count,//14
                $price,//15
                Order::is_charge[$d->is_charge],//16
                Order::charge_type[$d->charge_type],//17
                $discount_value, //18
                $d->oe_discount, //19
                $d->mat_discount, //20
                ($d->oe_discount == 1 && $d->mat_discount == 1) ? $discount_value * $price : $price, //21
                $amount,//22
                $staff_cast,//23
                $accessory_cast,//24
                $pay_amount,//25
                $d->model_name,//26
                Order::post_repair_type[$d->type],//27
                $mark[$d->mark],//28
            ];
            $formatData[] = $row;
            $i++;

        }

        // 旧编码统一转换 97004.00040 维修服务费
        foreach ($formatData as $key => $value) {
            if (strlen(trim($value[12])) < 11) {
                $sn = trim($value[2]);
                if (!empty($snToOldCodeArr[$sn])) {

                    // 价格叠加
                    $price = 0;
                    foreach ($snToOldCodeArr[$sn]['price'] as $k => $v) {
                        $price += $v;
                    }
                    $row = [
                        $value[0],
                        $value[1],
                        $value[2],
                        $value[3],
                        $value[4],
                        $value[5],
                        $value[6],
                        $value[7],
                        $value[8],
                        $value[9],
                        implode(',', $snToOldCodeArr[$sn]['title']) . ' ',
                        '维修服务费',
                        '97004.00040 ',
                        implode(',', $snToOldCodeArr[$sn]['code']),
                        $value[14],
                        $price,
                        implode('/', array_unique($snToOldCodeArr[$sn]['is_charge'])),
                        implode('/', array_unique($snToOldCodeArr[$sn]['charge_type'])),
                        '',
                        $value[19],
                        $value[20],
                        $price,
                        implode('/', array_filter($snToOldCodeArr[$sn]['amount'])),
                        implode('/', array_unique($snToOldCodeArr[$sn]['staff_cast'])),
                        implode('/', array_filter($snToOldCodeArr[$sn]['accessory_cast'])),
                        implode('/', array_filter($snToOldCodeArr[$sn]['pay_amount'])),
                        $value[26],
                        $value[27],
                        $value[28],
                    ];
                    $formatData[$key] = $row;
                    unset($snToOldCodeArr[$sn]);
                } else {
                    unset($formatData[$key]);
                }
            }
        }

        return $formatData;
    }

    /**
     * Desc: 对字符串中的emoji进行过滤操作
     * 去掉字符串里的表情
     * @param string $str 要过滤的字符串
     * @return string
     */
    private function filter($str)
    {
        $strArr = [];
        for ($i = 0; $i < mb_strlen($str); $i++) {
            $subStr = trim(mb_substr($str, $i, 1));
            if (strlen($subStr) <= 3 && !empty($subStr)) {
                array_push($strArr, mb_substr($str, $i, 1));
            }
        }

        $implodeStr = implode('', $strArr);

        return empty($implodeStr) ? str_random(strlen($str)) : $implodeStr;
    }
}
