<?php
/**
 * Created by PhpStorm.
 * User: 1
 * Date: 2017/5/15
 * Time: 14:02
 */

namespace App\Admin\Extensions\Tools;


use Encore\Admin\Grid\Tools\AbstractTool;
use Encore\Admin\Admin;
use Illuminate\Support\Facades\Request;
use App\Models\Order;

class InformTool extends AbstractTool
{
    public $type;

    public function __construct($type)
    {
        $this->type = $type;
    }

    protected function script()
    {
        $url = Request::fullUrlWithQuery(['inform_status' => '_status_2']);

        return <<<EOT

$('input:radio.user-gender2').change(function () {

    var url = "$url".replace('_status_2', $(this).val());

    $.pjax({container:'#pjax-container', url: url });

});

EOT;
    }

    public function render()
    {
        Admin::script($this->script());

        if($this->type == 2){ // 已检测

            $countIsTell = Order::where('status' , '=' , 500)->count();
            $countNoTell = Order::where('status' , '=' , 490)->count();

            $options2 = [
                500 => '已知会:'. $countIsTell,
                490 => '未知会:'. $countNoTell,
            ];

        } 
        else if($this->type == 3){  // 未检测

            $countIsTell = Order::where('status' , '=' , 480)->count();
            $countNoTell = Order::where('status' , '=' , 410)->count();

            $options2 = [
                480 => '已知会:'. $countIsTell,
                410 => '未知会:'. $countNoTell,
            ];

        }
        else if($this->type == 4){  // 待维修

            $count = Order::join('order_extend as oe' , 'order.sn' , 'oe.sn')->where('order.status' , Order::PAY_FINISH)->where('oe.order_mark' , 2)->count();

            $options2 = [
                2 => '缺少配件:'. $count,
            ];

        }else { // 已收货

            $countIsTell = Order::where('type' , '!=' , 2)->where('status' , '=' , 410)->count();
            $countNoTell = Order::where('type' , '!=' , 2)->where('status' , '=' , 400)->count();

            $options2 = [
                410 => '已知会:'. $countIsTell,
                400 => '未知会:'. $countNoTell,
            ];
        }
        
       

        return view('admin.inform', compact('options2'));
    }
}