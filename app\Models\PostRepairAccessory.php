<?php
/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 2019/7/8
 * Time: 20:12
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class PostRepairAccessory extends Model
{
    protected $table = "pr_accessory";
    protected $fillable = ['mar_id', 'count'];

    protected static function boot()
    {
        parent::boot();

        self::saved(function () {
            DB::table('pr_accessory')
                ->where('mar_id', 0)->delete();
        });
    }
}