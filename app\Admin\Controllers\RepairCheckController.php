<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\Sms;
use App\Admin\Extensions\Tags;
use App\Admin\Extensions\Tools\AccessoryLackNoticeTool;
use App\Admin\Extensions\Tools\AgingTestNoticeTool;
use App\Admin\Extensions\Tools\Button;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Admin\Extensions\Tools\InformTool;
use App\Admin\Extensions\CloudCustomer;
use App\Admin\Extensions\MessagePush;
use App\Admin\Extensions\DeviceUnbind;

use App\User;

use App\Models\AgentOrder;
use App\Models\AgentOrderCorrelation;
use App\Models\BrokenScreenInsurance;
use App\Models\BrokenScreenInsuranceUsage;
use App\Models\Damage;
use App\Models\Machine;
use App\Models\MachineAccessory;
use App\Models\MachineAccessoryRelation;
use App\Models\MachineAccessoryTree;
use App\Models\MachineMalfunction;
use App\Models\Material;
use App\Models\Order;
use App\Models\OrderExtend;
use App\Models\OrderRemark;
use App\Models\OrderOldAddress;
use App\Models\PostRepairAccessory;
use App\Models\PostRepairMalfunction;
use App\Models\PostRepairMaterial;
use App\Models\PostRepairOptionalAccessory;
use App\Models\PostRepairUsedMaterial;
use App\Models\OrderLog;
use App\Models\OrderLog2;
use App\Models\AdminUsersYkf;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;

use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use GuzzleHttp\Client;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\MessageBag;
use Encore\Admin\Grid\Tools\BatchAction;

use function PHPSTORM_META\type;

class RepairCheckController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修订单检测');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修订单检测');
            $content->description('检测');

            $content->body($this->form($id)->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修订单::寄修订单检测');
            $order = Order::where(['id' => $id])->first();
            $pr_material = DB::table('pr_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_material.mat_id')
                ->leftjoin('machine_malfunction', 'machine_malfunction.id', '=', 'pr_material.malfunction_id')
                ->leftjoin('material', 'material.id', '=', 'pr_material.material_id')
                ->where('pr_material.pr_sn', $order['sn'])
                ->select('machine_accessory_tree.title as title', 'machine_malfunction.title as malfunction',
                    'material.price as price', 'pr_material.count as count', 'pr_material.is_charge')
                ->get()
                ->toArray();
            $agent_order = null;
            if ($order->type == 3) {
                $agent_order = AgentOrder::rightJoin('agent_order_correlation as aoc', 'agent_order.sn', '=', 'aoc.agent_order_sn')
                    ->where([['aoc.order_sn', $order->sn]])->select('agent_order.*')->first();
            }

            $content->body(view('admin/repair_check/view', compact('order', 'pr_material', 'agent_order')));

        });
    }

    /**
     * 列表 查看按钮
     */
    public function view2($id)
    {
        return Admin::content(function (Content $content) use ($id) {
            $param = $id;
            $id = explode('-', $param)[0];

            // 额外参数,用于隐藏左侧菜单栏样式
            $ext_param = '';
            if (!empty(explode('-', $param)[1])) {
                $ext_param = explode('-', $param)[1];
            }

            $content->header('寄修订单检测 -- 订单详情');

            $order = Order::where(['id' => $id])->first();
            $order_old_address = OrderOldAddress::where(['sn' => $order->sn])->first();
            $order_extend = OrderExtend::where(['sn' => $order->sn])->first();

            if (!empty($order_extend)) {
                $order_extend->order_priority_span = Order::order_priority_span($order_extend->order_priority);
            }

            $pr_material = DB::table('pr_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_material.mat_id')
                ->leftjoin('machine_malfunction', 'machine_malfunction.id', '=', 'pr_material.malfunction_id')
                ->leftjoin('material', 'material.id', '=', 'pr_material.material_id')
                ->where('pr_material.pr_sn', $order['sn'])
                ->select('machine_accessory_tree.title as title', 'machine_malfunction.title as malfunction',
                    'material.price as price', 'material.price_first', 'pr_material.price_in',
                    'pr_material.count as count', 'pr_material.charge_type')
                ->get()
                ->toArray();
            $pr_used_material = DB::table('pr_used_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_used_material.mat_id')
                ->leftjoin('material', 'material.id', '=', 'pr_used_material.material_id')
                ->where('pr_used_material.pr_sn', $order['sn'])
                ->select('material.name as name', 'material.price as price', 'material.price_first', 'pr_used_material.count as count',
                    'material.code as code', 'material.old_code as old_code', 'material.specification as specification',
                    'material.from as from', 'pr_used_material.is_charge', 'pr_used_material.charge_type', 'pr_used_material.price_in')
                ->get()
                ->toArray();
            $pr_oa = PostRepairOptionalAccessory::getShowListBySn($order->sn);
            $agent_order = null;
            if ($order->type == 3) {
                $agent_order = AgentOrder::rightJoin('agent_order_correlation as aoc',
                    'agent_order.sn', '=', 'aoc.agent_order_sn')
                    ->where([['aoc.order_sn', $order->sn]])->select('agent_order.*')->first();
            }

            // 获取通话记录
            $callLog = DB::table('call_log')
                ->join('admin_users_ykf as auy', 'call_log.agent', 'auy.seat_number')
                ->join('admin_users as au', 'auy.uid', 'au.id')
                ->where('call_log.bill_id', $order->sn)
                ->groupBy('call_log.call_sheet_id')
                ->orderBy('call_log.id', 'desc')
                ->get();

            // 获取云客服账号密码
            $usersYkfInfo = AdminUsersYkf::where('uid', Admin::user()->id)->first();

            // 获取所有维修知会备注
            $allOrderRemarkInfo = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
                ->where('order_remark.type', 3)
                ->where('order_remark.sn', $order->sn)
                ->orderBy('order_remark.id', 'desc')
                ->select('au.name', 'order_remark.*')
                ->get();

            foreach ($allOrderRemarkInfo as $key => $value) {
                if ($value['status'] == 410) {
                    $allOrderRemarkInfo[$key]['status_name'] = '未检测,未知会';
                } else {
                    $allOrderRemarkInfo[$key]['status_name'] = Order::STATUS[$value['status']];
                }
                $allOrderRemarkInfo[$key]['connect_name'] = Order::CONNECT[$value['connect']];
            }

            // 此页面可以拨打电话
            $is_ykf_repair = 1;

            // 获取通话记录
            $callLog1 = DB::table('call_log')
                ->join('admin_users_ykf as auy', 'call_log.agent', 'auy.seat_number')
                ->join('admin_users as au', 'auy.uid', 'au.id')
                ->where('call_log.bill_id', $order->sn)
                ->groupBy('call_log.call_sheet_id')
                ->orderBy('call_log.id', 'desc')
                ->get();

            // 获取最新的客服知会记录
            $orderRemarkInfo1 = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
                ->where('order_remark.sn', $order->sn)
                ->where('order_remark.type', 1)
                ->orderBy('order_remark.id', 'desc')
                ->select('au.name', 'order_remark.created_at', 'order_remark.remark', 'order_remark.status')
                ->first();

            if (!empty($orderRemarkInfo1)) {
                $orderRemarkInfo1['status_name'] = Order::STATUS[$orderRemarkInfo1['status']];
            }

            // 获取所有客服备注
            $allOrderRemarkInfo1 = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
                ->where('order_remark.type', 1)
                ->where('order_remark.sn', $order->sn)
                ->orderBy('order_remark.id', 'desc')
                ->select('au.name', 'order_remark.*')
                ->get();

            foreach ($allOrderRemarkInfo1 as $key => $value) {
                $allOrderRemarkInfo1[$key]['status_name'] = Order::STATUS[$value['status']];
                $allOrderRemarkInfo1[$key]['connect_name'] = Order::CONNECT[$value['connect']];
            }

            // 此页面可以记录维修时间
            $can_record_repair_time = 1;

            $content->body(view('admin/post_repair/view', compact('order', 'pr_material', 'pr_used_material',
                'pr_oa', 'order_old_address', 'order_extend', 'agent_order',
                'callLog', 'is_ykf_repair', 'usersYkfInfo', 'allOrderRemarkInfo',
                'ext_param', 'allOrderRemarkInfo1', 'orderRemarkInfo1', 'callLog1', 'can_record_repair_time')));

        });
    }

    public function printf($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('打印检测报告');
            $order = Order::where(['id' => $id])->first();
            $post_malfunction = DB::table('pr_malfunction')
                ->join('machine_malfunction', 'machine_malfunction.id', '=', 'pr_malfunction.malfunction_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_malfunction.title')->toArray();
            $malfunction = implode('，', $post_malfunction);
            $post_accessory = DB::table('pr_accessory')
                ->join('machine_accessory_relation', 'machine_accessory_relation.id', '=', 'pr_accessory.mar_id')
                ->join('machine_accessory', 'machine_accessory.id', '=', 'machine_accessory_relation.accessory_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_accessory.title')->toArray();
            $accessory = implode('，', $post_accessory);
            $content->body(view('admin/repair_check/print', compact('order', 'malfunction', 'accessory')));

        });
    }

    /**
     * 打印检测报告
     */
    public function print_order($id)
    {
        $order = Order::where(['order.id' => $id])
            ->leftJoin('admin_users as au', 'order.check_man', '=', 'au.id')
            ->leftJoin('pr_staff as ps', 'order.check_man', '=', 'ps.user_id')
            ->select('order.*', 'au.name as check_man_name', 'ps.wechat_id')
            ->first();
        $category = $order->machine_type->category;
        $category_is_watch = stripos($category->name, '手表');
        if (is_int($category_is_watch) && $category_is_watch > 0) {
            $order->deal_remark_add = '（为不影响正常使用，请家长手机端的“电话手表”APP升级至最新版本，并重新绑定手表。）';
        }

        $post_malfunction = DB::table('pr_malfunction')
            ->join('machine_malfunction', 'machine_malfunction.id', '=', 'pr_malfunction.malfunction_id')
            ->where('pr_sn', '=', $order['sn'])->pluck('machine_malfunction.title')->toArray();
        $malfunction = implode('，', $post_malfunction);
        $post_accessory = DB::table('pr_accessory')
            ->join('machine_accessory_relation', 'machine_accessory_relation.id', '=', 'pr_accessory.mar_id')
            ->join('machine_accessory', 'machine_accessory.id', '=', 'machine_accessory_relation.accessory_id')
            ->where('pr_sn', '=', $order['sn'])->pluck('machine_accessory.title')->toArray();
        $accessory = implode('，', $post_accessory);

        $pr_material = DB::table('pr_material')
            ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_material.mat_id')
            ->leftjoin('machine_malfunction', 'machine_malfunction.id', '=', 'pr_material.malfunction_id')
            ->leftjoin('material', 'material.id', '=', 'pr_material.material_id')
            ->where('pr_material.pr_sn', $order['sn'])
            ->select('machine_accessory_tree.title as title', 'machine_malfunction.title as malfunction',
                'material.price as price', 'material.price_first as price_first',
                'pr_material.count as count', 'pr_material.is_charge', 'pr_material.charge_type')
            ->get()
            ->toArray();
        if (!empty($order->check_man_name)) {
            $order->check_man_name = mb_substr($order->check_man_name, 0, 1) . '工 (' . $order->wechat_id . ')';
        }
        $pr_oa = PostRepairOptionalAccessory::getShowListBySn($order->sn);
        $pr_oa = array_filter($pr_oa, function ($item) {
            return $item->status == PostRepairOptionalAccessory::STATUS_NORMAL;
        });
        return view('admin/repair_check/print',
            compact('order', 'malfunction', 'accessory', 'pr_material', 'pr_oa'));
    }

    public function cancel(Request $request)
    {
        foreach (Order::find(request()->get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form());
        });
    }

    public function freeze()
    {
        $ids = Request::get('ids');
        $id = Request::get('id');
        if (!empty($ids)) {
            foreach (Order::find($ids) as $post) {
                $post->freeze = 1;
                $post->save();
            }
        }
        if (!empty($id)) {
            $post = Order::find($id);
            $post->freeze = 1;
            $post->save();
        }
    }

    /**
     * 获取维修配件
     */
    public function repair_check_material()
    {
        $qid = Request::get('q');
        $type = Request::get('type');
        $charge_type = Request::get('charge_type');
        $data = DB::table('machine_accessory_material_relation as mamr')
            ->join('material', 'mamr.material_id', '=', 'material.id')
            ->leftjoin('machine_accessory_tree as mat', 'mat.id', '=', 'mamr.mat_id')
            ->where('mamr.mat_id', $qid)
            ->where('material.price', '>', 0)
            ->where(function ($query) {
                $query->orwhere([['material.from', '=', 1], ['material.quantity', '>', 0]])
                    ->orwhere([['material.from', '=', 0]]);
            })
            ->select('material.id as id', 'material.name as name',
                'material.price as price', 'material.price_first as price_first', 'material.specification as specification',
                'material.quantity as quantity', 'material.code as code', 'material.old_code as old_code',
                'material.from as from', 'mat.discount')
            ->get()->toArray();
        // $type = Order::where('id', '=', $qid)->value('type');

        $ret = array();
        if ($data) {
            foreach ($data as $d) {
                $ret[$d->id]['id'] = $d->id;
                $from = $d->from == 1 ? '新仓库' : '旧仓库';
                //价格一定要放到最后，不然js计算价格时会有问题
                if ($charge_type == 2) {
                    $ret[$d->id]['text'] = $d->name . $d->specification . '|编码:' . $d->code . '|旧编码:' . $d->old_code .
                        '|仓库:' . $from . '|库存 : ' . $d->quantity . '|折扣(0-否|1-是) : ' . $d->discount . '|价格 : ' . $d->price_first;
                } else {
                    $ret[$d->id]['text'] = $d->name . $d->specification . '|编码:' . $d->code . '|旧编码:' . $d->old_code .
                        '|仓库:' . $from . '|库存 : ' . $d->quantity . '|折扣(0-否|1-是) : ' . $d->discount . '|价格 : ' . $d->price;
                }
            }
        }
        return $ret;
        // return Agenc ::Second()->where('pid', $cityId)
        //     ->get([DB::raw('id as id'), DB::raw('name as text')])
        //     ->prepend(['id' => 0, 'text' => '选择二级代理[可不选,代表只归属于一级代理]']);
    }

    public function repair_check_malfunction()
    {
        $qid = Request::get('q');
        $data = DB::table('machine_accessory_malfunction_relation as mamr')
            ->join('machine_malfunction as m', 'mamr.malfunction_id', '=', 'm.id')
            ->where('mamr.mat_id', $qid)
            ->select('m.id as id', 'm.title as title')
            ->get()->toArray();
        $ret = array();
        if ($data) {
            foreach ($data as $d) {
                $ret[$d->id]['id'] = $d->id;
                $ret[$d->id]['text'] = $d->title;
            }
        }
        return $ret;
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<js

        $("input[name='sn']").focus();

        // 打印检测报告
        $('.print').click(function(){
            var id = $(this).attr('value');
            layer.open({
                  type: 2,
                  title: '检测报告（双击:切换配件列表显示；单击图片或按P键:启动打印）',
                  shadeClose: true,
                  shade: 0.8,
                  area: ['600px', '90%'],
                  content: 'repair_check/print/'+id //iframe的url
            });
        });

        // 冻结订单-询问框
        $('.freeze').click(function(){
            var id = $(this).attr('value');
            layer.confirm('确定冻结该订单？', {
                btn: ['确认冻结', '取消']
            }, function(){
                $.get('/admin/repair_check_freeze?id='+id, function(result){
                    layer.msg('冻结成功'+result, {time:500},function(){
                        $('.grid-refresh').click();
                    });
                });
            }, function(){
                layer.msg('取消冻结');
            });
        });

        // 订单标注
        $('.order_mark').click(function(){
            var param = $(this).attr('value');
            $.ajax({
                url: 'repair_check/order_mark?' + param,
                method: 'get',
                success: function () {
                    $.pjax.reload('#pjax-container');
                    toastr.success('操作成功');
                }
            });
        });

        // 知会备注
        $('.cloud_cunstomer_remark').click(function(e){
            sn = e.currentTarget.dataset.sn
            var html = '<div class="box box-info">'+
                        '<div class="box-header with-border">维修知会备注<\/div>'+
                        '<div class="box-body" style="min-height:300px;min-width:500px" >'+
                            '<table class="table">'+
                                '<tr>'+
                                    '<th style="text-align: center;" >时间<\/th>'+
                                    '<th style="text-align: center;" >订单状态<\/th>'+
                                    '<th style="text-align: center;" >操作人<\/th>'+
                                    '<th style="text-align: center;" >备注<\/th>'+
                                '</tr>'+
                                '<tbody id="tbody"><\/tbody>'+
                            '<\/table>'+
                        '<\/div>'+
                        '<\/div>'+
                        '<div style="display:flex">'+
                            '<input style="margin:1%" name="remarkRadioRepairs" value="需要再次通知" type="radio"> 需要再次通知'+
                            '<input style="margin:1%" name="remarkRadioRepairs" value="已沟通未协商好" type="radio"> 已沟通未协商好'+
                            '<input style="margin:1%" name="remarkRadioRepairs" value="顾客有争议" type="radio"> 顾客有争议'+
                            '<input style="margin:1%" name="remarkRadioRepairs" value="终端有争议" type="radio"> 终端有争议'+
                            '<input style="margin:1%" name="remarkRadioRepairs" value="等待回复" type="radio"> 等待回复'+
                        '<\/div>';
            html += '<div style="display:flex">'+
                        '<input id="remarkInput" placeholder="请输入备注,不超过100字" size="70">'+
                        '<a class="btn btn-sm btn-primary" type="button" id="saveRemark" >确定<\/a>'+
                    '<\/div>';


            // 获取备注内容
            $.ajax({
                url: '/admin/post_connect/getRemarkInfo',
                data: {
                    sn: sn,
                    type: 3
                },
                type: 'get',
                dataType: 'json',
                success: function(res){
                    var tbodyHtml = '';
                    $('#tbody').html('');
                    $.each(res , function(i , v){
                        tbodyHtml +='<tr>'+
                                        '<td style="text-align: center;">'+v.created_at+'</td>'+
                                        '<td style="text-align: center;">'+v.status_name+'</td>'+
                                        '<td style="text-align: center;">'+v.name+'</td>'+
                                        '<td style="text-align: center;">'+v.remark+'</td>'+
                                    '</tr>';
                    })

                    $('#tbody').append(tbodyHtml);
                }
            })
            $.fancybox.open(html);

            $("input[name='remarkRadioRepairs']").change(function(){
                $('#remarkInput').val($("input[name='remarkRadioRepairs']:checked").val());
            })

            $('#saveRemark').click(function(){
                remark = $('#remarkInput').val()
                if(!remark){
                    layer.msg('备注不能为空');
                    return false;
                }
                $.ajax({
                    url: '/admin/repair_check/setRepairRemark',
                    data: {
                        sn: sn,
                        remark: remark
                    },
                    type: 'get',
                    dataType: 'json',
                    success: function(res){
                        layer.msg(res.info);
                        if(res.status == 1){
                            var newTbodyHtml = '<tr>'+
                                                    '<td style="text-align: center;color:#29db6f">'+res.data.created_at+'<\/td>'+
                                                    '<td style="text-align: center;color:#29db6f">'+res.data.status_name+'<\/td>'+
                                                    '<td style="text-align: center;color:#29db6f">'+res.data.name+'<\/td>'+
                                                    '<td style="text-align: center;color:#29db6f">'+res.data.remark+'<\/td>'+
                                                '<\/tr>';

                            $('#tbody').append(newTbodyHtml);
                            $('.grid-refresh').click();
                        }
                    }
                })
            })
        })

        // 设备解绑
        $('.unbind').click(function(){
            var id = $(this).attr('value');
            layer.confirm('该设备确定解绑吗？', {
                btn: ['确认', '取消']
            }, function(){
                $.ajax({
                    url: '/admin/repair_check/deviceUnbind',
                    data: {
                        id: id,
                    },
                    dataType: 'json',
                    type: 'get',
                    success: function(res){
                        // console.log(res)
                        layer.msg(res.info);  
                        if(res.status == 1){
                            toastr.success(res.info);
                            // $('.grid-refresh').click();
                            layer.closeAll()
                        } 
                    },
                    error:function(res){
                        console.log(res)
                    }
                })
            }, function(){
                layer.msg('取消解绑');
            });
        });

        // 知会推送
        $('.push_msg_to_app').on('click', function(){
            var id = $(this).attr('value');
            var msg = "是否向用户推送知会备注信息?";
            var res = confirm(msg);
            if (res == true){
                $.ajax({
                    method: 'get',
                    url: '/admin/customer_service_manage/push_msg_to_app',
                    data: {
                        id: id,
                        type:3
                    },
                    success: function (res) {
                        layer.msg(res.info)
                        if(res.status == 1){
                            toastr.success(res.info);
                            setTimeout(function(){
                                window.location.reload();
                            },1000)
                        } else {
                            toastr.error(res.info);
                        }
                    }
                });    
            }
        });
   
js;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
            // $grid->model()->where('status', '>=', 200);
            // 快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => [['status', '>=', Order::COME_SURE_IS_TELL], ['status', '<=', Order::CHECK_FINISH_IS_TELL]],
                ],
                1 => [
                    'name' => '未检测',
                    'param' => [[DB::Raw('status in (' . Order::COME_SURE_IS_TELL . ',' . Order::NO_CHECK_IS_TELL . ')'), '1']],
                ],
                2 => [
                    'name' => '已检测',
                    'param' => [[DB::Raw('status in (' . Order::CHECK_FINISH . ',' . Order::CHECK_FINISH_IS_TELL . ')'), '1']],
                ],
            ];
            // 角色判断
            if (Admin::user()->inRoles(['pr_check'])) {
                foreach ($option as $key => $item) {
                    $option[$key]['param'] = array_merge($item['param'], [['check_man', '=', Admin::user()->id]]);
                }
            }
            // 筛选条数
            foreach ($option as $key => $value) {
                $option[$key]['count'] = Order::where($value['param'])->count();
            }

            $grid->disableCreation();
            $grid->disableExport();
            $grid->tools(function ($tools) use ($option) {
                //自定义状态快捷筛选按钮
                $tools->append(new QuickPickTool($option));

                if (Request::get('quick_pick') == 2) { // 已检测才有筛选按钮
                    // 自定义状态快捷筛选按钮
                    $tools->append(new InformTool(2));
                }

                if (Request::get('quick_pick') == 1) { // 未检测也有筛选按钮
                    // 自定义状态快捷筛选按钮
                    $tools->append(new InformTool(3));
                }

                // 批量操作按钮
                if (Admin::user()->can('batch_cloud_customer_repair')) {
                    $tools->batch(function ($batch) {
                        $batch->add('批量维修知会', new BatchReplicate(1));
                    });
                }

                $tools->batch(function ($batch) {
                    $batch->add('取消订单', new Cancel());
                    $batch->add('短信通知（老化测试）', new AgingTestNoticeTool());
                    $batch->add('短信通知（配件缺少）', new AccessoryLackNoticeTool());
                });
            });

            // 根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');
            }

            // 根据自定义状态按钮搜索数据
            if (in_array(Request::get('inform_status'), [Order::CHECK_FINISH, Order::CHECK_FINISH_IS_TELL]) && Request::get('quick_pick') == 2) {
                $status = Request::get('inform_status');
                $grid->model()->where('status', '=', $status)->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');
            }

            // 根据自定义状态按钮搜索数据
            if (in_array(Request::get('inform_status'), [Order::COME_SURE_IS_TELL, Order::NO_CHECK_IS_TELL]) && Request::get('quick_pick') == 1) {
                $status = Request::get('inform_status');
                $grid->model()->where('status', '=', $status)->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');
            }

            $grid->model()->where($option[-1]['param'])->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('come_exp_sn', '快递单号')->setPlaceholder('光标移到此处扫码');
                $filter->like('sn', '维修订单号')->setPlaceholder('光标移到此处扫码');
                $filter->like('barcode', 'S/N码');
                $filter->is('status', '订单状态')
                    ->select(Order::STATUS);
                $filter->equal('model_name', '产品型号')->select(
                    Machine::orderBy('name')->pluck('name', 'name')->all()
                );

                $data = DB::table('order as o')->join('admin_users as au', 'o.check_man', '=', 'au.id')
                    ->select('o.check_man', 'au.name')->distinct('check_man')->get()->toArray();
                $map = array();
                foreach ($data as $key => $item) {
                    $map[$item->check_man] = $item->name;
                }
                unset($data);
                $filter->equal('check_man', '检测员')->select(
                    $map
                );

                $filter->between('create_at', '订单提交日期')->datetime();
                $filter->equal('type', '寄修类型')->select(Order::post_repair_type);
                $filter->equal('order_extend.order_mark', '订单标注')->select(Order::ORDER_MARK);
            });

            //表格显示列
            $grid->id('ID')->sortable();
            $grid->uid('UID')->sortable();
            Order::order_priority_column($grid);
            $grid->sn('寄修订单编号');
            //$grid->come_exp_sn('快递单号');
            $grid->column('快递单号')->display(function () {
                return $this->come_exp_sn . '<br/>' . $this->come_exp_com;
            });
            //$grid->model_name('机型');
            //$grid->barcode('S/N码');
            $grid->column('S/N码-机型')->display(function () {
                return $this->barcode . '<br/>' . $this->model_name;
            });
            //$grid->phone('用户联系方式');
            //$grid->name('联系人');
            $grid->column('联系人')->display(function () {
                return $this->name . '<br/>' . $this->phone;
            });
            $grid->receive_time('签收时间')->display(function ($time) {
                $receive_time = date('Y-m-d', strtotime($time . " + 7 days"));
                $now = date('Y-m-d');
                if (date('Y-m-d', strtotime($time . " + 7 days")) < $now &&
                    $now < date('Y-m-d', strtotime($time . " + 15 days"))) {
                    return '<span style="color:#e5cda4">' . $time . '</span>';
                } elseif ($now > date('Y-m-d', strtotime($time . " + 15 days"))) {
                    return '<span style="color:#ff0000">' . $time . '</span>';
                }
                return '<span style="color:#a1ec96">' . $time . '</span>';
            })->sortable();
            $grid->updated_at('最后操作时间')->sortable();
            $grid->status('订单状态')->display(function ($status) {

                // 获取是否知会状态
                $isTell = OrderExtend::where('sn', $this->sn)->value("is_tell");
                if ($isTell == 1) {
                    $isTellName = "（需要知会）";
                } else {
                    $isTellName = "（无需知会）";
                }

                $s = Order::STATUS;
                if (($status == Order::CHECK_FINISH || $status == Order::CHECK_FINISH_IS_TELL) && $this->connect == 3) {
                    return '<span style="color:red">已弃修</span>';
                }
                if ($status == Order::COME_SURE_IS_TELL) {
                    return '未检测,未知会' . $isTellName;
                }
                if (array_key_exists($status, $s)) {
                    if ($status == Order::CHECK_FINISH) {
                        return $s[$status] . $isTellName;
                    } else {
                        return $s[$status];
                    }
                } else {
                    return "————";
                }
            });
            $grid->cloud_cunstomer_remark('维修知会备注')->display(function () {
                $remark = DB::table('order_remark')->where('sn', $this->sn)->where('type', 3)->orderBy('created_at', 'desc')->value('remark');
                $remark = $remark ? '<u>' . $remark . '</u>' : '<u style="color:#dd1144">Empty</u>';
                return '<a href="JavaScript:void(0);" data-sn="' . $this->sn . '" class="cloud_cunstomer_remark">' . $remark . '</a>';
            });
            $grid->check_user()->name('检测人')->display(function ($name) {
                if ($name == '')
                    return '———';
                return $name;
            });
            $grid->type('寄修类型')->display(function ($value) {
                return Order::post_repair_type[$value];
            });
            $grid->order_extend()->order_mark('订单标注')->display(function ($order_mark) {
                $str = '未知';
                if (array_key_exists($order_mark, Order::ORDER_MARK)) {
                    $str = Order::ORDER_MARK[$order_mark];
                }
                switch ($order_mark) {
                    case Order::ORDER_MARK_NORMAL:
                        return '<span>' . $str . '</span>';
                    case Order::ORDER_MARK_MISSING_ACCESSORY:
                        // 获取所需配件
                        $prMaterialArr = DB::table('pr_material')
                            ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_material.mat_id')
                            ->where('pr_sn', $this->sn)
                            ->pluck('machine_accessory_tree.title')
                            ->toArray();
                        $prMaterialStr = implode(',', $prMaterialArr);
                        return '<span  style="color: red">' . $str . ': ' . $prMaterialStr . '</span>';
                    default:
                        return '<span  style="color: red">' . $str . '</span>';
                }
            });

            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $status = $actions->row->status;
                $freeze = $actions->row->freeze;

                $v = $actions->row->id;
                $html = '<a href="javascript:void(0);" class="unbind" value="' . $v . '"> 解绑 </a>';
                $actions->append($html);

                if ($freeze) {
                    $actions->append('<span>已冻结</span>');
                } else {
                    $v = $actions->row->id;
                    $html = '<a href="javascript:void(0);" class="freeze" value="' . $v . '"><span style="color:blue"> 冻结 </span></a>';
                    // $actions->append($html);
                }

                $id = $actions->row->id;
                $html = '<a href="javascript:void(0);" class="push_msg_to_app" value="' . $id . '"> 知会推送 </a>';
                $actions->append($html);

                if ($status == Order::COME_SURE_IS_TELL || $status == Order::NO_CHECK_IS_TELL) {
                    $c = 'repair_check/' . $actions->getKey();
                    $html = '<a href="' . $c . '/edit"><span style="color:orange"> 检测 </span></a>';
                    $actions->append($html);
                } elseif ($status == Order::CHECK_FINISH || $status == Order::CHECK_FINISH_IS_TELL) {
                    $c = 'repair_check/' . $actions->getKey();
                    $html = '<a href="' . $c . '/edit"><span style="color:green"> 修改 </span></a>';
                    $actions->append($html);
                } else {
                    $actions->append('<span>检测 </span>');
                }
                $c = 'repair_check/view2/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue"> 查看 </span></a>';
                $actions->append($html);

                if ($status < Order::CHECK_FINISH) {
                    $actions->append('<span>打印</span>');
                } else {
                    $v = $actions->row->id;
                    $html = '<a href="javascript:void(0);" class="print" value="' . $v . '"><span style="color:blue"> 打印检测报告 </span></a>';
                    $actions->append($html);
                }

                $sn = $actions->row->sn;
                // $accessory_mark = $actions->row->order_extend['accessory_mark'];
                $order_mark = $actions->row->order_extend['order_mark'];
                if ($order_mark != 1) {
                    $param = 'sn=' . $sn . '&order_mark=1';
                    $html = '<a href="javascript:void(0);" class="order_mark" value="' . $param . '"><span style="color:orange"> 【等待通知】 </span></a>';
                    $actions->append($html);
                } else {
                    $param = 'sn=' . $sn . '&order_mark=0';
                    $html = '<a href="javascript:void(0);" class="order_mark" value="' . $param . '"><span style="color:blue"> 【取消等待】 </span></a>';
                    $actions->append($html);
                }

                if ($order_mark != 2) {
                    $param = 'sn=' . $sn . '&order_mark=2';
                    $html = '<a href="javascript:void(0);" class="order_mark" value="' . $param . '"><span style="color:orange"> 【缺少配件】 </span></a>';
                    $actions->append($html);
                } else {
                    $param = 'sn=' . $sn . '&order_mark=0';
                    $html = '<a href="javascript:void(0);" class="order_mark" value="' . $param . '"><span style="color:blue"> 【材料正常】 </span></a>';
                    $actions->append($html);
                }

                if ($order_mark != 3) {
                    $param = 'sn=' . $sn . '&order_mark=3';
                    $html = '<a href="javascript:void(0);" class="order_mark" value="' . $param . '"><span style="color:orange"> 【故障不稳定】 </span></a>';
                    $actions->append($html);
                } else {
                    $param = 'sn=' . $sn . '&order_mark=0';
                    $html = '<a href="javascript:void(0);" class="order_mark" value="' . $param . '"><span style="color:blue"> 【故障稳定】 </span></a>';
                    $actions->append($html);
                }
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id = null)
    {
        $ar_title_js = MachineAccessoryTree::get_ar_title_js();
        $ar_need_title_js = MachineAccessoryTree::get_ar_need_title_js();
        $ar_need_category_js = MachineAccessoryTree::get_ar_need_category_js();
        $staff_default = Order::express_default();
        $oa_status_cancel = PostRepairOptionalAccessory::STATUS[PostRepairOptionalAccessory::STATUS_CANCEL];

        $optional_accessory_style = <<<css
    #optional_accessory_table_in td,th {
        padding: 10px;
    }
css;
        $optional_accessory_style_str = base64_encode($optional_accessory_style);
        $optional_accessory_script = <<<js
    // 获取定位元素
    let n_m_hr_top = $('#has-many-repair_material').prev();
    let n_m_title = $('#has-many-repair_material').prev().prev();
    let n_oa_title_in = $('#optional_accessory_title');
    let n_oa_title_raw = n_oa_title_in.parent().hide().parent().parent();
    // 设置标题行
    let n_oa_title_new = n_m_title.clone();
    let n_oa_title_text = n_oa_title_new.find('h4.pull-right');
    n_oa_title_text.parent().css('text-align', 'right');
    n_oa_title_text.html(n_oa_title_in.text());
    n_oa_title_raw.before(n_oa_title_new);
    n_oa_title_raw.before(n_m_hr_top.clone());
    n_oa_title_raw.after('<hr>');
    // 读取数据
    let n_oa_json = $('#optional_accessory_json');
    const oa_json_str = n_oa_json.text();
    const oa_list = JSON.parse(oa_json_str);
    const oa_status_cancel = '$oa_status_cancel';
    let oa_all_cancel = oa_list.every(function (d) {
        return d.status == oa_status_cancel;
    });
    let oa_some_cancel = oa_list.some(function (d) {
        return d.status == oa_status_cancel;
    });
    const oa_price_total = oa_list.reduce(function (b, d) {
        return b + parseFloat(d.price_sum);
    }, 0);
    const oa_price_normal = oa_list.reduce(function (b, d) {
        return b + (d.status == oa_status_cancel ? 0 : parseFloat(d.price_sum));
    }, 0);
    const oa_count_total = oa_list.reduce(function (b, d) {
        return b + d.count;
    }, 0);
    if (!oa_list || oa_list.length <= 0) {
        n_oa_title_raw.hide();
        oa_all_cancel = false;
        oa_some_cancel = false;
    }
    console.log({ oa_list, oa_price_total, oa_price_normal, oa_count_total, oa_all_cancel, oa_some_cancel });
    let oa_title_brackets = oa_all_cancel ? '已取消' : ('￥' + oa_price_total + '，共计' + oa_count_total + '件');
    n_oa_title_text.html(n_oa_title_in.text() + '<br/>（' + oa_title_brackets + '）');
    // 设置hidden-form
    $('.optional_accessory_amount').val(oa_price_total);
    $('.optional_accessory_cast').val(oa_price_normal);
    // 设置表格
    let n_oa_table = $('#optional_accessory_table_in');
    let n_oa_table_thead = n_oa_table.find('thead');
    let n_oa_table_thead_tr = n_oa_table_thead.find('tr');
    let n_oa_table_tbody = n_oa_table.find('tbody');
    let n_oa_table_tfoot_tr = n_oa_table.find('tfoot tr');
    let oa_table_columns = [
        { name: '配件分类', key: 'oac_name' },
        { name: '配件名称', key: 'oa_name' },
        { name: '物料编码', key: 'm_code' },
        { name: '物料名称', key: 'm_name' },
        { name: '购买数量', key: 'count' },
        { name: '购买单价', key: 'price' },
        { name: '合计', key: 'price_sum' },
        { name: '状态', key: 'status' },
    ];
    // 设置表格-设置表头
    oa_table_columns.forEach(oa_table_set_thead_th);
    function oa_table_set_thead_th(col) {
        let th_str = '<th>' + col.name + '<\/th>';
        n_oa_table_thead_tr.append(th_str);
    }
    // 设置表格-设置内容
    for (var ri in oa_list) {
        let tr_str = oa_table_get_tbody_tr_td(oa_table_columns, oa_list[ri]);
        n_oa_table_tbody.append(tr_str);
    }
    function oa_table_get_tbody_tr_td(columns, row) {
        let td_str = columns.reduce(function (b, c) {
            return b + '<td>' + row[c.key] + '<\/td>';
        }, '');
        return '<tr>' + td_str + '<\/tr>';
    }
    // 设置表格-设置合计行
    tfoot_td_price_str = oa_price_total + '元';
    tfoot_td_price_str = oa_some_cancel ?
        ('<span style="text-decoration: line-through;">' + tfoot_td_price_str + '<\/span>&nbsp;<span>' + oa_price_normal + '元<\/span>') :
        ('<span>' + tfoot_td_price_str + '<\/span>');
    tfoot_td_str = '<td colspan="' + oa_table_columns.length + '">' +
                   '自选购配件价格：' +
                   tfoot_td_price_str +
                   '<\/td>';
    n_oa_table_tfoot_tr.append(tfoot_td_str);
    // 设置表格-设置样式
    const oa_style = decodeURIComponent(escape(atob('$optional_accessory_style_str')));
    $('head').append('<style type="text/css">' + oa_style + '</style>');
js;
        Admin::script($optional_accessory_script);

        $auto_ar_mat_script = <<<js

    function get_machine_category() {
        let category_label = $('.control-label').filter(':contains(机型品类)');
        let category_box = category_label.next().find('.box-body');
        let category_text = category_box[0].innerText;
        console.log({fn: 'get_machine_category', category_label, category_box, category_text});
        return category_text;
    }
    
    function get_in_ar_need_category() {
        let machine_category_text = get_machine_category();
        let ar_need_category = $ar_need_category_js;
        let in_ar_need_category = ar_need_category.some(item => machine_category_text.indexOf(item) !== -1);
        console.log({fn: 'get_in_ar_need_category', machine_category_text, ar_need_category, in_ar_need_category});
        return in_ar_need_category;
    }
    
    function get_ar_repeat() {
        let ar_repeat = Number($('#ar_repeat').text());
        console.log({fn: 'get_ar_repeat', ar_repeat});
        return ar_repeat
    }

    function get_mat_ar_index() {
        let ar_title = $ar_title_js;
        let material_form_groups = $('.has-many-repair_material-form.fields-group');
        let mat_select_node = material_form_groups.find('.repair_material.mat_id:first')[0];
        let mat_options = mat_select_node.options; 
        let ar_index = -1;
        for(var i = 0; i < mat_options.length; i++) {
            let is_ar_title = ar_title.some(title => mat_options[i].innerText.indexOf(title) !== -1);
            if (is_ar_title) {
                ar_index = i;
                break;
            }
        }
        console.log({
            fn: 'get_mat_ar_index',
            material_form_groups,
            mat_select_node,
            mat_options,
            ar_index
        });
        return ar_index;
    }

    function get_mat_selected_texts() {
        let material_form_groups = $('.has-many-repair_material-form.fields-group:visible');
        let mat_select_nodes = material_form_groups.find('.repair_material.mat_id');
        let mat_selected_options = mat_select_nodes.map((index, node) => node.selectedOptions[0]);
        let mat_selected_texts = mat_selected_options.map((index, option) => option.innerText).toArray();
        console.log({
            fn: 'get_mat_selected_texts',
            material_form_groups,
            mat_select_nodes,
            mat_selected_options,
            mat_selected_texts
        });
        return mat_selected_texts;
    }

    function get_has_ar_need_title(mat_selected_texts) {
        let ar_need_title = $ar_need_title_js;
        let has_ar_need_title = ar_need_title.some(title => mat_selected_texts.some(item => item.indexOf(title) !== -1));
        console.log({fn: 'get_has_ar_need_title', mat_selected_texts, ar_need_title, has_ar_need_title});
        return has_ar_need_title;
    }

    function get_has_ar_title(mat_selected_texts) {
        let ar_title = $ar_title_js;
        let has_ar_title = ar_title.some(title => mat_selected_texts.some(item => item.indexOf(title) !== -1));
        console.log({fn: 'get_has_ar_title', mat_selected_texts, ar_title, has_ar_title});
        return has_ar_title;
    }

    function add_ar(mat_ar_index) {
        if (mat_ar_index === -1) {
            return;
        }
        let material_add_button = $('#has-many-repair_material .add');
        material_add_button.click();
        let material_form_group = $('.has-many-repair_material-form.fields-group:last');
        let mat_select_node = material_form_group.find('.repair_material.mat_id')[0];
        mat_select_node.selectedIndex = mat_ar_index;
        console.log({
            fn: 'add_ar',
            mat_ar_index,
            material_add_button,
            material_form_group,
            mat_select_node
        });
        $(mat_select_node).trigger('change');
    }

    function check_auto_ar_mat() {
        console.group('check_auto_ar_mat');
        let in_ar_need_category = get_in_ar_need_category();
        let ar_repeat = get_ar_repeat();
        if(in_ar_need_category && ar_repeat > 0) {
            let mat_selected_texts = get_mat_selected_texts();
            let has_ar_need_title = get_has_ar_need_title(mat_selected_texts);
            if (has_ar_need_title) {
                let has_ar_title = get_has_ar_title(mat_selected_texts);
                if (has_ar_title) {
                    console.log({fn: 'check_auto_ar_mat', msg: '已有拆机检测费'});
                } else {
                    console.log({fn: 'check_auto_ar_mat', msg: '需添加拆机检测费'});
                    let mat_ar_index = get_mat_ar_index();
                    add_ar(mat_ar_index);
                }
            }
        }
        console.groupEnd();
    }
    $(document).on('change', '.mat_id', check_auto_ar_mat);
js;
        Admin::script($auto_ar_mat_script);


        $material_check_func = <<<js
    // 表头
    var html1 = '<tr>'+
                    '<td align="left" width="30%">配件</td>'+
                    '<td align="left" width="20%">更换故障</td>'+
                    '<td align="left" width="15%">价格</td>'+
                    '<td align="left" width="35%" colspan="4">数量</td>'+
                '</tr>';
    var html2 = '<tr>'+
                    '<td align="left" width="">物料名称规格</td>'+
                    '<td align="left" width="">物料编码</td>'+
                    '<td align="left" width="">旧编码</td>'+
                    '<td align="left" width="10%">仓库</td>'+
                    '<td align="left" width="10%">价格</td>'+
                    '<td align="left" width="5%">数量</td>'+
                    '<td align="left" width="10%">是否收费</td>'+
                    '<td align="left" width="">在本订单的实收价格</td>'+
                '</tr>';
    var html3 ='';

    // 各配件行
    $('.material_id:visible').each(function() {
        // 维修配件
        let title = $(this).parent().parent().prev().prev().find("option:selected").text();
        title = $.trim(title);
        // 配件故障
        let malfunction = $(this).parent().parent().prev().find("option:selected").text();
        // 价格处理
        let optionText = $(this).find("option:selected").text();
        // 仓库
        let from = optionText.split('|库存 :')[0].split('|仓库:')[1];
        // 旧编码
        let old_code = optionText.split('|仓库:')[0].split('|旧编码:')[1];
        // 编码
        let code = optionText.split('|旧编码:')[0].split('|编码:')[1];
        // 物料名称规格
        let name = optionText.split('|编码:')[0];
        // 价格
        let price = optionText.split('价格 : ')[1];
        // 数量
        let count = $(this).parent().parent().next().find('#count').val();
        // 是否收费
        let is_charge = $(this).parent().parent().next().next().find("option:selected").text();
        is_charge = $.trim(is_charge);
        // 指定价格
        let price_in = $(this).parent().parent().next().next().next().next()[0].value.replace(/,/g, "");
        if (!malfunction || !title ||!optionText) {
            html1 = html1 +
                    '<tr style="color:red;">'+
                        '<td>物料异常</td>'+
                        '<td>物料异常</td>'+
                        '<td>物料异常</td>'+
                        '<td colspan="4">'+count+'</td>'+
                    '</tr>';
            html2 = html2 +
                    '<tr style="color:red;">'+
                        '<td align="left" width="">物料异常</td>'+
                        '<td align="left" width="">物料异常</td>'+
                        '<td align="left" width="">物料异常</td>'+
                        '<td align="left" width="">物料异常</td>'+
                        '<td align="left" width="">物料异常</td>'+
                        '<td align="left" width="">'+count+'</td>'+
                        '<td align="left" width="">'+is_charge+'</td>'+
                        '<td align="left" width="">物料异常</td>'+
                    '</tr>';
        } else {
            html1 = html1 +
                    '<tr>'+
                        '<td>'+title+'</td>'+
                        '<td>'+malfunction+'</td>'+
                        '<td>'+price+'</td>'+
                        '<td colspan="4">'+count+'</td>'+
                    '</tr>';
                    
            html2 = html2 +
                    '<tr>'+
                        '<td align="left" width="">'+name+'</td>'+
                        '<td align="left" width="">'+code+'</td>'+
                        '<td align="left" width="">'+old_code+'</td>'+
                        '<td align="left" width="">'+from+'</td>'+
                        '<td align="right" width="">'+price+'</td>'+
                        '<td align="center" width="">'+count+'</td>'+
                        '<td align="left" width="">'+is_charge+'</td>'+
                        '<td align="right" width="">'+
                            (price_in && price_in > 0 ? price_in : price) +
                        '</td>'+
                    '</tr>';
        }
        
    });
    
    // 实际收取配件费用
    var accessory_cast = $('#accessory_cast').val();
    var accessory_in_ar = $('#accessory_in_ar').val();
    // 总配件费用
    var staff_cast =  $('#staff_cast').val();
    var amount = $('#amount').val();
    var pay_amount = $('#pay_amount').val();
    var amount_in_ar = $('#amount_in_ar').val();

    // 开启弹窗
    let content =
        '<div>'+
            '<table style="margin-top: 0px">'+
                // '<caption align="center" style="text-align: center; font-size:20px;">'+
                // '请确认相关配件库存及价格是否有误</caption>'+
                '<tbody>'+
                    '<tr>'+
                        '<th align="left" width="">更换配件:</th>'+
                    '</tr>'+html1+
                '</tbody>'+
            '</table>'+
            '<table style="margin-top: 10px">'+
                '<tbody>'+
                    '<tr>'+
                        '<th align="left" width="">更换物料:</th>'+
                    '</tr>'+html2+
                '</tbody>'+
            '</table>'+
            '<table style="margin-top: 10px">'+
                '<tbody>'+
                    '<tr>'+
                        '<th align="center" width="30%">维修的配件费用:</th>'+
                        '<td colspan="6">'+accessory_cast+'</td>'+
                    '</tr>'+
                    '<tr>'+
                        '<th align="center" width="30%">弃修时配件费用:</th>'+
                        '<td colspan="6">'+accessory_in_ar+'</td>'+
                    '</tr>'+
                    '<tr>'+
                        '<th align="center" width="30%">快递费用:</th>'+
                        '<td colspan="6">'+staff_cast+'</td>'+
                    '</tr>'+
                    '<tr>'+
                        '<th align="center" width="30%">总费用:</th>'+
                        '<td colspan="6">'+amount+'</td>'+
                    '</tr>'+
                    '<tr>'+
                        '<th align="center" width="30%">维修时需支付金额:</th>'+
                        '<td colspan="6">'+pay_amount+'</td>'+
                    '</tr>'+
                    '<tr>'+
                        '<th align="center" width="30%">弃修时需支付金额:</th>'+
                        '<td colspan="6">'+amount_in_ar+'</td>'+
                    '</tr>'+
                '</tbody>'+
            '</table>'+
        '</div>';
    var index = layer.open({
        area: ['800px'], //宽高
        fixed: false,
        shadeClose: true,
        title: '请确认相关配件库存及价格是否有误',
        content: content,
        btn: ['返回'],
        yes: function(index, msg) {
            layer.close(index);
        }
    });
    return false;
js;
        $material_check_func_str = base64_encode($material_check_func);

        $script = <<<js

            // 配件选项变更
            $(document).on('change', ".mat_id", function () {
                // 自动变更是否收费 判断仅弃修时收费
                var mat_text = this.selectedOptions[0].text;
                var ar_title = $ar_title_js;
                var is_charge_dom = $(this).closest('.fields-group').find(".is_charge");
                if (ar_title.filter(x => mat_text.indexOf(x) != -1).length > 0) {
                    $(is_charge_dom).val(2).trigger('change');
                } else {
                    $(is_charge_dom).val(1).trigger('change');
                }
                // 自动变更物料
                var target = $(this).closest('.fields-group').find(".material_id");
                var type = $('.type').val();
                var charge_type = $(this).parent().parent().prev().find('.charge_type').val();
                $.get("/admin/repair_check_material?q="+this.value+"&type="+ type + "&charge_type="+charge_type, 
                    function (data) {
                        target.find("option").remove();
                        defaultdata = [{"id":0,"text":"请选择"}];
                        setdata = $.map(data, function (d) {
                            d.id = d.id;
                            d.text = d.text;
                            return d;
                        });
                        data = setdata;
                        $(target).select2({
                            data: data
                        }).trigger('change');
                    });
            });

            $(document).on('change', ".charge_type", function () {
                var target = $(this).closest('.fields-group').find(".material_id");
                var type = $('.type').val();
                var charge_type = $(this).val();
                var mat_id = $(this).parent().parent().next().find('.mat_id').val();
                var target1 = $(this).closest('.fields-group').find(".is_charge");
                if (charge_type == 3) {
                    $(target1).val(0).trigger('change');
                } else {
                    $(target1).val(1).trigger('change');
                }
                if (mat_id != 0) {
                    $.get("/admin/repair_check_material?q=" + mat_id + "&type=" + type + "&charge_type=" + charge_type,
                        function (data) {
                            target.find("option").remove();
                            defaultdata = [{"id":0,"text":"请选择"}];
                            setdata = $.map(data, function (d) {
                                    d.id = d.id;
                                    d.text = d.text;
                                    return d;
                                });
                            data = setdata;
                            $(target).select2({
                                data: data
                            }).trigger('change');
                        });
                }
            });

            let staff_default = $staff_default;

            // 别问我为什么不直接操作模板div,是因为用了DocumentFragment,操作不了,不是我用的!
            $(document).ready(function() {
                var in_period = $('.in_period').val()
                if (in_period != '1') {
                    calculate();
                }

                function total(from) {
                    var in_period = $('.in_period').val();
                    var staff = Number($('#staff_cast').val().replace(/,/g, ""));
                    // var is_exchange_for_repair =  $('.order_extend_is_exchange_for_repair_.la_checkbox').is(":checked");
                    var is_exchange_for_repair =  $('.order_extend_is_exchange_for_repair_').val();
                    // var staff = 0;
                    var exp_cast = 0;
                    var accessory = Number($('#accessory_cast').val().replace(/,/g, ""));
                    var accessory_amount = Number($('#accessory_amount').val().replace(/,/g, ""));
                    var accessory_in_ar = Number($('#accessory_in_ar').val().replace(/,/g, ""));
                    var order_extend_balance_amount = Number($('#order_extend_balance_amount').val().replace(/,/g, ""));
                    // 如果是保内 且 不是手动改快递费引发的
                    if (in_period == 1 && from != 'staff_cast') {
                        // 配件总价格大于实际支付配件价格
                        if (accessory_amount > accessory) {
                            $('#staff_cast').val(0.00);
                        } else if (accessory_amount == accessory && staff == 0) {
                            calculateExpress();
                        }
                    }
                    // 如果是保外 且 不是手动改快递费引发的
                    if (in_period == 2 && from != 'staff_cast') {
                        calculateExpress();
                    }
                    var staff = Number($('#staff_cast').val().replace(/,/g, ""));
                    var ar_repeat = Number($('#ar_repeat').text());
                    var amount = 0;
                    var pay_amount = 0;
                    var amount_in_ar = 0;
                    // 多次弃修判断
                    var staff_ar_base = staff > 0 ? staff : staff_default;
                    if (ar_repeat > 0) { //弃修过
                        amount_in_ar = staff_ar_base * 2 + accessory_in_ar + exp_cast * 2;
                    } else { //没弃修过
                        amount_in_ar = staff_ar_base + accessory_in_ar + exp_cast;
                    }
                    // 如果是以换代修
                    if (is_exchange_for_repair == 1) {
                        var amount = staff + accessory_amount + exp_cast + order_extend_balance_amount + oa_price_normal;
                        var pay_amount = staff + accessory + exp_cast + order_extend_balance_amount + oa_price_normal;
                    } else {
                        var amount = staff + accessory_amount + exp_cast + oa_price_normal;
                        var pay_amount = staff + accessory + exp_cast + oa_price_normal;
                    }
                    $('#amount').val(amount);
                    $('#pay_amount').val(pay_amount);
                    $('#amount_in_ar').val(amount_in_ar);
                }

                // 快递费发生改变
                $(document).on('change', '.staff_cast', function() {
                    total('staff_cast');
                });
                // 差价是否改变
                $(document).on('change', '#order_extend_balance_amount', function() {
                    total();
                });
                // $(document).on('change', '.order_extend_is_exchange_for_repair_', function() {
                //     alert(1);
                //     total();
                // });
                // 是否以换代修
                $(document).on('change', '.order_extend_is_exchange_for_repair_', function() {
                    total();
                });

                function calculate() {
                    var barcode = $('.barcode').val();
                    var discount = $('.order_extend_discount_').val();
                    if (discount == 1) {
                        $.get('/admin/repair_check/discount_type?barcode=' + barcode,
                            function (data) {
                                calculatePrice(data);
                            });
                    } else {
                        calculatePrice(0);
                    }
                }

                // 统计配件价格
                function calculatePrice(discount_type) {
                    let totalPrice = 0;
                    let accessoryAmount = 0;
                    let accessoryInAr = 0;
                    $('.material_id:visible').each(function() {
                        let optionText = $(this).find("option:selected").text();
                        let material_type = optionText.split('|价格 : ')[0].split('|折扣(0-否|1-是) : ')[1];
                        // alert(discount_type);
                        let price = optionText.split('价格 : ')[1];
                        let count = $(this).parent().parent().next().find('#count').val();
                        let is_charge = $(this).parent().parent().next().next().find('.is_charge').val();
                        let price_in = $(this).parent().parent().next().next().next().next()[0].value.replace(/,/g, "");
                        // 总费用
                        if (material_type == 1) {
                            if (discount_type == 1) {
                                price = price * 0.8;
                            } else if(discount_type == 2) {
                                price = price * 0.85;
                            }
                        }

                        // 手动备注的价格
                        if (price_in > 0) {
                            price = price_in;
                        } else {
                            $(this).parent().parent().next().next().next().next()[0].value = price;
                            $(this).parent().parent().next().next().next().find('.box-body')[0].textContent = price;
                        }

                        accessoryAmount += price * count;
                        if (price == null) {
                            price = 0;
                        }
                        // 判断是否收费
                        if (is_charge == '1') {
                            totalPrice += price * count;
                        } else if (is_charge == '2') {
                            accessoryInAr += price * count;
                        }
                    });
                    // 实际收取维修配件费用
                    $('#accessory_cast').val(totalPrice);
                    // 总维修配件费用
                    $('#accessory_amount').val(accessoryAmount);
                    $('.hidden_amount').val(accessoryAmount);
                    // 弃修时需支付配件费用
                    $('#accessory_in_ar').val(accessoryInAr);

                    total();
                }
                // 计算快递费
                function calculateExpress() {
                    var id = $('.id').val();
                    var type = $('.type').val();
                    var reason = $('.reason').val();
                    var rb_come_exp_sn = $('.rb_come_exp_sn').val();
                    var come_exp_sn = $('.come_exp_sn').val();
                    var url_str = '/admin/repair_check/express?rb_come_exp_sn='+rb_come_exp_sn+'&come_exp_sn='+come_exp_sn+'&id='+id+'&type='+type+'&reason='+reason;
                    var success_func = function(data) {
                            if (data != '无法获取') {
                                $('#staff_cast').val(data);
                            } else {
                                $('#staff_cast').val(0);
                            }
                        };
                    /*
                    $.get('/admin/repair_check/express?rb_come_exp_sn='+rb_come_exp_sn+'&come_exp_sn='+come_exp_sn+'&id='+id+'&type='+type+'&reason='+reason, 
                        success_func);
                    */
                    $.ajax({
                        type : "get",
                        url : url_str,
                        async : false, //取消异步
                        success : success_func,
                    });
                }

                // 确认配件信息
                $(".material_b").on('click', new Function(decodeURIComponent(escape(atob('$material_check_func_str')))));

                // 下拉框变化
                $(document).on('change', '.material_id', function() {
                    $(this).parent().parent().next().next().next().next()[0].value = 0.00;
                    $(this).parent().parent().next().next().next().find('.box-body')[0].textContent = 0.00;
                    calculate();
                });
                // 是否收费改变
                $(document).on('change', '.is_charge', function() {
                    calculate();
                });
                // 材料类型
                $(document).on('change', '.material_type', function() {
                    calculate();
                });
                // 加减数量按钮点击
                $('.has-many-repair_material-forms').on('click', 'button', function() {
                    calculate();
                });
                // 移除按钮点击
                $(document).on('click', '.remove', function() {
                    calculate();
                });
                // 元件损坏还是人为损坏
                $(document).on('change', '.reason', function () {
                    var id = $('.id').val();
                    var type = $('.type').val();
                    var reason = $(this).val();
                    var rb_come_exp_sn = $('.rb_come_exp_sn').val();
                    var come_exp_sn = $('.come_exp_sn').val();
                    $.get('/admin/repair_check/express?rb_come_exp_sn='+rb_come_exp_sn+'&come_exp_sn='+come_exp_sn+'&id='+id+'&type='+type+'&reason='+reason, 
                        function (data) {
                            if (data != '无法获取') {
                                $('#staff_cast').val(data);
                            } else {
                                $('#staff_cast').val(0);
                            }
                            calculate();
                        });
                });
            });

            // 变更状态为已知会
            $('#edit_is_tell_repair').click(function(e) {
                var sn = e.currentTarget.dataset.sn
                var html = '<div class="box box-info">'+
                    '<div class="box-header with-border">维修知会<\/div>'+
                    '<div class="box-body" style="max-width:750px">'+
                        '<div class="form-group">'+
                            '<label class="control-label">维修知会<\/label>'+
                            '<input type="checkbox" class="status la_checkbox" name="repair_status" value="1"  /><label class="control-label">已知会<\/label>'+
                        '<\/div>'+
                        '<div class="form-group">'+
                            '<label for="order_remark_repair" class="control-label">知会备注<\/label>'+
                            '<div style="display:flex">'+
                                '<input style="margin:1%" name="remarkRadioRepairs" value="需要再次通知" type="radio"> 需要再次通知'+
                                '<input style="margin:1%" name="remarkRadioRepairs" value="已沟通未协商好" type="radio"> 已沟通未协商好'+
                                '<input style="margin:1%" name="remarkRadioRepairs" value="顾客有争议" type="radio"> 顾客有争议'+
                                '<input style="margin:1%" name="remarkRadioRepairs" value="终端有争议" type="radio"> 终端有争议'+
                                '<input style="margin:1%" name="remarkRadioRepairs" value="等待回复" type="radio"> 等待回复'+
                                '<input style="margin:1%" name="remarkRadioRepairs" value="未打电话" type="radio"> 未打电话'+
                                '<input style="margin:1%" name="remarkRadioRepairs" value="已打电话" type="radio"> 已打电话'+
                            '<\/div>'+
                            '<div class="input-group">'+
                                '<span class="input-group-addon"><i class="fa fa-pencil"><\/i><\/span>'+
                                '<input type="text" id="order_remark_repair" name="order_remark_repair" value="" class="form-control"  />'+
                            '<\/div>'+
                        '<\/div>'+
                        '<button class="btn btn-primary" id="is_tell_repair_save" >提交<\/button>'+
                    '<\/div>'+
                '<\/div>';
                $.fancybox.open(html);

                $("input[name='remarkRadioRepairs']").change(function(){
                    $('#order_remark_repair').val($("input[name='remarkRadioRepairs']:checked").val());
                })

                $('#is_tell_repair_save').click(function(){
                    order_remark = $('#order_remark_repair').val();
                    status = $("input[name='repair_status']:checked").val();
                    if(!order_remark){
                        order_remark = 'Empty'
                    }
                    if(status == 1){
                        $.ajax({
                            url: '/admin/repair_check/setOrderRemark',
                            data: {
                                sn: sn,
                                order_remark: order_remark,
                            },
                            dataType: 'json',
                            type: 'get',
                            success: function(res){
                                layer.msg(res.info)
                                if(res.status == 1){
                                    $('#edit_is_tell_repair').hide();
                                    parent.$.fancybox.close();
                                }
                            }
                        })
                    } else {
                        $.ajax({
                            url: '/admin/repair_check/setRepairRemark',
                            data: {
                                sn: sn,
                                remark: order_remark
                            },
                            type: 'get',
                            dataType: 'json',
                            success: function(res){
                                layer.msg(res.info);
                                if(res.status == 1){
                                    parent.$.fancybox.close();
                                }
                            },
                        })
                    }
                    
                })
            })
js;
        Admin::script($script);
        return Admin::form(Order::class, function (Form $form) use ($id) {
            $form->hidden('id');
            $form->display('name', '寄修人');
            $form->display('phone', '联系方式');
            $form->display('sn', '寄修订单号');
            // $form->display('barcode', '寄修条码条码');
            $form->display('model_name', '机器型号');
            $form->display('machine_type.category.name', '机型品类')->with(function () use ($form) {
                return $form->model()->machine_type->category->name;
            });
            $form->display('in_period', '保修状态')->with(function ($in_period) {
                return Order::in_period[$in_period];
            });
            $form->display('come_exp_type', '寄件方式')->with(function ($value) {
                return Order::come_exp_type[$value];
            });
            $form->hidden('in_period');
            $form->hidden('sn', '寄修订单号');
            $form->divider('');
            $form->select('reason', '受损状态')->options(Order::reason)->value(function ($reason) {
                return $reason;
            });
            // $form->text('deal', '维修方式');
            $form->divider('');

            $form->display('type', '寄修方式')->with(function ($value) {
                if ($value == 2) {
                    return '<span style="color:red">代理商寄修</span>';
                } elseif ($value == 3) {
                    return '<span style="color:red">终端代寄</span>';
                }
                return '<span style="color:red">用户寄修</span>';
            });
            $states = [
                'on' => ['value' => 1, 'text' => '是', 'color' => 'success'],
                'off' => ['value' => 0, 'text' => '否', 'color' => 'danger'],
            ];
            $form->divider();

            $html = <<<EOF
                    <span style="color:red;">以换代修关联保卡操作，谨慎操作！</span>&nbsp;
EOF;
            $form->html($html);
            // $form->switch('order_extend.warehouse_type', '旧机入仓')->states($states);
            $form->select('order_extend.is_exchange_for_repair', '是否以换代修')->options(Order::is_exchange_for_repair);
            $form->text('order_extend.new_barcode', '新机SN码');
            $form->select('order_extend.warehouse_type', '旧机入仓')->options(Order::warehouse_type);
            $form->currency('order_extend.balance_amount', '补充差价')->symbol('￥');
            $form->divider();
            $form->hidden('type');

            $barcode = Order::where('id', '=', $id)->value('barcode');
            $bsi = BrokenScreenInsurance::firstByBarcode($barcode);
            $has_screen_insurance = Order::where('id', '=', $id)->value('has_screen_insurance');
            $screen_insurance = Order::where('id', '=', $id)->value('used_screen_insurance');
            $in_si_period_status = Order::where('id', '=', $id)->value('in_si_period');
            $form->display('has_screen_insurance', '是否有碎屏保')->with(function ($value) {
                if ($value == 1) {
                    return '<span style="color:red">有碎屏保</span>';
                }
                return '无';
            });
            if ($has_screen_insurance == 1 && $bsi) {
                $form->display('in_si_period', '碎屏保状态')->with(function ($value) {
                    return Order::in_period[$value];
                });
                $form->display('碎屏保剩余次数')->with(function ($value) use ($bsi) {
                    return $bsi->insurance_times_remain . '次';
                });

                if ($screen_insurance == 0) {
                    if ($in_si_period_status == 1) {
                        if ($bsi->insurance_times_remain > 0) {
                            $used_options = [0 => '不使用'];
                            for ($i = 1; $i <= $bsi->insurance_times_remain; $i++) {
                                $used_options[$i] = '使用 ' . $i . ' 次';
                            }
                            $form->select('used_screen_insurance', '是否使用碎屏保')
                                ->options($used_options);
                        } else {
                            $html = '<span style="color: red">此机器碎屏保次数已用完</span>';
                            $form->html($html);
                        }
                    } else {
                        $html = '<span style="color: red">此机器碎屏保已过期无法使用</span>';
                        $form->html($html);
                    }
                } else {
                    $html = '<span style="color: red">此订单或机器已使用过碎屏保</span>';
                    $form->html($html);
                }
            }

            $form->hidden('has_screen_insurance');
            $form->hidden('in_si_period');
            $form->hidden('serial');

            $form->divider();
            $form->display('order_extend.discount', '是否打折')->with(function ($discount) {
                if ($discount == 1) {
                    return ' <span style="color: red">是</span>';
                }
                return '否';
            });
            $form->hidden('order_extend.discount');
            $form->select('repeat_order', '是否二次维修')->options(function ($v) {
                if (Admin::user()->inRoles(['repeat_order_edit'])) {
                    if ($v == 0) {
                        return [0 => "首次寄修"];
                    } else {
                        return [1 => "二次寄修", 2 => "二次返修"];
                    }
                } else {
                    if ($v == 0) {
                        return [0 => "首次寄修"];
                    } else if ($v == 1) {
                        return [1 => "二次寄修"];
                    } else if ($v == 2) {
                        return [2 => "二次返修"];
                    }
                }
            })->load('repeat_remark', '/admin/repair_check/repeat_remark');
            $form->multipleSelect('repeat_remark', '故障导致原因分析')
                ->options(function ($v) {
                    if ($this->repeat_order != 2) {
                        return ["主板不良" => "主板不良", "主控不良" => "主控不良",
                            "DDR不良" => "DDR不良", "数据IC不良" => "数据IC不良", "FPC类不良" => "FPC类不良",
                            "屏幕不良" => "屏幕不良", "电池不良" => "电池不良", "电阻不良" => "电阻不良",
                            "电容不良" => "电容不良", "电感不良" => "电感不良", "喇叭不良" => "喇叭不良",
                            "摄像头不良" => "摄像头不良", "芯片不良" => "芯片不良", "晶振不良" => "晶振不良",
                            "滤波器不良" => "滤波器不良", "使用环境因素导致" => "使用环境因素导致",
                            "天线不良" => "天线不良", "材料不良" => "材料不良",
                            "设计缺陷导致故障" => "设计缺陷导致故障", "用户人为因素导致故障" => "用户人为因素导致故障",
                            "检测未发现异常" => "检测未发现异常", "生产工艺不良导致故障" => "生产工艺不良导致故障",
                            "软件导致故障" => "软件导致故障", "充电设备不良" => "充电设备不良", 'TF卡不良导致故障' => 'TF卡不良导致故障',
                            "马达不良" => "马达不良",
                        ];
                    } else {
                        return ["主板不良" => "主板不良", "主控不良" => "主控不良",
                            "DDR不良" => "DDR不良", "数据IC不良" => "数据IC不良", "FPC类不良" => "FPC类不良",
                            "屏幕不良" => "屏幕不良", "电池不良" => "电池不良", "电阻不良" => "电阻不良",
                            "电容不良" => "电容不良", "电感不良" => "电感不良", "喇叭不良" => "喇叭不良",
                            "摄像头不良" => "摄像头不良", "芯片不良" => "芯片不良", "晶振不良" => "晶振不良",
                            "滤波器不良" => "滤波器不良", "使用环境因素导致" => "使用环境因素导致",
                            "天线不良" => "天线不良", "材料不良" => "材料不良",
                            "设计缺陷导致故障" => "设计缺陷导致故障", "用户人为因素导致故障" => "用户人为因素导致故障",
                            "检测未发现异常" => "检测未发现异常", "生产工艺不良导致故障" => "生产工艺不良导致故障",
                            "软件导致故障" => "软件导致故障", "充电设备不良" => "充电设备不良", 'TF卡不良导致故障' => 'TF卡不良导致故障',
                            "马达不良" => "马达不良",
                            "维修技能导致" => "维修技能导致"
                        ];
                    }

                });
            $form->display('barcode', '维修记录')->with(function ($value) use ($form, $id) {
                $data = $form->model()->where('barcode', $value)->where('id', '<>', $id)->get()->toArray();
                // dd($data);
                $str = '';
                if (count($data) > 0) {
                    foreach ($data as $i => $d) {
                        $str .= '<span style="color:red">最后更新时间：' . $d['updated_at'] . '，回寄时间：
                        ' . $d['updated_at_last'] . '</span>  &nbsp; <a href="/admin/post_repair/view/' .
                            $d['id'] . '">查看维修记录（返回请按F5刷新）</a>';
                        if ($i < count($data) - 1) {
                            $str .= '<br>';
                        }
                    }
                } else {
                    $str = '无';
                }
                return $str;
            });
            $form->display('ar_repeat', '该条码之前弃修订单数')->with(function ($ar_repeat) {
                if ($ar_repeat > 0) {
                    return '<span id="ar_repeat" style="color: red">' . $ar_repeat . '</span>';
                } else {
                    return '<span id="ar_repeat">' . $ar_repeat . '</span>';
                }
            });
            $prdmoBom = $this->prdmoBom($barcode);
            if ($prdmoBom) {
                $html = <<<EOF
                    <label><span style="color:red;">Bom清单：<a href="/admin/repair_check/boom_list/$barcode">点击查看详情</a></span>&nbsp;</label>
EOF;
                $form->html($html);
            }
            $form->multipleSelect('damages', '实际故障类型')
                ->options(Damage::where([['o.id', $id], ['mt.visibility', 1], ['mt.category_id', '<>', 0]])
                    ->leftJoin('machine_type as mt', 'mt.category_id', '=', 'damage.machine_category_id')
                    ->leftJoin('order as o', 'mt.model_id', '=', 'o.model_id')
                    ->pluck('damage.title', 'damage.id'));

            $form->display('audit_opinion', '审核备注');
            $form->divider();

            $sn = Order::where('id', '=', $id)->value('sn');
            $oas = $sn ? PostRepairOptionalAccessory::getShowListBySn($sn) : array();
            $oas = PostRepairOptionalAccessory::statusToTextInList($oas);
            $oas_json = json_encode($oas, JSON_UNESCAPED_UNICODE);
            $optional_accessory_html_title = '<div id="optional_accessory_title">自选配件列表</div>';
            $optional_accessory_html_oas = '<div id="optional_accessory_json">' . $oas_json . '</div>';
            $optional_accessory_html_hide = '<div id="optional_accessory_hide">' .
                $optional_accessory_html_title .
                $optional_accessory_html_oas .
                '</div>';
            $optional_accessory_html_table = <<<html
    <div id="optional_accessory_table">
        <table id="optional_accessory_table_in">
            <thead>
                <tr></tr>
            </thead>
            <tbody>
            </tbody>
            <tfoot>
                <tr></tr>
            </tfoot>
        </table>
    </div>
html;
            $form->html($optional_accessory_html_hide . $optional_accessory_html_table);

            $type = Order::where('id', '=', $id)->value('type');
            // dd($type);
            $now = date('Y-m-d H:i:s');
            $form->hasMany('repair_material', '维修配件列表',
                function (Form\NestedForm $form) use ($id, $type, $now) {
                    $modelId = Order::where('id', '=', $id)->value('model_id');
                    $pr_sn = Order::where('id', '=', $id)->value('sn');
                    $form->select('charge_type', '收费类型')->options(function ($value) use ($type) {
                        if ($type == 2) {
                            return Order::charge_type;
                        } else {
                            return [1 => '客户价格'];
                        }
                    });
                    $form->select('mat_id', '维修配件')->options(MachineAccessoryTree::model_options($modelId))->load('malfunction_id', admin_url('repair_check_malfunction'));
                    $form->select('malfunction_id', '配件故障')->options(function ($value) {
                        $data = MachineMalfunction::where('id', $value)->get()->toArray();
                        $ret = array();
                        if (count($data) > 0) {
                            foreach ($data as $d) {
                                $ret[$d['id']] = $d['title'];
                            }
                        }
                        return $ret;
                    });

                    $form->select('material_id', '维修物料')->options(function ($value) use ($pr_sn, $form, $type) {
                        $data = Material::leftjoin('machine_accessory_material_relation as mamr', 'mamr.material_id', '=', 'material.id')
                            ->leftjoin('machine_accessory_tree as mat', 'mat.id', '=', 'mamr.mat_id')
                            ->where('material.id', $value)
                            ->select('material.id as id', 'material.name as name',
                                'material.price as price', 'material.price_first as price_first', 'material.specification as specification',
                                'material.quantity as quantity', 'material.code as code', 'material.old_code as old_code',
                                'material.from as from', 'mat.discount')
                            ->get()->toArray();
                        $ret = array();
                        $charge_type = PostRepairMaterial::where([['pr_sn', '=', $pr_sn], ['material_id', '=', $value]])->value('charge_type');
                        if (count($data) > 0) {
                            foreach ($data as $d) {
                                $from = $d['from'] == 1 ? '新仓库' : '旧仓库';
                                if ($charge_type == 2) {
                                    $ret[$d['id']] = $d['name'] . $d['specification'] . '|编码:' . $d['code'] . '|旧编码:' . $d['old_code'] .
                                        '|仓库:' . $from . '|库存 :' . $d['quantity'] . '|折扣(0-否|1-是) : ' . $d['discount'] . '|价格 : ' . $d['price_first'];
                                } else {
                                    $ret[$d['id']] = $d['name'] . $d['specification'] . '|编码:' . $d['code'] . '|旧编码:' . $d['old_code'] .
                                        '|仓库:' . $from . '|库存 :' . $d['quantity'] . '|折扣(0-否|1-是) : ' . $d['discount'] . '|价格 : ' . $d['price'];
                                }

                            }
                        }
                        return $ret;
                    });
                    // $form->hidden('material_id');
                    $form->number('count', '数量')->default(1);
                    $form->select('is_charge', '是否收费')->options(Order::is_charge)->default(1);
                    // $form->select('material_type', '材料类型')->options([0=>'默认', 1=>'主板', 2=>'屏幕'])->help('如果寄修审核选了打折，这里选择了主板或屏幕时，过保1月8折，两月85折');

                    $form->display('price_in', '该配件在本订单的实收价格')
                        ->help('特殊打折的配件 请到【寄修管理】处修改价格，默认使用【维修物料】顾客价格。');
                    $form->hidden('price_in', '该配件在本订单的实收价格');
                    $form->hidden('created_at', '创建时间')->value($now);
                    $form->hidden('statistic_time', '统计时间')->value($now);
                    //$form->hidden('statistic_time', '统计时间');
                });

            // $form->text('order_extend.exchange_number', '更换主板后的序列号');
            // js 统计配件价格
            $form->hidden('optional_accessory_amount', '总自选配件价格');
            $form->hidden('optional_accessory_cast', '实际收取自选配件价格');
            $form->hidden('hidden_amount', '维修配件价格')->value(0.00);
            $form->currency('accessory_amount', '总维修配件价格')->symbol('￥');
            $form->currency('accessory_cast', '实际收取维修配件价格')->symbol('￥');
            $form->currency('accessory_in_ar', '弃修时收取配件价格')->symbol('￥');
            $form->divider();

            $form->hidden('rb_come_exp_sn');
            $form->hidden('come_exp_sn');
            $exp_default = [];
            // 如果是人为的  保内保外都要算快递费
            if ($id && $form->model()->find($id)->reason == 1) {
                $rb_exp_sn = $form->model()->find($id)->rb_come_exp_sn;
                $exp_sn = $form->model()->find($id)->come_exp_sn;
                $exp_cast = Express::query_order($rb_exp_sn, $exp_sn);
                // 用户为代理商寄修时分摊快递费
                if ($form->model()->find($id)->type == 3 && $exp_cast != '无法获取') {
                    $agent_order_sn = AgentOrderCorrelation::leftJoin('order', 'order.sn', '=', 'agent_order_correlation.order_sn')
                        ->where('order.id', $id)->value('agent_order_sn');
                    $count = AgentOrderCorrelation::leftJoin('order', 'order.sn', '=', 'agent_order_correlation.order_sn')
                        ->where([['agent_order_correlation.agent_order_sn', $agent_order_sn], ['order.come_exp_sn', $exp_sn]])->count();
                    if ($count > 0) {
                        // dd(intval($exp_cast));
                        $exp_cast = floatval($exp_cast) / $count;
                    }
                }

                if ($form->model()->find($id)->status < Order::CHECK_FINISH) {
                    $exp_default = ['value' => $exp_cast];
                }
            } else if ($id && $form->model()->find($id)->in_period !== 1) {
                $rb_exp_sn = $form->model()->find($id)->rb_come_exp_sn;
                $exp_sn = $form->model()->find($id)->come_exp_sn;
                $exp_cast = Express::query_order($rb_exp_sn, $exp_sn);
                // 用户为代理商寄修时分摊快递费
                if ($form->model()->find($id)->type == 3 && $exp_cast != '无法获取') {
                    $agent_order_sn = AgentOrderCorrelation::leftJoin('order', 'order.sn', '=', 'agent_order_correlation.order_sn')
                        ->where('order.id', $id)->value('agent_order_sn');
                    $count = AgentOrderCorrelation::leftJoin('order', 'order.sn', '=', 'agent_order_correlation.order_sn')
                        ->where([['agent_order_correlation.agent_order_sn', $agent_order_sn], ['order.come_exp_sn', $exp_sn]])->count();
                    if ($count > 0) {
                        // dd(intval($exp_cast));
                        $exp_cast = floatval($exp_cast) / $count;
                    }
                }

                if ($form->model()->find($id)->status < Order::CHECK_FINISH) {
                    $exp_default = ['value' => $exp_cast];
                }
                // $exp_cast = 15;
            } else {
                $exp_cast = 0;
            }
            // dd($exp_default);
            // 直接编辑快递费用
            $form->currency('staff_cast', '快递费用')->symbol('￥')->attribute($exp_default)->help('应收快递费用');
            // $form->hidden('staff_cast', '检测费用')->value($exp_cast);

            // $form->display('staff_cast', '检测费用')->value($exp_cast)->help('快递费用  '.$exp_cast. '元');
            $form->currency('amount', '总费用')->symbol('￥');
            $form->currency('pay_amount', '维修时需支付金额')->symbol('￥')->help('客户应该支付金额');
            $form->currency('amount_in_ar', '弃修时需支付金额')->symbol('￥')->help('客户弃修时收取');
            $form->divider();

            // 未知会才有的按钮
            $status = Order::where('id', '=', $id)->value('status');
            $sn = Order::where('id', '=', $id)->value('sn');
            if ($status == Order::COME_SURE_IS_TELL || $status == Order::CHECK_FINISH) {
                $html = <<<EOF
                    <a type="button" id="edit_is_tell_repair" data-sn="$sn" href="JavaScript:void(0);" class="btn btn-danger" >变更状态为已知会</a>
EOF;
                $form->html($html);
            }

            $form->display('receive_case', '收到的配件');
            $form->text('deal_remark', '维修备注')->help('必填')->rules('required');
            $form->text('order_extend.backstage_remark', '后台备注');

            // $form->multipleSelect('deal_remark', '维修备注')->options(Order::deal_remark);
            $form->hidden('check_man');
            $form->hidden('status');

            $form->multipleImage('repair_image', '维修图片')
                ->move('rbcare/repair/repair_image')
                ->options([
                    'allowedFileTypes' => ['image', 'video'], // 支持图片和视频
                    'msgInvalidFileExtension' => '{name}文件类型不正确，只支持{extensions}文件',
                    'allowedFileExtensions' => ['jpg', 'png', 'jpeg', 'mp4', 'avi'], // 增加视频格式
                ])
                ->help('保外或者人为损坏维修图片至少上传一张');
            $form->multipleSelect('order_extend.overhaul_remark', '市场拆修')
                ->options(["外拆缺少材料配件" => "外拆缺少材料配件", "外修造成人为损坏" => "外修造成人为损坏",
                    "使用非原装正规配件材料" => "使用非原装正规配件材料", "外拆有过分析维修" => "外拆有过分析维修"]);
            // $form->hidden('pay_amount');
            $form->hidden('barcode');
            $form->hidden('order_extend.check_time');
            $form->hidden('created_at');
            $form->select('repair_check_status', '检测状态')->options(Order::REPAIR_CHECK_STATUS2);

            $html = <<<EOF
                <div class="btn-group pull-right" style="right: -300px; bottom: -20px;" >
                    <button type="button" class="material_b" style="background-color: #00c0ef; border: None" >
                        <span style="color: #f7f7f9;">【确认配件信息】</span>
                    </button>
                </div>
EOF;
            $form->html($html);


            $form->saving(function (Form $form) use ($exp_cast) {

                // 角色判断
                if (Admin::user()->inRoles(['pr_check'])) {
                    $form->check_man = Admin::user()->id;
                }

                if (!$form->repair_image && !$form->model()->repair_image && ($form->model()->in_period != 1 || $form->model()->reason == 1)) {
                    $error = new MessageBag([
                        "title" => '错误提示',
                        "message" => "保外或者人为损坏维修图片至少上传一张"
                    ]);
                    return back()->withInput()->with(compact('error'));
                }

                $discount_type = 0;
                if ($form->order_extend['discount'] == 1) {
                    $discount_type = self::get_discount_type($form->barcode);
                }
                $malfunction_id = array();
                if ($form->repair_material) {
                    $amount = 0;
                    foreach ($form->repair_material as $material) {
                        if ($material['material_id'] == 0) {
                            $error = new MessageBag([
                                "title" => '错误提示（1）',
                                "message" => "有物料提交不成功，请按F5刷新并重新提交"
                            ]);
                            return back()->withInput()->with(compact('error'));
                        }
                        if ($material['_remove_'] == 0) {
                            $malfunction_id[] = $material['malfunction_id'];
                            if ($material['charge_type'] == 2) {
                                // 一代价格
                                $material_amount = Material::where('id', $material['material_id'])->value('price_first');
                                if (!$material_amount) {
                                    $error = new MessageBag([
                                        "title" => '错误提示（2）',
                                        "message" => "有物料提交不成功，请按F5刷新并重新提交"
                                    ]);
                                    return back()->withInput()->with(compact('error'));
                                }
                                $discount = DB::table('machine_accessory_tree as mat')
                                    ->leftjoin('machine_accessory_material_relation as mamr', 'mat.id', '=', 'mamr.mat_id')
                                    ->leftjoin('material as m', 'mamr.material_id', '=', 'm.id')
                                    ->where([['m.id', '=', $material['material_id']], ['mat.id', "=", $material['mat_id']]])
                                    ->value('mat.discount');
                                if ($form->order_extend['discount'] == 1 and $discount == 1) {
                                    // 如果订单打折同时配件允许打折
                                    if ($discount_type == 1) {
                                        $material_amount = $material_amount * 0.8;
                                    } elseif ($discount_type == 2) {
                                        $material_amount = $material_amount * 0.85;
                                    }
                                }
                                // $amount = $amount + $material_amount * $material['count'];
                            } else {
                                $material_amount = Material::where('id', $material['material_id'])->value('price');
                                if (!$material_amount) {
                                    $error = new MessageBag([
                                        "title" => '错误提示（3），物料没有配置价格',
                                        "message" => "有物料提交不成功，请按F5刷新并重新提交"
                                    ]);
                                    return back()->withInput()->with(compact('error'));
                                }
                                $discount = DB::table('machine_accessory_tree as mat')
                                    ->leftjoin('machine_accessory_material_relation as mamr', 'mat.id', '=', 'mamr.mat_id')
                                    ->leftjoin('material as m', 'mamr.material_id', '=', 'm.id')
                                    ->where([['m.id', '=', $material['material_id']], ['mat.id', "=", $material['mat_id']]])
                                    ->value('mat.discount');
                                if ($form->order_extend['discount'] == 1 and $discount == 1) {
                                    // 如果订单打折同时配件允许打折
                                    if ($discount_type == 1) {
                                        $material_amount = $material_amount * 0.8;
                                    } elseif ($discount_type == 2) {
                                        $material_amount = $material_amount * 0.85;
                                    }
                                }
                            }

                            if ($material['price_in'] > 0) {
                                $material_amount = $material['price_in'];
                            } else {
                                $material['price_in'] = $material_amount;
                            }
                            $amount = $amount + $material_amount * $material['count'];
                        }
                    }
                    if ($amount != $form->accessory_amount) {
                        $error = new MessageBag([
                            "title" => '错误提示（4）',
                            "message" => "物料提交不成功，请按F5刷新重新提交(统计价格：" . $amount . "提交价格：" . $form->accessory_amount . ")"
                        ]);
                        return back()->withInput()->with(compact('error'));
                    }
                }
                if (!$form->repair_image && !$form->model()->repair_image && self::image_mark($malfunction_id) == 1) {
                    $error = new MessageBag([
                        'title' => '操作错误',
                        'message' => '配件故障存在为【屏幕碎裂】或【进液材料氧化】，请上传维修图片。',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }

                if ($form->model()->status == 600) {
                    $error = new MessageBag([
                        'title' => '操作错误',
                        'message' => '此单已支付，无法二次检测',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }

                // 以换代修
                if ($form->order_extend['is_exchange_for_repair'] == 'on') { //这只有0或1,所以似乎if无效
                    if (empty($form->order_extend['new_barcode'])) {
                        $error = new MessageBag([
                            "title" => '错误提示',
                            "message" => "《以换代修》:新条码不能为空"
                        ]);
                        return back()->withInput()->with(compact('error'));
                    }
                    if (empty($form->order_extend['warehouse_type'])) {
                        $error = new MessageBag([
                            "title" => '错误提示',
                            "message" => "《以换代修》:请选择旧机入仓类型"
                        ]);
                        return back()->withInput()->with(compact('error'));
                    }
                    $data = DB::connection('mysql2')
                        ->table('warranty')
                        ->where([['barcode', '=', $form->barcode], ['status', '=', '1']])
                        ->select('buy_date')->first();
                    $mes_data = $this->mes_device($form->order_extend['new_barcode'], "", "");
                    if (empty($mes_data)) {
                        $error = new MessageBag([
                            "title" => '错误提示',
                            "message" => "《以换代修》新条码（" . $form->order_extend['new_barcode'] . "）无效，请确认",
                        ]);
                        return back()->withInput()->with(compact('error'));
                    }
                    if ($data) {
                        // 有旧保卡执行换机操作
                        // 换机操作
                        $warranty_exchange = $this->warranty_exchange($form->barcode, $form->order_extend['new_barcode']);
                        if ($warranty_exchange) {
                            if ($warranty_exchange['ok'] == 0) {
                                $error = new MessageBag([
                                    "title" => '错误提示',
                                    "message" => "《以换代修》换机操作失败：" . $warranty_exchange['msg'],
                                ]);
                                return back()->withInput()->with(compact('error'));
                            }
                        } else {
                            $error = new MessageBag([
                                "title" => '错误提示',
                                "message" => "《以换代修》换机操作失败：访问接口出错",
                            ]);
                            return back()->withInput()->with(compact('error'));
                        }
                    }
                }

                // 微信支付如果有发起过支付请求，必须保证金额等信息一致，所以有金额变动，需要去掉内部支付单号重新生成
                if ($form->pay_amount != $form->model()->pay_amount && !empty($form->model()->rb_pay_sn) && empty($form->model()->is_paid)) {
                    $data = ['rb_pay_sn' => '', 'pay_com' => 0, 'pay_sn' => ''];
                    DB::table('order')->where('sn', $form->model()->sn)->update($data);
                }

                // 有碎屏保  碎屏保在保修期内  使用碎屏保
                if ($form->has_screen_insurance == 1) {
                    $bsi = BrokenScreenInsurance::firstByBarcode($form->barcode);
                    $bsi_insurance_times_remain = $bsi ? $bsi->insurance_times_remain : 0;
                    if ($form->in_si_period == 1 && $form->used_screen_insurance > 0
                        && $bsi_insurance_times_remain > 0
                        && empty($form->model()->used_screen_insurance)) {
                        DB::table('order')->where('sn', $form->model()->sn)->update(["used_screen_insurance" => 1]);
                        $now = date('Y-m-d H:i:s');
                        $bsi_usage = array(
                            'barcode' => $form->barcode,
                            'bsi_id' => $bsi->id,
                            'standard_id' => $bsi->standard,
                            'status_before' => $bsi->status,
                            'insurance_times_before' => $bsi_insurance_times_remain,
                            'pr_sn' => $form->sn,
                            'admin_uid' => Admin::user()->id,
                            'record_effective' => BrokenScreenInsuranceUsage::RecordEffectiveEnable,
                            'created_at' => $now,
                            'updated_at' => $now,
                        );
                        if ($bsi->status == BrokenScreenInsurance::STATUS_PAY_EFFECTIVE) {
                            $bsi_remain_new = $bsi_insurance_times_remain - $form->used_screen_insurance;
                            $bsi->insurance_times_remain = $bsi_remain_new;
                            if ($bsi_remain_new <= 0) {
                                $bsi->status = BrokenScreenInsurance::STATUS_REPORTED;
                                $bsi->usage_state = 1;
                                $bsi->usaged_at = $now;
                            }
                            $bsi->updated_at = $now;
                            $bsi->save();
                        }
                        $form->used_screen_insurance = 1;
                        $bsi_usage['status_after'] = $bsi->status;
                        $bsi_usage['insurance_times_after'] = $bsi_remain_new;
                        BrokenScreenInsuranceUsage::Insert($bsi_usage);
                    }
                }
                if ($form->has_screen_insurance == 0 && $form->used_screen_insurance > 0) {
                    $form->used_screen_insurance = 0;
                }

                // 状态变更
                if ($form->model()->status < Order::CHECK_FINISH) {
                    $form->status = Order::CHECK_FINISH;
                } else {
                    $form->status = $form->model()->status;
                }

                // 微信通知
                $open_id = DB::table("admin_users as u")
                    ->rightjoin('order as o', 'u.id', '=', 'o.uid')
                    ->where([['sn', '=', $form->sn], ["o.uid", '<>', 0]])->value("u.open_id");
                if ($open_id) {
                    $mat_data = DB::table("machine_accessory_tree as t")
                        ->rightjoin("pr_used_material as um", "t.id", "=", "um.mat_id")
                        ->where([["um.pr_sn", "=", $form->sn]])
                        ->limit(3)
                        ->pluck("t.title")
                        ->toArray();
                    if ($mat_data) {
                        $mat_data = implode("、", $mat_data) . "等";
                    } else {
                        $mat_data = "无";
                    }
                    $param = [
                        "first" => [
                            "Value" => "您提交的维修订单已处理，请尽快查看订单详情",
                            "Color" => "#173177",
                        ],
                        "keyword1" => [
                            "Value" => $form->sn,
                            "Color" => "#173177",
                        ],
                        "keyword2" => [
                            "Value" => $mat_data,
                            "Color" => "#173177",
                        ],
                        "keyword3" => [
                            "Value" => $form->created_at,
                            "Color" => "#173177",
                        ],
                        "keyword4" => [
                            "Value" => "已检测",
                            "Color" => "#173177",
                        ],
                        "keyword5" => [
                            "Value" => $form->pay_amount,
                            "Color" => "#173177",
                        ],
                        "remark" => [
                            "Value" => "点击可查看维修详情——【读书郎客户售后服务部竭诚为您服务】",
                            "Color" => "#173177",
                        ],
                    ];
                    // dump($form->created_at);
                    self::wechat_notice($open_id, $param);
                }
            });

            $form->saved(function ($form) {
                $now = date('Y-m-d H:i:s');
                if ($form->repair_material) {
                    //自动添加物料处理
                    $auto_amount = 0;
                    $need_check_auto_accessory = [];
                    $model_name = Order::where('id', '=', $form->id)->value('model_name');
                    if ($model_name && (in_array(strtoupper($model_name), MachineAccessoryTree::$ar_need_model2))) {
                        $need_check_auto_accessory = MachineAccessoryTree::$auto_add_accessory2;
                    }
                    if ($model_name && (in_array(strtoupper($model_name), MachineAccessoryTree::$ar_need_model))){
                        $need_check_auto_accessory = array_merge($need_check_auto_accessory,MachineAccessoryTree::$ar_title2);
                    }
                    if ($model_name && (in_array(strtoupper($model_name), MachineAccessoryTree::$ar_need_model3))){
                        $need_check_auto_accessory = array_merge($need_check_auto_accessory,MachineAccessoryTree::$auto_add_accessory3);
                    }
                    //收费配件
                    $charge_need_check_auto_accessory = [];
                    if ($model_name && (in_array(strtoupper($model_name), MachineAccessoryTree::$charge_ar_need_model1))){
                        $charge_need_check_auto_accessory = array_merge($charge_need_check_auto_accessory,MachineAccessoryTree::$charge_auto_add_accessory1);
                    }
                    //已有物料过滤掉
                    $in_material = array_column($form->repair_material, 'material_id');
                    $model_id = Order::where('id', '=', $form->id)->value('model_id');
                    //免费配件
                    if (!empty($need_check_auto_accessory)) {
                        $auto_material = Material::leftjoin('machine_accessory_material_relation as mamr', 'mamr.material_id', '=', 'material.id')
                            ->leftjoin('machine_accessory_tree as mat', 'mat.id', '=', 'mamr.mat_id')
                            ->leftjoin('machine_accessory_malfunction_relation as malfunction', 'malfunction.mat_id', '=', 'mamr.mat_id')
                            ->groupBy('mat.id')
                            ->whereNotIn('material.id', $in_material)
                            ->where('mat.model_id', $model_id)
                            ->whereIn("mat.title", $need_check_auto_accessory)
                            ->select('material.id as material_id', 'mamr.mat_id', 'malfunction.malfunction_id', 'material.price as price_in')->get()->toArray();

                        if (!empty($auto_material)) {
                            foreach ($auto_material as $auto_one) {
                                $new_auto_material = new PostRepairMaterial();
                                $new_auto_material->pr_sn = $form->sn;
                                $new_auto_material->mat_id = $auto_one['mat_id'];
                                $new_auto_material->material_id = $auto_one['material_id'];
                                $new_auto_material->malfunction_id = $auto_one['malfunction_id'] ?? 0;
                                $new_auto_material->is_charge = 0;
                                $new_auto_material->charge_type = 1;
                                $new_auto_material->count = 1;
                                $new_auto_material->price_in = $auto_one['price_in'] ?? 0;
                                $new_auto_material->created_at = $now;
                                $new_auto_material->save();

                                $auto_amount = $auto_amount+ $auto_one['price_in'];
                            }
                        }
                    }
                    //收费配件
                    $auto_accessory_cast = 0;
                    if (!empty($charge_need_check_auto_accessory)){
                        $charge_auto_material = Material::leftjoin('machine_accessory_material_relation as mamr', 'mamr.material_id', '=', 'material.id')
                            ->leftjoin('machine_accessory_tree as mat', 'mat.id', '=', 'mamr.mat_id')
                            ->leftjoin('machine_accessory_malfunction_relation as malfunction', 'malfunction.mat_id', '=', 'mamr.mat_id')
                            ->groupBy('mat.id')
                            ->whereNotIn('material.id', $in_material)
                            ->where('mat.model_id', $model_id)
                            ->whereIn("mat.title", $charge_need_check_auto_accessory)
                            ->select('material.id as material_id', 'mamr.mat_id', 'malfunction.malfunction_id', 'material.price as price_in')->get()->toArray();

                        if (!empty($charge_auto_material)) {
                            foreach ($charge_auto_material as $auto_one) {
                                $new_auto_material = new PostRepairMaterial();
                                $new_auto_material->pr_sn = $form->sn;
                                $new_auto_material->mat_id = $auto_one['mat_id'];
                                $new_auto_material->material_id = $auto_one['material_id'];
                                $new_auto_material->malfunction_id = $auto_one['malfunction_id'] ?? 0;
                                $new_auto_material->is_charge = 1;
                                $new_auto_material->charge_type = 1;
                                $new_auto_material->count = 1;
                                $new_auto_material->price_in = $auto_one['price_in'] ?? 0;
                                $new_auto_material->created_at = $now;
                                $new_auto_material->save();

                                $auto_amount = $auto_amount+ $auto_one['price_in'];
                                $auto_accessory_cast= $auto_accessory_cast+$auto_one['price_in'];
                            }
                        }
                    }

                    //配件费用和总费用需要累加自动添加配件金额
                    if ($auto_amount > 0) {
                        $save = Order::where('id', '=', $form->id)->first();
                        $save->amount = $save->amount + $auto_amount;
                        $save->accessory_amount = $save->accessory_amount + $auto_amount;
                        $save->accessory_cast = $save->accessory_cast + $auto_accessory_cast;
                        $save->hidden_amount = $save->hidden_amount + $auto_accessory_cast;
                        $save->pay_amount = $save->pay_amount + $auto_accessory_cast;
                        $save->save();
                    }
                }
                // 同步处理已使用的物料
                PostRepairUsedMaterial::where('pr_sn', $form->sn)->delete();
                $pr_material = PostRepairMaterial::where('pr_sn', $form->sn)->get();
                if (count($pr_material) > 0) {
                    foreach ($pr_material as $material) {
                        $new_used_material = new PostRepairUsedMaterial();
                        $new_used_material->pr_sn = $material->pr_sn;
                        $new_used_material->mat_id = $material->mat_id;
                        $new_used_material->material_id = $material->material_id;
                        $new_used_material->is_charge = $material->is_charge;
                        $new_used_material->charge_type = $material->charge_type;
                        $new_used_material->count = $material->count;
                        $new_used_material->price_in = $material->price_in;
                        $new_used_material->created_at = $now;
                        $new_used_material->statistic_time = $now;
                        $new_used_material->save();
                        // 使用物料后减库存,只减新仓库
                        Material::where('id', $material->material_id)->where('from', 1)->decrement('quantity');
                    }
                }
                $order = $form->model();
                // 处理保修期内并且是0元的订单，直接修改为已支付的状态
                if ($order->status == Order::CHECK_FINISH_IS_TELL && $order->in_period == 1 && empty(floatval($order->pay_amount))) {
                    $save = Order::where('sn', $order->sn)->first();
                    $save->status = Order::PAY_FINISH;
                    $save->save();
                }
                if ($order->status == Order::CHECK_FINISH_IS_TELL && $order->in_period == 2 && empty(floatval($order->pay_amount))) {
                    $save = Order::where('sn', $order->sn)->first();
                    $save->status = Order::PAY_FINISH;
                    $save->save();
                }
                //处理保修期外并且是自寄的弃修订单，直接修改为已支付的状态
                if ($order->status == Order::CHECK_FINISH_IS_TELL && $order->in_period == 2 && $order->come_exp_type == 2 && $order->connect == 3 && empty(floatval($order->pay_amount))) {
                    $save = Order::where('sn', $order->sn)->first();
                    $save->status = Order::PAY_FINISH;
                    $save->save();
                }
                //处理保修期内并且是弃修订单，直接修改为已支付的状态
                if ($order->status == Order::CHECK_FINISH_IS_TELL && $order->in_period == 1 && $order->connect == 3 && empty(floatval($order->pay_amount))) {
                    $save = Order::where('sn', $order->sn)->first();
                    $save->status = Order::PAY_FINISH;
                    $save->save();
                }
                //处理保修期外并且是质量问题的0元订单，直接修改为已支付的状态
                if ($order->status == Order::CHECK_FINISH_IS_TELL && $order->in_period == 2 && $order->reason == 2 && empty(floatval($order->pay_amount))) {
                    $save = Order::where('sn', $order->sn)->first();
                    $save->status = Order::PAY_FINISH;
                    $save->save();
                }

            });
        });
    }

    private function prdmoBom($barcode = null, $number = null, $imei = null)
    {
        if ($barcode) {
            $param = 'barcode=' . $barcode;
        } elseif ($imei) {
            $param = 'imei=' . $imei;
        } elseif ($number) {
            $param = 'number=' . $number;
        } else {
            return false;
        }
        $appId = 'Web';
        $time = time();
        $appSecret = 'M4S8tUB8OBBvIUN7';
        $sn = md5("$appId-$time-$appSecret");
        $authKey = "$appId-$time-$sn";
        $url = "http://api-mes.readboy.com/index.php?s=/Api/PrdmoBom/info.html&$param&authKey=$authKey";
        $client = new Client();
        $res = $client->get($url);
        if ($res->getStatusCode() == 200) {
            $body = $res->getBody();
            $content = json_decode($body->getContents(), true);
            if ($content['errcode'] == 0) {
                $data = $content['data'];
                return $data;
            }
            return false;
        }
//        dd($res->getBody()->getContents());
        return false;
    }

    public function boom_list($barcode)
    {
        return Admin::content(function (Content $content) use ($barcode) {
            $content->header('Boom清单');
            $content->description('Boom清单列表');
            $data = $this->prdmoBom($barcode);
//            dd($data);
            $data = json_decode(json_encode($data));
//            dd($data);
            $content->body(view('admin/repair_check/promoBoom', compact('data')));
        });
    }

    public function express()
    {
        $id = request()->get('id');
        $type = request()->get('type');
        $reason = request()->get('reason');
        $rb_come_exp_sn = request()->get('rb_come_exp_sn');
        $come_exp_sn = request()->get('come_exp_sn');
        $order = Order::find($id);

        //全部人为损坏
        if ($reason == 1) {
            $exp_cast = Express::query_order($rb_come_exp_sn, $come_exp_sn);
            # 用户为代理商寄修时分摊快递费
            //$exp_cast = 22;
            if ($order->type == 3 && $exp_cast != '无法获取') {
                $agent_order_sn = AgentOrderCorrelation::leftJoin('order', 'order.sn', '=', 'agent_order_correlation.order_sn')
                    ->where('order.id', $id)->value('agent_order_sn');
                $count = AgentOrderCorrelation::leftJoin('order', 'order.sn', '=', 'agent_order_correlation.order_sn')
                    ->where([['agent_order_correlation.agent_order_sn', $agent_order_sn], ['order.come_exp_sn', $come_exp_sn]])->count();
                if ($count > 0) {
                    $exp_cast = floatval($exp_cast) / $count;
                }
            }
            if ($order->type != 2 && $exp_cast == '无法获取') {
                if ($order->come_exp_type === 1) { // 上门取件的才默认收, 自主寄件的默认不收
                    $exp_cast = Order::express_default();
                }
            }
        } else if ($order->in_period !== 1) { //保外
            $exp_cast = Express::query_order($rb_come_exp_sn, $come_exp_sn);
            # 用户为代理商寄修时分摊快递费
            if ($order->type == 3 && $exp_cast != '无法获取') {
                $agent_order_sn = AgentOrderCorrelation::leftJoin('order', 'order.sn', '=', 'agent_order_correlation.order_sn')
                    ->where('order.id', $id)->value('agent_order_sn');
                $count = AgentOrderCorrelation::leftJoin('order', 'order.sn', '=', 'agent_order_correlation.order_sn')
                    ->where([['agent_order_correlation.agent_order_sn', $agent_order_sn], ['order.come_exp_sn', $come_exp_sn]])->count();
                if ($count > 0) {
                    $exp_cast = floatval($exp_cast) / $count;
                }
            }
            //$exp_cast = 15;
            if ($order->type != 2 && $exp_cast == '无法获取') {
                if ($order->come_exp_type === 1) { // 上门取件的才默认收, 自主寄件的默认不收
                    $exp_cast = Order::express_default();
                }
            }
        } else {
            $exp_cast = 0;
        }
        // 非代理商寄修 且 保外自主寄件 且 单号是顺丰开头
        if ($order->type != 2 && ($exp_cast == '无法获取' || $exp_cast == 0) && $order->in_period !== 1 &&
            $order->come_exp_type === 2 && strpos($come_exp_sn, 'SF') === 0) {
            $exp_cast = Order::express_default();
        }
        return $exp_cast;
    }


    public static function warranty_exchange($old_barcode, $new_barcode)
    {
        $appId = 'repair.readboy.com';
        $time = time();
        $appSecret = '31879b82704954028802c9325bbcd55c';
        $device_item = ['', '', $appId, ''];
        $device_id = join('/', $device_item);
        $sn = md5($device_id . $appSecret . $time);

        $param = [
            "device_id" => $device_id,
            "t" => $time,
            "sn" => $sn,
            "barcode" => $old_barcode,
            "barcode_new" => $new_barcode,
            "reason" => "以换代修",

        ];
        //$field = http_build_query($param);
        $client = new \GuzzleHttp\Client();
        //$url = "http://api-yxtest.readboy.com/api/warranty/repair/exchange";
        $url = "https://api-yx.readboy.com/api/warranty/repair/exchange";
        $response = $client->post($url, [
            "form_params" => $param
        ]);

        if ($response->getStatusCode() == '200') {
            $body = $response->getBody();

            return json_decode($body->getContents(), true);
        } else {
            return [];
        }
    }

    public static function mes_device($barcode, $number, $imei)
    {
        if ($barcode) {
            $where = [['barcode', '=', $barcode]];
        } elseif ($number) {
            $where = [['number', '=', $number]];
        } else {
            $where = [['imei', '=', $imei]];
        }
        $data = DB::connection('mysql3')->table('mes_devices')->where($where)->whereIn('state', [0, 1, 2, 3])
            ->orderBy('update_time', 'desc')->limit(1)->select('*')->first();
        return $data;
    }

    public function aging_test_notice(Request $request)
    {
        $req = request()->get('ids');
        $sms = new Sms();
        $data = Order::whereIn('id', $req)->select('model_name', 'phone')->get()->toArray();
        foreach ($data as $d) {
            $phone = $d['phone'];
            $template = 'SMS_215344127';
            $templateParam = ['model' => $d['model_name']];
//            dump($phone);
            $ret = $sms->send($phone, $template, $templateParam);
//            $ret = $sms->query_info($phone);
//            dump($ret);
        }
    }

    public function accessory_lack_notice(Request $request)
    {
        $req = request()->get('ids');
        $sms = new Sms();
        $data = Order::whereIn('id', $req)->select('model_name', 'phone')->get()->toArray();
        foreach ($data as $d) {
            $phone = $d['phone'];
            $template = 'SMS_215339152';
            $templateParam = ['model' => $d['model_name']];
//            dump($phone);
            $ret = $sms->send($phone, $template, $templateParam);
//            $ret = $sms->query_info($phone);
//            dump($ret);
        }
    }

    public function order_mark()
    {
        $sn = request()->get('sn');
        $status = request()->get('order_mark');
        OrderExtend::where('sn', $sn)->update(['order_mark' => $status]);
    }


    public function accessory_mark()
    {
        $sn = request()->get('sn');
        $status = request()->get('accessory_mark');
        if ($status == 0) {
            OrderExtend::where('sn', $sn)->update(['accessory_mark' => 1]);
        } else {
            OrderExtend::where('sn', $sn)->update(['accessory_mark' => 0]);
        }
    }


    public function discount_type()
    {
        $barcode = request()->get('barcode');
        $data = DB::connection('mysql2')
            ->table('warranty')
            ->where([['barcode', '=', $barcode], ['status', '=', '1']])
            ->select('buy_date')->first();
        if (empty($data)) {
            return 0;
        }
        $buy_date = $data->buy_date;
        $time1 = date('Y-m-d', strtotime(' + 13 months', strtotime($buy_date)));
        $time2 = date('Y-m-d', strtotime(' + 14 months', strtotime($buy_date)));
        $time = date('Y-m-d', strtotime(' + 12 months', strtotime($buy_date)));
        $now = date('Y-m-d');
        if ($now > $time2) {
            return 0;  // 过保两个月以上
        } elseif ($now < $time) {
            return 1;  // 在保修期内8折
        } elseif ($now < $time1 && $now > $time) {
            return 1;     // 一个月内8折
        } elseif ($now < $time2 && $now > $time1) {
            return 2;     // 两个月内85折
        } else {
            return 0;
        }
    }

    private function get_discount_type($barcode)
    {
        $data = DB::connection('mysql2')
            ->table('warranty')
            ->where([['barcode', '=', $barcode], ['status', '=', '1']])
            ->select('buy_date')->first();
        if (empty($data)) {
            return 0;
        }
        $buy_date = $data->buy_date;
        $time1 = date('Y-m-d', strtotime(' + 13 months', strtotime($buy_date)));
        $time2 = date('Y-m-d', strtotime(' + 14 months', strtotime($buy_date)));
        $time = date('Y-m-d', strtotime(' + 12 months', strtotime($buy_date)));
        $now = date('Y-m-d');
        if ($now > $time2) {
            return 0;  // 在保修期内 或者 过保两个月以上
        } elseif ($now < $time) {
            return 1;  // 在保修期内8折
        } elseif ($now < $time1 && $now > $time) {
            return 1;     // 一个月内8折
        } elseif ($now < $time2 && $now > $time1) {
            return 2;     // 两个月内85折
        } else {
            return 0;
        }
    }


    public function repeat_remark()
    {
        $repeat_order = \request()->get("q");
        if ($repeat_order == "0" || $repeat_order == 1) {
            return ["主板不良" => "主板不良", "主控不良" => "主控不良",
                "DDR不良" => "DDR不良", "数据IC不良" => "数据IC不良", "FPC类不良" => "FPC类不良",
                "屏幕不良" => "屏幕不良", "电池不良" => "电池不良", "其他材料不良" => "其他材料不良",
                "设计缺陷导致故障" => "设计缺陷导致故障", "用户人为因素导致故障" => "用户人为因素导致故障",
                "检测未发现异常" => "检测未发现异常", "生产工艺不良导致故障" => "生产工艺不良导致故障",
                "软件导致故障" => "软件导致故障", "充电设备不良" => "充电设备不良", "马达不良" => "马达不良"];
        } else {
            return ["主板不良" => "主板不良", "主控不良" => "主控不良",
                "DDR不良" => "DDR不良", "数据IC不良" => "数据IC不良", "FPC类不良" => "FPC类不良",
                "屏幕不良" => "屏幕不良", "电池不良" => "电池不良", "其他材料不良" => "其他材料不良",
                "设计缺陷导致故障" => "设计缺陷导致故障", "用户人为因素导致故障" => "用户人为因素导致故障",
                "检测未发现异常" => "检测未发现异常", "生产工艺不良导致故障" => "生产工艺不良导致故障",
                "软件导致故障" => "软件导致故障", "充电设备不良" => "充电设备不良", "马达不良" => "马达不良",
                "维修技能导致" => "维修技能导致"];
        }
    }


    private function image_mark($malfunction_id)
    {
        $data = MachineMalfunction::where("image_mark", 1)
            ->whereIn('id', $malfunction_id)
            ->pluck('image_mark', "id", "title")->toArray();

        if (empty($data)) {
            return 0;
        } else {
            return 1;
        }
    }

    private function wechat_notice($open_id, $data)
    {
        $token = self::wechat_token();
//        dump($token);
        if ($token == null) {
            return false;
        }

        $t = time();
        $secret = "oYS#KY3hd&ES66dWcR5w655*pNt0oRSlGcKF";
        $sn = md5($open_id . $secret . $t);
        $template_id = "EkMO8m7ZUEfQWdhH0YtXGgP88MbGmuRypIuP7FaC5xI";
        $redirect_url = "https://h5-repair-hub.readboy.com/";
        $url = "https://api-weixin.readboy.com/api/template/send" . "?tsp=" . $t . "&sn=" . $sn;
        $param = [
            "access_token" => $token,
            "touser" => $open_id,
            "template_id" => $template_id,
            "redirect_url" => $redirect_url,
            "data" => json_encode($data)
        ];
        $client = new Client();
        $response = $client->post($url, [
            "form_params" => $param
        ]);
        if ($response->getStatusCode() == '200') {
            $body = $response->getBody();
            $content = json_decode($body->getContents(), true);
//            dd($content);
            return $content;
        } else {
            return false;
        }
    }


    private function wechat_token()
    {

        $timestamp = time();
        $token = "df6b88a899";
        $sn = $timestamp . md5($timestamp . $token) . $token;
        $url = "http://wxadmin.readboy.com/weiphp/index.php?s=/home/<USER>/get_classone_token&sn=" .
            $sn . "&tokenval=gh_223743faa015";
        $client = new Client();
        $response = $client->get($url);

        if ($response->getStatusCode() == '200') {
            $body = $response->getBody();
            $content = json_decode($body->getContents(), true);
            return $content;
        } else {
            return null;
        }
    }


    private function wechat_refresh()
    {
        $url = "https://api-weixin.readboy.com/access-token/refresh";
        $t = time();
        $app_id = "wx25e116fe3f5d5287";
        $secret = "MTPpoG5hbGwQhOsDzl3Izwz0wyZnPEoSBk";
        $sn = md5($app_id . $t . $secret);
        $client = new Client();
        $url = $url . "?appid=" . $app_id . '&tsp=' . $t . "&sn=" . $sn;
        $response = $client->get($url);
        if ($response->getStatusCode() == '200') {
            $body = $response->getBody();
            $content = json_decode($body->getContents(), true);
            if ($content['ok'] == 1) {
                return true;
            }
            return false;
        } else {
            return false;
        }
    }

    /**
     * 对接云客服系统,拨打电话
     */
    public function call()
    {
        $phone = request()->input('phone');
        $bill_id = request()->input('bill_id');
        $url = '/v20160818/call/dialout/';

        // 获取坐席工号
        $seat_number = AdminUsersYkf::Where('uid', Admin::user()->id)->value('seat_number');
        $data = array(
            'FromExten' => $seat_number,
            'Exten' => $phone,
            // 'ExtenType' => 'Local/sip/gateway'
            'DialoutStrVar' => json_encode(array('system' => 'repair', 'bill_id' => $bill_id))
        );
        $cloudCustomer = new  CloudCustomer();

        $result = $cloudCustomer->curlRequest($url, $data);

        return $result;
    }

    /**
     * 对接云客服系统,拨打电话（合力亿捷）
     */
    public function callHolly(){

    }

    /**
     * 发送支付短信
     */
    public function sendMsg()
    {
        $phone = request()->input('phone');

        $sms = new Sms();
        $template = 'SMS_175240098';
        $data = null;

        if ($sms->send($phone, $template, $data)) {
            return array('status' => 1, 'info' => '发送成功!');
        } else {
            return array('status' => 0, 'info' => '发送失败!');
        }
    }

    /**
     * 变更状态为未检测已知会/已检测已知会
     */
    public function setOrderRemark()
    {

        $sn = request()->input('sn');
        $order_remark = request()->input('order_remark');

        // 获取订单信息
        $orderInfo = Order::where('sn', $sn)->first();

        if ($orderInfo->status == Order::COME_SURE_IS_TELL) {
            $newStatus = Order::NO_CHECK_IS_TELL;
        }

        if ($orderInfo->status == Order::CHECK_FINISH) {
            $newStatus = Order::CHECK_FINISH_IS_TELL;
        }

        // 更新订单状态
        $orderData = array(
            'status' => $newStatus,
            'updated_at' => date('Y-m-d H:i:s')
        );

        if (!Order::where('sn', $sn)->update($orderData)) {
            return array('status' => 0, 'info' => '操作失败');
        }

        // 添加知会日志
        $orderRemarkData = array(
            'sn' => $sn,
            'operator' => Admin::user()->id,
            'status' => $newStatus,
            'connect' => $orderInfo['connect'],
            'remark' => $order_remark,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'type' => 3
        );
        OrderRemark::insert($orderRemarkData);

        // // 备注推送到APP(无需知会才推送)
        // $orderExtendIsTell = OrderExtend::where('sn' , $orderInfo->sn)->value('is_tell');
        // if(!empty($order_remark) && $orderExtendIsTell != 1  && Admin::user()->id != 1){
        //     $msgPush = new  MessagePush();

        //     if($orderInfo->type == 3){ // 终端代寄
        //         $msgPush->pushRemarkEndpoint($orderInfo->endpoint , $order_remark);
        //         // $msgPush->pushRemarkEndpoint(2 , $order_remark);
        //     }else { // 正常寄修 代理商寄修
        //         $account_center_id = DB::table('admin_users')->where('id' , $orderInfo->uid)->value('account_center_id'); // 账户中心ID
        //         $msgPush->pushRemark($account_center_id , $order_remark);
        //         // $msgPush->pushRemark(5317926 , $order_remark);
        //     }
        // } 

        // 添加订单日志
        $orderLogData = array(
            'pr_sn' => $sn,
            'pr_status' => $newStatus,
            'log_status' => $newStatus,
            'log_from' => 'web',
            'uid' => $orderInfo['uid'],
            'admin' => Admin::user()->id,
            'title' => Order::title[$newStatus],
            'date' => date('Y-m-d H:i:s')
        );
        OrderLog::updateOrInsert(array('pr_sn' => $sn, 'log_status' => $orderLogData['log_status']), $orderLogData);

        // 备用日志
        OrderLog2::insert($orderLogData);

        // 处理保修期内并且是0元的订单，直接修改为已支付的状态
        if ($newStatus == Order::CHECK_FINISH_IS_TELL && $orderInfo->in_period == 1 && empty(floatval($orderInfo->pay_amount))) {
            $save = Order::where('sn', $orderInfo->sn)->first();
            $save->status = Order::PAY_FINISH;
            $save->save();
        }
        if ($newStatus == Order::CHECK_FINISH_IS_TELL && $orderInfo->in_period == 2 && empty(floatval($orderInfo->pay_amount))) {
            $save = Order::where('sn', $orderInfo->sn)->first();
            $save->status = Order::PAY_FINISH;
            $save->save();
        }
        //处理保修期外并且是自寄的弃修订单，直接修改为已支付的状态
        if ($newStatus == Order::CHECK_FINISH_IS_TELL && $orderInfo->in_period == 2 && $orderInfo->come_exp_type == 2 && $orderInfo->connect == 3 && empty(floatval($orderInfo->pay_amount))) {
            $save = Order::where('sn', $orderInfo->sn)->first();
            $save->status = Order::PAY_FINISH;
            $save->save();
        }
        //处理保修期内并且是弃修订单，直接修改为已支付的状态
        if ($newStatus == Order::CHECK_FINISH_IS_TELL && $orderInfo->in_period == 1 && $orderInfo->connect == 3 && empty(floatval($orderInfo->pay_amount))) {
            $save = Order::where('sn', $orderInfo->sn)->first();
            $save->status = Order::PAY_FINISH;
            $save->save();
        }
        //处理保修期外并且是质量问题的0元订单，直接修改为已支付的状态
        if ($newStatus == Order::CHECK_FINISH_IS_TELL && $orderInfo->in_period == 2 && $orderInfo->reason == 2 && empty(floatval($orderInfo->pay_amount))) {
            $save = Order::where('sn', $orderInfo->sn)->first();
            $save->status = Order::PAY_FINISH;
            $save->save();
        }

        // 发送短信
        if ($newStatus == Order::CHECK_FINISH_IS_TELL && $orderInfo->pay_amount > 0) {
            $sms = new Sms();
            $phone = $orderInfo->phone;
            $template = 'SMS_175240098';
            $data = null;
            $sms->send($phone, $template, $data);

            // 微信通知
            $open_id = DB::table("admin_users as u")
                ->rightjoin('order as o', 'u.id', '=', 'o.uid')
                ->where([['sn', '=', $orderInfo->sn], ["o.uid", '<>', 0]])->value("u.open_id");
            if ($open_id) {
                $mat_data = DB::table("machine_accessory_tree as t")
                    ->rightjoin("pr_used_material as um", "t.id", "=", "um.mat_id")
                    ->where([["um.pr_sn", "=", $orderInfo->sn]])
                    ->limit(3)
                    ->pluck("t.title")
                    ->toArray();
                if ($mat_data) {
                    $mat_data = implode("、", $mat_data) . "等";
                } else {
                    $mat_data = "无";
                }
                $param = [
                    "first" => [
                        "Value" => "您提交的维修订单已处理，请尽快查看订单详情",
                        "Color" => "#173177",
                    ],
                    "keyword1" => [
                        "Value" => $orderInfo->sn,
                        "Color" => "#173177",
                    ],
                    "keyword2" => [
                        "Value" => $mat_data,
                        "Color" => "#173177",
                    ],
                    "keyword3" => [
                        "Value" => $orderInfo->created_at,
                        "Color" => "#173177",
                    ],
                    "keyword4" => [
                        "Value" => "已检测",
                        "Color" => "#173177",
                    ],
                    "keyword5" => [
                        "Value" => $orderInfo->pay_amount,
                        "Color" => "#173177",
                    ],
                    "remark" => [
                        "Value" => "点击可查看维修详情——【读书郎客户售后服务部竭诚为您服务】",
                        "Color" => "#173177",
                    ],
                ];
                self::wechat_notice($open_id, $param);
            }
        }

        return array('status' => 1, 'info' => '操作成功');
    }

    /**
     * 保存备注信息
     */
    public function setRepairRemark()
    {
        $sn = request()->input('sn');
        $remark = request()->input('remark');

        // 获取订单信息
        $orderInfo = Order::where('sn', $sn)->first();

        $data = array(
            'sn' => $sn,
            'operator' => Admin::user()->id,
            'type' => 3,
            'status' => $orderInfo['status'],
            'connect' => $orderInfo['connect'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'remark' => $remark
        );

        // // 备注推送到APP(无需知会才推送)
        // $orderExtendIsTell = OrderExtend::where('sn' , $orderInfo->sn)->value('is_tell');
        // if(!empty($remark) && $orderExtendIsTell != 1 && Admin::user()->id != 1){
        //     $msgPush = new  MessagePush();

        //     if($orderInfo->type == 3){ // 终端代寄
        //         $msgPush->pushRemarkEndpoint($orderInfo->endpoint , $remark);
        //         // $msgPush->pushRemarkEndpoint(2 , $remark);
        //     }else { // 正常寄修 代理商寄修
        //         $account_center_id = DB::table('admin_users')->where('id' , $orderInfo->uid)->value('account_center_id'); // 账户中心ID
        //         $msgPush->pushRemark($account_center_id , $remark);
        //         // $msgPush->pushRemark(5317926 , $remark);
        //     }
        // }

        if (OrderRemark::insert($data)) {

            $data['status_name'] = Order::STATUS[$data['status']];
            $data['connect_name'] = Order::CONNECT[$data['connect']];
            $data['name'] = Admin::user()->name;

            return array('status' => 1, 'info' => '添加成功', 'data' => $data);
        } else {
            return array('status' => 0, 'info' => '添加失败');
        }
    }

    /**
     * 维修知会批处理
     */
    public function batchIsTell()
    {
        $ids = request()->input('ids');
        $order_remark = request()->input('order_remark');
        $status = request()->input('status', 0);

        if (empty($ids)) {
            return array('status' => 0, 'info' => '请至少选择一个订单');
        }

        // 获取订单号码
        $orderList = Order::whereIn('id', $ids)->select('sn', 'uid', 'connect', 'status', 'type', 'endpoint')->get();

        if ($status == 1) {

            // 知会备注
            $remarkData = array(
                'operator' => Admin::user()->id,
                'remark' => $order_remark,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => 3
            );

            // 操作日志
            $logData = array(
                'log_from' => 'web',
                'admin' => Admin::user()->id,
                'date' => date('Y-m-d H:i:s')
            );

            $remarkDataAll = $logDataAll = $sns410 = $sns490 = array();
            foreach ($orderList as $key => $value) {

                if (!in_array($value->status, [Order::COME_SURE_IS_TELL, Order::CHECK_FINISH])) {
                    return array('status' => 0, 'info' => '订单: ' . $value->sn . ' 无法操作');
                }

                if ($value->status == Order::COME_SURE_IS_TELL) {
                    $newStatus = Order::NO_CHECK_IS_TELL;
                    $sns410[] = $value->sn;
                }
                if ($value->status == Order::CHECK_FINISH) {
                    $newStatus = Order::CHECK_FINISH_IS_TELL;
                    $sns490[] = $value->sn;
                }

                $remarkData['sn'] = $value->sn;
                $remarkData['status'] = $newStatus;
                $remarkData['connect'] = $value->connect;

                $remarkDataAll[] = $remarkData;

                // // 备注推送到APP(无需知会才推送)
                // $orderExtendIsTell = OrderExtend::where('sn' , $value->sn)->value('is_tell');
                // if(!empty($order_remark) && $orderExtendIsTell != 1 && Admin::user()->id != 1){
                //     $msgPush = new  MessagePush();

                //     if($value->type == 3){ // 终端代寄
                //         $msgPush->pushRemarkEndpoint($value->endpoint , $order_remark);
                //         // $msgPush->pushRemarkEndpoint(2 , $order_remark);
                //     }else { // 正常寄修 代理商寄修
                //         $account_center_id = DB::table('admin_users')->where('id' , $value->uid)->value('account_center_id'); // 账户中心ID
                //         $msgPush->pushRemark($account_center_id , $order_remark);
                //         // $msgPush->pushRemark(5317926 , $order_remark);
                //     }
                // } 

                $logData['pr_status'] = $newStatus;
                $logData['log_status'] = $newStatus;
                $logData['title'] = Order::title[$newStatus];
                $logData['pr_sn'] = $value->sn;
                $logData['uid'] = $value->uid;

                $logDataAll[] = $logData;

            }

            if (!empty($sns410)) {

                // 更新订单状态
                $orderData = array(
                    'status' => Order::NO_CHECK_IS_TELL,
                    'updated_at' => date('Y-m-d H:i:s')
                );
                if (!Order::whereIn('sn', $sns410)->update($orderData)) {
                    return array('status' => 0, 'info' => '操作失败');
                }
            }

            if (!empty($sns490)) {

                // 更新订单状态
                $orderData = array(
                    'status' => Order::CHECK_FINISH_IS_TELL,
                    'updated_at' => date('Y-m-d H:i:s')
                );
                if (!Order::whereIn('sn', $sns490)->update($orderData)) {
                    return array('status' => 0, 'info' => '操作失败');
                }
            }

            // 添加知会记录
            if (!OrderRemark::insert($remarkDataAll)) {
                return array('status' => 0, 'info' => '操作失败');
            }

            // 添加日志
            OrderLog::insert($logDataAll);

            // 备用日志
            OrderLog2::insert($logDataAll);

        } else {

            // 知会备注
            $remarkData = array(
                'operator' => Admin::user()->id,
                'remark' => $order_remark,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'type' => 3
            );

            $remarkDataAll = array();
            foreach ($orderList as $key => $value) {

                $remarkData['sn'] = $value->sn;
                $remarkData['status'] = $value->status;
                $remarkData['connect'] = $value->connect;

                $remarkDataAll[] = $remarkData;

                // // 备注推送到APP(无需知会才推送)
                // $orderExtendIsTell = OrderExtend::where('sn' , $value->sn)->value('is_tell');
                // if(!empty($order_remark) && $orderExtendIsTell != 1){
                //     $msgPush = new  MessagePush();

                //     if($value->type == 3){ // 终端代寄
                //         $msgPush->pushRemarkEndpoint($value->endpoint , $order_remark);
                //         // $msgPush->pushRemarkEndpoint(2 , $order_remark);
                //     }else { // 正常寄修 代理商寄修
                //         $account_center_id = DB::table('admin_users')->where('id' , $value->uid)->value('account_center_id'); // 账户中心ID
                //         $msgPush->pushRemark($account_center_id , $order_remark);
                //         // $msgPush->pushRemark(5317926 , $order_remark);
                //     }
                // }
            }

            // 添加知会记录
            if (!OrderRemark::insert($remarkDataAll)) {
                return array('status' => 0, 'info' => '操作失败');
            }
        }

        return array('status' => 1, 'info' => '操作成功');
    }

    /**
     * 记录时间
     */
    public function saveRecordTime()
    {
        $sn = request()->input('sn');
        $type = request()->input('type');

        $data = array();

        // 拆屏
        if ($type == 'screen_time') {
            $data['screen_record_time'] = date('Y-m-d H:i:s');
        }

        // 维修
        if ($type == 'repair_time') {
            // 获取已存在记录时间
            $repairRecordTimes = OrderExtend::where('sn', $sn)->value('repair_record_times');
            if (!empty($repairRecordTimes)) {
                $data['repair_record_times'] = $repairRecordTimes . ' / ' . date('Y-m-d H:i:s');
            } else {
                $data['repair_record_times'] = date('Y-m-d H:i:s');
            }
        }

        // 品检
        if ($type == 'quality_time') {
            $data['quality_time'] = date('Y-m-d H:i:s');
            $data['quality_uid'] = Admin::user()->id;
        }

        if (OrderExtend::where('sn', $sn)->update($data)) {
            return array('status' => 1, 'info' => '操作成功!');
        } else {
            return array('status' => 0, 'info' => '操作失败!');
        }

    }

    /**
     * 设备解绑
     */
    public function deviceUnbind()
    {
        $id = request()->input('id');

        // 订单信息
        if (!$orderInfo = Order::where('id', $id)->first()) {
            return array('status' => 0, 'info' => '订单不存在');
        }

        // 获取机型类别名称
        $categoryName = DB::table('machine_type as mt')
            ->leftjoin('machine_category as mc', 'mc.id', 'mt.category_id')
            ->where('mt.model_id', $orderInfo->model_id)
            ->value('mc.name');

        // 记录日志
        $logData = array(
            'sn' => $orderInfo->sn,
            'uid' => Admin::user()->id,
            'model' => $orderInfo->model_name,
            'created_at' => date('Y-m-d H:i:s'),
            'category_name' => $categoryName
        );
        $du = new  DeviceUnbind();
        if (strstr($categoryName, '平板')) {
            $result = $du->unbindPad($orderInfo->serial);
            // $result = $du->unbindPad('1f3d1881');

            $logData['number'] = $orderInfo->serial;
            $logData['status'] = $result['status'] == 1 ? 1 : 2;
            DB::table('unbind_log')->insert($logData);

            if ($result['status'] == 1) {
                return array('status' => 1, 'info' => '家长助手解绑成功!');
            } else {
                return array('status' => 0, 'info' => '解绑失败!');
            }
        }
        if (strstr($categoryName, '手表')) {
            $result = $du->unbindWatch($orderInfo->imei);

            $logData['imei'] = $orderInfo->imei;
            $logData['status'] = $result['ok'] == 1 ? 1 : 2;
            DB::table('unbind_log')->insert($logData);

            if ($result['ok'] == 1) {
                return array('status' => 1, 'info' => '手表解绑成功!');
            } else {
                return array('status' => 0, 'info' => '解绑失败!');
            }
        }

        return array('status' => 0, 'info' => '该设备暂不支持一键解绑');
    }
}

/**
 * 批量处理维修知会
 */
class BatchReplicate extends BatchAction
{
    protected $action;

    public function __construct($action = 1)
    {
        $this->action = $action;
    }

    public function script()
    {
        if ($this->action == 1) {
            return <<<EOT
            $('{$this->getElementClass()}').on('click', function() {
                var html = '<div class="box box-info">'+
                                '<div class="box-header with-border">维修知会批处理<span style="color:orange">(勾选“已知会”订单状态修改为 “未检测已知会” 或 “已检测已知会”)<\/span><\/div>'+
                                '<div class="box-body" style="max-width:750px">'+
                                    '<div class="form-group">'+
                                        '<label class="control-label">维修知会<\/label>'+
                                        '<input type="checkbox" class="status la_checkbox" name="status" value="1" ><label class="control-label">已知会<\/label>'+
                                    '<\/div>'+
                                    '<div class="form-group">'+
                                        '<label for="order_remark" class="control-label">维修知会备注<\/label>'+
                                        '<div style="display:flex">'+
                                            '<input style="margin:1%" name="remarkRadioRepair" value="需要再次通知" type="radio"> 需要再次通知'+
                                            '<input style="margin:1%" name="remarkRadioRepair" value="已沟通未协商好" type="radio"> 已沟通未协商好'+
                                            '<input style="margin:1%" name="remarkRadioRepair" value="顾客有争议" type="radio"> 顾客有争议'+
                                            '<input style="margin:1%" name="remarkRadioRepair" value="终端有争议" type="radio"> 终端有争议'+
                                            '<input style="margin:1%" name="remarkRadioRepair" value="等待回复" type="radio"> 等待回复'+
                                        '<\/div>'+
                                        '<div class="input-group">'+
                                            '<span class="input-group-addon"><i class="fa fa-pencil"><\/i><\/span>'+
                                            '<input type="text" id="order_remark" name="order_remark" value="" class="form-control">'+
                                        '<\/div>'+
                                    '<\/div>'+
                                    '<button class="btn btn-sm btn-primary" id="save" >提交<\/button>'+
                                '<\/div>'+
                            '<\/div>';
                $.fancybox.open(html);

                $("input[name='remarkRadioRepair']").change(function(){
                    $('#order_remark').val($("input[name='remarkRadioRepair']:checked").val());
                })
                
                // 确定
                $('#save').click(function(){

                    status = $("input[name='status']:checked").val();
                    order_remark = $('#order_remark').val();
                    if(!order_remark){
                        layer.msg('备注不能为空');
                        return false;
                    }

                    $.ajax({
                        method: 'get',
                        url: '/admin/repair_check/batchIsTell',
                        data: {
                            ids: selectedRows(),
                            order_remark: order_remark,
                            status:status
                        },
                        success: function (res) {
                            // console.log(res)
                            if(res.status == 1){
                                // $.pjax.reload('#pjax-container');
                                toastr.success(res.info);
                                $('.grid-refresh').click();
                            } else {
                                toastr.error(res.info);
                            }
                        }, 
                        error:function(res){
                            toastr.error('操作失败');
                            // console.log(res)
                        }
                            
                    });
                });
                
            });
EOT;
        }

    }
}