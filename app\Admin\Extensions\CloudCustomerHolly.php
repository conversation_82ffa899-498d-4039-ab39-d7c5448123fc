<?php

namespace App\Admin\Extensions;

use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * 云客服系统对接（合力亿捷2025）
 * Create By Su Amber 2025/05/29
 * 坐席外呼接口: ''
 */
class CloudCustomerHolly {

    private $url = 'https://a8.7x24cc.com';
    private	$accountid = "N000000053078";//云呼账号
    private	$secret	= "25d21a70351f11f086dc8542ee47ae8b";//云呼密码
    private $appid = "uyvabuw8acs";//应用ID

    // accessToken缓存key
    const ACCESS_TOKEN_CACHE_KEY = 'cloud_customer_holly_access_token';
    // accessToken有效期（分钟）- 接口说明2小时有效，这里设置110分钟确保提前刷新
    const ACCESS_TOKEN_CACHE_MINUTES = 115;

    /**
     * 通用接口调用（外呼等功能）
     * @param string $phone 外呼号码
     * @param string $integratedid 云客服7x24平台的登录名、工号、手机号码或邮箱
     * @param array $options 可选参数数组，支持以下参数：
     *   - Variable: 外显号码，格式为 directCallerIDNum=5810xxxx
     *   - ExternalData: 业务参数，多个参数用逗号连接
     *   - ExtenType: 接听方式，支持 Local（直线方式）、sip（软电话）、gateway（语音网关/IP话机）
     *   - IsAES: 外呼号码是否已采用AES加密，传 true 表示已加密
     *   - dialoutType: 呼叫类型，workPhone（工作手机）、yunxi（云犀卡）
     * @param int $flag 功能标识，默认106（外呼）
     * @return array|string
     */
    public function curlRequest($phone, $integratedid, $options = [], $flag = 106){

        // 获取accessToken
        $tokenResult = $this->getAccessToken();
        if (!$tokenResult['success']) {
            return array(
                'errcode' => 401,
                'errmsg' => '获取accessToken失败: ' . $tokenResult['message']
            );
        }

        $accessToken = $tokenResult['accessToken'];

        // 构建基础请求参数
        $params = [
            'flag' => $flag,
            'account' => $this->accountid,
            'phonenum' => $phone,
            'integratedid' => $integratedid,
            'accessToken' => $accessToken
        ];

        // 添加可选参数
        if (!empty($options['Variable'])) {
            // 处理外显号码格式
            if (strpos($options['Variable'], 'directCallerIDNum') === false) {
                $params['Variable'] = 'directCallerIDNum=' . $options['Variable'];
            } else {
                $params['Variable'] = $options['Variable'];
            }
        }

        if (!empty($options['ExternalData'])) {
            $params['ExternalData'] = $options['ExternalData'];
        }

        if (!empty($options['ExtenType'])) {
            // 验证接听方式参数
            $validExtenTypes = ['Local', 'sip', 'gateway'];
            if (in_array($options['ExtenType'], $validExtenTypes)) {
                $params['ExtenType'] = $options['ExtenType'];
            }
        }

        if (isset($options['IsAES']) && $options['IsAES'] === true) {
            $params['IsAES'] = 'true';
        }

        if (!empty($options['dialoutType'])) {
            // 验证呼叫类型参数
            $validDialoutTypes = ['workPhone', 'yunxi'];
            if (in_array($options['dialoutType'], $validDialoutTypes)) {
                $params['dialoutType'] = $options['dialoutType'];
            }
        }

        // 构建完整URL
        $url = $this->url . '/commonInte?' . http_build_query($params);

        $header = [];
        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url); // 地址
        curl_setopt($ch, CURLOPT_HTTPGET, true); // GET请求
        curl_setopt($ch, CURLINFO_HEADER_OUT, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

        $return = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if($return === FALSE ){
            $error = curl_error($ch);
            curl_close($ch);
            return array(
                'errcode' => 400,
                'errmsg' => 'curl请求失败: ' . $error
            );
        }

        curl_close($ch);

        // 检查HTTP状态码
        if ($httpCode !== 200) {
            return array(
                'errcode' => $httpCode,
                'errmsg' => 'HTTP请求失败，状态码: ' . $httpCode
            );
        }

        return $return;
    }

    /**
     * 获取accessToken（优先从缓存获取）
     * @return array 返回格式：['success' => true/false, 'accessToken' => 'token值', 'message' => '消息']
     */
    public function getAccessToken()
    {
        // 先尝试从缓存获取
        $cachedToken = Cache::get(self::ACCESS_TOKEN_CACHE_KEY);

        if ($cachedToken) {
            return [
                'success' => true,
                'accessToken' => $cachedToken,
                'message' => '从缓存获取accessToken成功',
                'source' => 'cache'
            ];
        }

        // 缓存中没有，调用接口获取
        return $this->fetchAccessTokenFromApi();
    }

    /**
     * 从接口获取accessToken并缓存
     * @return array 返回格式：['success' => true/false, 'accessToken' => 'token值', 'message' => '消息']
     */
    private function fetchAccessTokenFromApi()
    {
        // 构建请求URL
        $url = $this->url . '/accessToken';
        $params = [
            'account' => $this->accountid,
            'appid' => $this->appid,
            'secret' => $this->secret
        ];

        $requestUrl = $url . '?' . http_build_query($params);

        // 发起GET请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $requestUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json',
            'Content-Type: application/json'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // 检查curl错误
        if ($response === false || !empty($error)) {
            return [
                'success' => false,
                'accessToken' => '',
                'message' => 'curl请求失败: ' . $error,
                'source' => 'api'
            ];
        }

        // 检查HTTP状态码
        if ($httpCode !== 200) {
            return [
                'success' => false,
                'accessToken' => '',
                'message' => 'HTTP请求失败，状态码: ' . $httpCode,
                'source' => 'api'
            ];
        }

        // 解析响应
        $result = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'accessToken' => '',
                'message' => 'JSON解析失败: ' . json_last_error_msg(),
                'source' => 'api'
            ];
        }

        // 检查接口返回结果
        if (!isset($result['success']) || $result['success'] !== true) {
            return [
                'success' => false,
                'accessToken' => '',
                'message' => $result['message'] ?? '获取accessToken失败',
                'source' => 'api'
            ];
        }

        // 获取成功，缓存token
        $accessToken = $result['accessToken'] ?? '';
        if (!empty($accessToken)) {
            Cache::put(self::ACCESS_TOKEN_CACHE_KEY, $accessToken, self::ACCESS_TOKEN_CACHE_MINUTES);
        }

        return [
            'success' => true,
            'accessToken' => $accessToken,
            'message' => $result['message'] ?? '获取accessToken成功',
            'invalidTime' => $result['invalidTime'] ?? '',
            'source' => 'api'
        ];
    }

    /**
     * 清除accessToken缓存（强制下次重新获取）
     * @return bool
     */
    public function clearAccessTokenCache()
    {
        return Cache::forget(self::ACCESS_TOKEN_CACHE_KEY);
    }

    /**
     * 强制刷新accessToken（清除缓存后重新获取）
     * @return array
     */
    public function refreshAccessToken()
    {
        $this->clearAccessTokenCache();
        return $this->fetchAccessTokenFromApi();
    }

    /**
     * 外呼接口便捷方法
     * @param string $phonenum 外呼号码
     * @param string $integratedid 坐席标识（登录名、工号、手机号码或邮箱）
     * @param string|null $externalData 业务参数（可选）
     * @param string|null $callerIdNum 外显号码（可选）
     * @return array|string
     */
    public function makeCall($phonenum, $integratedid, $externalData = null, $callerIdNum = null)
    {
        $options = [];

        if (!empty($externalData)) {
            $options['ExternalData'] = $externalData;
        }

        if (!empty($callerIdNum)) {
            $options['Variable'] = $callerIdNum;
        }

        return $this->curlRequest($phonenum, $integratedid, $options, 106);
    }

    /**
     * 工作手机外呼
     * @param string $phonenum 外呼号码
     * @param string $integratedid 坐席标识
     * @param string|null $externalData 业务参数（可选）
     * @return array|string
     */
    public function makeWorkPhoneCall($phonenum, $integratedid, $externalData = null)
    {
        $options = [
            'dialoutType' => 'workPhone'
        ];

        if (!empty($externalData)) {
            $options['ExternalData'] = $externalData;
        }

        return $this->curlRequest($phonenum, $integratedid, $options, 106);
    }

    /**
     * 云犀卡外呼
     * @param string $phonenum 外呼号码
     * @param string $integratedid 坐席标识
     * @param string|null $externalData 业务参数（可选）
     * @return array|string
     */
    public function makeYunxiCall($phonenum, $integratedid, $externalData = null)
    {
        $options = [
            'dialoutType' => 'yunxi'
        ];

        if (!empty($externalData)) {
            $options['ExternalData'] = $externalData;
        }

        return $this->curlRequest($phonenum, $integratedid, $options, 106);
    }
}