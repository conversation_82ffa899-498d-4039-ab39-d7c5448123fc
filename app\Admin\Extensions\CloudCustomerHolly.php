<?php

namespace App\Admin\Extensions;

use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * 云客服系统对接（合力亿捷2025）
 * Create By Su Amber 2025/05/29
 * 坐席外呼接口: ''
 */
class CloudCustomer {

    private $url = 'https://a8.7x24cc.com';
    private	$accountid = "N000000053078";//云呼账号
    private	$secret	= "25d21a70351f11f086dc8542ee47ae8b";//云呼密码
    private $appid = "uyvabuw8acs";//应用ID

    // accessToken缓存key
    const ACCESS_TOKEN_CACHE_KEY = 'cloud_customer_holly_access_token';
    // accessToken有效期（分钟）- 接口说明2小时有效，这里设置110分钟确保提前刷新
    const ACCESS_TOKEN_CACHE_MINUTES = 115;

    /**
     * 接口调用
     */
    public function curlRequest($partUrl , $data){

        $time =	date("YmdHis");
    	$authorization = base64_encode($this->accountid . ":" . $time);
    	$sig = strtoupper(md5($this->accountid . $this->secret . $time));

        $url = $this->url . $partUrl . $this->accountid . "?sig=" . $sig;



        $header = [];
        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "Content-Length: ".strlen( json_encode($data) );
        $header[] = "Authorization: ".$authorization;

        $ch = curl_init ();
        curl_setopt($ch, CURLOPT_URL, ($url) );//地址
        curl_setopt($ch, CURLOPT_POST, 1);   //请求方式为post
        curl_setopt($ch, CURLOPT_POSTFIELDS,json_encode($data)); //post传输的数据。
        curl_setopt($ch, CURLINFO_HEADER_OUT, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);

        $return = curl_exec ( $ch );

        if($return === FALSE ){
            return array(
                'errcode' => 400,
                'errmsg' => curl_error($ch)
            );
        }

        curl_close ( $ch );

        return $return;
    }

    /**
     * 获取accessToken（优先从缓存获取）
     * @return array 返回格式：['success' => true/false, 'accessToken' => 'token值', 'message' => '消息']
     */
    public function getAccessToken()
    {
        // 先尝试从缓存获取
        $cachedToken = Cache::get(self::ACCESS_TOKEN_CACHE_KEY);

        if ($cachedToken) {
            return [
                'success' => true,
                'accessToken' => $cachedToken,
                'message' => '从缓存获取accessToken成功',
                'source' => 'cache'
            ];
        }

        // 缓存中没有，调用接口获取
        return $this->fetchAccessTokenFromApi();
    }

    /**
     * 从接口获取accessToken并缓存
     * @return array 返回格式：['success' => true/false, 'accessToken' => 'token值', 'message' => '消息']
     */
    private function fetchAccessTokenFromApi()
    {
        // 构建请求URL
        $url = $this->url . '/accessToken';
        $params = [
            'account' => $this->accountid,
            'appid' => $this->appid,
            'secret' => $this->secret
        ];

        $requestUrl = $url . '?' . http_build_query($params);

        // 发起GET请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $requestUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json',
            'Content-Type: application/json'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // 检查curl错误
        if ($response === false || !empty($error)) {
            return [
                'success' => false,
                'accessToken' => '',
                'message' => 'curl请求失败: ' . $error,
                'source' => 'api'
            ];
        }

        // 检查HTTP状态码
        if ($httpCode !== 200) {
            return [
                'success' => false,
                'accessToken' => '',
                'message' => 'HTTP请求失败，状态码: ' . $httpCode,
                'source' => 'api'
            ];
        }

        // 解析响应
        $result = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'accessToken' => '',
                'message' => 'JSON解析失败: ' . json_last_error_msg(),
                'source' => 'api'
            ];
        }

        // 检查接口返回结果
        if (!isset($result['success']) || $result['success'] !== true) {
            return [
                'success' => false,
                'accessToken' => '',
                'message' => $result['message'] ?? '获取accessToken失败',
                'source' => 'api'
            ];
        }

        // 获取成功，缓存token
        $accessToken = $result['accessToken'] ?? '';
        if (!empty($accessToken)) {
            Cache::put(self::ACCESS_TOKEN_CACHE_KEY, $accessToken, self::ACCESS_TOKEN_CACHE_MINUTES);
        }

        return [
            'success' => true,
            'accessToken' => $accessToken,
            'message' => $result['message'] ?? '获取accessToken成功',
            'invalidTime' => $result['invalidTime'] ?? '',
            'source' => 'api'
        ];
    }

    /**
     * 清除accessToken缓存（强制下次重新获取）
     * @return bool
     */
    public function clearAccessTokenCache()
    {
        return Cache::forget(self::ACCESS_TOKEN_CACHE_KEY);
    }

    /**
     * 强制刷新accessToken（清除缓存后重新获取）
     * @return array
     */
    public function refreshAccessToken()
    {
        $this->clearAccessTokenCache();
        return $this->fetchAccessTokenFromApi();
    }
}