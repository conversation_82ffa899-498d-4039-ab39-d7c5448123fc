<?php

namespace App\Admin\Controllers;

use App\Models\Agency;
use App\Models\Machine;
use App\Models\MachineAccessory;
use App\Models\AccessoryPriceOffer;
use App\Admin\Extensions\AccessoryPriceOfferExporter;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\MessageBag;
use Maatwebsite\Excel\Facades\Excel;

class AccessoryPriceOfferController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {
            $model_id = Input::get('model_id');
            $model = Machine::where('model_id', '=', $model_id)->value('name');
            $content->header("$model 配件报价列表");
            $content->description('机型配件列表');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form()->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('添加配件');
            $content->description('添加配件');

            $content->body($this->form());
        });
    }

    public function upload(Request $request)
    {
        if (request()->isMethod('post')) {
            $path = $request->file('excel')->getRealPath();
            $model_id = $request->get('model_id');
            $res = [];
            Excel::load($path, function ($reader) use (&$res) {
                $reader = $reader->getSheet(0);
                $res = $reader->toArray();
            });
            if (count($res) < 2) {
                $error = new MessageBag([
                    'title' => '文件错误',
                    'message' => '不是Excel文件或表格不符合要求',
                ]);
                return back()->with(compact('error'));
            }
            $head = $res[0];
            $data = array_slice($res, 1);
//            dd($res);
            $toSave = [];
            foreach ($data as $k => $v) {
                foreach ($head as $k1 => $name) {
                    if ($name == null || $name == '') {
                        continue;
                    }
                    $toSave[$name] = $v[$k1];
                }
                $toSave['model_id'] = $model_id;
                if (!array_key_exists("title", $toSave)) {
                    $error = new MessageBag([
                        'title' => '文件错误',
                        'message' => '不是Excel文件或没有表头title',
                    ]);
                    return back()->with(compact('error'));
                }
                if (!array_key_exists("price", $toSave)) {
                    $error = new MessageBag([
                        'title' => '文件错误',
                        'message' => '不是Excel文件或没有表头price',
                    ]);
                    return back()->with(compact('error'));
                }
                $db = DB::table('accessory_price_offer')->where([['title', $toSave['title']], ['model_id', $model_id]])->pluck('id')->toArray();
//                dump($toSave);
                if (array_key_exists(0, $db)) {
                    $toSave['updated_at'] = date('Y-m-d H:i:s');
                    DB::table('accessory_price_offer')->where('id', $db[0])->update($toSave);
                } else {
                    $toSave['created_at'] = date('Y-m-d H:i:s');
                    $toSave['updated_at'] = date('Y-m-d H:i:s');
                    DB::table('accessory_price_offer')->insert($toSave);
                }
            }
            return redirect('/admin/accessory_price_offer?model_id='.$model_id);
        }
        return Admin::content(function (Content $content) {
            $model_id = Input::get('model_id');
            $model = Machine::where('model_id', '=', $model_id)->value('name');
            $content->header("$model 导入配件报价");
            $content->description('');
            $form = new \Encore\Admin\Widgets\Form();
            $form->action('/admin/accessory_price_offer/upload');
            $form->hidden('model_id')->default($model_id);
            $form->file("excel", "请选择Excel文件");
            $content->body($form);
        });
    }

    /**
     * Make a grid builder.
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(AccessoryPriceOffer::class, function (Grid $grid) {
            $model_id = Input::get('model_id');
//            $grid->disableFilter();
            $grid->model()->where('model_id', '=', $model_id);
            $grid->title('配件名称')->editable();
            $grid->price('配件价格(元)')->editable();
            $grid->created_at('创建时间');
            $grid->updated_at('修改时间');

            //查询过滤器
            $grid->filter(function($filter) {
                $filter->disableIdFilter();
                $filter->is('model_id', '机型')->select(Machine::all()->pluck('name', 'model_id'));

            });

            $grid->actions(function ($actions) {
                $actions->disableEdit();
            });
//            $grid->disableExport();
            $grid->exporter(new AccessoryPriceOfferExporter());
            $grid->disableCreation();
            $grid->tools(function ($tools) use ($model_id) {
                $tools->batch(function ($batch) {
//                    $batch->disableDelete();
                });
                $exportImagesButton = <<<EOF
                     <div class="btn-group pull-right" style="margin-left:10px">
                      <a href ="machine_type" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 返回
                      </a >
                    </div >
                    <div class="btn-group pull-right" style="margin-right:10px">
                      <a href ="accessory_price_offer/create?model_id=$model_id" class="btn btn-sm btn-success" >
                        <i class="fa fa-print" ></i > 新增配件
                      </a >
                    </div >
                    <div class="btn-group pull-right" style="margin-right:10px">
                      <a href ="accessory_price_offer/upload?model_id=$model_id" class="btn btn-sm btn-success" >
                        <i class="fa fa-print" ></i > 导入配件
                      </a >
                    </div >
EOF;
                $tools->append($exportImagesButton);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Admin::form(AccessoryPriceOffer::class, function (Form $form) {
            $model_id = Input::get('model_id');
            $form->hidden('model_id')->value($model_id);
            $form->display('id', 'ID');
            $form->text('title', '配件名称')->rules('required');
            $form->text('price', '价格')->help('单位 : 元');
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '修改时间');

            $form->tools(function (Form\Tools $tools) {
                $tools->disableListButton();
            });
        });
    }

    public function getAccessoriesByModelId()
    {
        $model_id = Input::get('model_id');
        $accessory = AccessoryPriceOffer::where('model_id', '=', $model_id)->get();
        $accessory->map(function ($item) {
            $item->title = $item->accessory->title;

            return $item;
        });
        return $accessory;

    }


}
