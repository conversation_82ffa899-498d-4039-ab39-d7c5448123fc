<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>an
 * Date: 2017/8/17
 * Time: 10:18
 */

namespace App\Api\Controllers;

use App\Api\Permission\Api;
use App\Http\Controllers\Controller;
use App\Http\Middleware\ApiPermission;
use Dingo\Api\Routing\Helpers;
use Illuminate\Support\Facades\Input;
use Tymon\JWTAuth\Facades\JWTAuth;

class BaseController extends Controller {
    use Helpers;

    public function returnArray($result = '', $code = 200, $message = 'success') {
        $res = [
            'message'     => $message,
            'status_code' => $code,
            'data'        => $result,
        ];
        return $this->response->array($res)->setStatusCode($code);
    }

    /**
     * 检测是否有token以及token是否正确,为什么不放在路由
     * 因为用户可以不登录查看部分信息,所以不能在路由直接拦截
     * @return bool
     */
    public function checkToken() {
        if (!empty(Input::header()['authorization'])) {
            $user = JWTAuth::parseToken()->authenticate();
            return $user ? true : false;
        }
        return false;
    }
}