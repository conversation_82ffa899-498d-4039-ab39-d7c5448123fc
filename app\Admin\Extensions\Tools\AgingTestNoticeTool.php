<?php


namespace App\Admin\Extensions\Tools;


use Encore\Admin\Grid\Tools\BatchAction;

class AgingTestNoticeTool extends BatchAction
{
    public function script()
    {
        return <<<EOF

$('{$this->getElementClass()}').on('click', function(){
    var res = confirm("确定要发送老化测试通知短信?");
    if (res == true){
        $.ajax({
            method: 'post',
            url: '{$this->resource}/aging_test_notice',
            data: {
                _token: LA.token,
                ids: selectedRows()
            },
            success: function () {
                $.pjax.reload('#pjax-container');
                toastr.success('操作成功');
            }
        });
    
    }else{
        alert("You pressed Cancel!");
    }
});
EOF;
    }
}
