<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class MaterialMessage extends Model
{
    //
    protected $table = 'material_message';


    public static function boot() {

        parent::boot();

        static::updating(function (Model $model) {

            $replyContent = DB::table('material_message')->where('id','=',$model->id)->value('reply_content');
            if ($model->reply_content&&!$replyContent){
                $model->replied_at = date('Y-m-d H:i:s',time());
            }

        });

    }
}
