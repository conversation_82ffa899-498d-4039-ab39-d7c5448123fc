<?php


namespace App\Admin\Controllers;


use App\Admin\Extensions\BrokenScreenInsuranceExporter;
use App\Admin\Extensions\MessagePush;
use App\Admin\Extensions\Sms;
use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\BrokenScreenInsurance;
use App\Models\BrokenScreenInsuranceStandard;
use App\Models\Machine;
use App\Models\MachineCategory;
use App\Models\Warranty;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Request;
use Symfony\Component\Yaml\Tests\B;
use function foo\func;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\MessageBag;


class BrokenScreenInsuranceSupplyController extends Controller
{
    use ModelForm;

    public function index()
    {
        return Admin::content(function (Content $content) {
            $content->header('碎屏保');
            $content->description('碎屏保列表');
            $content->body($this->grid());
        });
    }

    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {
            $content->header('碎屏保');
            $content->description('碎屏保补录');
            $content->body($this->form($id)->edit($id));
        });
    }

    public function create()
    {
        return Admin::content(function (Content $content) {
            $content->header('新增');
            $content->description('碎屏保补录');
            $content->body($this->form());
        });
    }

    /**
     * Create interface.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function grid()
    {
        return Admin::grid(BrokenScreenInsurance::class, function (Grid $grid) {

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->is('top_agency', '一级代理')
                    ->select(Agency::where([['level', 1], ['deleted_at', '=', null]])
                        ->pluck('name', 'id'))
                    ->load('second_agency', '/admin/agency/secondAgency');
                $filter->is('second_agency', '二级代理')->select();
                $filter->like('endpoint_name', '终端名称');
                $filter->like('sn', '单号');
                $filter->like('barcode', '条码');
                $filter->like('name', '投保人');
                $filter->is('model_id', '机型')->select(Machine::where('visibility', 1)->pluck('name', 'model_id'));
                $filter->is('standard_name.standard_id', '投保类型')->select(BrokenScreenInsurance::STANDARD_TYPE);
                $filter->is('type', '购买方式')->select(BrokenScreenInsurance::BUY_TYPE);
                $filter->is('is_direct_sales', '是否直营')->select(BrokenScreenInsurance::IS_DIRECT_SALES);
                $filter->is('status', '保单状态')->select(BrokenScreenInsurance::STATUS);
                $filter->is('trade_plat', '交易方式')->select(BrokenScreenInsurance::TRADE_PLAT);
                $filter->like('pay_id', '交易单号');
                $filter->like('refund_id', '退款单号');
                $filter->between('created_at', '投保时间')->datetime();
                $filter->between('audited_at', '审核时间')->datetime();
                $filter->between('refund_at', '退款时间')->datetime();
            });
            $grid->tools(function ($tools) {
                $button = <<<EOF
<!--                <div class="btn-group pull-right" style="margin-right:0px; margin-right: 10px;">-->
<!--                      <a href ="broken_screen_insurance?_export=1" class="btn btn-sm btn-twitter" >-->
<!--                        <i class="fa fa-download" ></i > 导出-->
<!--                      </a >-->
<!--                    </div >-->
                    <div class="btn-group pull-right" style="margin-top:10px; margin-right: 10px;">
                      <a href ="broken_screen_insurance/supply" class="btn btn-sm btn-warning" >
                        <i class="fa fa-save" ></i > 补录
                      </a >
                    </div >
EOF;

                $tools->append($button);
            });
//            dd($grid);
            $grid->disableCreation();
//            $grid->disableExport();
            $grid->exporter(new BrokenScreenInsuranceExporter());
            $grid->model()->orderBy('id', 'desc');
            $grid->id('ID');
            $grid->sn('保单编号');
            $grid->barcode('条码');
            $grid->model_name('机型');
            $grid->name('投保人');
            $grid->pay_amount('支付金额');
            $grid->column('standard_name.name', '投保类型');
            $grid->endpoint_name('终端名称');
            $grid->trade_plat('支付方式')->display(function ($trade_plat) {
                return BrokenScreenInsurance::TRADE_PLAT[$trade_plat];
            });
            $grid->pay_id('交易单号');
            $grid->refund_id('退款单号');
            $grid->status('审核状态')->display(function ($status) {
                $s = BrokenScreenInsurance::STATUS;
                if (array_key_exists($status, $s)) {
                    return $s[$status];
                }
                return '-----';
            });
            $grid->remark('备注');
            $grid->audited_at('审核时间');
            $grid->created_at('投保时间')->sortable();
            $grid->refund_at('退款时间')->sortable();
            $grid->is_direct_sales('是否直营')->display(function ($is_direct_sales) {
                return BrokenScreenInsurance::IS_DIRECT_SALES[$is_direct_sales];
            });
            $grid->type('购买类型')->display(function ($type) {
                return BrokenScreenInsurance::BUY_TYPE[$type];
            });
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();

                $status = $actions->row->status;
                $c = 'broken_screen_insurance/view/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">查看 </span></a>';
                $actions->append($html);
                if ($status == 100) {
                    $c = 'broken_screen_insurance/' . $actions->getKey();
                    $html = '<a href="' . $c . '/edit">【审核】</a>';
                    $actions->append($html);
                }
                if (in_array($status, [200, -200])) {
                    $c = 'broken_screen_insurance/' . $actions->getKey();
                    $html = '<a href="' . $c . '/edit">【修改】</a>';
                    $actions->append($html);
                }
            });
        });
    }


    public function form($id = null)
    {
        $script = <<<js

    // 条码变更
    $(document).on('change', '.barcode', function() {
        var value = $(this).val();
        var target = $(".standard");
        $.ajax({
            url: "/admin/broken_screen_insurance_supply/check?barcode=" + value,
            type: "GET",
            dataType: "json",
            success: function(data) {

                if (data['ok'] == 0) {
                    alert(data['message']);
                    window.location.reload();
                } else {
                    target.find("option").remove();
                    setdata = $.map(data['data']['standard'], function (d) {
                        d.id = d.id;
                        d.text = d.text;
                        return d;
                    });
                    $(target).select2({
                        data: setdata
                    }).trigger('change');
                }

            }
        });
    });
js;
        Admin::script($script);
        return Admin::form(BrokenScreenInsurance::class, function (Form $form) use ($id) {
            $form->html('<label>投保信息</label>');
            $form->text('barcode', '条码')->help('必填');
            $form->text('name', '姓名')->help('必填');
            $form->text('phone', '手机')->help('必填');
            $form->text('identity_card', '身份证号');
            $form->select('standard', '投保类型');
            $form->select('type', '购买方式')->options(BrokenScreenInsurance::BUY_TYPE);
            $form->multipleImage('image_path', '图片')->move('rbcare/broken_screen_insurance/upload/' . Admin::user()->id);
            $form->hidden('model_id');
            $form->hidden('model_name');
            $form->hidden('buy_date');
            $form->hidden('top_agency');
            $form->hidden('second_agency');
            $form->hidden('pay_amount');
            $form->hidden('month');
            $form->hidden('amount');
            $form->hidden('insurance_times');
            $form->hidden('insurance_times_remain');
            $form->hidden('endpoint_channel');
            $form->hidden('is_direct_sales');
            $form->hidden('status');
            $form->hidden('sn');
            $form->hidden('supply_at');
            $form->hidden('is_supply');
            $form->hidden('endpoint');
            $form->hidden('endpoint_name');


            $form->saving(function ($form) {

                // 各字段数值检查
                if (!$form->barcode) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '条码不能为空',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                if (!$form->name) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '姓名不能为空',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                if (!$form->phone) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '手机号不能为空',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                if (!preg_match("/^1[3456789]{1}\d{9}$/", $form->phone)) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '请填写正确电话',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                if ($form->identity_card && !preg_match("/^[1-9]\d{5}(19|20)\d{2}[01]\d[0123]\d\d{3}[xX\d]$/", $form->identity_card)) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '请填写正确身份证号',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                if (!$form->standard) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '投保标准不能为空',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                if (!$form->type) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '购买方式不能为空',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }

                // 检查是否申请过碎屏保
                $insurance = BrokenScreenInsurance::where('barcode', $form->barcode)
                    ->whereNotIn('status', [BrokenScreenInsurance::STATUS_CLOSED, BrokenScreenInsurance::STATUS_VOIDED])
                    ->select('status')->value('status');
                $status = [100, 200, 300, 400, 500];
                if (in_array($insurance, $status)) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '此条码已申请过碎屏保，无法再次申请',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }

                // 检查条码保卡
                $warranty = DB::connection('mysql2')
                    ->table('warranty')
                    ->where([['barcode', '=', $form->barcode], ['status', '=', '1']])
                    ->select('customer_name', 'customer_phone', 'buy_date', 'model_id', 'model', 'endpoint')
                    ->first();
                // dd($warranty->endpoint);
                if (empty($warranty)) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '此条码无保卡',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                $endpoint = DB::connection('mysql2')->table('endpoint as e')
                    ->where([['e.id', '=', $warranty->endpoint]])
                    ->leftjoin('agency as a', 'e.top_agency', '=', 'a.id')
                    ->select('e.name', 'e.top_agency', 'e.second_agency', 'e.is_direct_sales', 'a.channel as endpoint_channel')
                    ->first();
                // 直营电商才可以赠送
                if ($form->type == 2 && ($endpoint->is_direct_sales != 1 || $endpoint->endpoint_channel != 'e_commerce')) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '此终端渠道类型非电商，购买方式错误',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                // 检查机型投保标准
                $standards = BrokenScreenInsuranceStandard::where([['model_id', $warranty->model_id], ['visible', 1]])
                    ->select('id', 'name', 'amount', 'pay_amount', 'month')
                    ->get()->toArray();
                if (empty($standards)) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '此机型无投保标准，无法投保',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                // 检查提交的投保标准
                $this_standard = BrokenScreenInsuranceStandard::where([['id', $form->standard], ['visible', 1]])
                    ->select('id', 'name', 'amount', 'pay_amount', 'month', 'insurance_times')
                    ->first();
                if (empty($this_standard)) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '无此投保标准，无法投保',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }

                // 赋值
                $form->model_id = $warranty->model_id;
                $form->model_name = $warranty->model;
                $form->buy_date = $warranty->buy_date;
                $form->top_agency = $endpoint->top_agency;
                $form->second_agency = $endpoint->second_agency;
                $form->pay_amount = $this_standard->pay_amount;
                $form->month = $this_standard->month;
                $form->amount = $this_standard->amount;
                $form->insurance_times = $this_standard->insurance_times;
                $form->insurance_times_remain = $this_standard->insurance_times;
                $form->endpoint_channel = $endpoint->endpoint_channel;
                $form->is_direct_sales = $endpoint->is_direct_sales;
                $form->status = BrokenScreenInsurance::STATUS_CHECK_PENDING;
                $form->endpoint = $warranty->endpoint;
                $form->endpoint_name = $endpoint->name;
                $now = date('YmdHis');
                $sn = $now . 'spb' . rand(100000, 999999);
                $form->sn = $sn;
                $form->is_supply = 1;
                $form->supply_at = date('Y-m-d H:i:s');
            });
            $form->saved(function ($form) {
                return redirect('/admin/broken_screen_insurance');

            });
        });
    }

    public function view(Content $content, $id = null)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修审核--查看');
            $insurance_order = BrokenScreenInsurance::where(['id' => $id])->first();
            $content->body(view('admin/broken_screen_insurance/view', compact(['insurance_order'])));
        });
    }

    public function standard()
    {
        $barcode = Request::get('q');

    }

    public function check()
    {

        $barcode = request()->get('barcode');
        if (empty($barcode)) {
            $ret = array(
                'ok' => 0,
                'message' => '条码不能为空'
            );
            return $ret;
        }
        $insurance = BrokenScreenInsurance::where('barcode', $barcode)
            ->whereNotIn('status', [-400, -500])
            ->select('status')->value('status');
        $status = [100, 200, 300, 400, 500];
        if (in_array($insurance, $status)) {
            $ret = array(
                'ok' => 0,
                'message' => '此条码已申请过碎屏保，无法再次申请'
            );
            return $ret;
        }
        $warranty = DB::connection('mysql2')
            ->table('warranty')
            ->where([['barcode', '=', $barcode], ['status', '=', '1']])
            ->select('customer_name', 'customer_phone', 'buy_date', 'model_id', 'model', 'endpoint')
            ->first();
//        dd($warranty);
        if (empty($warranty)) {
            $a = array(
                'ok' => 0,
                'message' => '此条码无保卡'
            );
            return $a;
        }
//        dd($warranty);
        $standard = BrokenScreenInsuranceStandard::where([['model_id', $warranty->model_id], ['visible', 1]])
            ->select('id', 'name', 'amount', 'pay_amount', 'month')
            ->get()->toArray();
        if (empty($standard)) {
            $a = array(
                'ok' => 0,
                'message' => '此机型无投保标准，无法投保'
            );
            return $a;
        }
        $standard_array = array();
        foreach ($standard as $s) {
            $standard_array[$s['id']]['id'] = $s['id'];
            $standard_array[$s['id']]['text'] = $s['name'] . "(保修期" . $s['month'] . "个月，金额：" . $s['amount'] . ',支付金额：' . $s['pay_amount'] . ')';

        }
        $ret['ok'] = 1;
//        $ret['data']['warranty'] = $warranty;
        $ret['data']['standard'] = $standard_array;
        return $ret;
    }
}
