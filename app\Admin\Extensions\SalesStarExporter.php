<?php
/**
 * Created by PhpStorm.
 * User: Xian
 * Date: 2017/9/4
 * Time: 15:51
 */

namespace App\Admin\Extensions;

use App\Traits\ExcelExportTrait;

class SalesStarExporter {
    public static function detailExport($data = []) {
        if (!empty($data)) {
            $filename = "销售之星详情";
            $titles = [
                '排名',
                '总代',
                '二代',
                '终端店名',
                '终端地址',
                '终端联系方式',
                '终端导购',
                '销售数量',
            ];
            $formatData = self::formatDetailData($data->toArray(), $titles);
            ExcelExportTrait::exportToExcel($filename, $formatData);
        }
    }

    private static function formatDetailData($data, $titles) {
        $formatData = [];
        foreach ($data as $key => $value) {
            $row = [
                $key + 1,
                $value['endpoints']['top_agency'],
                $value['endpoints']['second_agency'],
                $value['endpoints']['name'],
                $value['endpoints']['address'],
                $value['endpoints']['phone'],
                $value['salesman'],
                $value['count'],

            ];
            $formatData[] = $row;
        }
        array_unshift($formatData, $titles);
        return $formatData;
    }

    public static function rankExport($data = []) {
        if (!empty($data)) {
            $filename = "销售之星排名";
            $titles = [
                '排名',
                '总代',
                '二代',
                '终端店名',
                '终端地址',
                '终端联系方式',
                '终端导购',
                '销售数量',
            ];
            $formatData = self::formatRankData($data, $titles);
            ExcelExportTrait::exportToExcel($filename, $formatData);
        }
    }

    private static function formatRankData($data, $titles) {
        $formatData = [];
//        dd($data);
        foreach ($data as $key => $value) {
            $row = [
                $value['rank'],
                $value['top_agency'],
                $value['second_agency'],
                $value['name'],
                $value['address'],
                $value['phone'],
                $value['salesman'],
                $value['count'],

            ];
            $formatData[] = $row;
        }
        array_unshift($formatData, $titles);
        return $formatData;
    }

}