<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\RepairBillExporter;
use App\Models\AfterSalesEndpoint;
use App\Models\Agency;
use App\Models\Machine;
use App\Models\MachineAccessory;
use App\Models\MachineAccessoryRelation;
use App\Models\MachineMalfunction;
use App\Models\RepairBill;

use App\Models\RepairBillMachineAccessoryRelation;
use App\Models\UserAfterSales;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Widgets\Table;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;

//维修工单管理
class RepairBillController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('维修工作单');
            $content->description('维修工作单');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('维修工作单');
            $content->description('维修工作单');

            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('维修工作单');
            $content->description('维修工作单');

            $content->body($this->form());
        });
    }

    protected function getRepairEndpointIdsByRole()
    {
        //判断角色,如果是总代的话,只显示自己所属的帐号
        if (Admin::user()->inRoles(['topAgency'])) {
            //获取当前用户的总代id
            $topAgencyId = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('top_agency');
            $secondAgencyId = DB::table('agency')->where('pid', '=', $topAgencyId)->pluck('id')->toArray();
            array_push($secondAgencyId, $topAgencyId);
            $endpointIds = DB::table('after_sales_endpoint')->whereIn('agency_id', $secondAgencyId)->pluck('id');
        } elseif (Admin::user()->inRoles(['secondAgency'])) {
            //获取当前用户的二代id
            $secondAgencyId = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('second_agency');
            $endpointIds = DB::table('after_sales_endpoint')->where('agency_id', '=', $secondAgencyId)->pluck('id');
        } else {
            $endpointIds = null;
        }

        return $endpointIds;
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {

        return Admin::grid(RepairBill::class, function (Grid $grid) {

            $endpointIds = $this->getRepairEndpointIdsByRole();
            $deleteFlag = 1;
            if ($endpointIds) {
                $grid->model()->whereIn('endpoint_id', $endpointIds);

                //总代和二代不能删除
                $deleteFlag = 0;
            }
            $grid->exporter(new RepairBillExporter());
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->between('created_at', '创建时间')->datetime();
                $filter->where(function ($query) {
                    //先找出所有二代
                    $secondAgencies = Agency::where('pid',$this->input)->pluck('id')->toArray();
                    //合并总代直属的终端
                    $secondAgencies = array_merge($secondAgencies, array(intval($this->input)));
                    $endpointIds = AfterSalesEndpoint::whereIn('agency_id',$secondAgencies)->pluck('id')->toArray();
                    $query->whereIn('endpoint_id',$endpointIds);
                }, '维修人员所属总代')->select(Agency::top()->pluck('name', 'id'));
                $filter->like('bill_number', '工单编号');
                $filter->like('customer_phone', '客户电话');
                $filter->like('customer_name', '客户姓名');
            });

            if (Admin::user()->inRoles(['afterSalesEngineer', 'administrator'])) {
                $states = [
                    'on' => ['value' => 1, 'text' => '已出'],
                    'off' => ['value' => 0, 'text' => '未出'],
                ];
                $grid->column('is_checked', '出单情况')->switch($states)->sortable();
                $grid->repair_source('维修来源');
                $grid->bill_number('工单编号');
                $grid->sn_code('条码')->display(function ($value) {
                    return $value ?: '无';
                });
                $grid->machine()->name('产品机型');
                $grid->customer_name('顾客姓名');
                $grid->column('故障类别')->expand(function () {
                    $malfunctions = MachineMalfunction::all()->pluck('title', 'id');
                    $malfunctionIds = array_column($this->machine_malfunction_relation, 'malfunction_id');
                    $result = [];
                    foreach ($malfunctionIds as $key => $value) {
                        $result['故障' . ($key + 1)] = $malfunctions[$value];
                    }

                    return new Table([], $result);
                }, '信息');
                $grid->repair_cost('维修费用总计(元)');
                $grid->model()->with('machine_malfunction_relation')
                    ->where('create_uid', '=', Admin::user()->id)
                    ->orderBy('created_at', 'desc');
            } else {
                $grid->disableCreation();
                $grid->bill_number('工单编号');
                $grid->admin_user()->name('处理人员');
                $grid->column('endpoint.name', '终端名称');
                $grid->column('所属维修点')->expand(function () {
                    $endpoint = [
                        '维修点名称' => $this->endpoint['name'],
                        '维修点地址' => $this->endpoint['address'],
                        '维修点电话' => $this->endpoint['phone'],
                    ];

                    return new Table([], $endpoint);
                }, '信息');
                $grid->model()->orderBy('created_at', 'desc');
            }


            $grid->created_at('创建日期');

            $grid->tools(function ($tools) {
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                });
            });

            $grid->actions(function ($action) use ($deleteFlag) {
                if (!$deleteFlag) {
                    $action->disableDelete();
                }
                $action->append('&nbsp;&nbsp;<a target="_blank" href="/admin/repair_bill/print?type=1&bill_id=' . $action->row->id . '" 
                    data-id="' . $action->row->id . '" class=""><i title="退货" class="fa fa-print"> A4打印</i></a> ');
                $action->append('<a target="_blank" href="/admin/repair_bill/print?type=2&bill_id=' . $action->row->id . '" 
                    data-id="' . $action->row->id . '" class=""><i title="退货" class="fa fa-print"> 多联打印</i></a> ');

            });
        });
    }

    //需要注意的是,在保单录入的页面,可以选择故障类型和配件列表,但是这两个都不是必须的,当时只是为了方便统计才要录入这个
    //但是由于后台之前的管理员交接给了别人,导致后面很多机型的配件或者故障类型没有去维护录入了,导致维修技师在录入的是没有找到可选项,后面的保单很多都没有去选择配件和故障类型
    //所以数据库数据里很多条记录都是没有配件和故障类型的,属于正常现象
    protected function form($id = null)
    {
        //因为配件列表是根据机型实时变化的,所以要注入js
        $script = <<<EOF
            //别问我为什么不直接操作模板div,是因为用了DocumentFragment,操作不了,不是我用的!
            $(document).ready(function() {
            $('#has-many-machine_accessory_relation').on('click', '.add', function () {
              $('.machine_accessory_relation_id').last().html('');
              //获取该机型对应的配件options
              modelId = $('.model_id').val();
              let optionHtml = "<option value='0'>请选择</option>";
              $.get('/admin/machine_accessory_relation/getAccessoriesByModelId',{modelId:modelId},function(result){
                for(x in result){              
                  optionHtml = optionHtml + "<option value=" + result[x]['id'] + ">" + result[x]['title'] + " | 价格 : " + result[x]['price'] + "</option>"
                }
                $('.machine_accessory_relation_id').last().html(optionHtml);
              });
            });
                                         
              //型号切换,要清除之前选择的所有配件
              $('.model_id').change(function(){
                let modelId = $('.modelId').val();
                $('.has-many-machine_accessory_relation-forms').html('');
              });
              
              //点击补全顾客信息
              $('#complete_customer_info').click(function(){
                sn = $('#sn_code').val();
                if(sn === ''){
                  layer.msg('sn码不能为空');
                }else{
                  //根据sn码获取相关信息
                  $.get('/admin/sn/getMachinePurchaseRecordBySn',{sn:sn},function(result){
                    console.log(result);
                    if(result==''){
                      layer.msg('暂无该机器的保卡记录');
                    }else{
                      $('#buy_date').val(result.buy_date);
                      $('#customer_name').val(result.customer_name);
                      $('#customer_phone').val(result.customer_phone);
                      $("select[name='model_id']").val((result.model_id).toString());
                      $("select[name='model_id']").select2(); 
                      layer.msg('已补全');
                    }
                  });
                }
              });
              
              //统计价格
              function calculatePrice(){
                let totalPrice = 0;
                $('.machine_accessory_relation_id:visible').each(function(){
                  let optionText = $(this).find("option:selected").text();
                  let price = optionText.split('价格 : ')[1];
                  let amount = $(this).parent().parent().next().find('#amount').val();
                  totalPrice += price * amount;
                });
                $('#repair_cost').val(totalPrice);
              }
              //下拉框变化
              $(document).on('change','.machine_accessory_relation_id',function(){
                calculatePrice();
              });
              //加减数量按钮点击
              $('.has-many-machine_accessory_relation-forms').on('click','button',function(){
                calculatePrice();
              });
              //移除按钮点击
              $(document).on('click','.remove',function(){
                calculatePrice();
              });
            });      
EOF;

        Admin::script($script);

        return Admin::form(RepairBill::class, function (Form $form) use ($id) {
            //获取uid,其实是user_after_sales的id,而且如果是修改操作,不需要获取默认值
            $endpointId = $billNumber = null;
            if (!$id) {
                $afterSalesUser = UserAfterSales::where('user_id', '=', Admin::user()->id)->first();
                if ($afterSalesUser) {
                    $endpointId = $afterSalesUser->endpoint_id;
                    $billNumber = sprintf('%03d', $afterSalesUser->id) . sprintf('%03d',
                            $afterSalesUser->endpoint_id) . date('YmdHi');
                }
            }
            $form->hidden('endpoint_id')->value($endpointId);
            $form->text('bill_number', '维修单号')->default($billNumber)
                ->attribute(['readonly' => true]);
            $form->text('sn_code', '机身S/N码');
            if (!$id) {
                $buttonHtml = <<<EOF
                <label id="complete_customer_info" class="btn btn-primary">点击补全机器信息</label> 
                <span class="help-block">
                    <i class="fa fa-info-circle"></i>
                    &nbsp;点击按钮会去查询该sn码的对应保卡信息,如果存在保卡信息会自动补全机器的购买相关信息
                </span>
EOF;
                $form->html($buttonHtml, $label = '');
            }
            $form->hidden('create_uid')->value(Admin::user()->id);
            $form->date('buy_date', '购机日期');
            $form->text('customer_name', '客户姓名');
            $form->text('customer_phone', '客户联系方式');
            $form->select('model_id', '机型')->options(
                Machine::getMachineModels()
            )->help('必填');
            $form->date('receive_machine_date', '维修收机日期')
                ->rules('required')
                ->help('必填');
            $form->text('repair_source', '维修来源')->help('非必填');
            $form->textarea('description', '服务请求(故障描述)')
                ->placeholder('请填入产品故障问题，描述500个字以内')
                ->rules('required')
                ->help('<span style="color: red;">必填，填写顾客服务请求(故障描述)，并不是最终的检测故障问题！</span>');
            $form->select('repair_type', '维修方式')->options(
                [0 => '请选择', 1 => '保内', 2 => '保外']
            );
            $form->select('repair_reason_type', '维修原因')->options(
                [0 => '请选择', 1 => '人为', 2 => '质量']
            );

            $form->textarea('repair_deal', '处理信息')
                ->placeholder('请填入产品故障处理信息，描述500个字以内')
                ->help('非必填，填写维修内容，如更换X屏+FPCx+更换xx电容+更换xx电阻等等');
            $form->date('express_date', '快递寄出日期')->help('非必填');
            $form->text('express_number', '快递单号')->help('非必填');
            $form->textarea('remark', '备注')
                ->placeholder('在此处填入备注信息')
                ->help('<span style="color: red;">非必填，备注维修配件列表上没有的小配件收费！或其他服务备注信息！</span>');

            $options = [];
            if ($id) {
                $modelId = RepairBill::where('id', '=', $id)->value('model_id');
                $accessoryRelations = MachineAccessoryRelation::with('accessory')
                    ->where('model_id', '=',
                        $modelId)->get();
                $options[0] = '请选择';
                foreach ($accessoryRelations as $key => $relation) {
                    $options [$relation->id] = $relation->accessory->title . ' | 价格 : ' . $relation->price;
                }
            }

            $form->hasMany('machine_malfunction_relation', '故障列表(工单故障原因)',
                function (Form\NestedForm $form) {
                    $form->select('malfunction_top', '故障位置')->options(
                        MachineMalfunction::where('parent_id', '=', 0)->pluck('title', 'id')->prepend('请选择', 0)
                    )->load('malfunction_id', '/admin/machine_malfunction/getMachineMalfunctionByParentId');
                    $form->select('malfunction_id', '故障类别')->options(function ($id) {
                        return MachineMalfunction::where('id', '=', $id)->pluck('title', 'id');
                    });
                });

            $form->hasMany('machine_accessory_relation', '维修配件列表',
                function (Form\NestedForm $form) use ($options) {
                    $form->select('machine_accessory_relation_id', '配件列表')->options(
                        $options
                    );
                    $form->number('amount', '数量')->default(1);
                });

            $form->text('repair_cost', '维修费用总计')->help('必填 | 单位 : 元');
            $form->switch('is_checked', '是否出单');

            $form->display('created_at', '创建时间');
            $form->display('updated_at', '最后修改时间');
        });
    }

    public function search()
    {
        return Admin::content(function (Content $content) {

            $content->header('维修工作单查询');
            $content->description('维修工作单查询');
            $keyword = Input::get('keyword');
            $type = Input::get('type');
            $repairBill = null;
            if ($keyword) {
                $repairBill = RepairBill::where($type, '=', $keyword)->first();
                if ($repairBill) {
                    $relationIds = [];
                    foreach ($repairBill->machine_accessory_relation as $item) {
                        $relationIds[] = $item->machine_accessory_relation_id;
                    }
                    $machineAccessoryRelations = MachineAccessoryRelation::whereIn('id', $relationIds)->get();
                    $amount = $repairBill->machine_accessory_relation->pluck('amount', 'machine_accessory_relation_id');
                    $malfunctions = MachineMalfunction::all()->pluck('title', 'id');
                    $machineModels = Machine::getMachineModels();
                }
            }
            $content->body(view('admin/repair_bill/search/search',
                compact('repairBill', 'malfunctions', 'machineModels',
                    'machineAccessoryRelations', 'amount')));

        });
    }

    const PRINT_BY_A4_PAPER = 1;
    const PRINT_BY_MULTI_UNiON_PAPER = 2;

    public function print()
    {
        $type = Input::get('type');
        $billId = Input::get('bill_id');
        $repairBill = RepairBill::where('id', '=', $billId)->first();

        $relationIds = [];
        foreach ($repairBill->machine_accessory_relation as $item) {
            $relationIds[] = $item->machine_accessory_relation_id;
        }
        $machineAccessoryRelations = MachineAccessoryRelation::whereIn('id', $relationIds)->get();
        $accessoryData = RepairBillMachineAccessoryRelation::where('repair_bill_id', $repairBill->id)->get()
            ->toArray();
        $amount = $this->calculateAmount($accessoryData);
        $malfunctions = MachineMalfunction::all()->pluck('title', 'id');
        $machineModels = Machine::getMachineModels();

        $viewName = $type === '1' ? 'admin/repair_bill/print/a4' : 'admin/repair_bill/print/multi_union';

        return view($viewName,
            compact('repairBill', 'malfunctions', 'machineModels', 'machineAccessoryRelations', 'amount'));
    }

    private function calculateAmount(array $accessoryData)
    {
        $amount = [];
        foreach ($accessoryData as $key => $value) {
            if (!\array_key_exists($value['machine_accessory_relation_id'], $amount)) {
                $amount[$value['machine_accessory_relation_id']] = $value['amount'];
            } else {
                $amount[$value['machine_accessory_relation_id']] += $value['amount'];
            }
        }

        return $amount;
    }
}
