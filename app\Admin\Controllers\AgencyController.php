<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\AgencyExporter;
use App\Models\Agency;
use App\Models\Partition;
use App\Services\Admin\AgencyService;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Tree;
use Symfony\Component\HttpFoundation\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Endpoint;

class AgencyController extends Controller
{
    use ModelForm;

    protected $service;

    public function __construct(AgencyService $service)
    {
        $this->service = $service;
    }

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('代理列表');
            $content->description('代理列表');
            $content->body($this->tree());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('编辑代理');
            $content->description('编辑代理');

            $content->body($this->form()->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('创建代理');
            $content->description('创建代理');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(Agency::class, function (Grid $grid) {

            $grid->id('ID')->sortable();

            $grid->created_at();
            $grid->updated_at();
        });
    }

    /**
     * Make a form builder.
     *
     * @return Tree
     */
    protected function tree()
    {
        $deletableStatus = $this->service->getDeletableStatus();
        $script = <<<SCRIPT
        $(document).off();
        $(function(){
          var temp = $('.dd-item');
            temp.each(function(){
            var dataId = $(this).attr('data-id');
            //判断当前代理是否已经有终端存在,有的话不让删除
            if($.inArray(parseInt(dataId), $deletableStatus) != -1){
                         $("a[data-id='"+dataId+"']").remove();
            }
            });
            $("#pjax-container").LoadingOverlay("hide", true);
 
        });   
SCRIPT;
        Admin::script($script);

        return Agency::tree(function (Tree $tree) {

            $partitions = Partition::all()
                ->pluck('name', 'id')->prepend('暂无', 0)->toArray();

            $tree->branch(function ($branch) use ($partitions) {
                if ($branch['pid'] == 0) {
                    return "{$branch['name']}_[{$partitions[$branch['partition']]}]";
                }

                return "{$branch['name']}";
            });
        });

    }


    public function del()
    {
        return response()->json([
            'status' => true,
            'message' => "删除成功!",
        ]);
    }

    public function export()
    {
        $export = new AgencyExporter();
        $export->export();
    }

    protected function form()
    {
        return Agency::form(function (Form $form) {
            $form->display('id', 'ID');
            $form->select('pid', '选择父级地区')->help('选择root代表你要添加的是顶级地区')->options(Agency::selectOptions());
            $form->text('name', '名称')->rules('required');
            $form->text('level', '级别')->help('代理地区级别,一级填1,二级填2')->rules('required');
            $form->select('partition', '所属大区')->options(Partition::pluck('name', 'id')->prepend('无',
                0))->rules('required');
            $form->text('letter_name', '字母缩写')->help('只能填写纯字母')->rules('regex:/[a-zA-Z]/');
            $form->text('market_coefficient', '市场占比');
        });
    }


    public function topAgency(Request $request)
    {
        $provinceId = $request->get('q');

        return Agency::Top()->where('pid', $provinceId)
            ->get([DB::raw('id as id'), DB::raw('name as text')]);
    }

    public function secondAgency(Request $request)
    {
        $cityId = $request->get('q');

        return Agency::Second()->where('pid', $cityId)
            ->get([DB::raw('id as id'), DB::raw('name as text')])
            ->prepend(['id' => 0, 'text' => '选择']);
    }

    public function secondAgencyEndpoint(Request $request)
    {
        $secondAgency = $request->get('q');

        return Endpoint::where('second_agency', $secondAgency)
            ->get([DB::raw('id as id'), DB::raw('name as text')]);
    }

    public function secondAgencyEndpointAddress(Request $request)
    {
        $secondAgency = $request->get('q');

        return Endpoint::where('second_agency', $secondAgency)
            ->get([DB::raw('id as id'), DB::raw("concat(address,' | 终端编号:',id) as text")]);
    }

    public function getEndpoint(Request $request)
    {
        $top = $request->get('top_agency_id');
        $second = $request->get('second_agency_id');

        return Endpoint::where('top_agency', $top)->where('status', '=', 1)->where('second_agency', $second)->get([
            DB::raw('id as id'),
            DB::raw('concat("【",name,"】",address) as text'),
        ])->prepend(['id' => ' ', 'text' => '选择終端[可不选,代表只归属于一级代理]']);

    }

    public function get4SEndpoint(Request $request)
    {
        $agencyId = $request->get('q');
        //先判断agency是一代还是二代
        $topAgency = Agency::where('id', $agencyId)->where('pid', 0)->first();
        if ($topAgency) {
            return Endpoint::where('top_agency', '=', $agencyId)
                ->where('second_agency', 0)
                ->where('type', 0)
                ->get([DB::raw('id as id'), DB::raw('address as text')]);
        }

        return Endpoint::where('second_agency', '=', $agencyId)
            ->where('type', 0)
            ->get([DB::raw('id as id'), DB::raw('address as text')]);

    }
}
