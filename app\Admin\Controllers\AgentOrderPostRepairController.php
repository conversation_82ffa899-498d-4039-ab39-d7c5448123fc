<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\Tools\Button;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Models\MachineAccessory;
use App\Models\MachineAccessoryRelation;
use App\Models\MachineAccessoryTree;
use App\Models\MachineMalfunction;
use App\Models\Material;
use App\Models\Order;

use App\Models\OrderOldAddress;
use App\Models\PostRepairAccessory;
use App\Models\PostRepairMalfunction;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Request;

class AgentOrderPostRepairController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('经销商寄修订单总览');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修订单');
            $content->description('编辑');

            $content->body($this->form($id)->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修订单');

            $order = Order::where(['id' => $id])->first();
//            $content->body($order);
            $order_old_address = OrderOldAddress::where(['sn'=>$order->sn])->first();
            $pr_material = DB::table('pr_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_material.mat_id')
                ->leftjoin('machine_malfunction', 'machine_malfunction.id', '=', 'pr_material.malfunction_id')
                ->leftjoin('material', 'material.id', '=', 'pr_material.material_id')
                ->where('pr_material.pr_sn', $order['sn'])
                ->select('machine_accessory_tree.title as title', 'machine_malfunction.title as malfunction', 'material.price as price', 'pr_material.count as count')
                ->get()
                ->toArray();
            $pr_used_material = DB::table('pr_used_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_used_material.mat_id')
                ->leftjoin('material', 'material.id', '=', 'pr_used_material.material_id')
                ->where('pr_used_material.pr_sn', $order['sn'])
                ->select('material.name as name', 'material.price as price', 'pr_used_material.count as count',
                    'material.code as code', 'material.old_code as old_code', 'material.specification as specification', 'material.from as from')
                ->get()
                ->toArray();
            $content->body(view('admin/post_repair/view', compact('order', 'pr_material', 'pr_used_material', 'order_old_address')));

        });
    }

    public function printf($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('打印检测报告');
            $order = Order::where(['id' => $id])->first();
            $post_malfunction = DB::table('pr_malfunction')
                ->join('machine_malfunction', 'machine_malfunction.id', '=', 'pr_malfunction.malfunction_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_malfunction.title')->toArray();
            $malfunction = implode('，', $post_malfunction);
            $post_accessory = DB::table('pr_accessory')
                ->join('machine_accessory', 'machine_accessory.id', '=', 'pr_accessory.mar_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_accessory.title')->toArray();
            $accessory = implode('，', $post_accessory);
            $content->body(view('admin/post_order/print', compact('order', 'malfunction', 'accessory')));

        });
    }

    public function cancel(Request $request)
    {
        foreach (Order::find($request->get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修订单');
            $content->description('创建');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOF
            //查看快递路由信息
            $('.express_route_fresh').click(function(){
                var id = $(this).attr('value');
                layer.open({
                      type: 2,
                      title: '快递路由',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['1000px', '60%'],
                      content: 'express/express_route_fresh?readboy_sn='+id //iframe的url
                });
            });
        $("input[name='sn']").focus();
//        $("input[name='come_exp_sn']").focus();
EOF;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
//            $grid->model()->where('status', '>=', 200);
            $agent_order_sn = Input::get('agent_order_sn');
            $grid->disableCreation();
            $grid->disableExport();
            //快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => [['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
                1 => [
                    'name' => '待审核',
                    'param' => [['order.status', '=', Order::WAIT_AUDIT], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
                2 => [
                    'name' => '待检测',
                    'param' => [['order.status', '=', Order::COME_SURE], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
                3 => [
                    'name' => '待支付',
                    'param' => [['order.status', '=', Order::CHECK_FINISH], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
                4 => [
                    'name' => '待维修',
                    'param' => [['order.status', '=', Order::PAY_FINISH], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
                5 => [
                    'name' => '待发货',
                    'param' => [['order.status', '=', Order::REPAIR_FINISH], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
            ];
            //筛选条数
            foreach ($option as $key => $value) {
                $option[$key]['count'] = Order::where($value['param'])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
            }
            //自定义状态快捷筛选按钮
            $grid->tools(function ($tools) use ($option) {
                $tools->append(new QuickPickTool($option));
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
//                    $batch->add('取消订单', new Cancel());
                });
                $button = <<<EOF
                     <div class="btn-group pull-right" style="margin-right:15px; margin-top: 10px;">
                      <a href ="agent_order" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 返回
                      </a >
                    </div >
EOF;
                $tools->append($button);
            });
            //根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->orderBy('order.id', 'desc');
            }
            $grid->model()
                ->select('order.*')
                ->where($option[-1]['param'])
                ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                ->orderBy('order.id', 'desc');

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('come_exp_sn', '快递单号');
                $filter->like('sn', '维修订单号');
                $filter->like('barcode', 'S/N码');
                $filter->like('name', '联系人');
                $filter->like('status', '订单状态')
                    ->select(Order::STATUS);
                $filter->like('need_invoice','开具发票')->select([0=>'否',1=>'是']);
                $filter->between('created_at', '订单提交日期')->datetime();

            });

            $grid->id('ID')->sortable();
            $grid->uid('UID');
            $grid->sn('寄修订单编号');
            $grid->column('come_exp_sn', '寄来快递单号')->display(function ($value) {
                $html = '<a class="express_route_fresh" href="javascript:void(0);" value="'.$value.'">'.$value.'</a>';
                return $html;
            });
            $grid->column('go_exp_sn', '寄去快递单号')->display(function ($value) {
                $html = '<a class="express_route_fresh" href="javascript:void(0);" value="'.$value.'">'.$value.'</a>';
                return $html;
            });
            $grid->barcode('S/N码');
            $grid->name('联系人');
            $grid->phone('用户联系方式');
//            $grid->endpoint()->name('寄修售后点');
            $grid->amount('总金额(元)');
            $grid->pay_amount('支付金额(元)');
            $grid->column('need_invoice','是否开具发票')->display(function ($value){
                if ($value==1){
                    return '是';
                }else{
                    return '否';
                }
            });
            $grid->created_at('订单提交时间');
            $grid->column('expense.updated_at', '核销时间')->display(function($value) {
                if ($value) {
                    return $value;
                } else {
                    return '未核销';
                }
            });
            $grid->status('订单状态')->display(function ($status) {
                $s = Order::STATUS;
                if (array_key_exists($status, $s)) {
                    return $status . $s[$status];
                } else {
                    return "————";
                }
            });
            $grid->check_user()->name('检测人')->display(function ($name) {
                if ($name == '')
                    return '———';
                return $name;
            });
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $status = $actions->row->status;
                $c = 'post_repair/view/' . $actions->getKey();
//                dd($c);
                $html = '<a href="' . $c . '"><span style="color:blue">查看 </span></a>';
                $actions->append($html);
            });

        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id = null)
    {
        $script = <<<EOF
            //改变配件物料选项
            $(document).on('change', ".mat_id", function () {
                var target = $(this).closest('.fields-group').find(".material_id");
                $.get("/admin/repair_check_material?q="+this.value, function (data) {
                    target.find("option").remove();
                    defaultdata = [{"id":0,"text":"请选择"}];
                    setdata = $.map(data, function (d) {
                        d.id = d.id;
                        d.text = d.text;
                        return d;
                    });
                    data = setdata;
                    $(target).select2({
                        data: data
                    }).trigger('change');
                });
            });

            //别问我为什么不直接操作模板div,是因为用了DocumentFragment,操作不了,不是我用的!
            $(document).ready(function() {
                var in_period = $('.in_period').val()
                if (in_period != '1') {
                    calculatePrice();
                }
//              calculatePrice();
              function total(){
//                var staff = Number($('#staff_cast').val().replace(/,/g, ""));
                var staff = 0;
                var exp_cast = Number($('#exp_cast').val().replace(/,/g, ""));
                var accessory = Number($('#accessory_cast').val().replace(/,/g, ""));
                 var accessory_amount = Number($('#accessory_amount').val().replace(/,/g, ""));
                $('#amount').val(staff + accessory_amount + exp_cast);
                $('#pay_amount').val(staff + accessory + exp_cast);
              }

              $(document).on('change','.staff_cast',function(){
                total();
              });


              //统计配件价格
              function calculatePrice(){
                let totalPrice = 0;
                let accessoryAmount = 0;
                $('.material_id:visible').each(function(){
                  let optionText = $(this).find("option:selected").text();
                  let price = optionText.split('价格 : ')[1];
                  let count = $(this).parent().parent().next().find('#count').val();
                  let is_charge = $(this).parent().parent().next().next().find('.is_charge').val();
                   accessoryAmount += price * count;
                  if (is_charge == '1'){
                    totalPrice += price * count;
                  }
                });
                $('#accessory_amount').val(accessoryAmount);
                $('#accessory_cast').val(totalPrice);
                total();
              }

              //下拉框变化
              $(document).on('change','.material_id',function(){
                calculatePrice();
              });
              $(document).on('change','.is_charge',function(){
                calculatePrice();
              });
              //加减数量按钮点击
              $('.has-many-repair_material-forms').on('click','button',function(){
                calculatePrice();
              });
              //移除按钮点击
              $(document).on('click','.remove',function(){
                calculatePrice();
              });
            });
EOF;

        Admin::script($script);
        return Admin::form(Order::class, function (Form $form) use ($id) {

            $form->text('name', '寄修人');
            $form->text('phone', '联系方式');
            $form->display('sn', '寄修订单号');
//            $form->barcode('sn', '寄修条码条码')->options(['width'=>1,]);
            $form->display('model_name', '机器型号');
            $form->select('in_period', '保修状态')->options(Order::in_period);
            $form->hidden('sn', '寄修订单号');
//            $form->divider('');
//            $form->hasMany('repair_malfunction','产品故障选择',
//                function (Form\NestedForm $form) {
//                    $form->hidden('id');
//                    $form->hidden('pr_sn');
//                    $form->select('malfunction_parent', '故障位置')->options(
//                        MachineMalfunction::where('parent_id', '=', 0)->pluck('title', 'id')->prepend('请选择', 0)
//                    )->load('malfunction_id', '/admin/machine_malfunction/getMachineMalfunctionByParentId');
//                    $form->select('malfunction_id', '故障类别')->options(function ($id) {
//                        return MachineMalfunction::where('id', '=', $id)->pluck('title', 'id');
//                    });
//                });
            $form->divider('');
            $form->select('reason', '损坏原因')->options(Order::reason)->value(function ($reason) {
                return $reason;
            });
            $form->text('deal', '维修方式');
            $form->divider('');

            $options = [];
            if ($id) {
                $modelId = Order::where('id', '=', $id)->value('model_id');
                $accessoryRelations = MachineAccessoryRelation::with('accessory')
                    ->where('model_id', '=',$modelId)->get();
                $options[0] = '请选择';
                foreach ($accessoryRelations as $key => $relation) {
                    $options[$relation->id] = $relation->accessory->title . ' | 价格 : ' . $relation->price;
                }
            }
            $form->hasMany('repair_material', '维修配件列表',
                function (Form\NestedForm $form) use ($id, $options) {
                    $modelId = Order::where('id', '=', $id)->value('model_id');
                    $pr_sn = Order::where('id', '=', $id)->value('sn');
                    $form->select('mat_id', '维修配件')->options(MachineAccessoryTree::model_options($modelId))->load('malfunction_id', admin_url('repair_check_malfunction'));
                    $form->select('malfunction_id', '配件故障')->options(function ($value) {
                        $data = MachineMalfunction::where('id', $value)->get()->toArray();
                        $ret = array();
                        if (count($data) > 0) {
                            foreach ($data as $d) {
                                $ret[$d['id']] = $d['title'];
                            }
                        }
                        return $ret;
                    });
                    $form->select('material_id', '维修物料')->options(function ($value) use ($pr_sn, $form){
                        $data = Material::where('id', $value)->get()->toArray();
                        $ret = array();
                        if (count($data) > 0) {
                            foreach ($data as $d) {
                                $ret[$d['id']] = $d['name'].'|价格 : '.$d['price'];
                            }
                        }
                        return $ret;
                    });
                    $form->number('count', '数量')->default(1);
                    $form->select('is_charge', '是否收费')->options([1=>'收费', 0=>'不收费'])->default(1);
                });

            $form->currency('accessory_cast', '配件价格')->symbol('￥');
            if ($id && $form->model()->find($id)->in_period !== 1) {
                $rb_exp_sn = $form->model()->find($id)->rb_come_exp_sn;
                $exp_sn = $form->model()->find($id)->come_exp_sn;
                $exp_cast = Express::query_order($rb_exp_sn, $exp_sn);
//                $exp_cast = 15;
            } else {
                $exp_cast = 0;
            }
            $form->currency('exp_cast', '快递费用')->symbol('￥')->default(intval($exp_cast))->help('快递费用  '.$exp_cast)->readOnly();
            $form->hidden('staff_cast', '检测费用')->value($exp_cast);
//            $form->display('staff_cast', '检测费用')->value($exp_cast)->help('快递费用  '.$exp_cast. '元');
            $form->currency('amount', '总计')->symbol('￥');
            $form->currency('pay_amount', '待支付金额')->symbol('￥')->help('修改前金额：');

            $form->display('receive_case', '收到的配件');
            $form->text('deal_remark', '备注');
            $form->hidden('check_man');
            $form->hidden('status');
//            $form->hidden('pay_amount');
            $form->saving(function (Form $form) use ($exp_cast) {
//                $form->check_man = Admin::user()->id;
                if ($form->model()->in_period == 1) {
                    $exp_cast = '0.00';
                } else {
                    $rb_exp_sn = $form->model()->rb_come_exp_sn;
                    $exp_sn = $form->model()->come_exp_sn;
                    $exp_cast = Express::query_order($rb_exp_sn, $exp_sn);
//                    $exp_cast = 15;
                }
                $form->staff_cast = floatval($exp_cast);
//                dd($form);
                if ($form->status < 500)
                    $form->status = 500;
                //待支付金额判断：
                if (($form->in_period == 1 || $form->model()->in_period == 1) && ($form->reason == 2)){
                    $form->pay_amount = '0.00';
                } else {
//                    $form->pay_amount = $form->amount;
                }
//                dd($form->pay_amount);
                //微信支付如果有发起过支付请求，必须保证金额等信息一致，所以有金额变动，需要去掉内部支付单号重新生成
                if ($form->pay_amount != $form->model()->pay_amount && !empty($form->model()->rb_pay_sn) && empty($form->model()->is_paid)) {
                    $data = ['rb_pay_sn' => '', 'pay_com' => 0, 'pay_sn' => ''];
                    DB::table('order')->where('sn', $form->model()->sn)->update($data);
                }
            });
            //快递费不保存,保存成staff_cast
            $form->ignore(['exp_cast']);

            $form->saved(function($form) {
                //同步处理已使用的物料
                PostRepairUsedMaterial::where('pr_sn', $form->sn)->delete();
                $pr_material = PostRepairMaterial::where('pr_sn', $form->sn)->get();
                if (count($pr_material) > 0) {
                    foreach ($pr_material as $material) {
//                        dd($material);
                        $new_used_material = new PostRepairUsedMaterial();
                        $new_used_material->pr_sn = $material->pr_sn;
                        $new_used_material->mat_id = $material->mat_id;
                        $new_used_material->material_id = $material->material_id;
                        $new_used_material->is_charge = $material->is_charge;
                        $new_used_material->count = $material->count;
                        $new_used_material->save();
                    }
                }
//                dd(PostRepairUsedMaterial::where('pr_sn', $form->sn)->get()->toArray());
                $order = $form->model();
//                dd($order->status == Order::CHECK_FINISH && $order->in_period == 1 && $order->reason == 2 && empty(intval($order->pay_amount)));
                //处理保修期内并且是质量问题的订单，直接修改为已支付的状态
                if ($order->status == Order::CHECK_FINISH && $order->in_period == 1 && $order->reason == 2 && empty(floatval($order->pay_amount))) {
                    $save = Order::where('sn', $order->sn)->first();
                    $save->status = Order::PAY_FINISH;
                    $save->save();
                }
            });
        });
    }
}