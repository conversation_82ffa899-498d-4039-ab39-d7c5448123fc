<?php

namespace App\Admin\Extensions;

use App\Models\Endpoint;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;
use App\Models\Agency;

class AccessoryPriceOfferExporter extends AbstractExporter
{
    public function export()
    {
        $filename = '配件报价';
        $data = $this->getData();
        if (!empty($data)) {
            $titles = [
                'title',
                'price',
            ];
            $formatData = $this->getFormatData($data);
            array_unshift($formatData, $titles);
            ExcelExportTrait::exportToExcel($filename, $formatData);
        }
    }

    private function getFormatData($data)
    {
        foreach ($data as $row) {
            $row = [
                $row['title'],
                $row['price'],
            ];
            $formatData[] = $row;
        }

        return $formatData;
    }


}