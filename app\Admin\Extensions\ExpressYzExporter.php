<?php

namespace App\Admin\Extensions;

use App\Models\Order;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Barryvdh\Debugbar\Facade as Debugbar;

class ExpressYzExporter extends AbstractExporter
{
    protected const title = [
        '客户订单',
        '邮件号',
        '*收件人姓名',
        '*手机号',
        '固话号',
        '*详细地址',
        '公司',
        '*寄递产品',
        '*付款方式',
        '*物品类型',
        '内件详情',
        '内件重量（kg）',
        '备注',
        '物品总价值（元）',
        '应收货款金额（元）',
        '密码投递（收取服务费1元）',
        '返单服务',
        '特安',
        '预约返单',
        '法律文书',
    ];

    public function export()
    {
        $filename = '邮政订单-协议客户';
        $data = $this->getData();
        Debugbar::info([__CLASS__ . '.data' => $data]);
        if (!empty($data)) {
            $formatData = $this->getFormatData($data);
            array_unshift($formatData, self::title);
            array_unshift($formatData, []);
            ExcelExportTrait::exportToExcel($filename, $formatData, 'Sheet1');
        }
    }

    private function getFormatData($data)
    {
        $formatData = [];
        foreach ($data as $d) {
            if ($d['go_sure'] == 0 || $d['go_exp_com'] !== '邮政快递' ||
                ($d['status'] != Order::EXP_GO_SUCCESS && $d['status'] != Order::ORDER_FINISH)) {
                continue;
            }
            //TODO:获取最新订单的收件人信息
            $row = [
                $d['rb_go_exp_sn'],
                $d['go_exp_sn'],
                $d['name'], //收件人-姓名
                $d['phone'], //收件人-手机号
                '', //收件人-固话号
                $d['province'] . $d['city'] . $d['district'] . $d['address'], //收件人-详细地址
                '', //收件人-公司
                '特快专递', //寄递产品：特快专递/特快特惠/快递包裹，为空默认“特快专递”
                '寄付', //付款方式：寄付，到付，为空默认“寄付”
                '物品', //物品类型：分为文件和物品，为空默认“物品”，如果您寄递的是物品，可填写具体的内件详情名称，例如衣服，食物等
                '读书郎教育产品（含锂电池）', //内件详情
                '', //内件重量，为空默认“0kg”
                '', //备注
                '', //物品总价值（元）
                '', //代收货款（元）
                '否', //密码投递，为空默认“否”
                '', //返单服务
                '否', //特安
                '', //预约返单
                '否', //法律文书
            ];
            $formatData[] = $row;
        }
        return $formatData;
    }
}
