<?php

namespace App\Admin\Controllers;

use App\Models\UserEndpoint;
use App\User;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use App\Models\Agency;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use Validator;

//导购用户管理(注意是导购用户)
class UserController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {
            $content->header('导购用户管理');
            $content->description('导购用户根据添加的终端地点自动生成');
            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {
            $content->header('编辑信息');
            $content->description('编辑信息');
            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {
            $content->header('用户管理');
            $content->description('description');
            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<SCRIPT
           $('.grid-row-reset-password').unbind('click').click(function() {
               if(confirm("确定要重置该用户的密码?")) {
                   $.ajax({
                       method: 'post',
                       url: 'users/resetPassword',
                       data: {
                           _method:'post',
                           _token:LA.token,
                           id:$(this).data('id')
                       },
                       success: function (data) {
                           $.pjax.reload('#pjax-container');
                           if (typeof data === 'object') {
                               if (data.status) {
                                   toastr.success(data.message);
                               } else {
                                   toastr.error(data.message);
                               }
                           }
                       }
                   });
               }
           });
SCRIPT;
        Admin::script($script);

        return Admin::grid(User::class, function (Grid $grid) {
            $checkIsTopAgencyRole = Admin::user()->inRoles(['topAgency']);
            //判断角色,如果是总代的话,只显示自己所属的帐号
            if ($checkIsTopAgencyRole) {
                //获取当前用户的总代id
                $topAgency = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('top_agency');
                $where = [['admin_users.id', '>', 1], ['endpoint.top_agency', '=', $topAgency]];
            } else {
                $where = [['admin_users.id', '>', 1]];
            }

            $grid->model()
                ->select('admin_users.*', 'endpoint.name as endpoint_name', 'endpoint.manager', 'endpoint.phone',
                    'address', 'endpoint.id as eid', 'p.region_name AS province', 'c.region_name AS city',
                    'f.name AS top_agency', 's.name AS second_agency')
                ->rightjoin('user_endpoint', 'admin_users.id', '=', 'user_endpoint.uid')
                ->leftjoin('endpoint', 'user_endpoint.endpoint', '=', 'endpoint.id')
                ->leftjoin('region AS p', 'endpoint.province', '=', 'p.region_id')
                ->leftjoin('region AS c', 'endpoint.city', '=', 'c.region_id')
                ->leftjoin('agency AS f', 'endpoint.top_agency', '=', 'f.id')
                ->leftjoin('agency AS s', 'endpoint.second_agency', '=', 's.id')
                ->where($where);
            $grid->actions(function ($actions) use ($checkIsTopAgencyRole) {
                if ($checkIsTopAgencyRole) {
                    $actions->disableEdit();
                }
                $actions->append('<a href="" data-id="' . $actions->row->id . '" 
                    class="grid-row-reset-password"><i i title="重置为初始密码" class="fa fa-history"></i></a>');
                $actions->disableDelete();

            });

            $grid->exporter('endpoint-account-exporter');

            $grid->filter(function ($filter) use ($checkIsTopAgencyRole) {
                $filter->disableIdFilter();
                $filter->like('username', '帐号');
                $filter->like('address', '终端地址');
                $filter->where(function ($query) {

                    $query->where('endpoint.name', 'like', "%{$this->input}%");

                }, '终端名称');
                if (Admin::user()->inRoles(['manager', 'administrator'])) {
                    $filter->equal('top_agency', '一级代理')
                        ->select(Agency::top()->pluck('name', 'id'))
                        ->load('second_agency', '/admin/agency/secondAgency');
                    $filter->equal('second_agency', '二级代理')->select(function ($id) {
                        return Agency::options($id);
                    });
                }
                if ($checkIsTopAgencyRole) {
                    $filter->equal('second_agency', '二级代理')->select(function () {
                        $topAgency = DB::table('user_agency')->where('uid', '=',
                            Admin::user()->id)->value('top_agency');

                        return Agency::where('pid', '=', $topAgency)->pluck('name', 'id');
                    });

                }

            });

            $grid->tools(function ($tools) {
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                });
            });
            $grid->disableCreation();

            $grid->username('帐号');
            $grid->name('用户名');
            $grid->column('address', '终端地址')->prependIcon('location-arrow');
            $grid->column('status', '状态')->display(function ($value) {
                return $value ? '<span style="color:#3C8DBC">启用</span>' : '<span style="color:red">被禁用</span>';
            });
            $grid->created_at('创建时间')->sortable()->prependIcon('calendar-times-o');
            $grid->updated_at('更新时间')->sortable()->prependIcon('calendar-times-o');
        });
    }

    public function resetPassword()
    {
        $id = Input::get('id');
        $user = User::find($id);
        $endpointId = UserEndpoint::where('uid', '=', $id)->value('endpoint');
        $password = substr(md5($user->username . $endpointId), 0, 8);
        $user->password = Hash::make($password);
        $user->save();

        return response()->json([
            'status' => true,
            'message' => "重置密码成功!",
        ]);
    }

    /**
     * Make a form builder.
     * @param $id
     * @return Form
     */
    protected function form($id = null)
    {
        return Admin::form(User::class, function (Form $form) use ($id) {

            $form->text('name', '名称');
            $form->display('username', '用户名');
            $form->password('password', trans('admin::lang.password'))->rules('confirmed|required')
                ->default(function ($form) {
                    return $form->model()->password;
                });;
            $form->password('password_confirmation', trans('admin::lang.password_confirmation'))->rules('required')
                ->default(function ($form) {
                    return $form->model()->password;
                });

            $form->ignore(['password_confirmation']);


            $form->saving(function (Form $form) use ($id) {
                if ($form->password && $form->model()->password != $form->password) {
                    $form->password = bcrypt($form->password);

                    //判断是否重置了密码,如果重置了去删除接口对应的redis token
                    $user = Redis::command('zrangebyscore', ['token_uid', $form->model()->id, $form->model()->id]);
                    if (!empty($user)) {
                        Redis::zrem('token_uid', $user[0]);
                        Redis::zrem('token_expire', $user[0]);
                    }
                }
                //终端名称和用户名称要双向同步
                $eid = DB::table('user_endpoint')
                    ->where('uid', '=', $form->model()->id)->value('endpoint');
                DB::table('endpoint')->where('id', '=', $eid)->update([
                    'name' => $form->name,
                ]);
            });

            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }

    const SMS_HOST = 'https://api-sms.readboy.com';

    //获取手机验证码
    //为什么会有手机验证码这类的操作,就是因为之前运营那边说要整个后台的用户都要用手机登陆
    //但是为什么后面没有用到,很简单
    //第一很不合理,后台的机制是分配帐号,手机号的录入都要重新来过,没办法保证准确性,而且对后台管理人员的录入及时性要求太高
    //第二这个需求涉及到所有后台用户的登录操作,切换成手机登录成本太高
    //第三因为我知道这个需求只是他们随便提的,因为很明显,我这些代码早就写好了,一直在等他们说更新上去,结果是完全没有人还记得这块,所以压根就是坑人的存在
    //幸好没有更新,要不然只会麻烦更多
    public function sendPhoneCode(Request $request)
    {
        $credentials = $request->only([
            'phone',
        ]);
        $validator = Validator::make($credentials, [
            'phone' => 'required|exists:admin_users,phone',
        ]);

        if ($validator->fails()) {
            return [
                'status' => 0,
                'message' => $validator->errors()->first(),
            ];
        }

        $code = random_int(100000, 999999);
        $phone = $request->phone;
        $response = $this->handleSendPhoneCode($phone, $code);
        if ($response->getStatusCode() != '200') {
            Log::error(json_decode($response->getBody()));

            return ['status' => 0];
        }

        $this->updateUserCodeInfo($phone, $code);

        return ['status' => 1];
    }

    protected function updateUserCodeInfo($phone, $code)
    {
        $user = User::where('phone', '=', $phone)->first();
        $user->code = $code;
        $halfHour = 60 * 30;
        $user->expire_time = date("Y-m-d H:i:s", (time() + $halfHour));
        $user->save();
    }

    protected function handleSendPhoneCode($phone, $code)
    {
        $client = new Client([
            'base_uri' => 'https://www.baidu.com',
            'timeout' => 10.0,
        ]);
        $url = self::SMS_HOST . '/index.php?s=/Sms/Api/send';
        $authKey = $this->getAuthKey($code);

        $result = ['code' => $code];
        $response = $client->request('POST', $url, [
            'form_params' => [
                'authKey' => $authKey,
                'appName' => 'care.readboy.com',
                'templateCode' => 'SMS_69985149',
                'phoneNumber' => $phone,
                'templateParam' => json_encode($result),
            ],
        ]);

        return $response;
    }

    //获取短信服务的密钥
    protected function getAuthKey($code)
    {
        $authKey = time() . '-' . $code . '-' . md5(time() . '-' . $code . '-' . env('SMS_KEY'));

        return $authKey;
    }
}
