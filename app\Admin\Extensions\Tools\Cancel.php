<?php

namespace App\Admin\Extensions\Tools;

use Encore\Admin\Grid\Tools\BatchAction;

class Cancel extends BatchAction
{

    public function script()
    {
        return <<<EOT

$('{$this->getElementClass()}').on('click', function() {
    var res = confirm("确定要取消所有订单?");
    if (res == true)
    {
       $.ajax({
        method: 'post',
        url: '{$this->resource}/cancel',
        data: {
            _token:LA.token,
            ids: selectedRows()
        },
        success: function () {
            $.pjax.reload('#pjax-container');
            toastr.success('操作成功');
        }
    });
    }
    else
    {
      alert("You pressed Cancel!");
    }
});

EOT;

    }
}