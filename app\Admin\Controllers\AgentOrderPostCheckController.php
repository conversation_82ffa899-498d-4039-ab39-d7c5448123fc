<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\Sms;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Models\BrokenScreenInsurance;
use App\Models\ChinaArea;
use App\Models\Damage;
use App\Models\Endpoint;
use App\Models\Machine;
use App\Models\Order;
use App\Models\OrderOldAddress;
use App\Models\PseudoCode;
use Illuminate\Support\Facades\DB;
use App\Models\PostExpress;
use App\Models\PostRepairEndpoint;
use App\Models\RepairStaff;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Widgets\Box;
use Encore\Admin\Widgets\Table;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\MessageBag;
use SimpleXMLElement;
use Illuminate\Support\Facades\Input;

class AgentOrderPostCheckController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修审核');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修审核');
            $content->description('编辑');

            $content->body($this->form($id)->edit($id));
        });
    }

    public function view(Content $content, $id = null)
    {
//        $order = Order::where(['id' => $id])->first();
//        $data = [
//            '机型名称' => $order->model_name,
//            'S/N码' => $order->barcode,
//            '保修状态' => Order::in_period[$order->in_period],
//            '受损状态' => Order::reason[$order->reason],
//            '故障类型' => $order->damage,
//        ];
//        $table = new Table([], $data);
//        $table->setStyle();
//        $content->header('寄修审核--查看');
//        $content->body((new Box('Table-2', $table))->style('info')->solid());
//        $content->body($table);
//        return $content;
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修审核--查看');
            $order = Order::where(['id' => $id])->first();
            $order_old_address = OrderOldAddress::where(['sn'=>$order->sn])->first();
            $content->body(view('admin/post_check/view', compact(['order', 'order_old_address'])));
        });
    }

    public function express(Content $content, $id = null)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
//            dd($data);
            $data['custid'] = env('SF_CUSTID');//公司支付
            $data['express_type'] = 103;
            $data['is_docall'] = 1;
            $data['pay_method'] = 2;
//            $data['template'] = 'swt-下call';
            $data['type'] = 1;
            $data['remark'] = '读书郎客户 运费到付';
            $id = $data['id'];
            unset($data['id']);
            $express = new Express();
            $result = $express->create_express_order($data);
//            dd($result['Body']['OrderResponse']['@attributes']);
            if ($result['Head'] == 'OK' && $result['Body']['OrderResponse']['@attributes']['filter_result'] < 3) {
                //更新快递信息
                PostExpress::where(['readboy_sn' => $result['Body']['OrderResponse']['@attributes']['orderid']])->update([
                    'status' => 1,
                    'exp_sn' => $result['Body']['OrderResponse']['@attributes']['mailno'],
                ]);
                //写入订单信息
                $order = Order::where(['id' => $id])->first();
                $order->status = Order::EXP_COME_SUCCESS;
                $order->rb_come_exp_sn = $result['Body']['OrderResponse']['@attributes']['orderid'];
                $order->come_exp_sn = $result['Body']['OrderResponse']['@attributes']['mailno'];
                $order->come_exp_com = '顺丰快递';
                $order->save();
                return redirect('/admin/post_check');
            } else {
                $error = new MessageBag([
                    'title'   => '错误提示',
                    'message' => $result['ERROR'],
                ]);
//                Order::where(['id' => $id])->update(['status' => Order::EXP_COME_FAIL]);
                return back()->withInput()->with(compact('error'));
            }
        }

        $order = Order::where(['id' => $id])->first();

        $endpoint = PostRepairEndpoint::where(['id' => $order->repair_endpoint])->first();
//        $endpoint = Endpoint::where(['name' => $repair_endpoint->name])->first();
        $province = ChinaArea::where(['region_id' => $endpoint->province])->first();
        $city = ChinaArea::where(['region_id' => $endpoint->city])->first();
        $district = ChinaArea::where(['region_id' => $endpoint->district])->first();

        $content->header('上门取件-快递下单');

        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/post_check/express');
        $form->hidden('id')->default($id);
        $form->hidden('sn')->default($order->sn);
        $form->text('j_contact', '用户-姓名')->default($order->name)->rules('required');
        $form->text('j_tel', '用户-联系电话')->default($order->phone)->rules('required');
        $form->text('j_province', '用户-省')->default($order->province)->rules('required');
        $form->text('j_city', '用户-市')->default($order->city)->rules('required');
        $form->text('j_county', '用户-区')->default($order->district)->rules('required');
        $form->text('j_address', '用户-地址')->default($order->address)->rules('required');
        if ($order->pickup_time){
            $form->text('sendstarttime', '用户-取件时间')->default($order->pickup_time)->rules('required');
        }
        $form->divide();
        $form->text('d_contact', '终端-姓名')->default($endpoint->name)->rules('required');
        $form->text('d_tel', '终端-联系电话')->default($endpoint->phone)->rules('required');
        $form->text('d_province', '终端-省')->default($province->region_name)->rules('required');
        $form->text('d_city', '终端-市')->default($city->region_name)->rules('required');
        $form->text('d_county', '终端-区')->default($district->region_name)->rules('required');
        $form->text('d_address', '终端-地址')->default($endpoint->address)->rules('required');
        $content->body($form);
        return $content;
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form());
        });
    }

    public function cancel(Request $request)
    {
        foreach (Order::find(request()->get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOF
            //图片弹出层
            $('.img-thumbnail').click(
                 function(){
                     let src = $(this).attr('src');
                     let html = '<div class="message"><img style="max-width:900px;max-height:900px;" src="'+ src +'"><//div>'
                     $.fancybox.open(html);
                 }
             );

            //拨打电话
            $('.call_phone').click(function(){
                var id = $(this).attr('value');
                var login_name = $(this).attr('login_name');
                var pass_word = $(this).attr('pass_word');
                layer.open({
                      type: 2,
                      title: '拨打电话',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['300px', '30%'],
                      content: '/packages/edb_bar/phoneBar/phonebar.html?loginType=sip&loginName='+login_name+'&password='+pass_word+'&callNumber='+id //iframe的url
                });
            });
EOF;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
            $grid->disableCreation();
            $grid->disableExport();
            $agent_order_sn = Input::get('agent_order_sn');
            //快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => [['order.status', '>=', Order::EXP_COME_FAIL],['order.status', '<=',
                        Order::EXP_COME_SUCCESS], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
                1 => [
                    'name' => '待审核',
                    'param' => [['order.status', '=', Order::WAIT_AUDIT], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
                2 => [
                    'name' => '审核已通过',
                    'param' => [['order.status', '=', Order::AUDIT_PASS], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
                3 => [
                    'name' => '审核不通过',
                    'param' => [['order.status', '=', Order::AUDIT_NO_PASS], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
                4 => [
                    'name' => '用户已发货',
                    'param' => [['order.status', '=', Order::EXP_COME_SUCCESS], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
                5 => [
                    'name' => '用户发货失败',
                    'param' => [['order.status', '=', Order::EXP_COME_FAIL], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
                6 => [
                    'name' => '待发货',
                    'param' => [['order.status', '=', Order::AUDIT_PASS], ['order.come_exp_type', 1], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                ],
            ];
//
            //筛选条数
            foreach($option as $key => $value) {
//                dd($value);
                $option[$key]['count'] = Order::where($value['param'])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
            }
//            dd($option);
            //自定义状态快捷筛选按钮
            $grid->tools(function ($tools) use($option) {
                $tools->append(new QuickPickTool($option));
                $button = <<<EOF
                     <div class="btn-group pull-right" style="margin-right:15px; margin-top: 10px;">
                      <a href ="agent_order" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 返回
                      </a >
                    </div >
EOF;
                $tools->append($button);
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                    $batch->add('取消订单', new Cancel());
                });

            });
            //根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->orderBy('repeat_order', 'desc')
                    ->orderBy('id', 'desc');
            }

            $grid->model()
                ->select('order.*')
                ->where($option[-1]['param'])
                ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                ->orderBy('repeat_order', 'desc')
                ->orderBy('id', 'desc');

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('sn', '寄修单号');
                $filter->like('barcode', 'SN条码');
                $filter->like('name', '联系人');
                $filter->equal('audit_status', '审批状态')->select([0 => '未审批', 1 => '同意寄修', 2 => '不需要寄修']);
                $filter->equal('damage', '故障类型')->select(
                    Damage::pluck('title', 'title')->all()
                );
                $filter->equal('model_name', '产品型号')->select(
                    Machine::orderBy('name')->pluck('name', 'name')->all()
                );
                $filter->like('auditor_user.name','审核人');
            });

            $grid->id('ID')->sortable();
            $grid->uid('UID')->sortable();
            Order::order_priority_column($grid);
            $grid->sn('寄修单号')->sortable();
            $grid->model_name('产品型号');
            $grid->barcode('SN');
//            $grid->damage('故障类型');
            $grid->description('故障描述详情')->display(function($value){
                return mb_strlen($value) > 20 ? mb_substr($value, 0 , 20).'...':$value;
            });
            $grid->name('联系人');
//            $grid->column('保修期证明')->display(function ($value) {
//                $ret = '无';
//                $p = $this->period_file;
////                dd($p);
//                if (is_array($p)) {
//                    $ret = '';
//                    foreach ($p as $key => $value) {
//                        $path = rtrim(config('admin.upload.host'), '/') . '/' . $value;
//                        $ret .= '<img src="' . $path . '" style="max-height:80px;max-width:80px" class="img img-thumbnail fancybox-thumbs" data=' . $this->id . ' i=' . $key . ' />';
//                        $ret .= '&nbsp;';
//                    }
//                }
//                return $ret;
//            });
//            $grid->column('上传说明')->display(function ($value) {
//                $ret = '无';
//                $p = $this->upload_file;
////                dd($p);
//                if (is_array($p)) {
//                    $ret = '';
//                    foreach ($p as $key => $value) {
//                        $path = rtrim(config('admin.upload.host'), '/') . '/' . $value;
//                        $ret .= '<img src="' . $path . '" style="max-height:80px;max-width:80px" class="img img-thumbnail fancybox-thumbs" data=' . $this->id . ' i=' . $key . ' />';
//                        $ret .= '&nbsp;';
//                    }
//                }
//                return $ret;
//            });
            $grid->column('联系信息')->expand(function(){
//                $info = array();
                $data[] = array('联系人', $this->name);
                $data[] = array('联系电话', $this->phone);
                $data[] = array('地址详情', $this->province.$this->city.$this->district.$this->address);
                $info = $data;
                return new Table(['联系信息',''], $info);
            });
            $grid->phone('电话拨打')->display(function($value){
                $user_id = Admin::user()->id;
                $staff = RepairStaff::where('user_id', $user_id)->first();
                if (!empty($staff) && !empty($staff->service_phone_username) && !empty($staff->service_phone_password)) {
                    $login_name = $staff->service_phone_username;
                    $pass_word = $staff->service_phone_password;
                    return '<a href="javascript:void(0);" class="call_phone" value="'.$value.'" login_name="'.$login_name.'" pass_word="'.$pass_word.'">'.$value.'</a>';
                } else {
                    return $value;
                }
            });
            $grid->come_exp_type('寄来方式')->display(function ($come_exp_type) {
                return Order::come_exp_type[$come_exp_type];
            });
            $grid->created_at('提交时间')->display(function ($value) {
                if ($this->repeat_order) {
                    return '<span style="color:red">'.$value.'</span>';
                } else {
                    return $value;
                }
            });
            $grid->pickup_time('取件时间')->editable('datetime');
            $grid->audit_status('审批状态')
                ->display(function ($audit_status) {
                    if ($audit_status == 1)
                        return '同意寄修';
                    elseif ($audit_status == 2)
                        return '不同意寄修';
                    else
                        return '未审批';
                });
            $grid->status('订单状态')->display(function($value){
                $s = Order::STATUS;
                if (array_key_exists($value, $s)) {
                    return $value.$s[$value];
                } else {
                    return "————";
                }
            });
            $grid->column('auditor_user.name','审核人');
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $status = $actions->row->status;
//                if ($actions->row->come_exp_type == 1 && $actions->row->audit_status == 1 && $status == Order::AUDIT_PASS) {
//                    $c = 'post_check/express/' . $actions->getKey();
//                    $html = '<a href="' . $c . '"><span style="color:blue">下单 </span></a>';
//                    $actions->append($html);
//                }
                $c = 'post_check/view/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">查看 </span></a>';
                $actions->append($html);
                if ($status == Order::WAIT_AUDIT) {
                    $c = 'agent_order_post_check/' . $actions->getKey();
                    $html = '<a href="' . $c . '/edit"><span style="color:orange">【审核】</span></a>';
                    $actions->append($html);
                } else {
                    $c = 'agent_order_post_check/' . $actions->getKey();
                    $html = '<a href="' . $c . '/edit"><span style="color:green">【修改】</span></a>';
                    $actions->append($html);
                }
            });
        });
    }

    private function mes_info($barcode)
    {
        $appId = 'Web';
        $time = time();
        $appSecret = 'M4S8tUB8OBBvIUN7';
        $md5Str = md5("$appId-$time-$appSecret");

        $authKey =  "$appId-$time-$md5Str";
        $client = new \GuzzleHttp\Client();
        $url = "http://api-mes.readboy.com/index.php?s=/Api/Barcode/all.html&barcode=$barcode&authKey=$authKey";
        $response = $client->get($url);

        if ($response->getStatusCode() == '200') {
            $body = $response->getBody();
            return json_decode($body->getContents(), true);;
        } else {
            return [];
        }
    }

    private function wear_info($imei)
    {
        $client = new \GuzzleHttp\Client();
        $url = 'http://wear.readboy.com:8080/api/info?imei='.$imei;
        $response = $client->request('GET', $url, [
            'auth' => ['wearapi', 'readboy999']
        ]);

        if ($response->getStatusCode() == '200') {
            $data = json_decode($response->getBody());
            return isset($data->data->device) ? array('add_date'=>$data->data->device->addtime, 'bind_date'=>$data->data->device->bindtime, 'type'=>'machine', 'model'=>$data->data->device->type) : [];
        } else {
            return [];
        }
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id = null)
    {
        $script = <<<EOF
            //图片弹出层
            $('.img-thumbnail').click(
                 function(){
                     let src = $(this).attr('src');
                     let html = '<div class="message" style=""><button id="button" style="display:block;margin: 0 auto;"><img id="img" style="max-width:900px;max-height:900px; transform: rotate(0deg)" src="'+ src +'"><//button><//div>'
                     $.fancybox.open(html);
                 }
             );
            $(document).on('click','#button',function(){
                var tr = $('#img').css("transform");
                a = eval('get'+$('#img').css('transform'));
                var step=90; 
                $('#img').css("transform","rotate("+(a+step)%360+"deg)");
              });
              
              function getmatrix(a,b,c,d,e,f){  
                var aa=Math.round(180*Math.asin(a)/ Math.PI);  
                var bb=Math.round(180*Math.acos(b)/ Math.PI);  
                var cc=Math.round(180*Math.asin(c)/ Math.PI);  
                var dd=Math.round(180*Math.acos(d)/ Math.PI);  
                var deg=0;  
                if(aa==bb||-aa==bb){  
                    deg=dd;  
                }else if(-aa+bb==180){  
                    deg=180+cc;  
                }else if(aa+bb==180){  
                    deg=360-cc||360-dd;  
                }  
                return deg>=360?0:deg;  
                //return (aa+','+bb+','+cc+','+dd);

	          }
EOF;
        Admin::script($script);
        return Admin::form(Order::class, function (Form $form) use ($id) {

            $form->html('<label>设备信息</label>');
            $form->display('sn', '寄修单号');
            $form->display('model_name', '机型');
            $form->display('barcode', 'S/N码');
            $form->display('come_exp_type', '寄件方式')->with(function($value) {
                return Order::come_exp_type[$value];
            });
            $form->display('in_period', '保修状态')->with(function ($value) {
                switch ($value) {
                    case 1:
                        return '保修期内';
                    case 2:
                        return '保修期外';
                    default:
                        return '无保修信息';
                }
            });
            $form->display('reason', '受损状态')->with(function ($value) {
                return $value == 1 ? '人为损坏' : '元件问题';
            });
            $form->display('damage', '故障类型');
            $form->display('description', '故障详情描述');
            $form->display('attachment', '附件信息');
            $form->html('<label>联系人信息</label>');
            $form->display('name', '联系人');
            $form->display('phone', '联系方式');
            $form->display('agency', '代理区域');
            $form->divider('');
            $form->html('<label>审核</label>');
            $form->select('reason', '受损状态')->options(Order::reason);
            if (!empty($id)){
                $order = Order::where('id', $id)->first();
                $barcode = $order->barcode;
                $has_warranty = $order->has_warranty;
//                dump($has_warranty);
//                dump(gettype($has_warranty));
                if ($has_warranty == 1){
                    $data = DB::connection('mysql2')
                        ->table('warranty')
                        ->where([['barcode','=',$barcode],['status','=','1']])
                        ->select('buy_date')->first();
//                    dd($data);
                    if (empty($data)){
                        $html = <<<EOF
                    <span style="color:red;">此机器无保卡信息，但记录为有保卡, 请确认</span>&nbsp;
EOF;
                        $form->html($html);
                    }else{
                        $time = $data->buy_date;
                        $time2 = strtotime('+1 year',strtotime($time));
                        if (strtotime('2020-01-20') < $time2 && $time2< strtotime('2020-02-29')){
                            $html = <<<EOF
                    <span style="color:red;">此用户在延保时间内，请确定是否允许延保,购买时间为：{$time}</span>&nbsp;
EOF;
                            $form->html($html);
                        }else{
                            $html = <<<EOF
                    <span style="color:red;">保卡购买时间为：{$time}</span>&nbsp;
EOF;
                            $form->html($html);
                        }
                    }

                }
//                dump(date('Y-m-d',$time2));
            }
            if (!empty($id)) {
                $order = Order::where('id', $id)->first();
                $barcode = $order->barcode;
                $mes_info = $this->mes_info($barcode);
                $pseudo_code = PseudoCode::where('barcode',$order->barcode)->first();
                if (empty($pseudo_code)){
                    $html = <<<EOF
                    <span style="color:red;">MES生产时间：{$mes_info['data']['update_time']}</span>&nbsp;
EOF;
                    $form->html($html);
                    if ($mes_info) {
                        if (!empty($mes_info['data']['imei1'])) {
                            $wear_info = $this->wear_info($mes_info['data']['imei1']);
//                        dd($wear_info);
                            if ($wear_info) {
                                $html = <<<EOF
                                <span style="color:red;">手表激活时间：{$wear_info['add_date']}</span>&nbsp;
EOF;
                                $form->html($html);
                                $html = <<<EOF
                                <span style="color:red;">手表绑定时间：{$wear_info['bind_date']}</span>&nbsp;
EOF;
                                $form->html($html);
                            }
                        }
                    }
                }else{
                    $html = <<<EOF
                    <span style="color:red;">此条码为伪码，无MES生产时间}</span>&nbsp;
EOF;
                    $form->html($html);
                }

            }
            $form->switch('order_extend.discount', '是否打折')->states([
                'on'  => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ]);
            $form->select('in_period', '审核保修')
                ->options([0 => '无保修信息', 1 => '保修期内', 2 => '保修期外'])->help('根据提交材料判断是否在保修期内')->rules('required|between:1,2');
            $states_extend = [
                'on'  => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ];
            $form->switch('extend_warranty', '是否延保标记')->states($states_extend);

            if(!empty($id)){
                $order = Order::where('id', $id)->first();
                $barcode = $order->barcode;
                $has_screen_insurance = $order->has_screen_insurance;
                if ($has_screen_insurance == 1){
                    $data = BrokenScreenInsurance::where([['barcode', '=', $barcode], ['status', '=', 300]])
                        ->leftJoin('broken_screen_insurance_standard as bsis', 'broken_screen_insurance.standard', '=', 'bsis.id')
                        ->select('broken_screen_insurance.created_at', 'bsis.month')->first();
                    if ($data){
//                        dd($data);
                        $buy_date = $data->created_at;
                        $month = '+'.$data->month.' month';   //碎屏保投保月份
                        // 日期加投保月份  减去一天
                        $buy_date = strtotime('-1 day', strtotime($month, strtotime(date('Y-m-d', strtotime($buy_date)))));
                        $buy_date = date('Y-m-d', $buy_date);

                        $now = date('Y-m-d');
//                        dump($now);
                        if($now < $buy_date){
                            $html = <<<EOF
                    <span style="color:red;">碎屏保在保修期内,到期时间：{$buy_date}</span>&nbsp;
EOF;
                            $form->html($html);
                        }else{
                            $html = <<<EOF
                    <span style="color:red;">碎屏保已过期</span>&nbsp;
EOF;
                            $form->html($html);
                        }
                    }
                }
            }
            $form->display('has_screen_insurance', '是否有碎屏保')->with(function ($value){
                return Order::has_screen_insurance[$value];
            });
            $form->select('in_si_period', '审核碎屏保')
                ->options([0 => '无保修信息', 1 => '保修期内', 2 => '保修期外'])->rules('required|between:1,2');

            $form->display('period_file', '保修材料')->with(function ($value){
                $ret = '无';
                $p = $value;
                if (is_array($p)) {
                    $ret = '';
                    foreach ($p as $key => $value) {
                        $path = rtrim(config('admin.upload.host'), '/') . '/' . $value;
                        $ret .= '<img src="' . $path . '" style="max-height:80px;max-width:80px" class="img img-thumbnail fancybox-thumbs" data=' . $this->id . ' i=' . $key . ' />';
                        $ret .= '&nbsp;';
                    }
                }
                return $ret;
            });
            $form->display('upload_file', '损坏资料')->with(function ($value){
                $ret = '无';
                $p = $value;
                if (is_array($p)) {
                    $ret = '';
                    foreach ($p as $key => $value) {
                        $path = rtrim(config('admin.upload.host'), '/') . '/' . $value;
                        $ret .= '<img src="' . $path . '" style="max-height:80px;max-width:80px" class="img img-thumbnail fancybox-thumbs" data=' . $this->id . ' i=' . $key . ' />';
                        $ret .= '&nbsp;';
                    }
                }
                return $ret;
            });
            $form->display('video_file', '视频')->with(function ($value){
                $ret = '无';
                $p = $value;
                if (is_array($p)) {
                    $ret = '';
                    foreach ($p as $key => $value) {
                        $path = rtrim(config('admin.upload.host'), '/') . '/' . $value;

                        $ret .= '<video src="' . $path . '" style="height:200px;width:400px"  controls="controls"  data=' . $this->id . ' i=' . $key . ' ></video>';
                        $ret .= '&nbsp;';
                    }
                }
                return $ret;
            });
            $states = [
                'on'  => ['value' => 1, 'text' => '是', 'color' => 'success'],
                'off' => ['value' => 0, 'text' => '否', 'color' => 'danger'],
            ];
            $form->select('repeat_order', '是否二次维修')->options(function ($v){
                if ($v == 0){
                    return [0=>"首次寄修"];
                }else{
                    return [1=>"二次寄修", 2=>"二次返修"];
                }
            });
            $form->display('barcode', '维修记录')->with(function($value) use ($form, $id) {
                $data = $form->model()->where('barcode', $value)->where('id', '<>', $id)->get()->toArray();
//                dd($data);
                $str = '';
                if (count($data) > 0) {
                    foreach ($data as $i => $d) {
                        $str .= '<span style="color:red">最后更新时间：'.$d['updated_at'].'</span>  &nbsp; <a href="/admin/post_repair/view/'. $d['id'] .'">查看维修记录</a>';
                        if ($i < count($data) - 1) {
                            $str .= '<br>';
                        }
                    }
                } else {
                    $str = '无';
                }
                return $str;
            });
            $form->switch('is_agency', '是否终端寄修')->states($states);
            $form->select('audit_status', '审核寄修')
                ->options([1 => '支持寄修', 2 => '不支持寄修']);
//            $form->datetime('pickup_time','取件时间');
            $form->textarea('audit_opinion', '审核备注');
            Order::order_priority_form($form);
            $form->hidden('status');
            $form->display('auditor_user.name','审核人');
            $form->saving(function (Form $form) {
                //操作状态判断
                $form->model()->auditor = Admin::user()->id;
                if (abs($form->model()->status) > Order::EXP_COME_SUCCESS) {
                    $error = new MessageBag([
                        'title'   => '无法操作',
                        'message' => '当前状态不可编辑！',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                //保修期检查
                if ($form->audit_status == 1 && empty(intval($form->in_period))) {
                    $error = new MessageBag([
                        'title'   => '缺少保修期信息',
                        'message' => '保修期信息必须审核！',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                if (abs($form->status) < Order::EXP_COME_SUCCESS) {
                    if ($form->audit_status == 1) {
                        $form->status = Order::AUDIT_PASS;
                    } else {
                        $form->status = Order::AUDIT_NO_PASS;
                    }
                }

            });
        });
    }

}
