<?php

namespace App\Admin\Extensions;

use App\Models\Endpoint;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;
use App\Models\Agency;

class EndpointExporter extends AbstractExporter
{
    public function export()
    {
        $filename = '终端列表';
        $data = $this->getData();
        if (!empty($data)) {
            $titles = [
                '所属代理',
                '省区',
                '市区',
                '负责人',
                '负责人电话',
                '名称',
                '地址',
                '渠道类型',
                '渠道级别',
                '终端位置',
                '终端面积',
                '销售额',
                '竞争品牌信息',
                '类型',
                '创建时间',
                '更新时间',
                '活跃时间',
            ];
            $formatData = $this->getFormatData($data);
            array_unshift($formatData, $titles);
            ExcelExportTrait::exportToExcel($filename, $formatData);
        }
    }

    private function getFormatData($data)
    {
        $channelType = DB::table('endpoint_channel')->pluck('name', 'id')->prepend('')->toArray();
        $channelLevel = [
            0 => '',
            1 => '省级',
            2 => '市级',
            3 => '县级',
            4 => '镇级',
        ];
        foreach ($data as $row) {
            if ($row['status'] == 0) {
                continue;
            }
            //获取竞争品牌信息
            $brandsList = $this->getCompeteBrandsFormatInfo($row['compete_brands']);
            $row = [
                $row['agency']['name'] ? $row['agency']['name'] : Agency::where('id', '=', $row['top_agency'])
                        ->value('name') . "[一级代理]",
                $row['province'],
                $row['city'],
                $row['manager'],
                $row['phone'] . "\t",
                $row['name'],
                $row['address'],
                isset($row['endpoint_extend']) ? $channelType[intval($row['endpoint_extend']['channel_type'])] : '无',
                isset($row['endpoint_extend']) ? $channelLevel[intval($row['endpoint_extend']['channel_level'])] : '无',
                isset($row['endpoint_extend']) ? $row['endpoint_extend']['position_priority'] : '无',
                isset($row['endpoint_extend']) ? ($row['endpoint_extend']['area'] ? $row['endpoint_extend']['area'] . '㎡' : '') : '无',
                isset($row['endpoint_extend']) ? ($row['endpoint_extend']['sales'] ? $row['endpoint_extend']['sales'] . '元' : '') : '无',
                $brandsList ?: '无',
                Endpoint::ENDPOINT_TYPE[$row['type']] ?: '无',
                $row['created_at'],
                $row['updated_at'],
                $row['active_at'],
            ];
            $formatData[] = $row;
        }

        return $formatData;
    }

    private function getCompeteBrandsFormatInfo($brands)
    {
        $competeBrandsFormatInfo = '';
        foreach ($brands as $key => $brand) {
            $competeBrandsFormatInfo .= "品牌:{$brand['brand']}[{$brand['position_priority']}]";
        }

        return $competeBrandsFormatInfo;
    }

}