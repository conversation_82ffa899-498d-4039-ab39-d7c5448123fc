<?php

namespace App\Admin\Extensions;

/**
 * 云客服系统对接
 * Create By Su Guanghua 2021/10/23
 * 坐席外呼接口: '/v20160818/call/dialout/'
 */
class CloudCustomer {

    private $url = 'http://yjwh.tycc100.com';
    private	$accountid = "N00000001434";//云呼账号 
    private	$secret	= "ea3ca64d-6003-4ff8-aef5-c01649ce3c98";//云呼密码

    /**
     * 接口调用
     */
    public function curlRequest($partUrl , $data){

        $time =	date("YmdHis");
    	$authorization = base64_encode($this->accountid . ":" . $time);
    	$sig = strtoupper(md5($this->accountid . $this->secret . $time));

        $url = $this->url . $partUrl . $this->accountid . "?sig=" . $sig;

       

        $header = [];
        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "Content-Length: ".strlen( json_encode($data) );
        $header[] = "Authorization: ".$authorization;

        $ch = curl_init ();
        curl_setopt($ch, CURLOPT_URL, ($url) );//地址
        curl_setopt($ch, CURLOPT_POST, 1);   //请求方式为post
        curl_setopt($ch, CURLOPT_POSTFIELDS,json_encode($data)); //post传输的数据。
        curl_setopt($ch, CURLINFO_HEADER_OUT, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE); 
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);

        $return = curl_exec ( $ch );
			
        if($return === FALSE ){
            return array(
                'errcode' => 400,
                'errmsg' => curl_error($ch)
            );
        }

        curl_close ( $ch );

        return $return;
    }
}