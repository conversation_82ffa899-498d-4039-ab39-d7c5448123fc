<?php

namespace App\Admin\Extensions;

use App\Models\Endpoint;
use App\Models\PayOrder;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;
use App\Models\Agency;

class PayOrderExporter extends AbstractExporter
{
    public function export()
    {
        $filename = '收款报表';
        $data = $this->getData();
        if (!empty($data)) {
            $titles = [
                '寄修订单号',
                '支付内部单号',
                '支付公司',
                '支付公司单号',
                '是否已支付',
                '支付金额（元）',
                '支付时间',
            ];
            $formatData = $this->getFormatData($data);
            array_unshift($formatData, $titles);
            ExcelExportTrait::exportToExcel($filename, $formatData);
        }
    }

    private function getFormatData($data)
    {
        foreach ($data as $row) {
            $row = [
                $row['pr_sn'],
                $row['readboy_sn'],
                PayOrder::COM[$row['com']],
                $row['pay_sn'],
                PayOrder::IS_PAID[$row['is_paid']],
                $row['pay_amount'],
                $row['updated_at'],
            ];
            $formatData[] = $row;
        }

        return $formatData;
    }


}