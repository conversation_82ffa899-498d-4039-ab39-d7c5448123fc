<?php


namespace App\Admin\Extensions\Tools;


use Encore\Admin\Grid\Tools\BatchAction;

class DeleteAgentOrder extends BatchAction
{
    public function script()
    {
        return <<<EOF
        $('{$this->getElementClass()}').on('click', function() {
    var res = confirm("确定要删除所有记录?");
    if (res == true)
    {
       $.ajax({
        method: 'post',
        url: '{$this->resource}/deleteAgentOrder',
        data: {
            _token:LA.token,
            ids: selectedRows()
        },
        success: function () {
            $.pjax.reload('#pjax-container');
            toastr.success('操作成功');
        }
    });
    }
    else
    {
      alert("You pressed Cancel!");
    }
});
EOF;

    }
}