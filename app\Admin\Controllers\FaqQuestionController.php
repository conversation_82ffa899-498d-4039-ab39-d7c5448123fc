<?php


namespace App\Admin\Controllers;


use App\Admin\Extensions\Tools\QuickPickTool;
use App\Http\Controllers\Controller;
use App\Models\FaqCategory;
use App\Models\FaqQuestion;
use App\Models\MachineCategory;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Form;
use Encore\Admin\Layout\Content;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Grid;
use function foo\func;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\MessageBag;
use function PHPSTORM_META\map;

class FaqQuestionController extends Controller
{
    use ModelForm;

    const SWITCH_TEXT = [
        'on' => ['text' => '是', 'value' => 1],
        'off' => ['text' => '否', 'value' => 0],
    ];
    const ENDPOINT_CHECK_TIPS = '开启终端可见 需要填视频以及预览图';
    const TYPE_VIDEO_CHECK_TIPS = '选了问题类型为 视频，需要填视频以及预览图';
    const COLUMN_TITLE_VISIBLE = '可见平台';

    public function index(){
        return Admin::content(function (Content $content){
            $content->header('常见问题');
            $content->description('常见问题内容');
            $content->body($this->grid());
        });
    }

    public function edit($id){
        return Admin::content(function (Content $content) use ($id){
            $content->header('常见问题');
            $content->description('常见问题编辑');
            $content->body($this->form($id)->edit($id));
        });
    }

    public function create(){
        return Admin::content(function (Content $content){
           $content->header('新增');
           $content->description('新增常见问题');
           $content->body($this->form());
        });
    }

    public function grid()
    {
        $column_title_visible = self::COLUMN_TITLE_VISIBLE;
        $script =
<<<script
    $(fn_ready);
    function fn_ready() {
        let n_grid_tr_list = $($($('.box-body .table tbody')[0]).children());
        let n_grid_tr_title = $(n_grid_tr_list[0]);

        // 查找指定列-可见平台
        let column_title_visible = '$column_title_visible';
        function d_check_visible_title() {
            return this.innerText == column_title_visible;
        }
        let n_grid_th_visible = $(n_grid_tr_title.children().filter(d_check_visible_title)[0]);
        n_grid_th_visible.css('width', '100px'); // 设置列宽
    }
script;
        Admin::script($script);
        return Admin::grid(FaqQuestion::class, function (Grid $grid) {
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('title', '问题标题');
                $filter->like('content', '问题回复');
                $filter->equal('question_type', '问题类型')->select(FaqQuestion::QUESTION_TYPE);
                $filter->is('machine_category_id', '机器品类')
                    ->select(MachineCategory::getVisibleSelectArray())
                    ->load('faq_category', '/admin/faq/category');
                $filter->is('faq_category', '问题分类')->select();
                $filter->equal('top', '是否置顶')->select(FaqQuestion::IS_TOP);
                $filter->equal('enable', '是否可见')->select(FaqQuestion::IS_ENABLE);
                $filter->where(function ($query) {
                    $query->where($this->input, '=', 1);
                }, '可见平台', 'visible_platform')->select(FaqQuestion::VISIBLE_PLATFORM);
                $filter->between('updated_at', '更新时间')->datetime();
            });

            // 快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => [],
                ],
                1 => [
                    'name' => '寄修可见',
                    'param' => [['repair_visible', '=', 1]],
                ],
                2 => [
                    'name' => '终端可见',
                    'param' => [['endpoint_visible', '=', 1]],
                ],
                3 => [
                    'name' => '平板可见',
                    'param' => [['tablet_visible', '=', 1]],
                ],
            ];
            // 筛选条数
            foreach ($option as $key => $value) {
                $option[$key]['count'] = FaqQuestion::where($value['param'])->count();
            }

            // 自定义状态快捷筛选按钮
            $grid->tools(function ($tools) use ($option) {
                $tools->append(new QuickPickTool($option));
            });

            // 根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])->orderBy('id', 'desc');
            }
            $grid->model()->where($option[-1]['param'])->orderBy('id', 'desc');

            $grid->id('ID')->sortable()->display(function($value) {
                return '<span id="id-' . $value . '" class="id">' . $value . '</span>';
            });
            $grid->title('问题标题');
            // $grid->content('问题回复');
            $grid->content('问题回复')->display(function ($content) {
                return "<span>$content</span>";
            });
            $grid->question_type('问题类型')->display(function ($qt_v) {
                $label = 'default';
                if (array_key_exists($qt_v, FaqQuestion::QUESTION_TYPE_LABEL)) {
                    $label = FaqQuestion::QUESTION_TYPE_LABEL[$qt_v];
                }
                $qt_s = '无';
                if (array_key_exists($qt_v, FaqQuestion::QUESTION_TYPE)) {
                    $qt_s = FaqQuestion::QUESTION_TYPE[$qt_v];
                }
                return '<span class="label label-' . $label . '">' . $qt_s . '</span>';
            });
            $grid->machine_category_id('机型品类')->display(function($value) {
                if ($value) {
                    return MachineCategory::find($value)->name;
                }
                return '无';
            });
            $grid->faq_category('常见问题分类')->display(function ($value) {
                if ($value) {
                    return FaqCategory::find($value)->title;
                }
                return '无';
            });
            /* $grid->column('机型品类-问题分类')->display(function () {
                $mci = $this->machine_category_id;
                $fci = $this->faq_category;
                $mc = $mci ? MachineCategory::find($mci)->name : '无';
                $fc = $fci ? FaqCategory::find($fci)->title : '无';
                return $mc . '<br/>' . $fc;
            }); */

            // $grid->video_preview('视频预览图')->image(null, 200);
            $grid->column('视频预览图')->display(function() {
                if (property_exists($this, 'video_preview') && !empty($this->video_preview)) {
                    $value = rtrim(config('admin.upload.host'), '/') . '/' . $this->video_preview;
                    $style = 'max-width:200px;max-height:200px;';
                    return '<img src="' . $value . '" alt="' . $this->video_preview .
                        '" class="img img-thumbnail" style="' . $style . '">';
                } else {
                    return '<span><空></span>';
                }
            });
            $grid->column('视频')->display(function() {
                if (property_exists($this, 'video_path') && !empty($this->video_path)) {
                    $value = rtrim(config('admin.upload.host'), '/') . '/' . $this->video_path;
                    $style = 'max-width:200px;max-height:200px;';
                    return '<video src="' . $value . '" controls style="' . $style . '"/>';
                } else {
                    return '<span><空></span>';
                }
            });

            $grid->top('是否置顶')->switch(self::SWITCH_TEXT);
            $grid->enable('是否可见')->switch(self::SWITCH_TEXT);
            // $grid->repair_visible('寄修可见')->switch(self::SWITCH_TEXT);
            // $grid->endpoint_visible('终端可见')->switch(self::SWITCH_TEXT);
            // $grid->tablet_visible('平板可见')->switch(self::SWITCH_TEXT);
            $grid->column(self::COLUMN_TITLE_VISIBLE)
                // ->style('width:200px;') //不知为何不生效
                // ->width(200)
                ->switchGroup(FaqQuestion::VISIBLE_PLATFORM, self::SWITCH_TEXT);
            $grid->count('点赞数');
            $grid->updated_at('更新时间')->sortable();
        });
    }

    public function form($id = null)
    {
        $endpoint_check_tips = self::ENDPOINT_CHECK_TIPS;
        $question_type_text = FaqQuestion::QUESTION_TYPE_TEXT;
        $question_type_video = FaqQuestion::QUESTION_TYPE_VIDEO;
        $script =
<<<script
    $(fn_ready);
    function fn_ready() {
        function fn_get_video() {
            let n_video_preview = $("label[for='video_preview']").next().find('.file-caption-name');
            let video_preview_title = n_video_preview.prop('title');
            let n_video_path = $("label[for='video_path']").next().find('.file-caption-name');
            let video_path_title = n_video_path.prop('title');
            let n_video_path_write = $('#video_path_write');
            let video_path_write = n_video_path_write.val();
            return { video_preview_title, video_path_title, video_path_write };
        }

        // 获取指定label行
        function fn_get_n_by_label(label) {
            return $("label[for='" + label + "']").parent();
        }

        // video部分 显示 隐藏
        let video_labels = ['video_preview', 'video_path', 'video_path_write'];
        function fn_hide_video() {
            video_labels.forEach(l => fn_get_n_by_label(l).hide());
        }
        function fn_show_video() {
            video_labels.forEach(l => fn_get_n_by_label(l).show());
        }

        fn_question_type_change();
        // .question_type 改变
        $('.question_type').on('change', fn_question_type_change);
        function fn_question_type_change() {
            let qt_v = $('.question_type').val();
            console.log('.question_type change', qt_v);
            if (qt_v == $question_type_text) {
                fn_hide_video();
            } else if (qt_v == $question_type_video) {
                fn_show_video();
            }
        }

        // endpoint_visible switchChange 事件
        /* $('.endpoint_visible.la_checkbox').on('switchChange.bootstrapSwitch', fn_endpoint_change); */
        function fn_endpoint_change(event, state) {
            let v = fn_get_video();
            console.log('.endpoint_visible switchChange', { event, state, v });
            if (state && (!v.video_preview_title || (!v.video_path_title && !v.video_path_write))) {
                let endpoint_check_tips = '$endpoint_check_tips';
                console.log(endpoint_check_tips);
                layer.msg(endpoint_check_tips); // 弹窗提示
                $(this).bootstrapSwitch('toggleState', !state, true); // 将状态设回off
            }
        }

        // switch div click 事件
        /* $('.endpoint_visible.la_checkbox').siblings('span').on('click', fn_endpoint_click);
        function fn_endpoint_click(event) {
            let ev = $('.endpoint_visible.la_checkbox').val();
            console.log({ event, ev });
        } */
    }
script;
        Admin::script($script);
        return Admin::form(FaqQuestion::class, function (Form $form) use ($id) {
            $form->display('id', 'ID');
            $form->text('title', '标题')->rules('required');
            if ($id > 0) {
                $m_question_type = FaqQuestion::where('id', '=', $id)->value('question_type');
                if ($m_question_type == FaqQuestion::QUESTION_TYPE_VIDEO) {
                    $form->textarea('content', '内容');
                } else {
                    $form->editor('content', '内容')->attribute(['route' => 'faq_question_config_picture_upload']);
                }
            } else {
                $form->textarea('content', '内容');
            }

            $form->select('question_type', '问题类型')
                ->options(FaqQuestion::QUESTION_TYPE)
                ->default(FaqQuestion::QUESTION_TYPE_TEXT);

            $form->divider();
            $form->image('video_preview', '视频预览图')
                ->move('rbcare/repair/faq/preview')
                ->uniqueName()
                ->options([
                    'allowedFileTypes' => '',
                    'msgInvalidFileExtension' => '{name}文件类型不正确，只支持{extensions}文件',
                    'allowedFileExtensions' => ['jpg', 'png', 'jpeg'],
                ])
                ->resize(1920, 1920, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });

            $init_preview = array();
            $init_preview[] = ['type'=>'video', 'filetype' => 'video/mp4', 'key' => 0];
            $preview_zoom = array();
            $preview_zoom['video'] = ['width' => "auto", 'height' => "300px"];
            $form->file('video_path', '视频')
                ->options([
                    'maxFileSize' => 102400,
                    'msgSizeTooLarge' => '文件 "{name}" (<b>{size} KB</b>) 超过了最大允许上传大小 <b>{maxSize} KB</b>.',
                    'maxFilePreviewSize' => 0,
                    'initialPreviewFileType' => 'video',
                    'previewFileType' => ['mp4', 'mov'],
                    'initialPreviewConfig' => $init_preview,
                    'previewSettings' => $preview_zoom,
                ])
                ->move('rbcare/repair/faq/video')
                ->uniqueName()
                ->help('如果视频文件体积超过100M，请联系开发人员进行后台上传！MP4文件请使用 H264 视频编码和 AAC 音频编码！否则无法播放！视频上传后请在列表中先播放验证再改变状态为通过，才能发布到客户端！');
            $form->text('video_path_write', '视频地址')
                ->help('请输入相对地址如“rbcare/repair/faq/video/1.mp4”，如果要手动输入视频地址，则不要再上传视频，否则会覆盖掉手动输入的视频地址！')
                ->default($id ? (FaqQuestion::find($id)->video_path) : null);
            $form->divider();

            $form->select('machine_category_id', '机型品类')
                ->options(MachineCategory::getVisibleSelectArray()->prepend('请选择', 0))
                ->rules('required')->load('faq_category', '/admin/faq/category');
            $form->select('faq_category', '问题分类')
                ->options(function ($value) {
                    return FaqCategory::where('id', $value)->pluck('title', 'id')->prepend('请选择', 0);
                })
                ->rules('required');

            $form->switch('top', '是否置顶')->default(0);
            $form->switch('enable', '是否可见')->default(0);
            $form->switch('repair_visible', '寄修可见')->default(0);
            $form->switch('endpoint_visible', '终端可见')->default(0);
            $form->switch('tablet_visible', '平板可见')->default(0);

            $form->saving(function($form) use ($id) {
                $m = $form->model();
                // 检查重复视频
                /* if ($form->video_path_write) {
                    $id = $id ? $id : $form->model()->id;
                    $count = FaqQuestion::where('video_path', $form->video_path_write)->where('id', '<>', $id)->count();
                    if ($count > 0) {
                        $error = new MessageBag([
                            'title'   => '重复视频文件',
                            'message' => '已存在该视频文件，请更换别的视频！',
                        ]);
                        return back()->with(compact('error'));
                    }
                } */
                // 开启终端时 检查有无视频地址
                // $is_endpoint_visible = strcasecmp($form->endpoint_visible, 'on') == 0;
                $is_type_video = $form->question_type == FaqQuestion::QUESTION_TYPE_VIDEO;
                $is_empty_video_path = is_null($form->video_path) &&
                    empty($form->video_path) && empty($m->video_path);
                $in_video_path_write = $form->video_path_write;
                $is_empty_video_path_write = empty($in_video_path_write);
                $is_empty_video_preview = is_null($form->video_preview) &&
                    empty($form->video_preview) && empty($m->video_preview);
                if ((($is_empty_video_path && $is_empty_video_path_write) || // 没填视频
                    $is_empty_video_preview) && $is_type_video) { // 或没预览图
                    $error = new MessageBag([
                        'title'   => '错误提示：没有视频',
                        'message' => self::TYPE_VIDEO_CHECK_TIPS,
                    ]);
                    return back()->with(compact('error'));
                }
            });
        });
    }

    public function faq_category(Request $request)
    {
        $machine_category_id = request()->get('q');
        $a = FaqCategory::where([['machine_category_id', '=', $machine_category_id],['visibility', '=',1]])->get(['id', DB::raw('title as text')])->prepend(['请选择']);
        return $a;
    }

    public function uploadPicture(\Illuminate\Http\Request $request)
    {
        $pathList = [];
        foreach ($request->file('images') as $key => $value) {
            $path = $value->store('rbcare/repair/explain', 'oss');
            $pathList[] = config('admin.upload.host') . $path;
        }
        //这个是专门提供给富文本编辑器的json响应,要这样子的格式前端编辑器才会认为你成功上传了图片
        $responseData = ['errno' => 0, 'data' => $pathList];

        return response()->json($responseData);
    }
}
