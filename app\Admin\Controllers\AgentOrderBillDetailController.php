<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\OrderExporter;
use App\Admin\Extensions\Tools\Button;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Models\MachineAccessory;
use App\Models\MachineAccessoryRelation;
use App\Models\MachineAccessoryTree;
use App\Models\MachineMalfunction;
use App\Models\Material;
use App\Models\Order;

use App\Models\OrderLog;
use App\Models\PostRepairAccessory;
use App\Models\PostRepairMalfunction;
use App\Models\PostRepairMaterial;
use App\Models\PostRepairUsedMaterial;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\MessageBag;

class AgentOrderBillDetailController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('经销商寄修小单账单详情');
            $content->description('经销商寄修小单账单详情');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('经销商寄修小单账单详情');
            $content->description('编辑');

            $content->body($this->form($id)->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修订单查看');
            $order = Order::where(['id' => $id])->first();
            $pr_material = DB::table('pr_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_material.mat_id')
                ->leftjoin('machine_malfunction', 'machine_malfunction.id', '=', 'pr_material.malfunction_id')
                ->leftjoin('material', 'material.id', '=', 'pr_material.material_id')
                ->where('pr_material.pr_sn', $order['sn'])
                ->select('machine_accessory_tree.title as title', 'machine_malfunction.title as malfunction', 'material.price as price', 'pr_material.count as count')
                ->get()
                ->toArray();
            $pr_used_material = DB::table('pr_used_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_used_material.mat_id')
                ->leftjoin('material', 'material.id', '=', 'pr_used_material.material_id')
                ->where('pr_used_material.pr_sn', $order['sn'])
                ->select('material.name as name', 'material.price as price', 'pr_used_material.count as count',
                    'material.code as code', 'material.old_code as old_code', 'material.specification as specification', 'material.from as from')
                ->get()
                ->toArray();
            $content->body(view('admin/post_repair/view', compact('order', 'pr_material', 'pr_used_material')));

        });
    }


    public function log($id)
    {
        $data = DB::table('order_log')
            ->leftjoin('admin_users', 'order_log.admin', '=', 'admin_users.id')
            ->where('order_log.pr_sn', $id)
            ->get()->toArray();
        return view('admin/post_repair_manage/log', compact('data'));
    }

    public function printf($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('打印检测报告');
            $order = Order::where(['id' => $id])->first();
            $post_malfunction = DB::table('pr_malfunction')
                ->join('machine_malfunction', 'machine_malfunction.id', '=', 'pr_malfunction.malfunction_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_malfunction.title')->toArray();
            $malfunction = implode('，', $post_malfunction);
            $post_accessory = DB::table('pr_accessory')
                ->join('machine_accessory', 'machine_accessory.id', '=', 'pr_accessory.mar_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_accessory.title')->toArray();
            $accessory = implode('，', $post_accessory);
            $content->body(view('admin/post_order/print', compact('order', 'malfunction', 'accessory')));

        });
    }

    public function cancel(Request $request)
    {
        foreach (Order::find(request()->get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修订单');
            $content->description('创建');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(Order::class, function (Grid $grid) {
//            $grid->model()->where('status', '>=', 200);
            $grid->disableCreation();
            $grid->disableExport();
//            $grid->exporter(new OrderExporter());
            $agent_order_sn = Input::get('agent_order_sn');
            $grid->tools(function ($tools) {
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                    $batch->add('取消订单', new Cancel());
                });
                $button = <<<EOF
                     <div class="btn-group pull-right" style="margin-right:0px; margin-top: 0px;">
                      <a href ="agent_order_bill" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 返回
                      </a >
                    </div >
EOF;
                $tools->append($button);
            });
            $grid->model()
                ->select('order.*')
                ->where([['order.status', '=', Order::EXP_GO_SUCCESS], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]])
                ->orwhere([['order.status', '=', Order::ORDER_FINISH], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]])
                ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                ->orderBy('id', 'desc');

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('sn', '维修订单号');
                $filter->like('barcode', 'S/N码');
            });
            $grid->id('ID')->sortable();
            $grid->uid('UID');
            $grid->sn('寄修订单编号');
            $grid->column('come_exp_sn', '寄来快递单号')->display(function ($value) {
                $html = '<a class="express_route_fresh" href="javascript:void(0);" value="'.$value.'">'.$value.'</a>';
                return $html;
            });
            $grid->column('go_exp_sn', '寄去快递单号')->display(function ($value) {
                $html = '<a class="express_route_fresh" href="javascript:void(0);" value="'.$value.'">'.$value.'</a>';
                return $html;
            });
            $grid->barcode('S/N码');
            $grid->name('联系人');
            $grid->phone('用户联系方式');
//            $grid->endpoint()->name('寄修售后点');
            $grid->amount('总金额(元)');
            $grid->pay_amount('支付金额(元)');
            $grid->need_invoice('是否开具发票')->display(function ($value){
                if ($value == 1){
                    return '是';
                }else{
                    return '否';
                }
            });
            $grid->created_at('订单提交时间');
//            $grid->updated_at_last('订单回寄时间')->sortable();
            $grid->column('expense.updated_at', '核销时间')->display(function($value) {
                if ($value) {
                    return $value;
                } else {
                    return '未核销';
                }
            });
            $grid->status('订单状态')->display(function ($status) {
                $s = Order::STATUS;
                if (array_key_exists($status, $s)) {
                    return $status . $s[$status];
                } else {
                    return "————";
                }
            });
            $grid->check_user()->name('检测人')->display(function ($name) {
                if ($name == '')
                    return '———';
                return $name;
            });
            $grid->repeat_order('二次维修')->display(function ($value){
                if ($value == 1){
                    return '是';
                }else{
                    return '否';
                }
            });
//            $grid->column('pay_amount', '订单总价')->totalRow();
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $c = 'post_repair/view/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">查看 </span></a>';
                $actions->append($html);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
//    protected function form($id = null)
//    {
//        if (!Admin::user()->inroles(['Administrator'])) {
//            return Admin::content(function(Content $content){
//                $error = new MessageBag([
//                    'title'   => '无法访问',
//                    'message' => '没有权限！',
//                ]);
//                return $content->body($error);
//            });
//        }
//        $script = <<<EOF
//            //改变配件物料选项
//            $(document).on('change', ".mat_id", function () {
//                var target = $(this).closest('.fields-group').find(".material_id");
//                var type = $('.type').val();
//                $.get("/admin/repair_check_material?q="+this.value+"&type="+ type, function (data) {
//                    target.find("option").remove();
//                    defaultdata = [{"id":0,"text":"请选择"}];
//                    setdata = $.map(data, function (d) {
//                        d.id = d.id;
//                        d.text = d.text;
//                        return d;
//                    });
//                    data = setdata;
//                    $(target).select2({
//                        data: data
//                    }).trigger('change');
//                });
//            });
//
//            //别问我为什么不直接操作模板div,是因为用了DocumentFragment,操作不了,不是我用的!
//            $(document).ready(function() {
//                var in_period = $('.in_period').val()
//                if (in_period != '1') {
//                    calculatePrice();
//                }
////              calculatePrice();
//              function total(){
//                var staff = Number($('#staff_cast').val().replace(/,/g, ""));
////                var staff = 0;
//                var exp_cast = 0;
////                var exp_cast = Number($('#exp_cast').val().replace(/,/g, ""));
//                var accessory = Number($('#accessory_cast').val().replace(/,/g, ""));
//                var accessory_amount = Number($('#accessory_amount').val().replace(/,/g, ""));
//                $('#amount').val(staff + accessory_amount + exp_cast);
//                $('#pay_amount').val(staff + accessory + exp_cast);
//              }
//
//              $(document).on('change','.staff_cast',function(){
//                total();
//              });
//
//
//              //统计配件价格
//              function calculatePrice(){
//                let totalPrice = 0;
//                let accessoryAmount = 0;
//                $('.material_id:visible').each(function(){
//                  let optionText = $(this).find("option:selected").text();
//                  let price = optionText.split('价格 : ')[1];
//                  let count = $(this).parent().parent().next().find('#count').val();
//                  let is_charge = $(this).parent().parent().next().next().find('.is_charge').val();
//                  accessoryAmount += price * count;
//                  if (is_charge == '1'){
//                    totalPrice += price * count;
//                  }
//                });
////                实际收取配件费用
//                $('#accessory_cast').val(totalPrice);
////                总配件费用
//                $('#accessory_amount').val(accessoryAmount);
//                total();
//              }
//
//              //下拉框变化
//              $(document).on('change','.material_id',function(){
//                calculatePrice();
//              });
//              $(document).on('change','.is_charge',function(){
//                calculatePrice();
//              });
//              //加减数量按钮点击
//              $('.has-many-repair_material-forms').on('click','button',function(){
//                calculatePrice();
//              });
//              //移除按钮点击
//              $(document).on('click','.remove',function(){
//                calculatePrice();
//              });
//            });
//EOF;
//
//        Admin::script($script);
//        return Admin::form(Order::class, function (Form $form) use ($id) {
//
//            $form->text('name', '寄修人');
//            $form->text('phone', '联系方式');
//            $form->text('province', '省');
//            $form->text('city', '市');
//            $form->text('district', '区');
//            $form->text('address', '详细地址');
//            $form->display('sn', '寄修订单号');
//            $form->text('barcode', 'S/N码');
////            $form->barcode('sn', '寄修条码条码')->options(['width'=>1,]);
//            $form->display('model_name', '机器型号');
//            $form->select('in_period', '保修状态')->options(Order::in_period);
//            $form->hidden('sn', '寄修订单号');
//            $form->divider('');
//            $form->select('reason', '损坏原因')->options(Order::reason)->value(function ($reason) {
//                return $reason;
//            });
//            $form->text('deal', '维修方式');
//            $form->divider('');
//            $form->display('type', '寄修方式')->with(function ($value) {
//                if ($value == 2){
//                    return '<span style="color:red">经销商寄修</span>';
//                }
//                return '<span style="color:red">用户寄修</span>';
//
//            });
//            $form->hidden('type');
////            $options = [];
////            if ($id) {
////                $modelId = Order::where('id', '=', $id)->value('model_id');
////                $accessoryRelations = MachineAccessoryRelation::with('accessory')
////                    ->where('model_id', '=',$modelId)->get();
////                $options[0] = '请选择';
////                foreach ($accessoryRelations as $key => $relation) {
////                    $options[$relation->id] = $relation->accessory->title . ' | 价格 : ' . $relation->price;
////                }
////            }
//            $type = Order::where('id', '=', $id)->value('type');
//            $form->hasMany('repair_material', '维修配件列表',
//                function (Form\NestedForm $form) use ($id, $type) {
//                    $modelId = Order::where('id', '=', $id)->value('model_id');
//                    $pr_sn = Order::where('id', '=', $id)->value('sn');
//                    $form->select('mat_id', '维修配件')->options(MachineAccessoryTree::model_options($modelId))->load('malfunction_id', admin_url('repair_check_malfunction'));
//                    $form->select('malfunction_id', '配件故障')->options(function ($value) {
//                        $data = MachineMalfunction::where('id', $value)->get()->toArray();
//                        $ret = array();
//                        if (count($data) > 0) {
//                            foreach ($data as $d) {
//                                $ret[$d['id']] = $d['title'];
//                            }
//                        }
//                        return $ret;
//                    });
//                    $form->select('material_id', '维修物料')->options(function ($value) use ($pr_sn, $form, $type){
//                        $data = Material::where('id', $value)->get()->toArray();
//                        $ret = array();
//                        if (count($data) > 0) {
//                            foreach ($data as $d) {
//                                $from = $d['from'] == 1 ? '新仓库' : '旧仓库';
//                                if ($type == 2){
//                                    $ret[$d['id']] = $d['name'].$d['specification'].'|编码:'.$d['code'].'|旧编码:'.$d['old_code'].
//                                        '|仓库:'.$from.'|库存:'.$d['quantity'].'|价格 : '.$d['price_first'];
//                                }else{
//                                    $ret[$d['id']] = $d['name'].$d['specification'].'|编码:'.$d['code'].'|旧编码:'.$d['old_code'].
//                                        '|仓库:'.$from.'|库存:'.$d['quantity'].'|价格 : '.$d['price'];
//                                }
//                            }
//                        }
//                        return $ret;
//                    });
//                    $form->number('count', '数量')->default(1);
//                    $form->select('is_charge', '是否收费')->options([0=>'不收费',1=>'收费'])->default(1);
//                });
//
//            $form->currency('accessory_amount', '总配件价格')->symbol('￥');
//            $form->currency('accessory_cast', '实际收取配件价格')->symbol('￥');
//            if ($id && $form->model()->find($id)->in_period !== 1) {
//                $rb_exp_sn = $form->model()->find($id)->rb_come_exp_sn;
//                $exp_sn = $form->model()->find($id)->come_exp_sn;
//                $exp_cast = Express::query_order($rb_exp_sn, $exp_sn);
////                $exp_cast = 15;
//            } else {
//                $exp_cast = 0;
//            }
//            $form->currency('staff_cast', '快递费用')->symbol('￥')->default(intval($exp_cast))->help('快递费用  '.$exp_cast);
//            $form->hidden('exp_coat', '快递费用')->value(0);
////            $form->display('staff_cast', '检测费用')->value($exp_cast)->help('快递费用  '.$exp_cast. '元');
//            $form->currency('amount', '总计')->symbol('￥');
//            $form->currency('pay_amount', '待支付金额')->symbol('￥')->help('修改前金额：');
//
//            $form->text('receive_case', '收到的配件');
//            $form->text('deal_remark', '备注');
//            $form->hidden('check_man');
//            $form->select('status', '订单状态')->options(Order::STATUS);
////            $form->hidden('pay_amount');
//            $form->saving(function (Form $form) use ($exp_cast) {
////                $form->check_man = Admin::user()->id;
//                //不记录日志
//                $form->no_log = 1;
////                dd($form->pay_amount);
//                //微信支付如果有发起过支付请求，必须保证金额等信息一致，所以有金额变动，需要去掉内部支付单号重新生成
//                if ($form->pay_amount != $form->model()->pay_amount && !empty($form->model()->rb_pay_sn) && empty($form->model()->is_paid)) {
//                    $data = ['rb_pay_sn' => '', 'pay_com' => 0, 'pay_sn' => ''];
//                    DB::table('order')->where('sn', $form->model()->sn)->update($data);
//                }
//
//                if ($form->status == 800){
////                    dd($form->status);
//                    $data = ['go_sure' => 1, 'updated_at_last' => date('Y-m-d H:i:s')];
//                    DB::table('order')->where('sn', $form->model()->sn)->update($data);
////                    $form->go_sure = 1;
////                    $form->updated_at_last = date('Y-m-d H:i:s');
//                }
//            });
//            //快递费不保存,保存成staff_cast
//            $form->ignore(['exp_cast']);
//
//            $form->saved(function($form) {
//                //同步处理已使用的物料
//                PostRepairUsedMaterial::where('pr_sn', $form->sn)->delete();
//                $pr_material = PostRepairMaterial::where('pr_sn', $form->sn)->get();
//                if (count($pr_material) > 0) {
//                    foreach ($pr_material as $material) {
////                        dd($material);
//                        $new_used_material = new PostRepairUsedMaterial();
//                        $new_used_material->pr_sn = $material->pr_sn;
//                        $new_used_material->mat_id = $material->mat_id;
//                        $new_used_material->material_id = $material->material_id;
//                        $new_used_material->is_charge = $material->is_charge;
//                        $new_used_material->count = $material->count;
//                        $new_used_material->save();
//                    }
//                }
////                dd(PostRepairUsedMaterial::where('pr_sn', $form->sn)->get()->toArray());
//                $order = $form->model();
////                dd($order->status == Order::CHECK_FINISH && $order->in_period == 1 && $order->reason == 2 && empty(intval($order->pay_amount)));
//                //处理已检测的0元订单，直接修改为已支付的状态
//                if ($order->status == Order::CHECK_FINISH && empty(floatval($order->pay_amount))) {
//                    $save = Order::where('sn', $order->sn)->first();
//                    $save->status = Order::PAY_FINISH;
//                    $save->save();
//                }
//            });
//        });
//    }
}