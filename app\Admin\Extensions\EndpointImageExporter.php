<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>an
 * Date: 2017/9/26
 * Time: 10:05
 */
namespace App\Admin\Extensions;

use Encore\Admin\Grid\Exporters\AbstractExporter;

class EndpointImageExporter extends AbstractExporter
{
    public function export()
    {
        $filename = '终端形象图片.txt';

        $data = $this->getData();
        // 根据上面的数据拼接出导出数据，
        $output = '';

        if (!empty($data)) {
            $output = '';
            $imageData = [];
            foreach ($data as $row) {
                if ($row['images']) {
                    foreach ($row['images'] as $key => $item) {
                        $imageData[] = 'http://dt.readboy.com/' . $item;
                    }
                    $output .= implode("\r\n", $imageData) . "\r\n";
                }
            }
        }

        // 在这里控制你想输出的格式,或者使用第三方库导出Excel文件
        $headers = [
            'Content-Encoding'    => 'UTF-8',
            'Content-Type'        => 'text/plain;charset=UTF-8',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        echo "\xEF\xBB\xBF";

        // 导出文件，
        response(rtrim($output, "\n"), 200, $headers)->send();
        exit;
    }
}