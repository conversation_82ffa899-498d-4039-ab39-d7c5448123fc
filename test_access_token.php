<?php

/**
 * 测试CloudCustomer类的accessToken功能
 * 使用方法：在项目根目录运行 php test_access_token.php
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Admin\Extensions\CloudCustomer;

echo "=== CloudCustomer AccessToken 测试 ===\n\n";

try {
    // 创建CloudCustomer实例
    $cloudCustomer = new CloudCustomer();
    
    echo "1. 首次获取accessToken（应该从API获取）:\n";
    $result1 = $cloudCustomer->getAccessToken();
    echo "结果: " . json_encode($result1, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";
    
    if ($result1['success']) {
        echo "2. 再次获取accessToken（应该从缓存获取）:\n";
        $result2 = $cloudCustomer->getAccessToken();
        echo "结果: " . json_encode($result2, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";
        
        echo "3. 清除缓存:\n";
        $cleared = $cloudCustomer->clearAccessTokenCache();
        echo "缓存清除结果: " . ($cleared ? '成功' : '失败') . "\n\n";
        
        echo "4. 清除缓存后再次获取（应该重新从API获取）:\n";
        $result3 = $cloudCustomer->getAccessToken();
        echo "结果: " . json_encode($result3, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";
        
        echo "5. 强制刷新accessToken:\n";
        $result4 = $cloudCustomer->refreshAccessToken();
        echo "结果: " . json_encode($result4, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";
    }
    
} catch (Exception $e) {
    echo "测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误堆栈: " . $e->getTraceAsString() . "\n";
}

echo "=== 测试完成 ===\n";
