<?php
/**
 * Created by PhpStorm.
 * User: 1
 * Date: 2017/5/15
 * Time: 14:02
 */

namespace App\Admin\Extensions\Tools;


use Encore\Admin\Grid\Tools\AbstractTool;
use Encore\Admin\Admin;
use Illuminate\Support\Facades\Request;

class EndpointApplicationStatus extends AbstractTool
{
    protected function script()
    {
        $url = Request::fullUrlWithQuery(['status' => '_status_']);

        return <<<EOT

$('input:radio.status-gender').change(function () {

    var url = "$url".replace('_status_', $(this).val());

    $.pjax({container:'#pjax-container', url: url });

});

EOT;
    }

    public function render()
    {
        Admin::script($this->script());

        $options = [
            10 => '全部',
            -3 => '支持申请审核不通过',
            -1 => '建店审核不通过',
            0 => '未审核',
            1 => '建店审核通过',
            2 => '已申请支持,等待审核',
            3 => '支持申请审核通过',
            4 => '已回传核销文件',
            5 => '核销通过',
            -5 => '核销未通过',
        ];

        return view('admin.tools.endpoint_application_status', compact('options'));
    }
}