<?php
/**
 * Created by PhpStorm.
 * User: Xian
 * Date: 2017/8/17
 * Time: 10:27
 */

namespace App\Api\Controllers;

use App\Api\Transformers\MarketingDownloadTransformers;
use App\Models\MarketingCategory;
use App\Models\MarketingDownload;
use Dingo\Api\Exception\ValidationHttpException;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Validator;
use App\Api\Serializer\CustomSerializer;

class MarketingDownloadController extends BaseController
{
    public function index()
    {
        $hasValidatedToken = $this->checkToken();
        $input = Request::all();
        $validator = Validator::make($input, [
            'top_category_id'    => 'numeric',
            'second_category_id' => 'numeric',
            'count'              => 'numeric',
        ]);
        if ($validator->fails()) {
            throw new ValidationHttpException($validator->errors());
        }
        $count = Input::get('count') ?: 10;
        $map = [];
        $topCategoryId = Input::get('top_category_id');
        //判断是不是parent_id
        if ($topCategoryId && empty(Input::get('second_category_id'))) {
            //获取parent_id下的子分类
            $categoryIds = MarketingCategory::where('parent_id', '=', $topCategoryId)
                ->pluck('id')
                ->toArray();
            array_unshift($categoryIds, $topCategoryId);
            $marketingDownloads = MarketingDownload::whereIn('category', $categoryIds)
                ->orderBy('top', 'desc')
                ->orderBy('id', 'desc')
                ->paginate($count);
        } else {
            Input::get('second_category_id') ? array_unshift($map, [
                'category',
                '=',
                Input::get('second_category_id'),
            ]) : null;
            $marketingDownloads = MarketingDownload::where($map)
                ->orderBy('top', 'desc')
                ->orderBy('id', 'desc')
                ->paginate($count);
        }

        return $this->response->paginator($marketingDownloads, new MarketingDownloadTransformers($hasValidatedToken),
            [],
            function ($resource, $fractal) {
                $fractal->setSerializer(new CustomSerializer());
            });
    }


    public function show($id)
    {
        $hasValidatedToken = $this->checkToken();
        $material = MarketingDownload::find($id);

        return $this->response->item($material, new MarketingDownloadTransformers($hasValidatedToken),
            function ($resource, $fractal) {
                $fractal->setSerializer(new CustomSerializer());
            });
    }

    public function search()
    {
        $hasValidatedToken = $this->checkToken();
        $input = Request::all();
        $validator = Validator::make($input, [
            'name'  => 'required',
            'count' => 'numeric',
        ]);
        if ($validator->fails()) {
            throw new ValidationHttpException($validator->errors());
        }

        $count = Input::get('count') ?: 10;
        $name = Input::get('name');
        $marketingDownloads = MarketingDownload::where('name', 'like', "%{$name}%")
            ->orderBy('id', 'desc')
            ->paginate($count);

        return $this->response->paginator($marketingDownloads, new MarketingDownloadTransformers($hasValidatedToken),
            [],
            function ($resource, $fractal) {
                $fractal->setSerializer(new CustomSerializer());
            });
    }
}