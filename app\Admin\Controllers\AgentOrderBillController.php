<?php


namespace App\Admin\Controllers;


use App\Http\Controllers\Controller;
use App\Models\AgentOrder;
use App\Models\Order;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;

class AgentOrderBillController extends Controller
{
    use ModelForm;

    /**
     *Index interface.
     *
     * @return content
     */
    public function index(){
        return Admin::content(function (Content $content){

            $content->header('经销商寄修账单');
            $content->description('经销商寄修大单费用合计');
            $content->body($this->grid());
        });
    }

    /**
     * @param $id
     * @return mixed
     */
    public function edit($id){
        return Admin::content(function (Content $content) use ($id){
            $content->header('经销商寄修');
            $content->description('经销商寄修编辑');
            $content->body($this->form($id)->edit($id));
        });
    }



    public function grid(){
        $script = <<<EOF
            $('.bill_status').click(function(){
                var id = $(this).attr('value');
                layer.confirm('确认入账？', {
                    btn: ['确认', '取消']
                    }, function(){
                        $.ajax({
                            type: "GET",
                            url: "/admin/agent_order_bill/set_bill_status",
                            data: {
                            id: id,
                            },
                            dataType: "json",
                            success: function (data) {
                                layer.msg('入账成功'+data, {time:500},function(){
                                     $('.grid-refresh').click();
                                });
                            }
                        });
                    layer.msg("入账成功");
                    window.location.reload();
                })
            });
EOF;
        Admin::script($script);
        return Admin::grid(AgentOrder::class, function (Grid $grid){

            $grid->disableCreation();
            $grid->disableExport();
            $grid->tools(function ($tools){
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
//                    $batch->add('取消订单', new Cancel());
                });
            });
            $grid->filter(function ($filter){
               $filter->like('name', '联系人');
               $filter->like('agency', '地区');
               $filter->between('created_at', '时间')->datetime();
            });
            $grid->model()->where([['status', '=', '1'], ['type', '=', 1]]);
            $grid->id('ID')->sortable();
            $grid->agency('地区');
            $grid->sn('寄修大单单号');
            $grid->name('联系人');
            $grid->phone('手机号');
//            $grid->province('省份');
//            $grid->city('城市');
//            $grid->district('地区');
//            $grid->address('详细地址');

            $grid->column('应收费用')->display(function (){
//                dd($this->sn);
                $data = AgentOrder::where([['o.status', '=', 800],['aoc.agent_order_sn', '=', $this->sn]])
                    ->orwhere([['o.status', '=', 900],['aoc.agent_order_sn', '=', $this->sn]])
                    ->leftJoin('agent_order_correlation as aoc', 'agent_order.sn', '=', 'aoc.agent_order_sn')
                    ->leftJoin('order as o', 'aoc.order_sn', '=', 'o.sn')
                    ->sum('o.pay_amount');
                return $data;
            });
            $grid->column('回寄机器总数')->display(function (){
//                dd($this->sn);
                $param = [['order.status', '=', 800],['aoc.agent_order_sn', '=', $this->sn]];
                $count = Order::where($param)
                    ->orwhere([['order.status', '=', 900],['aoc.agent_order_sn', '=', $this->sn]])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
                return $count;
            });
            $grid->column('bill_status', '账单状态')->display(function ($value){
                if ($value == 0){
                    return '<span style="color: red">未入账</span>';
                }
                return '已入账';
            });
            $grid->actions(function ($actions){
                $actions->disableDelete();
                $actions->disableEdit();
                $c = 'agent_order_bill_detail';
                $k = $actions->row->sn;
                $html = '<a href="'.$c.'?agent_order_sn='.$k.'"><span style="color:red">【订单详情】</span></a>';
                $actions->append($html);
                if ($actions->row->bill_status == 0){
                    $html = '<a href="javascript:void(0);" class="bill_status" value="'. $actions->row->id . ' target="_blank"><span style="color:dodgerblue">【入账】</span></a>';
                    $actions->append($html);
                }

            });

        });
    }

    public function form($id = null){
        return Admin::form(AgentOrder::class, function (Form $form) use ($id){
            $form->display('id');
            $form->display('sn', '寄修单号');
            $form->text('name','联系人');
            $form->text('phone', '手机号');
            $form->text('province', '省份');
            $form->text('city', '城市');
            $form->text('district', '地区');
            $form->text('address', '详细地址');
        });
    }


    public function set_bill_status(){
        $ids = request()->get('ids');
        $id = request()->get('id');
        if (!empty($id)){
            $agent_order = AgentOrder::find($id);
            $agent_order->bill_status = 1;
            $agent_order->bill_updated_at = date('Y:m:d H:i:s');
            $agent_order->save();
        }

    }
}