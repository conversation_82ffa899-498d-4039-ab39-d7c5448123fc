<?php

namespace App\Api\Controllers\Auth;

use App\User;
use Dingo\Api\Routing\Helpers;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Tymon\JWTAuth\Facades\JWTAuth;
use Illuminate\Support\Facades\Validator;
use Dingo\Api\Exception\ValidationHttpException;
use Carbon\Carbon;


class LoginController extends Controller {
    use AuthenticatesUsers;
    use Helpers;


    public function login(Request $request) {
        $input = $request->toArray();
        $validator = Validator::make($input, [
            'username' => 'required',
            'password' => 'required',
        ]);
        if ($validator->fails()) {
            throw new ValidationHttpException($validator->errors());
        }

        $user = User::where('username', $request->username)->first();

        if ($user && Hash::check($request->get('password'), $user->password)) {
            $token = JWTAuth::fromUser($user);
            return $this->sendLoginResponse($request, $token);
        }

        return $this->sendFailedLoginResponse($request);
    }

    public function sendLoginResponse(Request $request, $token) {
        $this->clearLoginAttempts($request);

        return $this->authenticated($token);
    }

    public function authenticated($token) {
        return $this->response->array([
            'token'        => $token,
            'status_code'  => 200,
            'message'      => 'User Authenticated',
            'expired_time' => date('Y-m-d H:i:s', (time() + config('jwt.ttl') * 60)),
            'current_time' => date('Y-m-d H:i:s', time()),
            'active_time'  => config('jwt.ttl') * 60,
        ]);
    }

    public function sendFailedLoginResponse() {
        throw new UnauthorizedHttpException("Bad Credentials");
    }

    public function logout() {
        $this->guard()->logout();
    }
}