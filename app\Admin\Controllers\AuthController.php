<?php

namespace App\Admin\Controllers;

use Encore\Admin\Auth\Database\Administrator;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Layout\Content;
use GuzzleHttp\Client;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Foundation\Auth\AuthenticatesUsers;

class AuthController extends Controller
{
    use AuthenticatesUsers;

    // 认证中心host
    private $authCenterHost = "https://auth-hub.readboy.com";

//    protected $redirectTo = 'admin';

    public function localLogin()
    {
        if (!Auth::guard('admin')->guest()) {
            return redirect(config('admin.prefix'));
        }

        return view('admin::login');
    }

    /**
     * Login page.
     *
     * @return \Illuminate\Contracts\View\Factory|Redirect|\Illuminate\View\View
     */
    public function getLogin(Request $request)
    {
//        dd(Auth::guard('admin'));
        if (Auth::guard('admin')->check()) {  // 已经登录则直接跳转到站内页面
            return redirect($this->redirectPath());
        }
        // 跳转认证中心
        $redirect_uri = route('token_login');
        $client_id = config('client.id');
        $state = '123'; // 随机字符串，client端用来校验重定向调用, 自己更加实际情况去实现相应逻辑
        return redirect("$this->authCenterHost/sso/gettoken?redirect_uri=$redirect_uri&client_id=$client_id&state=$state");
    }


    /**
     * 阻止默认的登录方式
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function postLogin(Request $request)
    {
        return response()->json(["ok" => false, "msg" => "deny"]);
    }

    /**
     * 子应用注销&认证中心回调注销
     *
     * @param Request $request
     * @param null $session_id
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|\Illuminate\Support\Facades\Redirect|string
     */
    public function getLogout(Request $request, $session_id = null)
    {
//        $this->guard()->logout();
//        $request->session()->invalidate();
//        return;
//        return redirect(config('admin.route.prefix'));
        $code = $request->input('code', false);
//        dd($code);
        if (!$code) {   // 子应用触发注销
            $code = $request->session()->get('code', false);
            Auth::guard('admin')->logout();
            Auth::guard('web')->logout();
            if (!$code) {
                $client_id = config('client.id');
                $redirect_uri = route('auth_center_login');
                // 请求认证中心注销
                return redirect("$this->authCenterHost/logout?code=$code&client_id=$client_id&redirect_uri=$redirect_uri");
            } else {
                $client_id = config('client.id');
                $redirect_uri = route('auth_center_login');
                // 请求认证中心注销
                return redirect("$this->authCenterHost/logout?code=$code&client_id=$client_id&redirect_uri=$redirect_uri");
            }
        } else {    // 认证中心回调注销
//            \Log::info("call logout, destroy session:$session_id");
            if ($session_id) {
                $res = Session::getHandler()->destroy($session_id); // 通过删除session的方式实现注销
//                \Log::info($res);
                Auth::guard('admin')->logout();
                Auth::guard('web')->logout();
            } else {
                return redirect()->intended(config('admin.prefix'));
            }
        }
    }

    /**
     * getLogin的回调入口, 校验code, 有效再登录
     *
     * @param Request $request
     * @return \Illuminate\Http\Response|string
     * @throws GuzzleException
     */
    public function tokenLogin(Request $request)
    {
        $error = $request->input('error', false);
        if ($error) {
            return $error;
        }
        $code = $request->input('code', false);
        if (!$code) {
            return "登录失败1";
        }
        $session_id = Session::getId(); // 获取session id
        $userInfo = $this->code2Info($code, $session_id);    // 检查code, 获取用户信息
        if (!$userInfo) {
            return redirect(Admin::url('logout'));
        }
        $adminUser = Administrator::where('auth_center_id', $userInfo['id'])->first();  // 用户是否已经存在于该子应用
        if ($adminUser) {
            $credentials = [
                'auth_center_id' => $userInfo['id'],
                'password' => $userInfo['id']   // 我的demo默认用用户id作为登录认证密码
            ];
//            \Log::info('request session:'.$request->session()->getId());
//            dd($request);
//            \Log::info('before session:'.Session::getId());
            if (Auth::guard('admin')->attempt($credentials)) {  // 登录
//                dd(Auth::guard()->check());
//                dd($this->guard()->getUser());
//                dd($this->guard('admin')->getProvider());
                $resp = $this->sendLoginResponse($request);
//                Auth::login(Auth::guard('admin')->getUser());
                $request->session()->put('code', $code);   // session中存入code
                $request->session()->setId($session_id);    // 把登录后的session id设置为传给认证中心的session id
                return $resp;
            } else {
                $provider = $this->guard()->getProvider();
                $user = $provider->retrieveByCredentials($credentials);
                $ret = $provider->validateCredentials($user, $credentials);
//                dd($user);
            }
            return "登录失败3";
        } else {
            $newUser = $this->newAdminUser($userInfo);  // 用户不存在则新建用户
            if (!$newUser) {
                return "创建子应用账号失败";
            }
            $credentials = [
                'auth_center_id' => $userInfo['id'],
                'password' => $userInfo['id']
            ];
            if (Auth::guard('admin')->attempt($credentials, null)) {
                $resp = $this->sendLoginResponse($request);
                $request->session()->put('code', $code);   // session中存入code
                $request->session()->setId($session_id);
                return $resp;
            }
            return "登录失败4";
        }
    }

    /**
     * 检查回调返回的code是否有效，有效的话返回用户的基本信息
     *
     * @param $code
     * @param $session_id
     * @return bool
     * @throws GuzzleException
     */
    private function code2Info($code, $session_id)
    {
//        \Log::info($session_id);
//        \Log::info("check code:".$code);
        $client = new Client();
        $resp = $client->request("POST", "$this->authCenterHost/sso/verifytoken", [
            "form_params" => [
                'logout_uri' => route('getlogout', ['session_id' => $session_id]),  // 认证中心回调注销uri
                'client_id' => config('client.id'),
                'client_secret' => config('client.secret'),
                'code' => $code
            ]
        ]);
        if ($resp->getStatusCode() !== 200) {
            return false;
        }
        $resp = json_decode($resp->getBody()->getContents(), true);
        if (!isset($resp['ok']) || $resp['ok'] != 1) {
            return false;
        }
        $info = $resp['data'];
        return $info;
    }

    /**
     * 新建用户子应用账号
     *
     * @param $info
     * @return bool
     */
    private function newAdminUser($info)
    {
        $ok = false;
        // 使用事务
        DB::beginTransaction();
        try {
            $id = DB::table('admin_users')->insertGetId([
                'username' => $info['username'],
                'auth_center_id' => $info['id'],
                'name' => $info['name'],
//                'avatar' => $info['avatar'],
                'password' => bcrypt('' . $info['id']),    // 我的demo默认用用户id作为登录认证密码, 加密后存储
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
            DB::table('admin_role_users')->insert([     // 指定一个角色, 我的demo中指定为角色id为1的角色，即管理员
                'role_id' => 33,
                'user_id' => $id
            ]);
            //写入维修工
            DB::table('pr_staff')->insert([
                'user_id' => $id,
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            $ok = true;
            DB::commit();
        } catch (\Exception $e) {
            $ok = false;
            DB::rollBack();
        } finally {
            return $ok;
        }
    }

//    /**
//     * @param Request $request
//     *
//     * @return mixed
//     */
//    public function postLogin(Request $request)
//    {
//        $credentials = $request->only([
//            'username',
//            'password',
//        ]);
//        $geetestRequest = $request->only(['geetest_challenge']);
//
//        $result = Validator::make($geetestRequest,[
//            'geetest_challenge' => 'required',
//        ], [
//            'geetest' => config('geetest.server_fail_alert'),
//        ]);
//        if ($result->fails()) {
//            return Redirect::back()->withInput()->withErrors($result);
//        }
//
//        $validator = Validator::make($credentials, [
//            'username' => 'required',
//            'password' => 'required',
//        ]);
//
//        if ($validator->fails()) {
//            return Redirect::back()->withInput()->withErrors($validator);
//        }
////        dd($request->has('remember'));
////        if (Auth::guard('admin')->attempt($credentials, $request->has('remember'))) {
//        if (Auth::guard('admin')->attempt($credentials)) {
//            admin_toastr(trans('admin::lang.login_successful'));
////            $this->sendLoginResponse($request);
//            return redirect()->intended(config('admin.prefix'));
//        }
//
//        return Redirect::back()->withInput()->withErrors(['username' => $this->getFailedLoginMessage()]);
//    }


    const ONE_WEEK_TIME_MINUTES = 7 * 24 * 60;

    protected function sendLoginResponse(Request $request)
    {
        // 设置记住我的时间为60分钟
        $rememberTokenExpireMinutes = self::ONE_WEEK_TIME_MINUTES;

        // 首先获取 记住我 这个 Cookie 的名字, 这个名字一般是随机生成的,
        // 类似 remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d
        $rememberTokenName = Auth::getRecallerName();
        // 再次设置一次这个 Cookie 的过期时间
        Cookie::queue($rememberTokenName, Cookie::get($rememberTokenName), $rememberTokenExpireMinutes);

        // 下面的代码是从 AuthenticatesUsers 中的 sendLoginResponse() 直接复制而来
        $request->session()->regenerate();

        $this->clearLoginAttempts($request);

        return redirect()->intended(config('admin.prefix'));
    }

//    /**
//     * User logout.
//     *
//     * @return Redirect
//     */
//    public function getLogout()
//    {
//        Auth::guard('admin')->logout();
//
//        session()->forget('url.intented');
//
//        return redirect(config('admin.prefix'));
//    }

    /**
     * User setting page.
     *
     * @return mixed
     */
    public function getSetting()
    {
        return Admin::content(function (Content $content) {
            $content->header(trans('admin::lang.user_setting'));
            $content->body($this->settingForm()->edit(Admin::user()->id));
        });
    }

    /**
     * Update user setting.
     *
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function putSetting()
    {
        return $this->settingForm()->update(Admin::user()->id);
    }

    /**
     * Model-form for user setting.
     *
     * @return Form
     */
    protected function settingForm()
    {
        return Administrator::form(function (Form $form) {
            $form->display('username', trans('admin::lang.username'));
            $form->text('name', trans('admin::lang.name'))->rules('required');

            $endpointInfo = [];

            if (Admin::user()->isRole('endpoint')) {
                //获得用户和endpoint之间的关联信息
                $endpointInfo = DB::table('admin_users')
                    ->leftjoin('user_endpoint', 'admin_users.id', '=', 'user_endpoint.uid')
                    ->leftjoin('endpoint', 'user_endpoint.endpoint', '=', 'endpoint.id')
                    ->where('uid', '=', Admin::user()->id)
                    ->first();
                $form->text('manager', '负责人')->default($endpointInfo->phone);
                $form->text('phone', '联系电话')->default($endpointInfo->manager);
            }

            $form->image('avatar', trans('admin::lang.avatar'))->move('avatar');
            if (Admin::user()->inRoles(['manager', 'administrator'])) {
                $form->password('password', trans('admin::lang.password'))->rules('confirmed|required');
                $form->password('password_confirmation', trans('admin::lang.password_confirmation'))->rules('required')
                    ->default(function ($form) {
                        return $form->model()->password;
                    });
            }

            $form->setAction(admin_url('auth/setting'));

            $form->ignore(['password_confirmation', 'phone', 'manager']);

            $form->saving(function (Form $form) {
                if ($form->password && $form->model()->password != $form->password) {
                    $form->password = bcrypt($form->password);
                }
            });

            $form->saved(function (Form $form) use ($endpointInfo) {
                if (!empty($endpointInfo)) {
                    $data = ['phone' => Input::get('phone'), 'manager' => Input::get('manager')];
                    DB::table('endpoint')->where('id', $endpointInfo->id)->update($data);
                }


                admin_toastr(trans('admin::lang.update_succeeded'));

                return redirect(admin_url('auth/setting'));
            });
        });
    }

    /**
     * @return string|\Symfony\Component\Translation\TranslatorInterface
     */
    protected function getFailedLoginMessage()
    {
        return Lang::has('auth.failed')
            ? trans('auth.failed')
            : 'These credentials do not match our records.';
    }
}
