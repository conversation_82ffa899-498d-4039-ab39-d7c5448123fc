<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\Express\EmsExpress;
use App\Admin\Extensions\ExpressExporter;

use App\Models\Machine;
use App\Models\MachineCategory;
use App\Models\PostExpress;
use App\Models\PostRepairExpress;
use Barryvdh\Debugbar\Facade as Debugbar;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Widgets\Table;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

class ExpressController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('快递信息');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('');
            $content->description('');

            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('');
            $content->description('');

            $content->body($this->form());
        });
    }

    public function express_route(Request $request)
    {
        $readboy_sn = Request::get('readboy_sn');
        $routes = DB::table('express_route')->where('readboy_sn', $readboy_sn)->orwhere('exp_sn', $readboy_sn)->orderby('accept_time', 'asc')->get()->toArray();
//        dd($routes);
        return view('admin/express/routes', compact('routes'));
    }

    public function express_fee(Request $request)
    {
        $id = Request::get('id');
        $express_order = PostRepairExpress::where('id', $id)->first();
//        dd($express_order);
        $cast = Express::query_order($express_order->readboy_sn, $express_order->exp_sn);
        if (intval($cast) > 0) {
            DB::table('pr_express')->where('id', $id)->update(['fee' => $cast]);
        }
        return $cast;
    }

    public function express_route_fresh(Request $request)
    {
        $go_exp_sn = Request::get('readboy_sn');
        $post_express = PostExpress::where('exp_sn', $go_exp_sn)->Orwhere('readboy_sn', $go_exp_sn)->first();
        $result = null;

        if ($post_express['com_type'] == 1) {
            $express = new Express();
            $result = $express->route_order($post_express['readboy_sn']);
            if ($result['Head'] == 'OK') {
                if (!empty($result['Body']['RouteResponse']['Route'])) {
                    foreach ($result['Body']['RouteResponse']['Route'] as $route) {
//                    dd(in_array("@attributes", $route));
                        if (array_key_exists("@attributes", $route)) {
                            $save['accept_address'] = $route['@attributes']['accept_address'];
                            $save['remark'] = $route['@attributes']['remark'];
                            $save['opcode'] = $route['@attributes']['opcode'];
                            $save['accept_time'] = $route['@attributes']['accept_time'];
                            $save['com'] = '顺丰';
                            $save['readboy_sn'] = $result['Body']['RouteResponse']['@attributes']['orderid'];
                            $save['exp_sn'] = $result['Body']['RouteResponse']['@attributes']['mailno'];
                            $save['created_at'] = date('Y-m-d H:i:s');
                        } else {
                            $save['accept_address'] = $route['accept_address'];
                            $save['remark'] = $route['remark'];
                            $save['opcode'] = $route['opcode'];
                            $save['accept_time'] = $route['accept_time'];
                            $save['com'] = '顺丰';
                            $save['readboy_sn'] = $result['Body']['RouteResponse']['@attributes']['orderid'];
                            $save['exp_sn'] = $result['Body']['RouteResponse']['@attributes']['mailno'];
                            $save['created_at'] = date('Y-m-d H:i:s');
                        }
                        if (in_array(intval($save['opcode']), [50, 54, 43, 46])) {
                            $save['status'] = 1;
                        } else if (in_array(intval($save['opcode']), [30, 31, 130, 123, 607, 36, 77])) {
                            $save['status'] = 2;
                        } else if (in_array(intval($save['opcode']), [44, 204, 125, 47, 126, 658, 657, 630, 664, 34])) {
                            $save['status'] = 3;
                        } else if (in_array(intval($save['opcode']), [80, 8000])) {
                            $save['status'] = 4;
                        } else {
                            $save['status'] = 0;
                        }
                        DB::table('express_route')->updateOrInsert(array('readboy_sn' => $save['readboy_sn'], 'opcode' => $save['opcode']), $save);
                    }
                }
            }
        } elseif ($post_express['com_type'] == 2) {
            $express = new Express();

            $result = $express->yt_route_order($go_exp_sn);

            if (is_array($result) && array_key_exists(0, $result)) {
                foreach ($result as $item) {
//                    dd($item['infoContent']);
                    $save['remark'] = $item['processInfo'];
                    $save['opcode'] = $item['infoContent'];
                    $save['accept_time'] = $item['upload_Time'];
                    $save['com'] = '圆通';
                    $save['readboy_sn'] = $post_express['readboy_sn'];
                    $save['exp_sn'] = $item['waybill_No'];
                    $save['created_at'] = date('Y-m-d H:i:s');
                    if (in_array($item['infoContent'], ['GOT'])) {
                        $save['status'] = 1;
                    } else if (in_array($item['infoContent'], ['ARRIVAL', 'DEPARTURE', 'PACKAGE'])) {
                        $save['status'] = 2;
                    } else if (in_array($item['infoContent'], ['SENT_SCAN', 'INBOUND'])) {
                        $save['status'] = 3;
                    } else if (in_array($item['infoContent'], ['SIGNED'])) {
                        $save['status'] = 4;
                    } else if (in_array($item['infoContent'], ['FORWARDING'])) {
                        $save['status'] = 6;
                    } else if (in_array($item['infoContent'], ['TMS_RETURN'])) {
                        $save['status'] = 7;
                    } else if (in_array($item['infoContent'], ['FAILED'])) {
                        $save['status'] = 8;
                    } else {
                        $save['status'] = 0;
                    }
                    DB::table('express_route')->updateOrInsert(array('readboy_sn' => $save['readboy_sn'], 'opcode' => $item['infoContent']), $save);

                }
            }
        } elseif ($post_express['com_type'] == 3) {
            $express = new EmsExpress();
            $result = $express->get_route($post_express['exp_sn']);
            if ($result && $result['responseState']) {
                foreach ($result['responseItems'] as $item) {
//                    dd($item['infoContent']);
                    $save['accept_address'] = $item['opOrgCity'];
                    $save['remark'] = $item['opDesc'];
                    $save['opcode'] = $item['opCode'];
                    $save['accept_time'] = $item['opTime'];
                    $save['com'] = '邮政';
                    $save['readboy_sn'] = $post_express['readboy_sn'];
                    $save['exp_sn'] = $post_express['exp_sn'];
                    $save['created_at'] = date('Y-m-d H:i:s');
                    if (in_array($item['opCode'], ["203"])) {
                        $save['status'] = 1;
                    } else if (in_array($item['opCode'], ['305', '306', '352', '389', '399', 'PACKAGE'])) {
                        $save['status'] = 2;
                    } else if (in_array($item['opCode'], ['702', 'INBOUND'])) {
                        $save['status'] = 3;
                    } else if (in_array($item['opCode'], ['704', '747', '748', 'O_016', 'O_017'])) {
                        $save['status'] = 4;
                    } else if (in_array($item['opCode'], ['FORWARDING'])) {
                        $save['status'] = 6;
                    } else if (in_array($item['opCode'], ['708', 'O_011', 'O_012'])) {
                        $save['status'] = 7;
                    } else if (in_array($item['opCode'], ['705', 'O_013'])) {
                        $save['status'] = 8;
                    } else {
                        $save['status'] = 0;
                    }
                    DB::table('express_route')->updateOrInsert(array('readboy_sn' => $save['readboy_sn'], 'opcode' => $item['opCode']), $save);

                }
            }
        }

        $routes = DB::table('express_route')->where('readboy_sn', $post_express['readboy_sn'])->orwhere('exp_sn', $go_exp_sn)->orderby('accept_time', 'asc')->get()->toArray();
        DebugBar::info(compact('go_exp_sn', 'post_express', 'result', 'routes'));
        return view('admin/express/routes', compact('routes'));
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOF
            //查看快递路由信息
            $('.express_route').click(function(){
                var id = $(this).attr('value');
                layer.open({
                      type: 2,
                      title: '快递路由',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['1000px', '60%'],
                      content: 'express/express_route?readboy_sn='+id //iframe的url
                });
            });

            //查看快递费用
            $('.express_fee').click(function(){
                var id = $(this).attr('value');
                layer.open({
                      type: 2,
                      title: '快递费用',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['1000px', '60%'],
                      content: 'express/express_fee?id='+id //iframe的url
                });

            });

            //调用顺丰接口刷新路由
            $('.express_route_fresh').click(function(){
                var id = $(this).attr('value');
                layer.open({
                      type: 2,
                      title: '快递路由',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['1000px', '60%'],
                      content: 'express/express_route_fresh?readboy_sn='+id //iframe的url
                });
            });
EOF;
        Admin::script($script);
        return Admin::grid(PostRepairExpress::class, function (Grid $grid) {

//            $grid->exporter(new MachineAccessoryExporter());
            $grid->disableCreation();
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->where(function ($query) {
                    $key = '%' . $this->input . '%';
                    $query->where('pr_sn', 'like', $key)->orwhere('readboy_sn', 'like', $key)->orwhere('exp_sn', 'like', $key);
                }, '单号搜索');
                $filter->is('type', '寄来还是寄走')->select([1 => '寄来', 2 => '寄走']);
                $filter->like('order.name', '联系人');
                $filter->between('created_at', '订单提交日期')->datetime();
            });

            $grid->model()->orderBy('id', 'desc');

            //表格显示列
            $grid->id('ID')->sortable();
            $grid->pr_sn('寄修单号')->sortable();
            $grid->readboy_sn('内部单号')->sortable();
            $grid->com('快递公司')->sortable();
            $grid->exp_sn('快递公司单号')->sortable();
            $grid->column('status', '下单状态')->sortable()->display(function ($value) {
                return PostRepairExpress::STATUS[$value];
            });
            $grid->column('type', '寄来还是寄走')->sortable()->display(function ($value) {
                return PostRepairExpress::TYPE[$value];
            });
            $grid->order()->name('联系人');
            $grid->column('寄件信息')->expand(function () {
                $json = json_decode($this->data);
                $data[] = array('寄件人', $json->j_contact);
                $data[] = array('寄件电话', $json->j_tel);
                $data[] = array('寄件地址', $json->j_province . $json->j_city . $json->j_county . $json->j_address);
                $data[] = array('收件人', $json->d_contact);
                $data[] = array('收件电话', $json->d_tel);
                $data[] = array('收件地址', $json->d_province . $json->d_city . $json->d_county . $json->d_address);
                $info = $data;
                return new Table(['联系信息', ''], $info);
            });
            $grid->fee('快递费用');
            $grid->updated_at('下单时间');

            $grid->actions(function ($actions) {
                $actions->disableDelete();
            });
            $grid->exporter(new ExpressExporter());
            $grid->tools(function ($tools) {
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                });
            });
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                //这个是主键id,不是模型id
                $id = $actions->row->id;
                $html = <<<EOF
                    <a class='express_fee' href='javascript:void(0);' value='$id' title='查询快递费用'>
                      <i class='fa fa-briefcase'> 查询快递费用</i>
                    </a>
EOF;
                $actions->append($html);
                $key = $actions->row->readboy_sn;
                $html = <<<EOF
                    <a class='express_route_fresh' href='javascript:void(0);' value='$key' title='快递路由信息'>
                      <i class='fa fa-briefcase'> 刷新快递信息</i>
                    </a>
EOF;
//                $actions->append($html);
//                $c = 'machine_accessory_tree';
//                $k = $actions->row->model_id;
//                $html = '|<a href="'.$c.'?model_id='.$k.'"><span style="color:red">【配件树】</span></a>';
                $actions->append($html);
            });

        });
    }

    /**
     * Make a form builder.
     * @param id
     * @return Form
     */
    protected function form($id = null)
    {
        return Admin::form(Machine::class, function (Form $form) use ($id) {
            $form->display('name', '机器型号');
            $categories = MachineCategory::where('visible', 1)->get()->pluck('name', 'id')->prepend('请选择', 0);
            $form->select('category_id', '型号品类')->options($categories);
//            $form->currency('company_price', '公司价格')->symbol('￥');
//            $form->currency('top_agency_price', '总代价格')->symbol('￥');
//            $form->currency('second_agency_price', '二代价格')->symbol('￥');
//            $form->currency('customer_price', '顾客价格')->symbol('￥');
            $states = [
                'on' => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ];
            $form->switch('visibility', '是否可见')->states($states);
            $form->display('updated_at', '更新时间');
        });
    }


}
