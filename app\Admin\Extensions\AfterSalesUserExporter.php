<?php

namespace App\Admin\Extensions;

use App\Models\UserAfterSales;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;

class AfterSalesUserExporter extends AbstractExporter
{
    use ExcelExportTrait;

    public function export()
    {
        $filename = '售后维修技师列表';
        // 根据上面的数据拼接出导出数据
        $titles = [
            'UID',
            '维修工程师姓名',
            '(代理商)维修工程师类别',
            '(公司)维修工程师类别',
            '联系方式',
            '身份证号码',
            '是否经过培训',
            '学历',
            '入职时间',
            '用户名',
            '所属售后终端',
            '所属售后终端联系方式',
            '备注',
        ];
        $data = UserAfterSales::all();
        $formatData = [];
        if (!empty($data)) {
            foreach ($data as $key => $row) {
                if ($row->status == 0) {
                    continue;
                }
                $formatData[] = [
                    $row->id,
                    $row->admin_user->name,
                    $row->agency_type,
                    $row->company_type,
                    $row->phone,
                    " ".$row->identity_card,
                    $row->through_training ? "是" : "否",
                    $row->education,
                    $row->entry_time,
                    $row->admin_user->username,
                    $row->after_sales_endpoint->name,
                    $row->after_sales_endpoint->phone,
                    $row->remark,
                ];
            }
        }
        array_unshift($formatData, $titles);
        ExcelExportTrait::exportToExcel($filename, $formatData);
    }
}