<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OptionalAccessoryCategory extends Model
{

    protected $table = 'optional_accessory_category';

    public function optionalAccessory()
    {
        return $this->hasMany(OptionalAccessory::class, 'category_id', 'id');
    }

    public static function getEnableSelectArray()
    {
        return self::where('enable', 1)->orderBy('sort', 'desc')->pluck('name', 'id');
    }

    public static function getAllSelectArray()
    {
        return self::orderBy('sort', 'desc')->pluck('name', 'id');
    }
}
