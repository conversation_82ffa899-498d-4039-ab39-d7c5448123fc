<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\BrokenScreenInsuranceExporter;
use App\Admin\Extensions\MessagePush;
use App\Admin\Extensions\Sms;
use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\BrokenScreenInsurance;
use App\Models\BrokenScreenInsuranceStandard;
use App\Models\Machine;
use App\Models\MachineAccessoryTree;
use App\Models\MachineCategory;
use App\Models\Material;
use App\Models\Warranty;
use Barryvdh\Debugbar\Facade as DebugBar;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;
use Illuminate\Support\Facades\Input;
use Symfony\Component\Yaml\Tests\B;
use function foo\func;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\MessageBag;
use GuzzleHttp\Client;

/**
 * 碎屏保管理
 */
class BrokenScreenInsuranceController extends Controller
{
    use ModelForm;

    /**
     * 首页
     **/
    public function index()
    {
        return Admin::content(function (Content $content) {
            $content->header('碎屏保');
            $content->description('碎屏保列表');
            $content->body($this->grid());
        });
    }

//    public function create(){
//        $script = <<<EOF
// //改变配件物料选项
//            $(document).on('change', ".barcode", function () {
//                var barcode = $('.barcode').val();
//            }
//EOF;
//        $form = Admin::form(BrokenScreenInsuranceStandard::class, function (Form $form) {
//            $category_id = Input::get('category_id');
//            $model_id = Input::get('model_id');
//            if ($category_id) {
//                $machine_category = MachineCategory::where([['visible', 1], ['id', $category_id]])->pluck('name', 'id');
//
//            } else {
//                $machine_category = MachineCategory::where('visible', 1)->pluck('name', 'id');
//
//            }
//            $form->select('category_id', '机型品类')
//                ->options($machine_category)
//                ->load('model_id', '/admin/machine_type/category')
//                ->rules('required');
//            $form->select('model_id', '机型')->options(function () use ($model_id) {
//                $model = Machine::where([['visibility', 1], ['model_id', $model_id]])->pluck('name', 'model_id as id');
//                return $model;
//            })->rules('required');
//            $form->hidden('name');
//            $form->select('standard_id', '投保类型')
//                ->options(BrokenScreenInsuranceStandard::STANDARD_TYPE)
//                ->default(1)
//                ->rules('required');
//            $form->currency('amount', '用户价格')->symbol('￥')->rules('required');
//            $form->currency('pay_amount', '支付金额')->symbol('￥')->rules('required');
//            $form->text('month', '保单保修月份')->rules('required');
//            $form->switch('visible', '是否可见')->default(1);
//            $form->saving(function (Form $form) {
//                $form->name = BrokenScreenInsuranceStandard::STANDARD_TYPE[$form->standard_id];
//            });
//        });
////        $form = Admin::form(BrokenScreenInsurance::class, function (Form $form){
////            $form->text('barcode', '条码')->rules('required')->help('必填');
////            $form->display('uid', 'uid');
////            $form->display('endpoint', '终端id');
////            $form->display('top_agency', '一级代理');
////            $form->display('second_agency', '二级代理');
////            $form->display('sn', '流水号');
////            $form->text('name', '名称')->rules('required')->help('必填');
////            $form->text('phone','手机号')->rules('required')->help('必填');
////            $form->text('identity_card','手机号')->rules('required')->help('必填');
//////            $form->select('identity_card','手机号');
////        });
//        return Admin::content(function (Content $content) use ($form){
//            $content->header('碎屏保服务补录');
//            $content->description('碎屏保服务补录页面');
//            return $content->body($form);
//        });
//    }

    /**
     * 编辑审核
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {
            $content->header('碎屏保');
            $content->description('碎屏保审核');
            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function supply()
    {
        if (request()->isMethod("post")) {
            $data = request()->all();
            $param = ['barcode', 'name'];
            $message = ['barcode' => '条码', 'name' => '姓名'];
            foreach ($param as $p) {
                if (empty($data[$p])) {
                    $error = new MessageBag([
                        'title' => '错误提示',
                        'message' => $message[$p] . '不能为空',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
            }
            return $data;
        }
        $script = <<<EOF
        $(document).on('change', '#barcode', function(){
            var value = $(this).val();
            $.ajax({
                url: "/admin/broken_screen_insurance/check?barcode=" + value,
                type: "GET",
                dataType: "json",
                success: function(data){
                
                    if (data['ok'] == 0){
                        alert(data['message']);
                    }
                }
            });
        });
EOF;
        Admin::script($script);
        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/broken_screen_insurance/supply');
        $form->text('barcode', '条码');
        $form->text('name', '姓名')->rules('required')->help('必填');
        $form->text('phone', '手机')->rules('required')->help('必填');
        $form->text('identity_card', '身份证号');
        $form->text('endpoint', '手机');
        $form->hidden('endpoint', '终端id');

        return Admin::content(function (Content $content) use ($form) {
            $content->header('补录');
            $content->description('补录');
            $content->body($form);
        });
    }

    public function check()
    {
        $barcode = request()->get('barcode');
        if (empty($barcode)) {
            $ret = array(
                'ok' => 0,
                'message' => '条码不能为空'
            );
            return $ret;
        }
        $insurance = BrokenScreenInsurance::where('barcode', $barcode)
            ->whereNotIn('status', [-400, -500])
            ->select('status')->value('status');
        $status = [100, 200, 300, 400, 500];
        if (in_array($insurance, $status)) {
            $ret = array(
                'ok' => 0,
                'message' => '此条码已申请过碎屏保，无法再次申请'
            );
        }
        $warranty = DB::connection('mysql2')
            ->table('warranty')
            ->where([['barcode', '=', $barcode], ['status', '=', '1']])
            ->select('customer_name', 'customer_phone', 'buy_date', 'model_id', 'model', 'endpoint')
            ->first();
        if (empty($warranty)) {
            $a = array(
                'ok' => 0,
                'message' => '此条码无保卡'
            );
        }
        $standard = BrokenScreenInsuranceStandard::where([['model_id', $warranty->model_id], ['visible', 1]])
            ->select('id', 'name', 'amount', 'month')
            ->get()->toArray();
        $endpoint = DB::connection('mysql2')->table('endpoint as e')
            ->where('e.id', $warranty->endpoint)
            ->leftJoin('agency as a', 'e.top_agency', '=', 'a.id')
            ->select('e.name', 'e.top_agency', 'e.second_agency', 'e.is_direct_sales', 'a.channel as endpoint_channel')
            ->first();
        $ret['ok'] = 1;
        $ret['data']['warranty'] = $warranty;
        $ret['data']['standard'] = $standard;
        $ret['data']['endpoint'] = $endpoint;
        return $ret;
    }

    public function change_status(): array
    {
        $sn = Request::get('sn');
        if (!$sn || !is_string($sn) || strlen($sn) != 23) {
            return ['ok' => 0, 'msg' => '参数错误: sn'];
        }
        $to_status_str = Request::get('status');
        if (!$to_status_str) {
            return ['ok' => 0, 'msg' => '参数错误: status'];
        }
        $to_status = intval($to_status_str);
        if (!$to_status || !array_key_exists($to_status, BrokenScreenInsurance::STATUS)) {
            return ['ok' => 0, 'msg' => '参数错误: status'];
        }
        $to_status_map = BrokenScreenInsurance::STATUS[$to_status];
        DebugBar::info(compact('sn', 'to_status_str', 'to_status', 'to_status_map'));

        $bsi = BrokenScreenInsurance::where('sn', $sn)->first();
        $bsi->status = $to_status;
        $bsi->save();
        return ['ok' => 1, 'msg' => '订单【' . $sn . '】状态已设为【' . $to_status_map . '】'];
    }

    public function grid()
    {
        $action_style = <<<css
    .action_link {
        margin-right: 1mm;
    }
    .action_link:hover {
        text-decoration-line: underline;
    }
css;
        $action_style_str = base64_encode($action_style);

        $script_style = <<<js

    // 设置操作链接-设置样式
    const action_style = decodeURIComponent(escape(atob('$action_style_str')));
    $('head').append('<style type="text/css">' + action_style + '</style>');
js;
        Admin::script($script_style);

        $script = <<<js
        // 退款
        $('.refund').click(function(){
            var id = $(this).attr('value');
            layer.confirm('确定要退款吗？', {
                btn: ['确认', '取消']
            }, function(){
                $.ajax({
                    url: '/admin/broken_screen_insurance/refund',
                    data: {
                        id: id,
                    },
                    dataType: 'json',
                    type: 'get',
                    success: function(res){
                        console.log(res)
                        layer.msg(res.msg);  
                        if(res.ok == 1){
                            toastr.success('退款成功');
                            setTimeout(function(){
                                window.location.reload();
                            }, 1000)
                        } else {
                            toastr.error('退款失败');
                        }
                    },
                    error:function(res){
                        console.log(res)
                    }
                })
            }, function(){
                layer.msg('取消退款');
            });
        });

        // 变更订单状态
        function get_yes_fn(sn, key, status) {
            return function(index) {
                let url = '/admin/broken_screen_insurance/change_status?sn=' + encodeURIComponent(sn) +
                    '&id=' + encodeURIComponent(key) + '&status=' + encodeURIComponent(status);
                $.get(url, function(result) {
                    console.log(result);
                    let msg_display = result?.msg ?? '空';
                    layer.msg('msg: ' + msg_display);
                });
                layer.close(index);
            };
        }
        // 关闭订单
        $('.order_closed').click(function() {
            const attr_v = $(this).attr('value');
            const key = attr_v.split('_')[0];
            const sn = attr_v.split('_')[1];
            let opt = {
                title: '确认关闭订单',
                content: '确定要将订单【' + sn + '】的状态设为【订单关闭】吗？',
                btn: ['确认', '取消'],
                shadeClose: true
            };
            let yes_fn = get_yes_fn(sn, key, -400);
            layer.confirm(opt.title, opt, yes_fn);
        });

        $('.btn-broken_screen_insurance-export-async').click(function() {
                let paid_at_start = $('#paid_at_start')[0].value;
                let paid_at_end = $('#paid_at_end')[0].value;
                let created_at_start = $('#created_at_start')[0].value;
                let created_at_end = $('#created_at_end')[0].value;
                let audited_at_start = $('#audited_at_start')[0].value;
                let audited_at_end = $('#audited_at_end')[0].value;
                let refund_at_start = $('#refund_at_start')[0].value;
                let refund_at_end = $('#refund_at_end')[0].value;
                let content_ss = [
                    '在上方筛选栏中选择时间：',
                    '【投保时间】【审核时间】【支付时间】【退款时间】',
                    '填了多个条件的话 是“并且”关系',
                    '都为空则默认导出【投保时间】在今天 00:00-23:59',
                    '【投保时间】开始：' + created_at_start + ', 结束：' + created_at_end,
                    '【审核时间】开始：' + audited_at_start + ', 结束：' + audited_at_end,
                    '【支付时间】开始：' + paid_at_start + ', 结束：' + paid_at_end,
                    '【退款时间】开始：' + refund_at_start + ', 结束：' + refund_at_end,
                ];
                let content = content_ss.join('<br/>');
                let opt = {
                    title: '导出碎屏保信息',
                    content: content,
                    btn: ['确认','取消'],
                    shadeClose: true,
                    area: '500px'
                };
                let yes_fn = function(index) {
                    let url = '/admin/broken_screen_insurance_export?' + 
                        'paid_at_start=' + encodeURIComponent(paid_at_start) + '&paid_at_end=' + encodeURIComponent(paid_at_end) +
                        '&created_at_start=' + encodeURIComponent(created_at_start) + '&created_at_end=' + encodeURIComponent(created_at_end) +
                        '&audited_at_start=' + encodeURIComponent(audited_at_start) + '&audited_at_end=' + encodeURIComponent(audited_at_end) +
                        '&refund_at_start=' + encodeURIComponent(refund_at_start) + '&refund_at_end=' + encodeURIComponent(refund_at_end);
                    $.get(url, function(result) {
                        console.log(result);
                        let msg_display = result?.msg ?? '空';
                        layer.msg('msg: ' + msg_display);
                    });
                    layer.close(index);
                };
                layer.confirm(opt.title, opt, yes_fn);
            });
js;
        Admin::script($script);
        return Admin::grid(BrokenScreenInsurance::class, function (Grid $grid) {

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->is('top_agency', '一级代理')
                    ->select(Agency::where([['level', 1], ['deleted_at', '=', null]])->pluck('name', 'id'))
                    ->load('second_agency', '/admin/agency/secondAgency');
                $filter->is('second_agency', '二级代理')->select();
                $filter->like('endpoint_name', '终端名称');
                $filter->like('sn', '单号');
                $filter->like('barcode', '条码');
                $filter->like('name', '投保人');
                $filter->is('model_id', '机型')
                    ->select(Machine::where('visibility', 1)->orderBy('name', 'asc')->pluck('name', 'model_id'));
                $filter->is('standard_name.standard_id', '投保类型')->select(BrokenScreenInsurance::STANDARD_TYPE);
                $filter->is('type', '购买方式')->select(BrokenScreenInsurance::BUY_TYPE);
                $filter->is('is_direct_sales', '是否直营')->select(BrokenScreenInsurance::IS_DIRECT_SALES);
                $bsi_status = BrokenScreenInsurance::STATUS;
                unset($bsi_status[BrokenScreenInsurance::STATUS_VOIDED]); //不显示已作废选项
                $filter->is('status', '保单状态')->select($bsi_status);
                DebugBar::info(['bsi_status' => $bsi_status, 'raw_status' => BrokenScreenInsurance::STATUS]);
                $filter->is('trade_plat', '支付方式')->select(BrokenScreenInsurance::TRADE_PLAT);
                $filter->like('pay_id', '支付单号');
                $filter->like('refund_id', '退款单号');
                $filter->between('created_at', '投保时间')->datetime();
                $filter->between('audited_at', '审核时间')->datetime();
                $filter->between('paid_at', '支付时间')->datetime();
                $filter->between('refund_at', '退款时间')->datetime();
            });

            $grid->tools(function ($tools) {

                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                });

                $button_export = <<<EOF
                <div class="btn-group pull-right" style="margin-top:10px; margin-right: 10px;">
                    <a href ="broken_screen_insurance?_export=1" class="btn btn-sm btn-twitter" >
                        <i class="fa fa-download" ></i > 导出
                    </a >
                </div >
EOF;

                $button_supply = <<<EOF
                    <div class="btn-group pull-right" style="margin-top:10px; margin-right: 10px;">
                      <a href ="broken_screen_insurance_supply/create" class="btn btn-sm btn-warning" >
                        <i class="fa fa-save" ></i > 补录
                      </a >
                    </div >
EOF;
                $tools->append($button_supply);

                $button_export_async = <<<EOF
                <div class="btn-group pull-right" style="margin-top: 10px; margin-right: 10px;">
                    <div class="btn btn-sm btn-success btn-broken_screen_insurance-export-async" >
                        <i class="fa fa-print" ></i > 导出
                    </div >
                </div >
EOF;
                $tools->append($button_export_async);
            });

            $grid->disableCreation();
            $grid->disableExport();
            // $grid->exporter(new BrokenScreenInsuranceExporter());

            $grid->model()->where('status', '!=', BrokenScreenInsurance::STATUS_VOIDED)->orderBy('id', 'desc');
            //$grid->model()->orderBy('id', 'desc');

            $grid->id('ID');
            //$grid->sn('保单编号');
            //$grid->barcode('条码');
            //$grid->model_name('机型');
            $grid->column('保单编号-条码-机型')->display(function () {
                return $this->sn . '<br/>' . $this->barcode . '<br/>' . $this->model_name;
            });
            $grid->name('投保人');
            $grid->pay_amount('支付金额');
            $grid->column('standard_name.name', '投保类型');
            $grid->column('insurance_times', '投保标准的可保次数');
            $grid->column('insurance_times_remain', '剩余可保次数');
            //$grid->endpoint_name('终端名称');
            $grid->column('是否直营-终端名称')->display(function () {
                $is_direct_sales_v = $this->is_direct_sales;
                $label = 'default';
                if (array_key_exists($is_direct_sales_v, BrokenScreenInsurance::IS_DIRECT_SALES_LABEL)) {
                    $label = BrokenScreenInsurance::IS_DIRECT_SALES_LABEL[$is_direct_sales_v];
                }
                $is_direct_sales_s = '---';
                if (array_key_exists($is_direct_sales_v, BrokenScreenInsurance::IS_DIRECT_SALES)) {
                    $is_direct_sales_s = BrokenScreenInsurance::IS_DIRECT_SALES[$is_direct_sales_v];
                }
                //return '<span class="label label-' . $label . '">' . $is_direct_sales_s . '</span>'
                return $is_direct_sales_s
                    . '<br/>' . $this->endpoint_name;
            });
            $grid->column('支付方式-支付单号-退款单号')->display(function () {
                $trade_plat = $this->trade_plat;
                /*$label = 'default';
                if (array_key_exists($trade_plat, BrokenScreenInsurance::TRADE_PLAT_LABEL)) {
                    $label = BrokenScreenInsurance::TRADE_PLAT_LABEL[$trade_plat];
                }
                $trade_plat_s = '---';
                if (array_key_exists($trade_plat, BrokenScreenInsurance::TRADE_PLAT)) {
                    $trade_plat_s = BrokenScreenInsurance::TRADE_PLAT[$trade_plat];
                }
                return '<span class="label label-' . $label . '">' . $trade_plat_s . '</span>';*/
                //return BrokenScreenInsurance::TRADE_PLAT[$trade_plat];
                $trade_plat_s = BrokenScreenInsurance::TRADE_PLAT[$trade_plat];
                $pay_id = $this->pay_id ?: '---';
                $refund_id = $this->refund_id ?: '---';
                return $trade_plat_s . '<br/>' . $pay_id . '<br/>' . $refund_id;
            });
            //$grid->pay_id('交易单号');
            //$grid->refund_id('退款单号');
            $grid->status('保单状态')->display(function ($status) {
                $s = BrokenScreenInsurance::STATUS;
                if (array_key_exists($status, $s)) {
                    return $s[$status];
                }
                return '---';
            });
            $grid->remark('备注');
            $grid->audited_at('审核时间');
            $grid->created_at('投保时间')->sortable();
            $grid->refund_at('退款时间')->sortable();
            /*$grid->is_direct_sales('是否直营')->display(function ($is_direct_sales) {
                return BrokenScreenInsurance::IS_DIRECT_SALES[$is_direct_sales];
            });*/
            $grid->type('购买类型')->display(function ($type_v) {
                /*$label = 'default';
                if (array_key_exists($type_v, BrokenScreenInsurance::BUY_TYPE_LABEL)) {
                    $label = BrokenScreenInsurance::BUY_TYPE_LABEL[$type_v];
                }*/
                $type_s = '---';
                if (array_key_exists($type_v, BrokenScreenInsurance::BUY_TYPE)) {
                    $type_s = BrokenScreenInsurance::BUY_TYPE[$type_v];
                }
                //return '<span class="label label-' . $label . '">' . $type_s . '</span>';
                return $type_s;
            });

            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();

                $status = $actions->row->status;
                $c = 'broken_screen_insurance/view/' . $actions->getKey();
                $html = '<a href="' . $c . '" class="action_link"><span style="color:blue">查看</span></a>';
                $actions->append($html);

                if ($status == BrokenScreenInsurance::STATUS_CHECK_PENDING) {
                    $c = 'broken_screen_insurance/' . $actions->getKey();
                    $html = '<a href="' . $c . '/edit" class="action_link">【审核】</a>';
                    $actions->append($html);
                }
                if (in_array($status,
                    [BrokenScreenInsurance::STATUS_CHECK_APPROVED, BrokenScreenInsurance::STATUS_CHECK_FAILED])) {
                    $c = 'broken_screen_insurance/' . $actions->getKey();
                    $html = '<a href="' . $c . '/edit" class="action_link">【修改】</a>';
                    $actions->append($html);
                }
                $timeDifference = time() - strtotime($actions->row->created_at);
                if (!empty($actions->row->pay_id) && $status < 400 && $actions->row->pay_amount > 0 && Admin::user()->inRoles('post_repair_refund')) {
                    $html = '<a href="JavaScript:void(0);" class="action_link refund" value="' . $actions->getKey() .
                        '" style="color:#e50112">【退款】</a>';
                    $actions->append($html);
                }

                if ($status == BrokenScreenInsurance::STATUS_CHECK_APPROVED) {
                    $html = '<a href="javascript:void(0);" class="action_link order_closed" value="' . $actions->getKey() . '_' . $actions->row->sn .
                        '"><span style="color:orange">【关闭订单】</span></a>';
                    $actions->append($html);
                }
            });
        });
    }


    public function form($id = null)
    {
        $script = <<<EOF
            //图片弹出层
            $('.img-thumbnail').click(
                 function(){
                     let src = $(this).attr('src');
                     let html = '<div class="message" style=""><button id="button" style="display:block;margin: 0 auto;"><img id="img" style="max-width:900px;max-height:900px; transform: rotate(0deg)" src="'+ src +'"><//button><//div>'
                     $.fancybox.open(html);
                 }
             );
EOF;
        Admin::script($script);
        $script2 = <<<EOF
            $(document).on('click', '#button', function() {
                var tr = $('#img').css("transform");
                a = eval('get'+$('#img').css('transform'));
                var step=90; 
                $('#img').css("transform", "rotate("+(a+step)%360+"deg)");
            });
             
            function getmatrix(a,b,c,d,e,f) {
                var aa=Math.round(180*Math.asin(a)/ Math.PI);
                var bb=Math.round(180*Math.acos(b)/ Math.PI);
                var cc=Math.round(180*Math.asin(c)/ Math.PI);
                var dd=Math.round(180*Math.acos(d)/ Math.PI);
                var deg=0;
                if(aa==bb||-aa==bb) {
                    deg=dd;
                } else if(-aa+bb==180) {
                    deg=180+cc;
                } else if(aa+bb==180) {
                    deg=360-cc||360-dd;
                }
                return deg >= 360 ? 0 : deg;
                //return (aa+','+bb+','+cc+','+dd);
            }
EOF;
        Admin::script($script2);
        return Admin::form(BrokenScreenInsurance::class, function (Form $form) use ($id) {
            $form->html('<label>投保信息</label>');
            $form->display('id', 'ID');
            $form->display('sn', '保单编号');
            $form->hidden('sn');
            $form->display('barcode', '条码');
            $form->hidden('barcode');
            $form->display('buy_date', '购买时间');

            // 获取退货记录
            if (!empty($id)) {
                $order = BrokenScreenInsurance::where('id', $id)->first();
                $barcode = $order->barcode;
                $data = DB::connection('mysql2')
                    ->table('warranty_return as wr')
                    ->leftJoin('admin_users as au', 'au.id', '=', 'wr.uid')
                    ->where('wr.barcode', $barcode)
                    ->orderBy('wr.return_at', 'desc')
                    ->select('wr.reason', 'au.name', 'wr.return_at', 'wr.uid')->first();
                if (!empty($data)) {
                    if ($data->uid == 0) {
                        $data->name = '后台退货';
                    }

                    $html = <<<EOF
                    <span style="color:red;">退货记录: &nbsp;&nbsp;{$data->return_at}--{$data->reason}--{$data->name}</span>
EOF;
                    $form->html($html);
                } else {
                    $html = <<<EOF
                    <span style="color:red;">该机器没有退货记录</span>&nbsp;
EOF;
                    $form->html($html);
                }
            }

            $form->hidden('endpoint');
            $form->display('model_name', '机型');
            $form->hidden('model_name');
            $form->hidden('month');
            $form->hidden('amount');
            $form->display('endpoint_name', '终端');
            $form->display('standard_name.name', '投档类型');
            $form->display('insurance_times_remain', '可保次数')->with(function ($insurance_times) {
                return '<span style="font-weight: bold"> ' . $insurance_times . '</span>';
            });
            $form->display('name', '姓名');
            $form->display('phone', '手机号');
            $form->display('identity_card', '身份证号');
            $form->hidden('status');
            $form->hidden('audited_at');
            $form->hidden('is_paid');
            $form->hidden('paid_at');
            $form->hidden('sn');
            if (!empty($id)) {
                $order = BrokenScreenInsurance::where('id', $id)->first();
                $barcode = $order->barcode;
                $data = DB::connection('mysql2')
                    ->table('warranty')
                    ->where([['barcode', '=', $barcode], ['status', '=', '1']])
                    ->select('number')->first();
                if (empty($data)) {
                    $html = <<<EOF
                    <span style="color:red;">此机器无保卡信息。</span>&nbsp;
EOF;
                    $form->html($html);
                } else {
                    $html = <<<EOF
                    <span style="color:red;">序列号：$data->number</span>&nbsp;
EOF;
                    $form->html($html);
                }
            }
            $form->display('image_path', '图像信息')->with(function ($value) {
                $ret = '无';
                $p = $value;
                if (is_array($p)) {
                    $ret = '';
                    foreach ($p as $key => $value) {
                        $path = rtrim(config('admin.upload.host'), '/') . '/' . $value;
                        $ret .= '<img src="' . $path . '" style="max-height:80px;max-width:80px" class="img img-thumbnail fancybox-thumbs" data=' . $this->id . ' i=' . $key . ' />';
                        $ret .= '&nbsp;';
                    }
                }
                return $ret;
            });
            $form->display('video_path', '视频信息')->with(function ($value) {
                $ret = '无';
                $p = $value;
                if (is_array($p)) {
                    $ret = '';
                    foreach ($p as $key => $value) {
                        $path = rtrim(config('admin.upload.host'), '/') . '/' . $value;
                        $ret .= '<video src="' . $path . '" controls="controls" preload="auto" style="max-height:500px;max-width:400px" class="img img-thumbnail fancybox-thumbs" data=' . $this->id . ' i=' . $key . ' />';
                        $ret .= '&nbsp;';
                    }
                }
                return $ret;
            });

            $form->divider();
            $form->html('<label>审核</label>');

            $form->select('audit_status', '审核碎屏保')->options([1 => '允许投保', 2 => '不支持投保']);
            $form->textarea('audit_opinion', '审核意见');


            $form->saving(function (Form $form) {
                // dd($form);
                $form->model()->auditor = Admin::user()->id;
                // dd($form->model()->status);
                if ($form->model()->status > 100 and !Admin::user()->inroles(['broken_screen_insurance_manager', 'Administrator'])) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '状态不允许, 已审核通过,请勿重新操作',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }

                // 修复用户重复支付问题
                $count2 = BrokenScreenInsurance::where([['sn', '=', $form->sn], ['barcode', '=', $form->barcode]])
                    ->whereIn('status', [BrokenScreenInsurance::STATUS_PAY_EFFECTIVE, BrokenScreenInsurance::STATUS_REPORTED])
                    ->count();
                if ($count2 > 0) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '该机器碎屏保已生效或已报修,无法修改!',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }

                if (abs($form->status) == 200 || abs($form->status) == 100) {

                    if ($form->audit_status == 1) {
                        $count = BrokenScreenInsurance::where([['sn', '!=', $form->sn],
                            ['barcode', '=', $form->barcode]])->whereIn('status', [200, 300, 400])
                            ->count();
                        if ($count > 0) {
                            $error = new MessageBag([
                                'title' => '无法操作',
                                'message' => '此条码已有一单申请通过，请确认',
                            ]);
                            return back()->withInput()->with(compact('error'));
                        }
                        $form->status = BrokenScreenInsurance::STATUS_CHECK_APPROVED;

                        // 零元直接生效
                        if ($form->model()->pay_amount == 0) {
                            $form->status = BrokenScreenInsurance::STATUS_PAY_EFFECTIVE;
                            $form->is_paid = 1;
                            $form->paid_at = date('Y-m-d H:i:s');
                        }
                        $form->audited_at = date('Y-m-d H:i:s');
                    } else {
                        $form->status = BrokenScreenInsurance::STATUS_CHECK_FAILED;
                        $form->audited_at = date('Y-m-d H:i:s');
                    }
                }
                // dd($form->status);
                // 审核推送
                if ($form->status == BrokenScreenInsurance::STATUS_CHECK_APPROVED) {
                    $message_push = new MessagePush();
                    // dd($form->endpoint);
                    $message_push->push($form->sn, $form->barcode, $form->endpoint);
                    // $sms = new Sms();
                    // $phone = $form->model()->phone;
                    // $template = '649437';
                    // $data  = ['model'=> $form->model_name, 'sn'=>$form->barcode, 'money'=>$form->amount, 'expire'=>$form->month.'月'];
                    // $sms->send($phone, $template, $data);
                }

                // 零元直接生效推送 （原价258.00，2022.1.15—2022.2.28限时特惠）
                if ($form->status == BrokenScreenInsurance::STATUS_PAY_EFFECTIVE) {

                    // 获取标准A档价格
                    $price = DB::table('broken_screen_insurance_standard')
                        ->where('model_id', $form->model()->model_id)
                        ->where('standard_id', 1)
                        ->value('amount');
                    if (!empty($price)) {
                        $money = $form->amount . '（原价' . $price . '，' .
                            str_replace('-', '.', $form->model()->standard_name->activity_start_date) . '-' .
                            str_replace('-', '.', $form->model()->standard_name->activity_end_date) . '限时特惠）';
                    } else {
                        $money = $form->amount . '（' .
                            str_replace('-', '.', $form->model()->standard_name->activity_start_date) . '-' .
                            str_replace('-', '.', $form->model()->standard_name->activity_end_date) . '限时特惠）';

                    }
                    $sms = new Sms();
                    $phone = $form->model()->phone;
                    $template = '649437';
                    $data = ['model' => $form->model_name, 'sn' => $form->barcode, 'money' => $money, 'expire' => $form->month . '月'];
                    $sms->send($phone, $template, $data);
                }
            });
        });
    }

    public function view(Content $content, $id = null)
    {
        return Admin::content(function (Content $content) use ($id) {
            $content->header('寄修审核--查看');

            $insurance_order = BrokenScreenInsurance::where(['id' => $id])->first();
            $usages = $insurance_order->usages;
            $mat_ids = array();
            $material_ids = array();
            foreach ($usages as $item) {
                $pms = $item->pr_order->repair_material;
                foreach ($pms as $pm) {
                    array_push($mat_ids, $pm->mat_id);
                }
                $ums = $item->pr_order->used_material;
                foreach ($ums as $um) {
                    array_push($material_ids, $um->material_id);
                }
            }
            $mat_name = MachineAccessoryTree::get_title_array($mat_ids);
            $material_name = Material::getNameArray($material_ids);
            DebugBar::info(compact('insurance_order'));
            DebugBar::info(compact('usages'));
            DebugBar::info(compact('mat_ids'));
            DebugBar::info(compact('mat_name'));
            DebugBar::info(compact('material_ids'));
            DebugBar::info(compact('material_name'));
            $content->body(view('admin/broken_screen_insurance/view',
                compact('insurance_order', 'usages', 'mat_name', 'material_name')));
        });
    }


    /**
     * 退款
     */
    public function refund()
    {
        $id = request()->input('id');

        $orderInfo = BrokenScreenInsurance::where(['id' => $id])->first();

        $url = 'https://api-repair-hub.readboy.com/broken_screen_insurance/refund2'; // 正式地址
        // $url = 'http://api-repair-test.readboy.com/broken_screen_insurance/refund2'; // 测试地址

        $appid = 'yx.readboy.com';
        $appkey = '710eb100ff2120cbcde0d78e532de8cg';
        $device_item = ['1', '', '', $appid, '', ''];
        $device_id = implode('/', $device_item);
        $t = time();
        $sn = md5($device_id . $appkey . $t);
        $data = [
            't' => $t,
            'ua' => $device_id,
            'sn' => $sn,
            'order_sn' => $orderInfo->sn,
            'barcode' => $orderInfo->barcode
        ];

        $param = http_build_query($data);
        $url = $url . '?' . $param;
        $client = new Client();

        $response = $client->request('POST', $url);

        $body = $response->getBody();
        $responseContent = $body->getContents();
        $responseContent = json_decode($responseContent, 1);

        return $responseContent;
    }
}
