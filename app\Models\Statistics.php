<?php

namespace App\Models;

use App\Traits\StatisticsHelperTrait;
use Illuminate\Database\Eloquent\Model;
use Encore\Admin\Facades\Admin;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class Statistics extends Model
{

    use StatisticsHelperTrait;

    //处理天数选择的函数
    public static function dayGender($gender)
    {
        $endTime = date('Y-m-d H:i:s', time());
        //根据参数获得范围:
        switch ($gender) {
            case 'today':
                $startTime = Carbon::today();
                break;
            case 'lastThreeDays':
                $startTime = Carbon::parse('- 2 days')->startOfDay();
                break;
            case 'yesterday':
                $startTime = Carbon::yesterday()->startOfDay();
                $endTime = Carbon::yesterday()->endOfDay();
                break;
            case 'lastSevenDays':
                $startTime = Carbon::parse('- 6 days')->startOfDay();
                break;
            case 'thisWeek':
                $startTime = Carbon::now()->startOfWeek();
                break;
            case 'lastWeek':
                $startTime = Carbon::parse('last week')->startOfWeek();
                $endTime = Carbon::parse('last week')->endOfWeek();
                break;
            case 'thisMonth':
                $startTime = Carbon::now()->startOfMonth();
                break;
            case 'lastMonth':
                $time = new Carbon(date('Y-m', time()));
                $startTime = $time->copy()->subMonth();
                $endTime = $time->subMonth()->endOfMonth();
                break;
            default:
                $startTime = Carbon::today();
        }

        return [$startTime, $endTime];
    }

    /**
     * @param $gender
     * @return mixed
     */
    public function getAgencyWarrantyStatistics($gender, $createdAt = [])
    {

        if (empty($createdAt['start'])) {
            $time = self::dayGender($gender);
        } else {
            $time = [$createdAt['start'], $createdAt['end']];
        }
        $topAgencyCount = [];
        //判断权限决定要显示的内容
        if (Admin::user()->inRoles(['topAgency', 'topAgencyFinance'])) {
            //获取当前用户的总代id
            $topAgency = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('top_agency');
            $sqlKeyWord = 'endpoint.second_agency';
            $where = [
                ['agency.pid', '=', $topAgency],
                ['warranty.status', '=', 1],
            ];
            //获取所有二级代理
            $agencies = DB::table('agency')->where('pid', '=', $topAgency)->pluck('name')->toArray();

            //计算出直属总代的保卡数量,为什么单独算,因为放在一起算,数据库查询会很慢
            $topAgencyCount = DB::table('warranty')
                ->select(DB::raw('count(warranty.id) as count, agency.name,warranty.buy_date,endpoint.second_agency'))
                ->leftjoin('endpoint', 'warranty.endpoint', '=', 'endpoint.id')
                ->rightjoin('agency', 'endpoint.top_agency', '=', 'agency.id')
                ->whereBetween('buy_date', $time)
                ->where([
                    ['agency.id', '=', $topAgency],
                    ['endpoint.second_agency', '=', 0],
                    ['warranty.status', '=', 1],
                ])
                ->get()
                ->pluck('count', 'name')
                ->toArray();
        } else {
            $sqlKeyWord = 'endpoint.top_agency';
            $where = [
                ['warranty.status', '=', 1],
            ];
            //获取所有一级代理
            $agencies = DB::table('agency')->where('level', '=', 1)->pluck('name')->toArray();
        }

        $result = DB::table('warranty')
            ->select(DB::raw('count(warranty.id) as count, agency.name,warranty.buy_date'))
            ->leftjoin('endpoint', 'warranty.endpoint', '=', 'endpoint.id')
            ->rightjoin('agency', $sqlKeyWord, '=', 'agency.id')
            ->whereBetween('buy_date', $time)
            ->where($where)
            ->groupBy('agency.id')
            ->orderBy('count', 'desc')
            ->get()
            ->pluck('count', 'name')
            ->toArray();
        $result = array_merge($result, $topAgencyCount);
        arsort($result);

        $data['countList'] = array_values($result);
        $data['agencyList'] = array_keys($result);
        foreach ($agencies as $key => $value) {
            if (!in_array($value, $data['agencyList'])) {
                array_push($data['agencyList'], $value);
                array_push($data['countList'], 0);
            }
        }

        return $data;
    }

    /**
     * 根据时间断取每日保卡数量情况
     */
    public static function getDailyWarrantyStatistics($topAgency, $secondAgency, $createAt = [], $gender)
    {
        if (empty($createAt) || empty($createAt['start'])) {
            $time = self::dayGender($gender);
        } else {
            $time = [$createAt['start'], $createAt['end']];
        }
        //        $createAt = $createAt ?: self::dayGender(7);
        $rawSelectSql = "DATE_FORMAT( warranty.buy_date, '%m-%d' ) days, 
        count(warranty.id) warranty_count, count(DISTINCT(endpoint.id)) endpoint_count";

        $where = [['warranty.status', '=', 1]];
        //判断是否是总代,是的话加上根据总代获取记录
        if (Admin::user()->inRoles(['topAgency', 'topAgencyFinance'])) {
            $topAgency = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('top_agency');
        } elseif (Admin::user()->inRoles(['secondAgency'])) {
            $secondAgency = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('second_agency');
        }

        if ($topAgency) {
            array_push($where, ['endpoint.top_agency', '=', $topAgency]);

        }
        if ($secondAgency) {
            array_push($where, ['endpoint.second_agency', '=', $secondAgency]);

        }
        $data = DB::table('warranty')->select(DB::raw($rawSelectSql))
            ->leftJoin('endpoint', 'warranty.endpoint', 'endpoint.id')
            ->where($where)
            ->whereBetween('warranty.buy_date', $time)
            ->groupBy('days')
            ->get()->toArray();
        //转成纯数组
        $data = array_map('get_object_vars', $data);

        return $data;

    }


    /**
     * 根据时间段取每日订单数量情况
     */
    public static function getDailyOrderStatistics($createAt = [], $gender, $machine_category, $model_id)
    {
        if (empty($createAt) || empty($createAt['start'])) {
            $time = self::dayGender($gender);
        } else {
            $time = [$createAt['start'], $createAt['end']];
        }
        //        $createAt = $createAt ?: self::dayGender(7);
        $rawSelectSql = "DATE_FORMAT( o.created_at, '%m-%d' ) days,
        count(DISTINCT(o.barcode)) machine_count, count(DISTINCT(o.uid)) user_count";
        $where = [['o.status', '<>', -900]];
        if ($machine_category && $model_id){
            array_push($where, ['mt.model_id', '=', $model_id]);
        }elseif ($machine_category){
            array_push($where, ['mt.category_id', '=', $machine_category]);
        }

        $data = DB::table('order as o')->select(DB::raw($rawSelectSql))
            ->where($where)
            ->whereBetween('o.created_at', $time)
            ->leftjoin('machine_type as mt', 'o.model_id', '=', 'mt.model_id')
            ->groupBy('days')
            ->get()->toArray();
        //转成纯数组
        $data = array_map('get_object_vars', $data);
//        dd($data);
        return $data;

    }

    /**
     * 根据时间段取每日订单数量情况
     */
    public static function getOrderDistributeStatistics($gender, $createAt = [], $province, $city)
    {
        if (empty($createAt) || empty($createAt['start'])) {
            $time = self::dayGender($gender);
        } else {
            $time = [$createAt['start'], $createAt['end']];
        }

        if ($city || $province){
//            dump("city");
            $rawSelectSql = "count(DISTINCT(sn)) count, region.shortname as distribute";
        }else{
//            dump("province or no");
            $rawSelectSql = "count(DISTINCT(sn)) count, region.shortname as distribute";
        }
        $where = [['status', '!=', -900],['status', '!=', -200]];
        if ($city && $province){
//            dump('3');
            array_push($where, ['region.region_id', '=', $city]);
            $data = DB::table('order')->select(DB::raw($rawSelectSql))
                ->where($where)
                ->whereBetween('order.created_at', $time)
                ->rightJoin('region', 'region.region_name', '=', 'order.city')
                ->groupBy('city')
                ->get()->toArray();
//            array_push($where, ['province', '=', $province], ['city', '=', $city]);
        }elseif ($province){
//            dump($province);
            array_push($where, ['region.region_id', '=', $province]);
            $data = DB::table('order')->select(DB::raw($rawSelectSql))
                ->where($where)
                ->whereBetween('order.created_at', $time)
                ->rightJoin('region', 'region.region_name', '=', 'order.province')
                ->groupBy('city')
                ->get()->toArray();
//            dump($data);
        }else{
            $data = DB::table('order')->select(DB::raw($rawSelectSql))
                ->where($where)
                ->whereBetween('order.created_at', $time)
                ->rightJoin('region', 'region.region_name', '=', 'order.province')
                ->groupBy('province')
                ->get()->toArray();
//            dump('2');
        }

        //转成纯数组
        $data = array_map('get_object_vars', $data);
//        dd($data);
        return $data;

    }

    /**
     * @param $gender
     * @param $in_period
     * @param array $createdAt
     * @return array
     */
    public static function getInPeriodStatistics($gender,$in_period, $createdAt= [], $machine_category, $model_id){
//        时间为空
        if (empty($createdAt) || empty($createdAt['start'])){
            $time = self::dayGender($gender);
        }else{
            $time = [$createdAt['start'],$createdAt['end']];
        }
        $selectSql = "DATE_FORMAT(o.created_at, '%m-%d' ) as days, sum(o.in_period =1) as in_period1,sum(o.in_period =2)
         as in_period2,FORMAT(SUM(CASE WHEN o.in_period = 1 THEN o.amount/1000 ELSE 0 END),2) as amount1, FORMAT(SUM(CASE WHEN o.in_period = 2 
         THEN o.amount/1000 ELSE 0 END ),2) as amount2 ,FORMAT(SUM(CASE WHEN o.in_period = 1 THEN o.pay_amount/1000 ELSE 0 END),2) as pay_amount1,
          FORMAT(SUM(CASE WHEN o.in_period = 2 THEN o.pay_amount/1000 ELSE 0 END ),2) as pay_amount2 ";


        if ($machine_category && $model_id){
            $where = [['category_id', '=', $machine_category],['mt.model_id', '=', $model_id]];
        }else if ($machine_category){
            $where = [['category_id', '=', $machine_category]];
        }else{
            $where = [];
        }
        $data = DB::table('order as o')->select(DB::raw($selectSql))
            ->where($where)
            ->whereIn('o.status', [800,900])
            ->wherebetween('o.created_at', $time)
            ->leftjoin('machine_type as mt', 'o.model_id', '=', 'mt.model_id')
            ->groupBy('days')
            ->get()->toArray();

        $data = array_map('get_object_vars', $data);
//        $ret = array();
//        foreach ($data as $key => $value){
//            $a = array();
//            if (!in_array($a, $value['days'])){
//
//            }
//        }
        return $data;
    }

    public static function getDamageAccessoryStatistics($gender,$model_id,$createdAt= [])
    {
        if (empty($createdAt) || empty($createdAt['start'])){
            $time = self::dayGender($gender);
        }else{
            $time = [$createdAt['start'],$createdAt['end']];
        }
        $sqlSelect = "sum(pum.count) as count, concat_ws('-',mt.name,mat.title) as title";
//        dd($model_id);
        if ($model_id){
            $where = [['mat.model_id', '=', $model_id]];
        }else{
            $where = [];
        }
        $data = DB::table('pr_used_material as pum')
            ->select(DB::raw($sqlSelect))
            ->where($where)
            ->whereBetween('pum.created_at', $time)
            ->leftJoin('machine_accessory_tree as mat', 'pum.mat_id', '=','mat.id')
            ->leftJoin('machine_type as mt', 'mat.model_id', '=', 'mt.model_id')
            ->groupBy('pum.mat_id')
            ->get()->toArray();
        $data = array_map('get_object_vars', $data);
//        dd($data);
        return $data;
    }

    public static function getDailyBlockStatistics($gender, $createdAt = [], $machine_category, $model_id){
        if (empty($createdAt) || empty($createdAt['start'])){
            $time = self::dayGender($gender);

        }else{
            $time = [$createdAt['start'], $createdAt['end']];
        }
//        dump($time);
        $sqlSelect = "date_format(ol.date, '%m-%d') as days, sum(ol.pr_status=200) as post_check,sum(ol.pr_status=500) 
        as repair_check ,sum(ol.pr_status=600) as already_pay, sum(ol.pr_status=700) as already_repair,
        sum(ol.pr_status=800) as exp_go";
        $where = [];
        if ($machine_category && $model_id){
            array_push($where, ['mt.model_id', '=', $model_id]);
        }elseif ($machine_category){
            array_push($where, ['mt.category_id', '=', $machine_category]);
        }
//        $where = [['pr_status', 'in',]];
        $data = DB::table('order_log as ol')->select(DB::raw($sqlSelect))
            ->where($where)
            ->whereIn('ol.pr_status', [200,500,600,700,800])
            ->whereBetween('ol.date',$time)
            ->rightjoin('order as o', 'o.sn', '=', 'ol.pr_sn')
            ->rightjoin('machine_type as mt', 'mt.model_id', '=', 'o.model_id')
            ->groupBy('days')
            ->get()->toArray();
//        dd($data);
        $data = array_map('get_object_vars', $data);
        return $data;
    }

    /**
     * @param $topAgency
     * @param $secondAgency
     * @param array $createAt
     * @param $gender
     * @return array
     */
    public static function getModelWarrantyStatistics($topAgency, $secondAgency, $createAt = [], $gender)
    {
        if (empty($createAt) || empty($createAt['start'])) {
            $time = self::dayGender($gender);
        } else {
            $time = [$createAt['start'], $createAt['end']];
        }

        $where = [['warranty.status', '=', 1]];
        //判断是否是总代,是的话加上根据总代获取记录
        if (Admin::user()->inRoles(['topAgency', 'topAgencyFinance'])) {
            $topAgency = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('top_agency');
        } elseif (Admin::user()->inRoles(['secondAgency'])) {
            $secondAgency = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('second_agency');
        }

        if ($topAgency) {
            array_push($where, ['endpoint.top_agency', '=', $topAgency]);
        }
        if ($secondAgency) {
            array_push($where, ['endpoint.second_agency', '=', $secondAgency]);
        }

        $data = DB::table('warranty')->leftjoin('endpoint', 'warranty.endpoint', 'endpoint.id')
            ->select(DB::raw('warranty.model,count(warranty.id) as warranty_count'))
            ->where($where)
            ->whereBetween('buy_date', $time)
            ->orderBy('warranty_count', 'desc')
            ->groupBy('model')
            ->get()->toArray();
        $data = array_map('get_object_vars', $data);

        return $data;
    }


    const AUDIT_WAITING = 1; //等待审核
    const AUDIT_AGENCY_PASSED = 2; //大区审核通过
    const AUDIT_LEADER_PASSED = 3; //领导审核通过
    const ACTION_COMPLETED = 4; //已完成活动
    const WRITE_OFF_PASSED = 5; //核销通过
    const AUDIT_FAILED = 6; //审核失败
    const ACTION_NOT_COMPLETE = 7; //未完成活动
    const WRITE_OFF_FAILED = 8; //核销失败

    const STATISTICS_CACHE_TIME_MINUTE = 60000;

    /**
     * @param $data
     * @return array
     */
    public static function calculateAgencyActionStatistics($data)
    {
        $formatData = [];
        foreach ($data as $key => $value) {
            if (!in_array($value['name'], array_column($formatData, 'name')) && !empty($value['name'])) {
                $formatData[$key]['name'] = $value['name'];
                $formatData[$key]['count'] = 1;
                $formatData[$key]['amount'] = (int)$value['amount'];
                $formatData[$key]['quantity'] = (int)$value['quantity'];
                $formatData[$key]['write_off_passed_amount'] = ($value['status'] == self::WRITE_OFF_PASSED)
                    ? $formatData[$key]['amount'] : 0;
                $formatData[$key]['write_off_passed_quantity'] = ($value['status'] == self::WRITE_OFF_PASSED)
                    ? $formatData[$key]['quantity'] : 0;

                $formatData[$key]['audit_passed'] = $value['status'] == self::AUDIT_LEADER_PASSED ? 1 : 0;
                $formatData[$key]['write_off_passed'] = $value['status'] == self::WRITE_OFF_PASSED ? 1 : 0;
                $formatData[$key]['write_off_failed'] = $value['status'] == self::WRITE_OFF_FAILED ? 1 : 0;

            } else {
                foreach ($formatData as $k => $v) {
                    if ($value['name'] == $v['name']) {
                        $formatData[$k]['count']++;
                        $formatData[$k]['amount'] += $value['amount'];
                        //不要用===
                        if ($value['status'] == self::WRITE_OFF_PASSED) {
                            $formatData[$k]['write_off_passed_amount'] += $value['amount'];
                            $formatData[$k]['write_off_passed_quantity'] += $value['quantity'];
                        }
                        switch ($value['status']) {
                            case self::AUDIT_LEADER_PASSED:
                                $formatData[$k]['audit_passed']++;
                                break;
                            case self::WRITE_OFF_PASSED:
                                $formatData[$k]['write_off_passed']++;
                                break;
                            case self::WRITE_OFF_FAILED:
                                $formatData[$k]['write_off_failed']++;
                                break;
                        }
                    }
                }
            }
        }

        //排序
        array_multisort(array_column($formatData, 'write_off_passed_amount'), SORT_DESC, $formatData);

        return $formatData;

    }

    /**
     * @param array $data
     * @return array
     */
    public static function roundActionAmount($data = [])
    {
        //将金额四舍五入成万为单位
        $data = array_map(function ($v) {
            $v['write_off_passed_amount'] = round($v['write_off_passed_amount'] / 10000, 2);
            $v['amount'] = round($v['amount'] / 10000, 2);

            return $v;
        }, $data);

        return $data;
    }


    /**
     * @param $data
     * @return array
     */
    public static function calculateActionAmountRate($data)
    {
        $rateData = [
            '0-5W' => 0,
            '5-10W' => 0,
            '10-15W' => 0,
            '15-20W' => 0,
            '20-30W' => 0,
            '30-40W' => 0,
            '40-50W' => 0,
            '50W以上' => 0,
        ];
        foreach ($data as $key => $value) {
            if ($value['status'] == self::WRITE_OFF_PASSED) {
                if ((0 <= $value['amount'] && $value['amount'] < 50000)
                    || $value['amount'] === null
                ) {
                    $rateData['0-5W']++;
                    continue;
                }
                if (50000 <= $value['amount'] && $value['amount'] < 100000) {
                    $rateData['5-10W']++;
                    continue;
                }
                if (100000 <= $value['amount'] && $value['amount'] < 150000) {
                    $rateData['10-15W']++;
                    continue;
                }
                if (150000 <= $value['amount'] && $value['amount'] < 200000) {
                    $rateData['15-20W']++;
                    continue;
                }
                if (200000 <= $value['amount'] && $value['amount'] < 300000) {
                    $rateData['20-30W']++;
                    continue;
                }
                if (300000 <= $value['amount'] && $value['amount'] < 400000) {
                    $rateData['30-40W']++;
                    continue;
                }
                if (400000 <= $value['amount'] && $value['amount'] < 500000) {
                    $rateData['40-50W']++;
                    continue;
                }
                if (500000 <= $value['amount']) {
                    $rateData['50W以上']++;
                    continue;
                }
            }


        }

        $rateFormatData = [];
        foreach ($rateData as $key => $value) {
            $rateFormatData[] = ['name' => $key, 'value' => $value];
        }

        return $rateFormatData;

    }

    /**
     * @param $gender
     * @param array $createAt
     * @return array
     */
    public static function getAgencyActionStatistics($gender, $createAt = [])
    {

        if (empty($createAt) || empty($createAt['start'])) {
            $time = self::dayGender($gender);
        } else {
            $time = [$createAt['start'], $createAt['end']];
        }
        $time = array_map(function ($value) {
            //为什么要这样子做,因为活动的表不是我建立的,他的时间不是存在标准的时间,而是普通字符串,类似2017-10-01
            return date('Y-m-d', strtotime($value));
        }, $time);
        $data = DB::table('actions')->leftJoin('agency', 'actions.top_agency_id', 'agency.id')
            ->leftjoin('action_sales', 'actions.id', 'action_sales.aid')
            ->select(DB::raw('amount,agency.name,actions.status,total as quantity,actions.id'))
            ->whereBetween('actions.date_start', $time)
            ->groupBy('actions.id')
            ->orderBy('amount', 'desc')
            ->get()
            ->toArray();

        //转成纯数组
        $data = array_map('get_object_vars', $data);

        return $data;
    }

    /**
     * @param $time
     * @param $endpointId
     * @return array
     */
    public static function getModelCountOfEndpointStatisticsFlow($time, $endpointId)
    {
        $map = [['endpoint', '=', $endpointId]];
        $data = DB::table('warranty')
            ->leftjoin('endpoint', 'warranty.endpoint', 'endpoint.id')
            ->select(DB::raw('count(warranty.id) as count,warranty.model'))
            ->groupBy('warranty.model')
            ->whereBetween('buy_date', $time)
            ->where($map)
            ->where('warranty.status', 1)
            ->get()
            ->toArray();
        $data = array_map('get_object_vars', $data);
        //排序
        array_multisort(array_column($data, 'count'), SORT_DESC, $data);

        return $data;
    }

    /**
     * @param $inputs
     * @return mixed
     */
    public static function getSalesStartDetailData($inputs)
    {
        $data = Warranty::select(DB::Raw("count(warranty.id) as count,warranty.id,salesman,endpoint"))
            ->with('endpoints')
            ->whereBetween('buy_date', [$inputs['startTime'], $inputs['endTime']])
            ->where('status', '=', 1)
            ->groupBy('salesman', 'endpoint')
            ->orderBy('count', 'desc')
            ->orderBy('endpoint', 'asc')
            ->limit(10)->get()->toArray();
        $agencyArray = Agency::getAllAgencySimpleArrayCache();
        foreach ($data as &$value) {
            $value['endpoints']['top_agency'] = $agencyArray[$value['endpoints']['top_agency']];
            $value['endpoints']['second_agency'] = $value['endpoints']['second_agency'] ?
                (isset($agencyArray[$value['endpoints']['second_agency']]) ?: '暂无') : '无';
        }
        unset($value);

        return $data;
    }

    /**
     * @param $inputs
     * @return mixed
     */
    public static function getSalesStarDetail($inputs)
    {
        $needCache = StatisticsHelperTrait::checkNeedCacheByDateTime($inputs['endTime']);
        $cacheKey = 'sales_star_detail_' . $inputs['startTime'] . '_' . $inputs['endTime'];
        if ($needCache) {
            if (Cache::has($cacheKey)) {
                $result = Cache::get($cacheKey);
            } else {
                $result = self::getSalesStartDetailData($inputs);
                Cache::put($cacheKey, $result, self::STATISTICS_CACHE_TIME_MINUTE);
            }
        } else {
            $result = self::getSalesStartDetailData($inputs);
        }

        return $result;
    }

    /**
     * @param $inputs
     * @return array
     */
    public static function getSalesStartRankData($inputs, $onlyNationWideRank = false)
    {
        $query = Warranty::select(DB::Raw('count(warranty.id) as count,warranty.id,salesman,endpoint,endpoint.address,
        endpoint.top_agency,endpoint.second_agency,endpoint.name,endpoint.phone'))
            ->leftjoin('endpoint', 'warranty.endpoint', 'endpoint.id')
            ->whereBetween('warranty.created_at', [$inputs['startTime'], $inputs['endTime']])
            ->where('warranty.status', '=', 1)
            ->groupBy('salesman', 'endpoint')
            ->orderBy('count', 'desc')
            ->orderBy('endpoint', 'asc');
        $data = $onlyNationWideRank ? $query->limit(11)->get()->toArray() : $query->get()->toArray();
        $agencyArray = Agency::getAllAgencySimpleArrayCache();

        foreach ($data as &$value) {
            $value['top_agency'] = $value['top_agency'] ? $agencyArray[$value['top_agency']] : '未知';
            $value['second_agency'] = $value['second_agency'] ?
                (isset($agencyArray[$value['second_agency']]) ? $agencyArray[$value['second_agency']] : '无')
                : '无';
        }
        unset($value);

        $topAgencyRank = array_column($data, 'top_agency');
        $topAgencyRank = array_unique($topAgencyRank);
        //判断是否是只返回全国排名
        if ($onlyNationWideRank) {
            return $data;
        }
        //获得全国前三
        $nationWideRank = array_slice($data, 0, 3);
        foreach ($nationWideRank as $k => &$v) {
            $v = array_merge($v, ['rank' => ($k + 1) . ' [全国排名]']);
        }
        unset($v);

        $topAgencyRank = array_map(function () { return []; }, array_flip($topAgencyRank));
        foreach ($data as $key => $value) {
            foreach ($topAgencyRank as $k => $v) {
                if ($value['top_agency'] === $k) {
                    $topAgencyRank[$k][] = $value;
                }
            }
        }
        $topAgencyRank = array_map(function ($value) {
            $value = array_slice($value, 0, 3);
            foreach ($value as $k => &$v) {
                $v = array_merge($v, ['rank' => $k + 1]);
            }
            unset($v);

            return $value;
        }, $topAgencyRank);

        $result = [];
        foreach ($topAgencyRank as $value) {
            $result = array_merge($result, $value);
        }
        $totalRank = array_merge($nationWideRank, $result);

        return $totalRank;
    }

    /**
     * @param $inputs
     * @return array
     */
    public static function getSalesStarRank($inputs, $onlyNationWideRank = false)
    {
        if ($onlyNationWideRank) {
            return self::getSalesStartRankData($inputs, $onlyNationWideRank);
        }
        $needCache = StatisticsHelperTrait::checkNeedCacheByDateTime($inputs['endTime']);
        $cacheKey = 'sales_star_rank_' . $inputs['startTime'] . '_' . $inputs['endTime'];
        if ($needCache) {
            if (Cache::has($cacheKey)) {
                $result = Cache::get($cacheKey);
            } else {
                $result = self::getSalesStartRankData($inputs);
                Cache::put($cacheKey, $result, self::STATISTICS_CACHE_TIME_MINUTE);
            }
        } else {
            $result = self::getSalesStartRankData($inputs);
        }

        return $result;
    }

}
