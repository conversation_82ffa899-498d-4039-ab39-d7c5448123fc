<?php

namespace App\Admin\Controllers;

use App\Models\Order;
use App\Models\OrderExtend;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Support\MessageBag;

class OrderExtendController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修订单扩展信息');
            $content->description('编辑');

            $content->body($this->form()->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(OrderExtend::class, function (Grid $grid) {

            $grid->id('ID')->sortable();

            $grid->created_at();
            $grid->updated_at();

            $grid->actions(function ($actions) {
                // 去掉删除
                $actions->disableDelete();
                // 去掉编辑
                $actions->disableEdit();
                // 去掉查看
                $actions->disableView();
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Admin::form(OrderExtend::class, function (Form $form) {
            $form->tools(function (Form\Tools $tools) {
                // 去掉`列表`按钮
                $tools->disableListButton();
            });

            $form->display('sign_in_status', '签收状态')->with(function ($value) {
                if (array_key_exists($value, Order::SIGN_IN_STATUS)) {
                    return Order::SIGN_IN_STATUS[$value];
                } else {
                    return '未知';
                }
            });
            $form->multipleImage('sign_in_pictures', '签收图片')
                ->options([
                    'allowedFileTypes' => '',
                    'msgInvalidFileExtension' => '{name}文件类型不正确，只支持{extensions}文件',
                    'allowedFileExtensions' => ['jpg', 'png', 'jpeg'],
                ])
                ->move('rbcare/repair/sign_in_picture')
                ->uniqueName();
            $init_preview = array();
            $init_preview[] = ['type' => 'video', 'filetype' => 'video/mp4', 'key' => 0];
            $preview_zoom = array();
            $preview_zoom['video'] = ['width' => "auto", 'height' => "300px"];
            $form->multipleFile('sign_in_videos', '签收视频')
                ->options([
                    'maxFileSize' => 102400,
                    'msgSizeTooLarge' => '文件 "{name}" (<b>{size} KB</b>) 超过了最大允许上传大小 <b>{maxSize} KB</b>.',
                    'maxFilePreviewSize' => 0,
                    'initialPreviewFileType' => 'video',
                    'previewFileType' => ['mp4', 'mov'],
                    'initialPreviewConfig' => $init_preview,
                    'previewSettings' => $preview_zoom,
                ])
                ->move('rbcare/repair/sign_in_video')
                ->uniqueName()
                ->help('如果视频文件体积超过100M，请联系开发人员进行后台上传！MP4文件请使用 H264 视频编码和 AAC 音频编码！否则无法播放！');
            $form->divider();

            $form->saved(function ($form) {
                $success = new MessageBag([
                    'title' => '提交成功',
                    'message' => date('Y-m-d H:i:s'),
                ]);
                return back()->with(compact('success'));
            });
        });
    }
}
