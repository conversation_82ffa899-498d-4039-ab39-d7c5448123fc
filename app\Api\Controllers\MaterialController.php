<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>an
 * Date: 2017/8/17
 * Time: 10:27
 */

namespace App\Api\Controllers;

use App\Api\Transformers\MaterialTransformers;
use App\Models\Material;
use App\Models\MaterialCategory;
use Dingo\Api\Exception\ValidationHttpException;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Validator;
use App\Api\Serializer\CustomSerializer;

class MaterialController extends BaseController {
    public function index() {
        $hasValidatedToken = $this->checkToken();
        $input = Request::all();
        $validator = Validator::make($input, [
            'top_category_id'    => 'numeric',
            'second_category_id' => 'numeric',
            'count'              => 'numeric',
        ]);
        if ($validator->fails()) {
            throw new ValidationHttpException($validator->errors());
        }
        $count = Input::get('count') ?: 10;
        $map = [];

        //判断是不是pid
        if (Input::get('top_category_id') && empty(Input::get('second_category_id'))) {
            //获取pid下的子分类
            $categoryIds = MaterialCategory::where('pid', '=', Input::get('top_category_id'))
                ->pluck('id')
                ->toArray();
            $materials = Material::whereIn('category', $categoryIds)
                ->where('is_putaway', '=', 1)
                ->with('categories')
                ->orderBy('id', 'desc')
                ->paginate($count);
        } else {
            Input::get('second_category_id') ? array_unshift($map, [
                'category',
                '=',
                Input::get('second_category_id'),
            ]) : null;
            $materials = Material::where($map)->where('is_putaway', '=', 1)->with('categories')->orderBy('id', 'desc')->paginate($count);
        }

        return $this->response->paginator($materials, new MaterialTransformers($hasValidatedToken), [],
            function ($resource, $fractal) {
                $fractal->setSerializer(new CustomSerializer());
            });
    }

    public function show($id) {
        $hasValidatedToken = $this->checkToken();
        $material = Material::find($id);
        return $this->response->item($material, new MaterialTransformers($hasValidatedToken),
            function ($resource, $fractal) {
                $fractal->setSerializer(new CustomSerializer());
            });
    }

    public function search() {
        $hasValidatedToken = $this->checkToken();
        $input = Request::all();
        $validator = Validator::make($input, [
            'name'  => 'required',
            'count' => 'numeric',
        ]);
        if ($validator->fails()) {
            throw new ValidationHttpException($validator->errors());
        }

        $count = Input::get('count') ?: 10;
        $name = Input::get('name');
        $materials = Material::where('name', 'like', "%{$name}%")
            ->where('is_putaway', '=', 1)
            ->with('categories')
            ->orderBy('id', 'desc')
            ->paginate($count);
        return $this->response->paginator($materials, new MaterialTransformers($hasValidatedToken), [],
            function ($resource, $fractal) {
                $fractal->setSerializer(new CustomSerializer());
            });
    }
}