<?php

namespace App\Admin\Controllers;

use App\Models\MachineCategory;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Tree;

//机型品类管理,比如某个机型属于什么品类(学生平板.教育平板等等..)
class MachineCategoryController extends Controller {
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index() {
        return Admin::content(function (Content $content) {
           
            $content->header('机型品类');
            $content->description('机型品类列表');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id) {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('机型品类');
            $content->description('机型品类编辑');

            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create() {
        return Admin::content(function (Content $content) {

            $content->header('机型品类');
            $content->description('机型品类创建');

            $content->body($this->form());
        });
    }

    /**
     * Make a form builder.
     *
     * @return Tree
     */
    protected function tree() {
        return MachineCategory::tree(function (Tree $tree) {

            $tree->branch(function ($branch) {
                return "{$branch['name']}";
            });
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid() {
        return Admin::grid(MachineCategory::class, function (Grid $grid) {

            $grid->model()->orderBy('visible', 'desc');

            $grid->id('ID')->sortable();
            $grid->name('名称')->sortable()->editable();
            $status = [
                'on' => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ];
            $grid->column('image', '图标')->image(null, 50, 50);
            $grid->visible('是否可见')->switch($status)->editable();
            // $grid->faq_enable('常见问题品类是否启用')->switch($status)->editable();

            $grid->tools(function ($tools) {
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                });
            });

            $grid->actions(function ($actions) {
                $actions->disableDelete();
                //这个是主键id,不是模型id
                $key = $actions->row->id;
                $html = <<<EOF
                |   <a href='damage?machine_category_id=$key' title='故障管理'>
                      <i class='fa fa-briefcase'> 故障表</i>
                    </a>
EOF;
                $actions->append($html);
                $c = 'machine_quality_tree';
                $k = $actions->row->id;
                $html = '|<a href="'.$c.'?machine_category_id='.$k.'"><span style="color:red">【品检树】</span></a>';
                $actions->append($html);
            });

        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id=null) {
        return Admin::form(MachineCategory::class, function (Form $form) {

            $form->display('id', 'ID');
            $form->text('name', '名称')->rules('required');
            $form->image('image', '图标')->move('rbcare/image/machneCategory')->rules('required');
            $form->switch('visible', '是否可见')->rules('required');
            // $form->switch('faq_enable', '是否可见')->rules('required');
            $form->display('updated_at', '更新时间');
        });
    }

}
