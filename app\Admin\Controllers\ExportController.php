<?php


namespace App\Admin\Controllers;


use App\Http\Controllers\Controller;
use App\Models\ExportRecord;
use App\Models\Order;
use App\Models\OrderExtend;
use App\Models\OrderLog;
use App\Models\PayOrder;
use App\Traits\ExcelExport;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

class ExportController extends Controller
{

    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('导出下载页面');
            $content->description('');

            $content->body($this->grid());
        });
    }


    protected function grid()
    {
        return Admin::grid(ExportRecord::class, function (Grid $grid) {
            $grid->model()->whereRaw("TO_DAYS(created_at) = TO_DAYS(NOW())");
            $grid->column("id", "ID");
            $grid->column("title", "标题");
            $grid->column("url", "下载链接")->link();
            $status = [0 => "未完成", 1 => "已完成，可下载"];
            $grid->column('status', "是否可下载")->display(function ($data) use ($status) {
                return $status[$data];
            });
            $grid->column("created_at", "创建时间");
            $grid->disableCreation();
            $grid->disableExport();
            $grid->tools(function ($tool) {
                $tool->batch(function ($batch) {
                    $batch->disableDelete();
                });
            });
            $grid->actions(function ($action) {
                $action->disableDelete();
                $action->disableEdit();
            });
            $grid->disableActions();
        });
    }


    public function post_repair_manage_export()
    {
        $param = Request::all();
        if (!$param) {
            $err = ["ok" => 0, "msg" => "请先按条件搜索"];

            return $err;
        }
        $where = "1=1 ";
        $repeat_order = 0;
//        dd($param);
        if (array_key_exists("come_exp_sn", $param) && $param['come_exp_sn']) {
            $where = $where . " AND o.come_exp_sn like '%%" . $param['come_exp_sn'] . "%%'";
        }
        if (array_key_exists("sn", $param) && $param['sn']) {
            $where = $where . " AND o.sn like '%%" . $param['sn'] . "%%'";
        }
        if (array_key_exists("barcode", $param) && $param['barcode']) {
            $where = $where . " AND o.barcode like '%%" . $param['barcode'] . "%%'";
        }
        if (array_key_exists("name", $param) && $param['name']) {
            $where = $where . " AND o.name like '%%" . $param['name'] . "%%'";
        }
        if (array_key_exists("status", $param) && $param['status']) {
            $where = $where . " AND o.status = " . $param['status'];
        }
        if (array_key_exists("quality", $param) && $param['quality']) {
            $where = $where . " AND o.quality = " . $param['quality'];
        }
        if (array_key_exists("model_name", $param) && $param['model_name']) {
            $where = $where . " AND o.model_name = '" . $param['model_name'] . "'";
        }
        if (array_key_exists("type", $param) && $param['type']) {
            $where = $where . " AND o.type = " . $param['type'];
        }
        if (array_key_exists("check_man", $param) && $param['check_man']) {
            $where = $where . " AND o.check_man = " . $param['check_man'];
        }
        if (array_key_exists("repeat_order", $param) && $param['repeat_order']) {
            $where = $where . " AND o.repeat_order = " . $param['repeat_order'];
            $repeat_order = $param['repeat_order'];
        }
        if (array_key_exists("created_at", $param) && $param['created_at']['start'] &&
            $param['created_at']['end']) {
            $where = $where . " AND o.created_at between '" . $param['created_at']['start'] . "' AND '" .
                $param['created_at']['end'] . "'";
        }
//        if (array_key_exists("created_at", $param) && $param['created_at']['start'] &&
//            $param['created_at']['end']){
//            $where = $where." AND o.created_at between '".$param['created_at']['start']."' AND '".
//                $param['created_at']['end']."'";
//        }
        if (array_key_exists("updated_at", $param) && $param['updated_at']['start'] &&
            $param['updated_at']['end']) {
            $where = $where . " AND o.updated_at between '" . $param['updated_at']['start'] . "' AND '" .
                $param['updated_at']['end'] . "'";
        }
        if (array_key_exists("updated_at_last", $param) && $param['updated_at_last']['start'] &&
            $param['updated_at_last']['end']) {
            $where = $where . " AND o.updated_at_last between '" . $param['updated_at_last']['start'] . "' AND '" .
                $param['updated_at_last']['end'] . "'";
        }
        if (array_key_exists("receive_time", $param) && $param['receive_time']['start'] &&
            $param['receive_time']['end']) {
            $where = $where . " AND o.receive_time between '" . $param['receive_time']['start'] . "' AND '" .
                $param['receive_time']['end'] . "'";
        }
        if (array_key_exists("auditor_user", $param) && $param['auditor_user']['name']) {
            $where = $where . " AND u2.name like '%%" . $param['auditor_user']['name'] . "%%'";
        }
        if (array_key_exists("order_extend", $param) && $param['order_extend']['print_man']) {
            $where = $where . " AND oe.print_man = " . $param['order_extend']['print_man'];
        }
//        dd($where);
//        $data = DB::table('order as o')
//            ->leftjoin('admin_users as u', 'o.auditor', '=', 'u.id')
//            ->whereRaw($where)->first();
        $now = date('Y-m-d H:i:s');
        $title = '寄修订单信息' . date('YmdHis');
        DB::table('export_record')->insert(['title' => $title, 'sql' => $where, 'status' => 0, 'repeat_order' => $repeat_order, 'created_at' => $now]);
//        $export_records = DB::table('export_record')->where('status', 0)->select('id','title', 'sql')->get()->toArray();
//        if ($export_records){
//            foreach ($export_records as $record){
//                $data = $data = DB::table('order as o')
//                    ->leftjoin('admin_users as u', 'o.check_man', '=', 'u.id')
//                    ->leftjoin('order_extend as oe', 'o.sn', '=', 'oe.sn')
//                    ->whereRaw($record->sql)->select('o.id', 'o.sn','o.status','o.barcode','o.serial','o.model_name',
//                        'o.color','o.in_period','o.has_warranty','o.reason','o.damage','o.name','o.phone',
//                        'o.province','o.city','o.district','o.come_exp_type','o.rb_come_exp_sn','o.come_exp_sn',
//                        'o.come_exp_com','o.audit_opinion','o.pay_com','o.pay_sn','o.rb_pay_sn',
//                        'u.name as check_user_name','o.deal','o.accessory_cast','o.staff_cast','o.amount',
//                        'o.pay_amount','o.deal_remark','o.receive_case','o.rb_go_exp_sn','o.go_exp_sn',
//                        'o.go_exp_com','o.created_at','o.updated_at_last','o.is_agency','o.repeat_order',
//                        'o.repeat_remark','o.type','oe.pay_remark','oe.backstage_remark','oe.overhaul_remark')->get()->toArray();
//
//            }
//
//        }
        return ['ok' => 1, 'msg' => '任务生成中，请到导出页面查询任务情况。'];
    }

    public function repair_material_export_by_pay_time(): array
    {
        // 获取参数
        $req_param = Request::all();
        $err = ['ok' => 0, 'msg' => '请先按条件搜索'];
        if (!$req_param || !array_key_exists('start', $req_param) || !array_key_exists('end', $req_param)) {
            return $err;
        }
        $start_str = $req_param['start'];
        $end_str = $req_param['end'];
        if (!$start_str || !$end_str) {
            $start_str = date('Y-m-d 00:00:00');
            $end_str = date('Y-m-d 23:59:59');
        }
        $start_stamp = strtotime($start_str);
        $end_stamp = strtotime($end_str);
        if (!$start_stamp || !$end_stamp) {
            return ['ok' => 0, 'msg' => '请求参数不正确'];
        }
        $start_value = date('Y-m-d H:i:s', $start_stamp);
        $end_value = date('Y-m-d H:i:s', $end_stamp);
        $start_title = date('Ymd_His', $start_stamp);
        $end_title = date('Ymd_His', $end_stamp);
        // 保存任务
        $pay_time = ['start' => $start_value, 'end' => $end_value];
        $store_param = ['pay_time' => $pay_time];
        $store_param_json = json_encode($store_param, JSON_UNESCAPED_UNICODE);
        $title = '寄修物料导出-' . $start_title . '-' . $end_title;
        $now = date('Y-m-d H:i:s');
        DB::table('export_record')->insert(['title' => $title, 'type' => 'repair_material',
            'params' => $store_param_json, 'status' => 0, 'created_at' => $now]);
        // 返回
        $params = compact('req_param', 'store_param');
        return array_merge(['ok' => 1, 'msg' => '任务生成中，请到导出页面查询任务情况。'], $params);
    }

    public function broken_screen_insurance_export(): array
    {
        // 获取参数
        $req_param = Request::all();
        $err = ['ok' => 0, 'msg' => '请先按条件搜索'];
        if (!$req_param) {
            return $err;
        }
        $paid_at_start_str = $req_param['paid_at_start'];
        $paid_at_end_str = $req_param['paid_at_end'];
        $created_at_start_str = $req_param['created_at_start'];
        $created_at_end_str = $req_param['created_at_end'];
        $audited_at_start_str = $req_param['audited_at_start'];
        $audited_at_end_str = $req_param['audited_at_end'];
        $refund_at_start_str = $req_param['refund_at_start'];
        $refund_at_end_str = $req_param['refund_at_end'];
        if ((!$paid_at_start_str || !$paid_at_end_str) &&
            (!$created_at_start_str || !$created_at_end_str) &&
            (!$audited_at_start_str || !$audited_at_end_str) &&
            (!$refund_at_start_str || !$refund_at_end_str)) {
            $created_at_start_str = date('Y-m-d 00:00:00');
            $created_at_end_str = date('Y-m-d 23:59:59');
        }
        $paid_at_start_stamp = strtotime($paid_at_start_str);
        $paid_at_end_stamp = strtotime($paid_at_end_str);
        $created_at_start_stamp = strtotime($created_at_start_str);
        $created_at_end_stamp = strtotime($created_at_end_str);
        $audited_at_start_stamp = strtotime($audited_at_start_str);
        $audited_at_end_stamp = strtotime($audited_at_end_str);
        $refund_at_start_stamp = strtotime($refund_at_start_str);
        $refund_at_end_stamp = strtotime($refund_at_end_str);
        if ((!$paid_at_start_stamp || !$paid_at_end_stamp) &&
            (!$created_at_start_stamp || !$created_at_end_stamp) &&
            (!$audited_at_start_stamp || !$audited_at_end_stamp) &&
            (!$refund_at_start_stamp || !$refund_at_end_stamp)) {
            return ['ok' => 0, 'msg' => '请求参数不正确'];
        }
        // 保存任务
        $store_param_paid_at = [];
        $store_param_created_at = [];
        $store_param_audited_at = [];
        $store_param_refund_at = [];
        if ($paid_at_start_stamp && $paid_at_end_stamp) {
            $paid_at_start_value = date('Y-m-d H:i:s', $paid_at_start_stamp);
            $paid_at_end_value = date('Y-m-d H:i:s', $paid_at_end_stamp);
            $store_param_paid_at = ['paid_at' => ['start' => $paid_at_start_value, 'end' => $paid_at_end_value]];
        }
        if ($created_at_start_stamp && $created_at_end_stamp) {
            $created_at_start_value = date('Y-m-d H:i:s', $created_at_start_stamp);
            $created_at_end_value = date('Y-m-d H:i:s', $created_at_end_stamp);
            $store_param_created_at = ['created_at' => ['start' => $created_at_start_value, 'end' => $created_at_end_value]];
        }
        if ($audited_at_start_stamp && $audited_at_end_stamp) {
            $audited_at_start_value = date('Y-m-d H:i:s', $audited_at_start_stamp);
            $audited_at_end_value = date('Y-m-d H:i:s', $audited_at_end_stamp);
            $store_param_audited_at = ['audited_at' => ['start' => $audited_at_start_value, 'end' => $audited_at_end_value]];
        }
        if ($refund_at_start_stamp && $refund_at_end_stamp) {
            $refund_at_start_value = date('Y-m-d H:i:s', $refund_at_start_stamp);
            $refund_at_end_value = date('Y-m-d H:i:s', $refund_at_end_stamp);
            $store_param_refund_at = ['refund_at' => ['start' => $refund_at_start_value, 'end' => $refund_at_end_value]];
        }
        $store_param = array_merge($store_param_paid_at, $store_param_created_at, $store_param_audited_at, $store_param_refund_at);
        $store_param_json = json_encode($store_param, JSON_UNESCAPED_UNICODE);
        $title = '碎屏保信息-' . date('YmdHis');
        $now = date('Y-m-d H:i:s');
        DB::table('export_record')->insert(['title' => $title, 'type' => 'broken_screen_insurance',
            'params' => $store_param_json, 'status' => 0, 'created_at' => $now]);
        // 返回
        $params = compact('req_param', 'store_param');
        return array_merge(['ok' => 1, 'msg' => '任务生成中，请到导出页面查询任务情况。'], $params);
    }

}
