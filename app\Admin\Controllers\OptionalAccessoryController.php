<?php

namespace App\Admin\Controllers;

use App\Models\Material;
use App\Models\OptionalAccessory;
use App\Models\OptionalAccessoryCategory;

use App\Admin\Extensions\Tools\QuickPickTool;
use App\Http\Controllers\Controller;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

class OptionalAccessoryController extends Controller
{
    use ModelForm;

    const SWITCH_TEXT = [
        'on' => ['text' => '是', 'value' => 1],
        'off' => ['text' => '否', 'value' => 0],
    ];

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修可自选配件');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('编辑配件');
            $content->description('');

            $content->body($this->form()->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('添加配件');
            $content->description('');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(OptionalAccessory::class, function (Grid $grid) {
            $oacArray = OptionalAccessoryCategory::getAllSelectArray();

            $grid->filter(function ($filter) use ($oacArray) {
                $filter->disableIdFilter();
                $filter->like('name', '名称');
                $filter->is('category_id', '配件类别')
                    ->select($oacArray);
            });

            // 快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => [],
                ],
            ];
            // 筛选条数
            $oaCount = OptionalAccessory::getCategoryCount();
            $opt_i = 0;
            foreach ($oacArray as $oacKey => $oacName) {
                foreach ($oaCount as $id => $count) {
                    if ($oacKey == $id) {
                        $option[$opt_i + 1] = [
                            'name' => $oacName,
                            'param' => [['category_id', '=', $id]],
                            'count' => $count,
                        ];
                        break;
                    }
                }
                $opt_i++;
            }
            // $option[-1]['count'] = OptionalAccessory::where($option[-1]['param'])->count();
            $oaSum = 0;
            foreach ($oaCount as $key => $value) {
                $oaSum += $value;
            }
            $option[-1]['count'] = $oaSum;

            // 自定义状态快捷筛选按钮
            $grid->tools(function ($tools) use ($option) {
                $tools->append(new QuickPickTool($option));
            });

            // 根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])
                    ->orderBy('enable', 'desc')->orderBy('sort', 'desc');
            }
            $grid->model()->where($option[-1]['param'])
                ->orderBy('enable', 'desc')->orderBy('sort', 'desc');

            // 对集合列表统一操作
            $grid->model()->collection(function (Collection $collection) use ($oacArray) {

                // 读取所属分类名称
                foreach($collection as $index => $item) {
                    $item->category_name = $oacArray->get($item->category_id, null);
                }

                // 读取对应物料信息
                $material_ids = array_column($collection->toArray(), 'material_id');
                $material_ids = array_unique($material_ids);
                $materials = Material::find($material_ids);
                $material_map = [];
                foreach($materials as $index => $item) {
                    $material_map[$item->id] = $item;
                }
                foreach($collection as $index => $item) {
                    if (array_key_exists($item->material_id, $material_map)) {
                        $m = $material_map[$item->material_id];
                        $item->material_code = $m->code ?? null;
                        $item->material_price = $m->price ?? null;
                        $item->material_quantity = $m->quantity ?? null;
                    }
                }

                return $collection;
            });
            $display_null = function ($value) {
                return $value ?? '<空>';
            };

            $grid->id('ID')->sortable();

            $grid->column('配件预览图')->display(function() {
                if (property_exists($this, 'image') && !empty($this->image)) {
                    $value = rtrim(config('admin.upload.host'), '/') . '/' . $this->image;
                    $style = 'max-width:200px;max-height:200px;';
                    return '<img src="' . $value . '" alt="' . $this->image .
                        '" class="img img-thumbnail" style="' . $style . '">';
                } else {
                    return '<span><空></span>';
                }
            });

            $grid->category_name('分类')->display($display_null);
            $grid->name('名称')->editable();

            $grid->sell_price('售价（元）')->editable();
            $grid->sold_num('已售数量');
            
            $grid->material_code('物料编码')->display($display_null);
            $grid->material_quantity('物料库存')->display($display_null);
            $grid->material_price('物料顾客价格（元）')->display($display_null);

            $grid->sort('排序')->editable();
            $grid->enable('是否启用')->switch(self::SWITCH_TEXT);

            $grid->created_at('创建时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $script =
<<<script
    $(fn_ready);
    function fn_ready() {
        $(document).on('change', '.material_id', material_change);
        function material_change() {
            let optionText = $(this).find("option:selected").text();
            let selectedPrice = optionText.split('【价格￥')[1].split('】')[0];
            let sellPriceNode = $('#sell_price');
            let originPrice = sellPriceNode.val();
            if (selectedPrice?.length > 0) {
                sellPriceNode.val(selectedPrice);
            }
            console.log({ originPrice, optionText, selectedPrice });
        }
    }
script;
        Admin::script($script);
        return Admin::form(OptionalAccessory::class, function (Form $form) {

            $form->display('id', 'ID');
            $form->text('name', '名称')->placeholder('顾客看到的名称')->rules('required');

            $form->image('image', '配件预览图')
                ->move('rbcare/repair/optional_accessory/accessory_image')
                ->uniqueName()
                ->options([
                    'allowedFileTypes' => '',
                    'msgInvalidFileExtension' => '{name}文件类型不正确，只支持{extensions}文件',
                    'allowedFileExtensions' => ['jpg', 'png', 'jpeg'],
                ])
                ->resize(1920, 1920, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });

            $form->select('category_id', '配件分类')
                ->options(OptionalAccessoryCategory::getEnableSelectArray()->prepend('请选择分类(若没有请先创建)', 0))
                ->rules('required');
            $form->select('material_id', '物料')
                ->options(function ($id) {
                    if ($id) {
                        return Material::where('id', '=', $id)
                            ->select(DB::raw('concat_ws("","【",name,"】【规格:",specification,"】【编码",code,"】【旧编码",old_code,"】【来源：",`from`,"】【库存:",quantity,"】【价格￥",price,"】") as text'), 'id')
                            ->pluck('text', 'id');
                    }
                })
                ->ajax('/admin/material_search')
                ->placeholder('输入物料的名称或规格或编码');

            $form->currency('sell_price', '售价')->symbol('￥')
                ->rules('required')
                ->help('用户购买该配件的单价');

            $form->text('sort', '排序')->default(0);
            $form->switch('enable', '是否启用')->states(self::SWITCH_TEXT);
        });
    }
}
