<?php


namespace App\Admin\Controllers;


use App\Http\Controllers\Controller;
use App\Models\BrokenScreenInsuranceStandard;
use App\Models\Machine;
use App\Models\MachineCategory;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;
use function foo\func;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\MessageBag;
use Symfony\Component\Yaml\Tests\B;

class BrokenScreenInsuranceStandardController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {
            $content->header('投保标准表');
            $content->description('投保标准列表');
            $content->body($this->grid());
        });
    }


    /***
     * Edit interface
     *
     * @return Content
     **/
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {
            $content->header('投保标准表');
            $content->description('投保标准表编辑');
            $content->body($this->form($id)->edit($id));
        });
    }


    public function create()
    {
        return Admin::content(function (Content $content) {
            $content->header('投保标准表');
            $content->description('投保标准创建');
            $content->body($this->form());
        });
    }

    /***
     * Make a grid builder
     *
     * @return Grid
     *
     */
    public function grid()
    {
        return Admin::grid(BrokenScreenInsuranceStandard::class, function (Grid $grid) {
            $model_id = Input::get('model_id');
            $category_id = Input::get('category_id');

            $grid->tools(function ($tools) use ($category_id, $model_id) {

                $button = <<<EOF
                    <div class="btn-group pull-right" style="margin-right: 10px">
                        <a href="standard_category" class="btn btn-sm btn-warning" >
                            <i class="fa fa-back"></i>返回
                        </a>
                    </div>
                    <div class="btn-group pull-right" style="margin-right: 10px">
                        <a
                            href="broken_screen_insurance_standard/create?category_id=$category_id&model_id=$model_id"
                            class="btn btn-sm btn-success"
                        >
                            <i class="fa fa-save"></i>新增
                        </a>
                    </div>
EOF;
                $tools->append($button);
            });

            $grid->filter(function ($filter) use ($category_id) {
                $filter->disableIdFilter();
                //$filter->is('category_id', '机型品类')->select(MachineCategory::pluck('name','id'))
                //    ->load('model_id', '/admin/machine_type/category');
                if ($category_id) {
                    $filter->is('model_id', '投保机型')->select(
                        Machine::where('category_id', $category_id)
                            ->where('visibility', 1)
                            ->orderBy('name', 'asc')
                            ->pluck('name', 'model_id'));
                } else {
                    $filter->is('model_id', '投保机型')
                        ->select(Machine::where('visibility', 1)->orderBy('name', 'asc')->pluck('name', 'model_id'));
                }
            });

            $grid->disableCreation();
            $grid->disableExport();

            if ($model_id) {
                $grid->model()->where('model_id', $model_id)->orderBy('id', 'desc');
            }

            $grid->id('ID')->sortable();
            $grid->column('model_name.name', '机型');
            $grid->column('machine_category.name', '机型品类');
            $grid->column('标准类型')->display(function () {
                if (array_key_exists($this->standard_id, BrokenScreenInsuranceStandard::STANDARD_TYPE)) {
                    return BrokenScreenInsuranceStandard::STANDARD_TYPE[$this->standard_id];
                } else {
                    return '---';
                }
            });
            $grid->name('标准名称');
            $grid->amount('用户价格');
            $grid->pay_amount('支付价格');
            $grid->month('保修月份');
            $grid->insurance_times('可保次数');
            $grid->activity_date('活动时间')->display(function () {
                if ($this->standard_id == BrokenScreenInsuranceStandard::STANDARD_TYPE_TIME_LIMITED) {
                    return $this->activity_start_date . ' 至 ' . $this->activity_end_date;
                } else {
                    return '长期有效';
                }
            });
            $grid->visible('是否可见')->switch(['on' => ['text' => '是', 'value' => 1], 'off' => ['text' => '否', 'value' => 0]]);

            $grid->actions(function ($actions) use ($category_id, $model_id) {
                $actions->disableEdit();
                $actions->disableDelete();
                $id = $actions->row->id;
                $button = <<<EOF
                        <a href="broken_screen_insurance_standard/$id/edit?category_id=$category_id&model_id=$model_id">
                            <i class="fa fa-edit"></i>
                        </a>
EOF;
                $actions->append($button);
            });

        });
    }


    public function form($id = null)
    {
        $standard_k = array_keys(BrokenScreenInsuranceStandard::STANDARD_TYPE);
        $standard_v = array_values(BrokenScreenInsuranceStandard::STANDARD_TYPE);
        $standard_k_json = json_encode($standard_k, JSON_UNESCAPED_UNICODE);
        $standard_v_json = json_encode($standard_v, JSON_UNESCAPED_UNICODE);
        $script = <<<js

    // 读取 投保标准 映射
    const standard_k_json = '$standard_k_json';
    const standard_v_json = '$standard_v_json';
    const standard_k = JSON.parse(standard_k_json);
    const standard_v = JSON.parse(standard_v_json);
    const standard_m = new Map();
    for(let i = 0; i < standard_k.length; i++) {
        standard_m.set(String(standard_k[i]), standard_v[i]);
    }

    function get_standard_id() {
        return $('.standard_id').val();
    }

    function get_name_value() {
        return $('.name').val();
    }

    function set_name_value(new_value) {
        $('.name').val(new_value);
    }
    
    function check_name() {
        let s_id = get_standard_id();
        let s_name = standard_m.get(s_id);
        let name = get_name_value();
        console.log({ s_id, s_name, name });
        if (!name) {
            set_name_value(s_name);
        } else {
            if (standard_v.includes(name)) {
                set_name_value(s_name);
            }
        }
    }

    check_name();
    $(document).on('change', '.standard_id', check_name);
js;
        Admin::script($script);

        return Admin::form(BrokenScreenInsuranceStandard::class, function (Form $form) use ($id) {
            $category_id = Input::get('category_id');
            $model_id = Input::get('model_id');
            if ($category_id) {
                $machine_category = MachineCategory::where([['visible', 1], ['id', $category_id]])->pluck('name', 'id');
            } else {
                $machine_category = MachineCategory::where('visible', 1)->pluck('name', 'id');
            }

            $form->select('category_id', '机型品类')
                ->options($machine_category)
                ->load('model_id', '/admin/machine_type/category')
                ->rules('required');
            $form->select('model_id', '机型')->options(function () use ($model_id) {
                return Machine::where([['visibility', 1], ['model_id', $model_id]])->pluck('name', 'model_id as id');
            })->rules('required');

            $form->select('standard_id', '投保类型')
                ->options(BrokenScreenInsuranceStandard::STANDARD_TYPE)
                ->default(BrokenScreenInsuranceStandard::STANDARD_TYPE_A)
                ->rules('required');
            $form->text('name', '指定标准名称')->rules('required');

            $form->currency('amount', '用户价格')->symbol('￥');
            $form->currency('pay_amount', '支付金额')->symbol('￥');

            $form->text('month', '保单保修月份')->rules('required');
            $form->select('insurance_times', '保单可保次数')->options([1 => '1次', 2 => '2次'])->default(1);
            $form->switch('visible', '是否可见')->default(1);

            $form->date('activity_start_date', '活动开始日期')->help('限时活动档需填写');
            $form->date('activity_end_date', '活动截止时间')->help('限时活动档需填写');

            $form->saving(function ($form) {
                if ($form->standard_id) {
                    // 非限时活动
                    if ($form->standard_id != BrokenScreenInsuranceStandard::STANDARD_TYPE_TIME_LIMITED) {
                        if (!$form->amount || !$form->pay_amount || $form->amount == '0.00' || $form->pay_amount == '0.00') {
                            $error = new MessageBag([
                                'title' => '错误',
                                'message' => '非限时活动投档价格不能为0元'
                            ]);
                            return back()->with(compact('error'))->withInput();
                        }
                    }

                    // 是限时活动
                    if ($form->standard_id == BrokenScreenInsuranceStandard::STANDARD_TYPE_TIME_LIMITED) {
                        if (!$form->activity_start_date || !$form->activity_end_date) {
                            $error = new MessageBag([
                                'title' => '错误',
                                'message' => '限时活动档需填写起止时间'
                            ]);
                            return back()->with(compact('error'))->withInput();
                        }
                    }
                }

            });
        });
    }
}