<?php

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use App\Admin\Extensions\Express;
use App\Admin\Extensions\Express\EmsExpress;
use App\Models\MachineAccessoryTree;
use App\Models\MachineMalfunction;
use App\Models\Material;
use App\Models\Order;
use App\Models\PostRepairMaterial;
use Barryvdh\Debugbar\Facade as DebugBar;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Layout\Content;
use Illuminate\Http\Request;

class TestController extends Controller
{

    const ONE_WEEK_TIME = 3600 * 24 * 7;
    const ALL_USER_NOTICE_TYPE = 1;
    const REPAIR_USER_NOTICE_TYPE = 2;

    const YZ_TEST_HEADER = '邮政接口测试';

    public function yz_billno()
    {
        $express = new EmsExpress();
        $ret = $express->get_billno();
        return Admin::content(function (Content $content) use ($ret) {
            $content->header(self::YZ_TEST_HEADER);
            $content->description('取号接口');

            $content->body(json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        });
    }

    public function yz_create_order()
    {
        $express = new EmsExpress();
        $data = array(
            'sn' => '163412',
            'one_bill_flag' => 0,
            'j_contact' => '读书郎客户服务中心',
            'j_tel' => '4008325888',
            'j_province' => '广东省',
            'j_city' => '中山市',
            'j_county' => '五桂山街道',
            'j_address' => '广东省中山市五桂山区长逸路38号读书郎A栋3楼',
            'd_contact' => '马学长',
            'd_tel' => '13643072085',
            'd_province' => '广东省',
            'd_city' => '汕头市',
            'd_county' => '潮阳区',
            'd_address' => '和平镇下寨市场大街217号',
        );
        $ret = $express->create_order($data);
        return json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    public function yz_track()
    {
        $go_exp_sn = Request::get('readboy_sn');
        if (empty($go_exp_sn)) {
            $go_exp_sn = '9892019112608';
        }

        $express = new EmsExpress();
        $ret = $express->get_route($go_exp_sn);
        DebugBar::info(compact('go_exp_sn', 'ret'));
        return Admin::content(function (Content $content) use ($ret) {
            $content->header(self::YZ_TEST_HEADER);
            $content->description('物流路由轨迹接口');

            $content->body(json_encode($ret, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        });
    }

    public function response_request(Request $request)
    {
        $req_method = $request->method();
        $req_header = $request->header();
        $req_all = $request->all();
        $req_param = compact('req_method', 'req_header', 'req_all');
        DebugBar::info($req_param);
        return Admin::content(function (Content $content) use ($request, $req_param) {
            $content->header('请求测试接口');
            $content->description('接收请求并原样返回');

            $content->body(json_encode($req_param, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        });
    }

    public function edit_test()
    {
        return Admin::content(function (Content $content) {
            $content->header('表单测试页面');

            $content->body($this->form_test());
        });
    }

    protected function form_test(): Form
    {
        //Form::registerBuiltinFields();
        return Admin::form(Order::class, function (Form $form) {
            $now = date('Y-m-d H:i:s');
            //tips: 组件不显示:中间件要注册支持的field,源码Builder中会移除model保留field
            $form->display('no', 'No')->help('mt_rand')->value(mt_rand());
            $form->divider();
            $form->text('title', '标题')->default('默认标题');
            $form->divider();
            $form->datetime('statistic_time', '统计时间')->value($now);
            $form->datetime('statistic_time', '统计时间');
            $form->divider();

            $id = 57272;
            $type = 1;
            $form->hasMany('repair_material', '维修配件列表',
                function (Form\NestedForm $form) use ($id, $type, $now) {
                    $modelId = Order::where('id', '=', $id)->value('model_id');
                    $pr_sn = Order::where('id', '=', $id)->value('sn');
                    $form->select('charge_type', '收费类型')->options(function ($value) use ($type) {
                        if ($type == 2) {
                            return Order::charge_type;
                        } else {
                            return [1 => '客户价格'];
                        }
                    });
                    $form->select('mat_id', '维修配件')
                        ->options(MachineAccessoryTree::model_options($modelId))
                        ->load('malfunction_id', admin_url('repair_check_malfunction'));
                    $form->select('malfunction_id', '配件故障')->options(function ($value) {
                        $data = MachineMalfunction::where('id', $value)->get()->toArray();
                        $ret = array();
                        if (count($data) > 0) {
                            foreach ($data as $d) {
                                $ret[$d['id']] = $d['title'];
                            }
                        }
                        return $ret;
                    });
                    $form->select('material_id', '维修物料')->options(function ($value) use ($pr_sn, $form, $type) {
                        $data = Material::where('id', $value)->get()->toArray();
                        $ret = array();
                        $charge_type = PostRepairMaterial::where([['pr_sn', '=', $pr_sn], ['material_id', '=', $value]])->value('charge_type');

                        if (count($data) > 0) {
                            foreach ($data as $d) {
                                $from = $d['from'] == 1 ? '新仓库' : '旧仓库';
                                if ($charge_type == 2) {
                                    $ret[$d['id']] = $d['name'] . $d['specification'] . '|编码:' . $d['code'] . '|旧编码:' . $d['old_code'] .
                                        '|仓库:' . $from . '|库存:' . $d['quantity'] . '|价格 : ' . $d['price_first'];
                                } else {
                                    $ret[$d['id']] = $d['name'] . $d['specification'] . '|编码:' . $d['code'] . '|旧编码:' . $d['old_code'] .
                                        '|仓库:' . $from . '|库存:' . $d['quantity'] . '|价格 : ' . $d['price'];
                                }
                            }
                        }
                        return $ret;
                    });
                    $form->number('count', '数量')->default(1);
                    $form->select('is_charge', '是否收费')->options([1 => '收费', 0 => '不收费'])->default(1);

                    $form->hidden('created_at', '创建时间')->value($now);
                    $form->hidden('statistic_time', '统计时间')->value($now);
                });

            $form->setAction('/admin/test/response_request');
            //$fT = new Form\Field\Text('title', ['title']);
            //$fT->value('默认标题');
            //$form->pushField($fT);

            DebugBar::info(compact('form'), ['Form::$availableFields' => Form::$availableFields]);
            return $form;
        });
    }

    public function sf_test(Request $request)
    {
        // $go_exp_sn = $request->get('readboy_sn');
        // $data = request()->all();
        $rb_exp_sn = $request->get('rb_exp_sn');
        $exp_sn = $request->get('exp_sn');

        // $express = new Express();
        // $result = $express->create_express_order($data);
        $result = Express::query_order($rb_exp_sn, $exp_sn);

        $ret = json_encode(['result' => $result], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        // $ret = $result;
        return Admin::content(function (Content $content) use ($ret) {
            $content->header('顺丰接口对接测试');
            $content->description('副标题');

            $content->body($ret);
        });
    }

    public function express_d_address_test(Request $request)
    {
        $d_address = $request->get('d_address');
        $sn = $request->get('sn');
        $data = [];
        $data['custid'] = env('SF_CUSTID');//公司支付
        $data['express_type'] = 103;
        $data['is_docall'] = 1;
        $data['pay_method'] = 2;
        $data['type'] = 1;
        $data['remark'] = '读书郎客户 运费到付';
        $data['d_address'] = $d_address;
        $data['sn'] = $sn;

        $express = new Express();
        $xml = $express->create_express_xml($data);

        $ret = $xml;
        return Admin::content(function (Content $content) use ($ret) {
            $content->header('下单地址替换测试');
            $content->description('副标题');

            $content->body($ret);
        });
    }
}
