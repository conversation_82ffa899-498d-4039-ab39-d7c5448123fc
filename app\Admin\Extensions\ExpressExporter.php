<?php

namespace App\Admin\Extensions;

use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use App\Models\Order;
use App\Models\PostRepairExpress;

class ExpressExporter extends AbstractExporter
{
    public function export()
    {
        $filename = '快递信息';
        $data = $this->getData();
        if (!empty($data)) {
            $titles = [
                '下单日期',
                '寄修单号',
                '快递单号',
                '快递公司',
                '寄来还是寄走',
                '收件人',
                '寄件人',
                '费用',
                //'重量',
                '机型',
            ];
            $formatData = $this->getFormatData($data);
            array_unshift($formatData, $titles);
            ExcelExportTrait::exportToExcel($filename, $formatData);
        }
    }

    private function getFormatData($data)
    {
        foreach ($data as $row) {
            $sn = $row['pr_sn'];
            $model_name = Order::where('sn', '=', $sn)->value('model_name');
            $json = json_decode($row['data'], true);
            //$model_name_json = json_decode($model_name.);

            $row = [
                $row['created_at'],
                $row['pr_sn'],
                $row['exp_sn'],
                $row['com'],
                PostRepairExpress::TYPE[$row['type']],
                $json['d_contact'],
                $json['j_contact'],
                $row['fee'],
                $model_name
            ];
            $formatData[] = $row;
        }

        return $formatData;
    }
}