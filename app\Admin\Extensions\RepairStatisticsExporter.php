<?php
/**
 * Created by PhpStorm.
 * User: Xian
 * Date: 2017/12/18
 * Time: 9:56
 */

namespace App\Admin\Extensions;

use App\Services\Admin\ModelCategoryService;
use App\Services\Admin\RepairStatisticsService;
use Excel;

class RepairStatisticsExporter
{
    protected $service;

    protected $modelCategoryService;

    protected $categoryNames;

    public function __construct(RepairStatisticsService $service, ModelCategoryService $modelCategoryService)
    {
        $this->service = $service;
        $this->modelCategoryService = $modelCategoryService;
        $this->categoryNames = $this->modelCategoryService->getCachedModelCategory()
            ->pluck('name')->toArray();
    }

    public function exportSummary($gender, $createAt)
    {
        $fileName = "导出";
        $repairBills = $this->service->getBaseData($gender, $createAt);
        $bills = $this->service->groupBillsByTopAgency($repairBills);
        $result = $this->service->groupTopAgencyBillsByUid($bills);
        $allSummary = $this->service->getTopAgencySummary($repairBills);

        Excel::create($fileName, function ($excel) use ($result, $allSummary) {
            foreach ($result as $key => $value) {
                $excel->sheet($key, function ($sheet) use ($value) {
                    $data = $this->getTopAgencyExcelData($value);
                    $sheet->rows($data);
                    $sheet->setAutoSize(true);
                });
            }
            $excel->sheet('总表', function ($sheet) use ($allSummary) {
                $data = $this->getAllSummaryExcelData($allSummary);
                $sheet->rows($data);
                $sheet->setAutoSize(true);
            });
        })->export('xls');

    }

    protected function getAllSummaryExcelData($data)
    {
        $totalCount = ['bill' => 0, 'worker' => 0];
        foreach ($data as $key => $value) {
            $totalCount['bill'] += $value['bill_count'];
            $totalCount['worker'] += $value['repair_man_count'];
        }
        $commonHeader = array_merge(array_merge(['区域', '人数'], $this->categoryNames), [
            '保内量',
            '保内金额',
            '保内占比',
            '保外量',
            '保外金额',
            '保外占比',
            '合计',
            '区域比率',
            '市场系数',
            '区域考核得分',
            '人均',
            '人均达标考核',
        ]);
        $result = [];

        $personAverageSum = $this->getPersonAverageSum($data);

        foreach ($data as $key => $value) {
            $array = array_merge([
                $value['top_agency'],
                $value['repair_man_count'],
            ], array_values($value['category_distribution']));

            $result[] = array_merge($array, [
                $value['warranty_expiration_count'],
                $value['warranty_expiration_cost'],
                $value['warranty_expiration_rate'],
                $value['warranty_period_count'],
                $value['warranty_period_cost'],
                $value['warranty_period_rate'],
                $value['bill_count'],
                number_format($value['bill_count'] / $totalCount['bill'], 2) * 100 . '%',
                $value['market_coefficients'],
                $value['market_coefficients'] ?
                    (number_format($value['bill_count'] / (float)$value['market_coefficients'], 4)) * 100 : '没录入市场占比',
                number_format($value['bill_count'] / $value['repair_man_count'], 2),
                number_format($value['bill_count'] / $value['repair_man_count'] / $personAverageSum, 2),
            ]);
        }
        array_unshift($result, $commonHeader);

        return $result;
    }

    protected function getPersonAverageSum($data)
    {
        $personAverageSum = 0;
        foreach ($data as $value) {
            $temp = number_format((int)$value['bill_count'] / ((int)$value['repair_man_count'] ?: 1), 2);
            $personAverageSum += (int)$temp;
        }

        return $personAverageSum;
    }

    protected function getTopAgencyExcelData($data)
    {
        $commonHeader = array_merge(array_merge(['区域', '终端名称', '维修师姓名'], $this->categoryNames), [
            '保内量',
            '保内金额',
            '保内占比',
            '保外量',
            '保外金额',
            '保外占比',
            '合计',
        ]);
        $result = [];
        foreach ($data as $key => $value) {
            $array = array_merge([
                $value['agency'],
                $value['endpoint_name'],
                $value['repair_man_name'],
            ], array_values($value['category_distribution']));

            $result[] = array_merge($array, [
                $value['warranty_expiration_count'],
                $value['warranty_expiration_cost'],
                $value['warranty_expiration_rate'],
                $value['warranty_period_count'],
                $value['warranty_period_cost'],
                $value['warranty_period_rate'],
                $value['bill_count'],
            ]);
        }
        array_unshift($result, $commonHeader);

        return $result;
    }
}