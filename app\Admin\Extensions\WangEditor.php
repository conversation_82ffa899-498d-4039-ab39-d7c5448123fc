<?php

namespace App\Admin\Extensions;

use Encore\Admin\Form\Field;

class WangEditor extends Field
{
    protected $view = 'admin.wang-editor';

    protected static $css = [
        'packages/wangEditor-3.0.8/release/wangEditor.min.css',
    ];

    protected static $js = [
        'packages/wangEditor-3.0.8/release/wangEditor.min.js',
    ];

    public function render()
    {
        $name = $this->formatName($this->column);
        $uploadRoute = isset($this->attributes['route']) ? route($this->attributes['route']) : '';
        $menu = isset($this->attributes['menu']) ? $this->attributes['menu'] : json_encode(false);
        $token = csrf_token();
        $this->script = <<<EOT

            var E = window.wangEditor
            var editor = new E('#{$this->id}');
            editor.customConfig.onchange = function (html) {
                $('input[name=$name]').val(html);
            }
            editor.customConfig.uploadImgParams = {
                _token: '$token',
                
            }
            if ($menu) {
                editor.customConfig.menus = $menu;
            }
            editor.customConfig.uploadImgServer = '$uploadRoute';
            editor.customConfig.uploadFileName = 'images[]';
            editor.customConfig.uploadImgTimeout = 30000;
            editor.customConfig.uploadImgMaxSize = 3 * 1024 * 1024;
            editor.create()


EOT;

        return parent::render();
    }
}