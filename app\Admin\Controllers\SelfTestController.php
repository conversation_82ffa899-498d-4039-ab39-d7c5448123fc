<?php

namespace App\Admin\Controllers;

use App\Models\SelfTest;

use App\Models\Damage;
use App\Models\MachineCategory;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Http\Request;

class SelfTestController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('自检问题');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('自检问题');
            $content->description('');

            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('自检问题');
            $content->description('');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(SelfTest::class, function (Grid $grid) {

            $grid->id('ID')->sortable();
            $grid->title('标题');
            $grid->machine_category_id('机型品类')->display(function($value){
                if ($value) {
                    return MachineCategory::find($value)->name;
                }
                return '无';
            });
            $grid->damage_id('故障类型')->display(function($value){
                if ($value) {
                    $data = Damage::find($value);
                    if ($data){
                        return $data->title;
                    }
                    return '无';
                }
                return '无';
            });
            $grid->visible('是否可见')->switch();
            $grid->created_at('创建时间');
            $grid->updated_at('更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id = null)
    {
        return Admin::form(SelfTest::class, function (Form $form) use ($id) {

            $form->display('id', 'ID');
            $form->text('title', '标题')->rules('required');
            $form->editor('content', '内容')->attribute(['route' => 'config_picture_upload']);
            $form->select('machine_category_id', '机型品类')->options(MachineCategory::all()->pluck('name', 'id')->prepend('请选择', 0))->rules('required')->load('damage_id', admin_url('category_damage'));
            $form->select('damage_id', '故障类型')->options(function($value) use ($form) {
                return Damage::where('machine_category_id', $form->model()->machine_category_id)->get()->pluck('title', 'id');
            })->rules('required');
            $form->switch('visible', '是否可见')->states(['on' => ['text' => '可见'], 'off' => ['text' => '不可见']]);
            $form->switch('top', '是否置顶')->states(['on' => ['text' => '置顶'], 'off' => ['text' => '不置顶']]);
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }


    public function category_damage(Request $request) {
        $machine_category_id = $request->get('q');
        $data = Damage::where('machine_category_id', $machine_category_id)->get();
        $ret = array();
        if ($data) {
            foreach ($data as $d) {
                $ret[$d->id]['id'] = $d->id;
                $ret[$d->id]['text'] = $d->title;
            }
        }
        return $ret;
    }


    public function uploadPicture(Request $request)
    {
        $pathList = [];
        foreach ($request->file('images') as $key => $value) {
            $path = $value->store('rbcare/repair/self_test', 'oss');
            $pathList[] = config('admin.upload.host') . $path;
        }
        //这个是专门提供给富文本编辑器的json响应,要这样子的格式前端编辑器才会认为你成功上传了图片
        $responseData = ['errno' => 0, 'data' => $pathList];

        return response()->json($responseData);
    }
}
