<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\Tools\Button;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Admin\Extensions\Tools\InformTool;

use App\Models\AgentOrder;
use App\Models\MachineAccessory;
use App\Models\MachineAccessoryRelation;
use App\Models\MachineAccessoryTree;
use App\Models\MachineMalfunction;
use App\Models\Material;
use App\Models\Order;
use App\Models\OrderRemark;
use App\Models\OrderExtend;
use App\Models\OrderOldAddress;
use App\Models\PostRepairAccessory;
use App\Models\PostRepairMalfunction;
use App\Models\PostRepairOptionalAccessory;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;

use App\User;
use App\Http\Controllers\Controller;
use App\Models\OrderLog;
use Encore\Admin\Controllers\ModelForm;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\MessageBag;

class PostRepairController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修订单总览');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修订单');
            $content->description('编辑');

            $content->body($this->form($id)->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修总览--订单详情');
            $order = Order::where(['id' => $id])->first();

            $repeat_count = Order::where(['barcode' => $order->barcode, 'status' => Order::ORDER_FINISH])->count();
            $order->repeat_order_count = $repeat_count;

            $order_old_address = OrderOldAddress::where(['sn' => $order->sn])->first();
            $order_extend = OrderExtend::where(['sn' => $order->sn])->first();
            $pr_material = DB::table('pr_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_material.mat_id')
                ->leftjoin('machine_malfunction', 'machine_malfunction.id', '=', 'pr_material.malfunction_id')
                ->leftjoin('material', 'material.id', '=', 'pr_material.material_id')
                ->where('pr_material.pr_sn', $order['sn'])
                ->select('machine_accessory_tree.title as title', 'machine_malfunction.title as malfunction',
                    'material.price as price', 'material.price_first as price_first', 'pr_material.price_in',
                    'pr_material.count as count', 'pr_material.charge_type')
                ->get()
                ->toArray();
            $pr_used_material = DB::table('pr_used_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_used_material.mat_id')
                ->leftjoin('material', 'material.id', '=', 'pr_used_material.material_id')
                ->where('pr_used_material.pr_sn', $order['sn'])
                ->select('material.name as name', 'material.price as price', 'material.price_first as price_first',
                    'pr_used_material.count as count', 'material.code as code', 'material.old_code as old_code',
                    'material.specification as specification', 'material.from as from',
                    'pr_used_material.is_charge', 'pr_used_material.charge_type', 'pr_used_material.price_in')
                ->get()
                ->toArray();
            $pr_oa = PostRepairOptionalAccessory::getShowListBySn($order->sn);
            $agent_order = null;
            if ($order->type == 3) {
                $agent_order = AgentOrder::rightJoin('agent_order_correlation as aoc', 'agent_order.sn', '=', 'aoc.agent_order_sn')
                    ->where([['aoc.order_sn', $order->sn]])->select('agent_order.*')->first();
            }

            if (!empty($order_extend)) {
                // 签收状态与备注
                $order_extend->sign_in_status_name = Order::SIGN_IN_STATUS[$order_extend->sign_in_status];
                // 优先级
                $order_extend->order_priority_span = Order::order_priority_span($order_extend->order_priority);
            }

            // 获取通话记录
            $callLog = DB::table('call_log')
                ->join('admin_users_ykf as auy', 'call_log.agent', 'auy.seat_number')
                ->join('admin_users as au', 'auy.uid', 'au.id')
                ->where('call_log.bill_id', $order->sn)
                ->groupBy('call_log.call_sheet_id')
                ->orderBy('call_log.id', 'desc')
                ->get();

            // 获取最新的维修知会记录
            $orderRemarkInfo = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
                ->where('order_remark.sn', $order->sn)
                ->where('order_remark.type', 3)
                ->orderBy('order_remark.id', 'desc')
                ->select('au.name', 'order_remark.created_at', 'order_remark.remark', 'order_remark.status')
                ->first();

            if (!empty($orderRemarkInfo)) {
                $orderRemarkInfo['status_name'] = Order::STATUS[$orderRemarkInfo['status']];
            }

            // 获取通话记录
            $callLog1 = DB::table('call_log')
                ->join('admin_users_ykf as auy', 'call_log.agent', 'auy.seat_number')
                ->join('admin_users as au', 'auy.uid', 'au.id')
                ->where('call_log.bill_id', $order->sn)
                ->groupBy('call_log.call_sheet_id')
                ->orderBy('call_log.id', 'desc')
                ->get();

            // 获取最新的客服知会记录
            $orderRemarkInfo1 = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
                ->where('order_remark.sn', $order->sn)
                ->where('order_remark.type', 1)
                ->orderBy('order_remark.id', 'desc')
                ->select('au.name', 'order_remark.created_at', 'order_remark.remark', 'order_remark.status')
                ->first();

            if (!empty($orderRemarkInfo1)) {
                $orderRemarkInfo1['status_name'] = Order::STATUS[$orderRemarkInfo1['status']];
            }

            // 获取所有客服备注
            $allOrderRmarkInfo1 = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
                ->where('order_remark.type', 1)
                ->where('order_remark.sn', $order->sn)
                ->orderBy('order_remark.id', 'desc')
                ->select('au.name', 'order_remark.*')
                ->get();

            foreach ($allOrderRmarkInfo1 as $key => $value) {
                $allOrderRmarkInfo1[$key]['status_name'] = Order::STATUS[$value['status']];
                $allOrderRmarkInfo1[$key]['connect_name'] = Order::CONNECT[$value['connect']];
            }

            // 获取所有维修知会备注
            $allOrderRmarkInfo = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
                ->where('order_remark.type', 3)
                ->where('order_remark.sn', $order->sn)
                ->orderBy('order_remark.id', 'desc')
                ->select('au.name', 'order_remark.*')
                ->get();

            foreach ($allOrderRmarkInfo as $key => $value) {
                $allOrderRmarkInfo[$key]['status_name'] = Order::STATUS[$value['status']];
                $allOrderRmarkInfo[$key]['connect_name'] = Order::CONNECT[$value['connect']];
            }

            $content->body(view('admin/post_repair/view', compact('order', 'pr_material', 'pr_used_material',
                'pr_oa', 'order_old_address', 'order_extend', 'agent_order', 'orderRemarkInfo',
                'callLog', 'orderRemarkInfo1', 'callLog1', 'allOrderRmarkInfo', 'allOrderRmarkInfo1')));

        });
    }

    public function printf($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('打印检测报告');
            $order = Order::where(['id' => $id])->first();
            $post_malfunction = DB::table('pr_malfunction')
                ->join('machine_malfunction', 'machine_malfunction.id', '=', 'pr_malfunction.malfunction_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_malfunction.title')->toArray();
            $malfunction = implode('，', $post_malfunction);
            $post_accessory = DB::table('pr_accessory')
                ->join('machine_accessory', 'machine_accessory.id', '=', 'pr_accessory.mar_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_accessory.title')->toArray();
            $accessory = implode('，', $post_accessory);
            $content->body(view('admin/post_order/print', compact('order', 'malfunction', 'accessory')));

        });
    }

    public function cancel(Request $request)
    {
        foreach (Order::find($request->get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修订单');
            $content->description('创建');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<js

    // 拆屏时间记录
    $('.screen_time_begin').click(function(e){
        var sn = e.currentTarget.dataset.sn;
        layer.confirm('确认开始拆屏吗?', {
            btn: ['是', '否']
        }, function(){
            $.ajax({
                url: '/admin/repair_check/saveRecordTime',
                data: {
                    sn: sn,
                    type: 'screen_time'
                },
                dataType: 'json',
                type: 'get',
                success: function(res){
                    if (res.status == 1) {
                        $('.grid-refresh').click();
                        layer.closeAll()
                    } else {
                        layer.msg(res.info);  
                    }
                },
                error:function(res){
                    console.log(res)
                }
            })
        }, function(){
        });
    })

    // 查看快递路由信息
    $('.express_route_fresh').click(function(){
        var id = $(this).attr('value');
        layer.open({
              type: 2,
              title: '快递路由',
              shadeClose: true,
              shade: 0.8,
              area: ['1000px', '60%'],
              content: 'express/express_route_fresh?readboy_sn='+id //iframe的url
        });
    });
    $("input[name='sn']").focus();
    // $("input[name='come_exp_sn']").focus();
js;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
            // $grid->model()->where('status', '>=', 200);
            $grid->disableCreation();
            $grid->disableExport();

            // 快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => [],
                ],
                1 => [
                    'name' => '待审核',
                    'param' => [['status', '=', Order::WAIT_AUDIT]],
                ],
                2 => [
                    'name' => '待检测',
                    'param' => [[DB::Raw('status in (' . Order::COME_SURE_IS_TELL . ',' . Order::NO_CHECK_IS_TELL . ')'), '1']],
                ],
                3 => [
                    'name' => '待支付',
                    'param' => [['status', '=', Order::CHECK_FINISH_IS_TELL]],
                ],
                4 => [
                    'name' => '待维修',
                    'param' => [['status', '=', Order::PAY_FINISH]],
                ],
                5 => [
                    'name' => '待发货',
                    'param' => [['status', '=', Order::REPAIR_FINISH]],
                ],
                6 => [
                    'name' => '缺少配件',
                    'param' => [],
                ],
            ];
            // 筛选条数
            foreach ($option as $key => $value) {
                switch ($key) {
                    case 6:
                        $count = Order::leftJoin('order_extend', 'order.sn', '=', 'order_extend.sn')
                            ->whereIn('order.status', [ // [已签收,未回寄]
                                Order::COME_SURE,
                                Order::COME_SURE_IS_TELL,
                                Order::NO_CHECK_IS_TELL,
                                Order::CHECK_FINISH,
                                Order::CHECK_FINISH_IS_TELL,
                                Order::PAY_FINISH,
                                Order::REPAIR_FINISH
                            ])
                            ->where('order_extend.order_mark', Order::ORDER_MARK_MISSING_ACCESSORY)
                            ->count();
                        break;
                    default:
                        $count = Order::where($value['param'])->count();
                        break;
                }
                $option[$key]['count'] = $count;
            }
            // 自定义状态快捷筛选按钮
            $grid->tools(function ($tools) use ($option) {

                // 订单追踪查询
                $tools->append(new orderTrackBtn());

                $tools->append(new QuickPickTool($option));
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                    // $batch->add('取消订单', new Cancel());
                });

                if (Request::get('quick_pick') == 4) { // 待维修才有的按钮
                    // 自定义状态快捷筛选按钮
                    $tools->append(new InformTool(4));  // 缺少配件
                }

            });

            // 根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $order_mark = Request::get('inform_status');
                switch ($quick_pick) {
                    case 4:
                        $grid->model()->where($option[$quick_pick]['param'])->orderBy('id', 'desc');
                        if ($order_mark) {
                            $sns = OrderExtend::where('order_mark', $order_mark)->pluck('sn')->toArray();
                            $grid->model()->whereIn('sn', $sns)->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');
                        }
                        break;
                    case 6:
                        $grid->model()->leftJoin('order_extend', 'order.sn', '=', 'order_extend.sn')
                            ->whereIn('order.status', [ // [已签收,未回寄]
                                Order::COME_SURE,
                                Order::COME_SURE_IS_TELL,
                                Order::NO_CHECK_IS_TELL,
                                Order::CHECK_FINISH,
                                Order::CHECK_FINISH_IS_TELL,
                                Order::PAY_FINISH,
                                Order::REPAIR_FINISH
                            ])
                            ->where('order_extend.order_mark', Order::ORDER_MARK_MISSING_ACCESSORY)
                            ->select('order.*')
                            ->orderBy('id', 'desc');
                        break;
                    default:
                        $grid->model()->where($option[$quick_pick]['param'])->orderBy('id', 'desc');
                        break;
                }
            }

            // 根据自定义状态按钮搜索数据
            /*if (Request::get('inform_status') && Request::get('quick_pick') == 4) {
                $order_mark = Request::get('inform_status');
                $sns = OrderExtend::where('order_mark', $order_mark)->pluck('sn')->toArray();
                $grid->model()->whereIn('sn', $sns)->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');
            }*/
            $grid->model()->where($option[-1]['param'])->orderBy('id', 'desc');

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('come_exp_sn', '快递单号');
                $filter->like('sn', '维修订单号');
                $filter->like('barcode', 'S/N码');
                $filter->like('name', '联系人');
                $filter->like('phone', '手机号');
                $filter->equal('status', '订单状态')->select(Order::STATUS);
                $filter->like('need_invoice', '开具发票')->select([0 => '否', 1 => '是']);
                $filter->equal('order_extend.order_mark', '订单标注')->select(Order::ORDER_MARK);

                $filter->like('province', '所属省')
                    ->select(DB::table('region')->where('parent_id', 0)->pluck('region_name', 'region_name'))
                    ->load('city', '/admin/post_repair/city');

                $filter->like('city', '所属市')->select(function ($region_name) {
                    return DB::table('region')->where('region_name', $region_name)->pluck('region_name', 'region_name');
                });
                $filter->between('created_at', '订单提交日期')->datetime();
                $filter->equal('check_man', '检测人员')
                    ->select(Order::join('admin_users as au', 'order.check_man', '=', 'au.id')
                        ->pluck('au.name', 'order.check_man'));
                $filter->between('order_extend.print_time', '打印时间')->datetime();
                $filter->equal('order_extend.print_man', '打印人员')
                    ->select(DB::table('order_extend as oe')
                        ->join('admin_users as au', 'oe.print_man', '=', 'au.id')
                        ->pluck('au.name', 'oe.print_man'));

            });

            $grid->id('ID')->sortable();
            $grid->uid('UID');
            Order::order_priority_column($grid);
            $grid->sn('寄修订单编号')->display(function ($sn) {
                $s = Order::STATUS;
                $status = $this->status;
                if (array_key_exists($status, $s)) {
                    $receive_time = $this->receive_time;

                    if ($receive_time) {
                        $receive_time = date('Y-m-d', strtotime($receive_time . " + 5 days"));
                        $now = date('Y-m-d');
                        if ($receive_time < $now && $status >= 400 && $status < 800) {
                            return '<span style="color:red">' . $sn . '</span>';
                        }

                    }
                    return $sn;
                } else {
                    return $sn;
                }
            });
            //$grid->column('come_exp_sn', '寄来快递单号')->display(function ($value) {
            //    $html = '<a class="express_route_fresh" href="javascript:void(0);" value="' . $value . '">' . $value . '</a>';
            //    return $html;
            //});
            //$grid->column('go_exp_sn', '寄去快递单号')->display(function ($value) {
            //    $html = '<a class="express_route_fresh" href="javascript:void(0);" value="' . $value . '">' . $value . '</a>';
            //    return $html;
            //});
            $grid->column('寄来-寄去快递单号')->display(function () {
                $come = $this->come_exp_sn;
                $go = $this->go_exp_sn;
                $a_come = '<a class="express_route_fresh" href="javascript:void(0);" value="' . $come . '">' . $come . '</a>';
                $a_go = '<a class="express_route_fresh" href="javascript:void(0);" value="' . $go . '">' . $go . '</a>';
                return ($come ? $a_come : '———') . '<br/>' . ($go ? $a_go : '———');
            });
            $grid->barcode('S/N码');
            //$grid->name('联系人');
            //$grid->phone('用户联系方式');
            $grid->column('联系人')->display(function () {
                return $this->name . '<br/>' . $this->phone;
            });
            // $grid->endpoint()->name('寄修售后点');
            $grid->amount('总费用￥');
            $grid->pay_amount('维修时需支付金额￥');
            $grid->column('need_invoice', '是否开具发票')->display(function ($value) {
                if ($value == 1) {
                    return '是';
                } else {
                    return '否';
                }
            });

            $grid->created_at('订单提交时间');
            $grid->order_extend()->screen_record_time('拆屏时间')->display(function ($value) {
                if (!empty($value)) {
                    return $value . ' <button  class="btn btn-sm btn-danger screen_time_begin" data-sn=' . $this->sn . '>重新记录</button>';
                } else {
                    return '<button  class="btn btn-sm btn-primary screen_time_begin" data-sn=' . $this->sn . '>开始记录</button>';
                }
            });
            $grid->column('expense.updated_at', '核销时间')->display(function ($value) {
                if ($value) {
                    return $value;
                } else {
                    return '未核销';
                }
            });

            $grid->order_extend()->order_mark('订单标注')->display(function ($order_mark) {
                $order_mark_name = '未知';
                if (array_key_exists($order_mark, Order::ORDER_MARK)) {
                    $order_mark_name = Order::ORDER_MARK[$order_mark];
                }
                if ($order_mark == Order::ORDER_MARK_MISSING_ACCESSORY) {
                    // 获取所需配件
                    $prMaterialArr = DB::table('pr_material')
                        ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_material.mat_id')
                        ->where('pr_sn', $this->sn)
                        ->pluck('machine_accessory_tree.title')
                        ->toArray();
                    if (!empty($prMaterialArr)) {
                        $prMaterialStr = implode(',', $prMaterialArr);
                        $order_mark_name = $order_mark_name . ': ' . $prMaterialStr;
                    }
                }

                if ($order_mark == Order::ORDER_MARK_NORMAL) {
                    return '<span style="color: #26bb5f">' . $order_mark_name . '</span>';
                } else {
                    $timeDifference = time() - strtotime($this->updated_at); // 时间差
                    if ($timeDifference <= 7 * 86400) { // 7天内灰色
                        return '<span style="color: #252526">' . $order_mark_name . '</span>';
                    }
                    if (7 * 86400 < $timeDifference && $timeDifference <= 15 * 86400) { // 7天到15天橙色
                        return '<span style="color: orange">' . $order_mark_name . '</span>';
                    }
                    if ($timeDifference > 15 * 86400) { // 15天以上红色
                        return '<span style="color: red">' . $order_mark_name . '</span>';
                    }
                }
            });

            $grid->status('订单状态')->display(function ($status) {
                $s = Order::STATUS;
                if (array_key_exists($status, $s)) {
                    $receive_time = $this->receive_time;

                    if ($receive_time) {
                        $receive_time = date('Y-m-d', strtotime($receive_time . " + 5 days"));
                        $now = date('Y-m-d');
                        if ($receive_time < $now && $status >= 400 && $status < 800) {
                            return '<span style="color:red">' . $status . $s[$status] . '</span>';
                        }

                    }
                    return $status . $s[$status];
                } else {
                    return "————";
                }
            });
            $grid->check_user()->name('检测人')->display(function ($name) {
                if ($name == '')
                    return '———';
                return $name;
            });

            $grid->column('order_extend.print_time', '打印时间');
            $grid->column('order_extend.print_man', '打印人员')->display(function ($user_id) {
                if ($user_id) {
                    return DB::table('admin_users')->where('id', $user_id)->value('name');
                }
                return '---';
            });
            $grid->column('order_extend.pay_remark', '支付备注');

            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $status = $actions->row->status;
                $c = 'post_repair/view/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">查看 </span></a>';
                $actions->append($html);
            });
        });
    }

    /**
     * 通过省获取市
     */
    public function city()
    {
        $provinceName = request()->input('q');
        $provinceId = DB::table('region')->where('region_name', $provinceName)->value('region_id');
        return DB::table('region')->where('parent_id', $provinceId)->get([
            DB::raw('region_name as id'),
            DB::raw('region_name as text'),
        ])->prepend(['id' => '', 'text' => '选择']);
    }

    /**
     * 是否拨通
     */
    public function is_dial()
    {
        $sn = Request::input('sn');
        $is_dial = Request::input('is_dial');

        OrderExtend::where('sn', $sn)->update(array('is_dial' => $is_dial));

        return array('status' => 1, 'info' => '操作成功');
    }

    /**
     * 是否拨通(维修)
     */
    public function is_dial_repair()
    {
        $sn = Request::input('sn');
        $is_dial_repair = Request::input('is_dial_repair');

        OrderExtend::where('sn', $sn)->update(array('is_dial_repair' => $is_dial_repair));

        return array('status' => 1, 'info' => '操作成功');
    }

    /**
     * 记录订单完成备注
     */
    public function setCompleteRemark()
    {
        $sn = Request::input('sn');
        $remark = Request::input('remark');

        // 获取订单信息
        $orderInfo = Order::where('sn', $sn)->first();

        $data = array(
            'sn' => $sn,
            'operator' => Admin::user()->id,
            'type' => 4,
            'status' => $orderInfo['status'],
            'connect' => $orderInfo['connect'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'remark' => $remark
        );

        OrderRemark::insert($data);

        return array('status' => 1, 'info' => '操作成功');
    }

    /**
     * 订单追踪
     */
    public function orderTrack($param)
    {
        $result = array();
        $result[] = 'param';
        if (!$order = Order::where(['sn' => $param])->orWhere('barcode', $param)->first()) {
            // admin_toastr('订单不存在', 'warning');
        } else {

            // 获取用户拥有的菜单
            if (Admin::user()->isAdministrator()) {
                $menuGroup = DB::table('admin_menu')->pluck('title')->toArray();
            } else {
                $menuGroup = DB::table('admin_role_users as aru')
                    ->leftjoin('admin_role_menu as arm', 'aru.role_id', 'arm.role_id')
                    ->leftjoin('admin_menu as am', 'arm.menu_id', 'am.id')
                    ->groupBy('am.title')
                    ->pluck('am.title')->toArray();
            }
            $result[] = 'menuGroup';

            // 用户是否能正在已完成步骤评论
            $order_final_remark = 0;
            if (Admin::user()->can('order_final_remark')) {
                $order_final_remark = 1;
            }
            $result[] = 'order_final_remark';

            $result[] = 'order';
            $order_extend = OrderExtend::where(['sn' => $order->sn])->first();
            if (!empty($order_extend)) {
                $order_extend->allocation_realname = User::where('id', $order_extend->allocation_uid)->value('name');
                $order_extend->quality_realname = User::where('id', $order_extend->quality_uid)->value('name');
            }

            $result[] = 'order_extend';

            // 获取所有客服备注
            $kfzh_remark_list = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
                ->where('order_remark.type', 1)
                ->where('order_remark.sn', $order->sn)
                ->orderBy('order_remark.id', 'desc')
                ->select('au.name', 'order_remark.*')
                ->get();

            foreach ($kfzh_remark_list as $key => $value) {
                $kfzh_remark_list[$key]['status_name'] = Order::STATUS[$value['status']];
                $kfzh_remark_list[$key]['connect_name'] = Order::CONNECT[$value['connect']];
            }
            $result[] = 'kfzh_remark_list';

            // 获取所有维修知会备注
            $jxzh_remark_list = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
                ->where('order_remark.type', 3)
                ->where('order_remark.sn', $order->sn)
                ->orderBy('order_remark.id', 'desc')
                ->select('au.name', 'order_remark.*')
                ->get();

            foreach ($jxzh_remark_list as $key => $value) {
                $jxzh_remark_list[$key]['status_name'] = Order::STATUS[$value['status']];
                $jxzh_remark_list[$key]['connect_name'] = Order::CONNECT[$value['connect']];
            }
            $result[] = 'jxzh_remark_list';

            // 获取订单完成备注
            $wc_remark_list = OrderRemark::join('admin_users as au', 'order_remark.operator', 'au.id')
                ->where('order_remark.type', 4)
                ->where('order_remark.sn', $order->sn)
                ->orderBy('order_remark.id', 'desc')
                ->select('au.name', 'order_remark.*')
                ->get();
            foreach ($wc_remark_list as $key => $value) {
                $wc_remark_list[$key]['status_name'] = Order::STATUS[$value['status']];
                $wc_remark_list[$key]['connect_name'] = Order::CONNECT[$value['connect']];
            }
            $result[] = 'wc_remark_list';

            // 获取订单日志
            $logList = OrderLog::leftJoin('admin_users as au', 'order_log.admin', 'au.id')
                ->where('order_log.pr_sn', $order->sn)
                ->orderBy('order_log.date', 'asc')
                ->select('order_log.*', 'au.name')
                ->get();

            $overInfo = array();

            foreach ($logList as $key => $value) {
                $statusName = Order::STATUS[$value->log_status];

                // 审核
                if (in_array($value->log_status, [100, 200, -200])) {
                    $overInfo['sh']['date'] = $value->date;
                    $data['status_name'] = $statusName;
                    $data['realname'] = $value->name;
                    $data['log_status'] = $value->log_status;
                    $overInfo['sh']['data'][] = $data;
                }

                // 用户发货
                if (in_array($value->log_status, [300, -300])) {
                    $overInfo['yhfh']['date'] = $value->date;
                    $data['status_name'] = $statusName;
                    $data['realname'] = $value->name;
                    $data['log_status'] = $value->log_status;
                    $overInfo['yhfh']['data'][] = $data;
                }

                // 维修签收
                if (in_array($value->log_status, [400])) {
                    $overInfo['wxqs']['date'] = $value->date;
                    $data['status_name'] = '已收货' . ' ' . Order::SIGN_IN_STATUS[$order_extend->sign_in_status];
                    $data['realname'] = $value->name;
                    $data['log_status'] = $value->log_status;
                    $overInfo['wxqs']['data'][] = $data;
                }

                // 客服知会
                if (in_array($value->log_status, [410])) {
                    $overInfo['kfzh']['date'] = $value->date;
                    $data['status_name'] = $statusName;
                    $data['realname'] = $value->name;
                    $data['log_status'] = $value->log_status;
                    $overInfo['kfzh']['data'][] = $data;
                }

                // 寄修分派
                if (in_array($value->log_status, [410]) && !empty($order_extend->allocation_time)) {
                    $overInfo['jxfp']['date'] = $order_extend->allocation_time;
                    $data['status_name'] = '已分派';
                    $data['realname'] = $order_extend->allocation_realname;
                    $data['log_status'] = $value->log_status;
                    $overInfo['jxfp']['data'][] = $data;
                }

                // 寄修检测
                if (in_array($value->log_status, [480, 490])) {
                    $overInfo['jxjc']['date'] = $value->date;

                    if ($value->log_status == 480) {
                        $data['status_name'] = '检测沟通中';
                    }
                    if ($value->log_status == 490) {
                        $data['status_name'] = '检测报告';
                    }
                    $data['realname'] = $value->name;
                    $data['log_status'] = $value->log_status;
                    $overInfo['jxjc']['data'][] = $data;
                }

                // 寄修知会
                if (in_array($value->log_status, [500])) {
                    $overInfo['jxzh']['date'] = $value->date;
                    $data['status_name'] = $statusName;
                    $data['realname'] = $value->name;
                    $data['log_status'] = $value->log_status;
                    $overInfo['jxzh']['data'][] = $data;
                }

                // 推送支付
                if (in_array($value->log_status, [600])) {
                    $overInfo['tszf']['date'] = $value->date;
                    $data['status_name'] = $statusName;
                    $data['realname'] = $value->name;
                    $data['log_status'] = $value->log_status;
                    $overInfo['tszf']['data'][] = $data;
                }

                // 维修
                if (in_array($value->log_status, [700, -700])) {
                    $overInfo['wx']['date'] = $value->date;
                    $data['status_name'] = $statusName;
                    $data['realname'] = $value->name;
                    $data['log_status'] = $value->log_status;
                    $overInfo['wx']['data'][] = $data;
                }

                // 品检
                if (in_array($value->log_status, [700, -700]) && $order->quality > 0) {
                    $overInfo['pj']['date'] = $order_extend->quality_time;
                    $data['status_name'] = Order::quality[$order->quality];
                    $data['realname'] = $order_extend->quality_realname;
                    $data['log_status'] = $value->log_status;
                    $overInfo['pj']['data'][] = $data;
                }

                // 回寄
                if (in_array($value->log_status, [800, -800])) {
                    $overInfo['hj']['date'] = $value->date;
                    $data['status_name'] = $statusName;
                    $data['realname'] = $value->name;
                    $data['log_status'] = $value->log_status;
                    $overInfo['hj']['data'][] = $data;
                }

                // 完成
                if (in_array($value->log_status, [900, -900])) {
                    $overInfo['wc']['date'] = $value->date;
                    $data['status_name'] = $statusName;
                    $data['realname'] = $value->name;
                    $data['log_status'] = $value->log_status;
                    $overInfo['wc']['data'][] = $data;
                }
            }

            $result[] = 'overInfo';
        }

        return view('admin/post_repair/overview', compact($result));
    }

    /**
     * 错误返回
     */
    private function returnImportError($message)
    {
        $error = new MessageBag([
            'title' => '参数错误',
            'message' => $message
        ]);
        return back()->with(compact('error'))->withInput();
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id = null)
    {
        $ar_title_js = MachineAccessoryTree::get_ar_title_js();
        $script = <<<EOF

            // 配件选项变更
            $(document).on('change', ".mat_id", function () {
                // 自动变更是否收费 判断仅弃修时收费
                var mat_text = this.selectedOptions[0].text;
                var ar_title = $ar_title_js;
                var is_charge_dom = $(this).closest('.fields-group').find(".is_charge");
                if (ar_title.filter(x => mat_text.indexOf(x) != -1).length > 0) {
                    $(is_charge_dom).val(2).trigger('change');
                } else {
                    $(is_charge_dom).val(1).trigger('change');
                }
                // 自动变更物料
                var target = $(this).closest('.fields-group').find(".material_id");
                $.get("/admin/repair_check_material?q="+this.value, function (data) {
                    target.find("option").remove();
                    defaultdata = [{"id":0,"text":"请选择"}];
                    setdata = $.map(data, function (d) {
                        d.id = d.id;
                        d.text = d.text;
                        return d;
                    });
                    data = setdata;
                    $(target).select2({
                        data: data
                    }).trigger('change');
                });
            });

            //别问我为什么不直接操作模板div,是因为用了DocumentFragment,操作不了,不是我用的!
            $(document).ready(function() {
                var in_period = $('.in_period').val()
                if (in_period != '1') {
                    calculatePrice();
                }
                // calculatePrice();
                function total() {
                    // var staff = Number($('#staff_cast').val().replace(/,/g, ""));
                    var staff = 0;
                    var exp_cast = Number($('#exp_cast').val().replace(/,/g, ""));
                    var accessory = Number($('#accessory_cast').val().replace(/,/g, ""));
                    var accessory_amount = Number($('#accessory_amount').val().replace(/,/g, ""));
                    var accessory_in_ar = Number($('#accessory_in_ar').val().replace(/,/g, ""));
                    $('#amount').val(staff + accessory_amount + exp_cast);
                    $('#pay_amount').val(staff + accessory + exp_cast);
                    $('#amount_in_ar').val(staff + accessory_in_ar + exp_cast);
                }

                $(document).on('change', '.staff_cast', function() {
                    total();
                });

                // 统计配件价格
                function calculatePrice() {
                    let totalPrice = 0;
                    let accessoryAmount = 0;
                    let accessoryInAr = 0;
                    $('.material_id:visible').each(function() {
                        let optionText = $(this).find("option:selected").text();
                        let price = optionText.split('价格 : ')[1];
                        let count = $(this).parent().parent().next().find('#count').val();
                        let is_charge = $(this).parent().parent().next().next().find('.is_charge').val();

                        accessoryAmount += price * count;
                        if (price == null) {
                            price = 0;
                        }
                        // 判断是否收费
                        if (is_charge == '1') {
                            totalPrice += price * count;
                        } else if (is_charge == '2') {
                            accessoryInAr += price * count;
                        }
                    });
                    // 实际收取配件费用
                    $('#accessory_cast').val(totalPrice);
                    // 总配件费用
                    $('#accessory_amount').val(accessoryAmount);
                    // 弃修时需支付配件费用
                    $('#accessory_in_ar').val(accessoryInAr);

                    total();
                }

                // 下拉框变化
                $(document).on('change', '.material_id', function() {
                    calculatePrice();
                });
                // 是否收费改变
                $(document).on('change', '.is_charge', function() {
                    calculatePrice();
                });
                // 加减数量按钮点击
                $('.has-many-repair_material-forms').on('click', 'button', function() {
                    calculatePrice();
                });
                // 移除按钮点击
                $(document).on('click', '.remove', function() {
                    calculatePrice();
                });
            });
EOF;

        Admin::script($script);
        return Admin::form(Order::class, function (Form $form) use ($id) {

            $form->text('name', '寄修人');
            $form->text('phone', '联系方式');
            $form->display('sn', '寄修订单号');
//            $form->barcode('sn', '寄修条码条码')->options(['width'=>1,]);
            $form->display('model_name', '机器型号');
            $form->select('in_period', '保修状态')->options(Order::in_period);
            $form->hidden('sn', '寄修订单号');
//            $form->divider('');
//            $form->hasMany('repair_malfunction','产品故障选择',
//                function (Form\NestedForm $form) {
//                    $form->hidden('id');
//                    $form->hidden('pr_sn');
//                    $form->select('malfunction_parent', '故障位置')->options(
//                        MachineMalfunction::where('parent_id', '=', 0)->pluck('title', 'id')->prepend('请选择', 0)
//                    )->load('malfunction_id', '/admin/machine_malfunction/getMachineMalfunctionByParentId');
//                    $form->select('malfunction_id', '故障类别')->options(function ($id) {
//                        return MachineMalfunction::where('id', '=', $id)->pluck('title', 'id');
//                    });
//                });
            $form->divider('');
            $form->select('reason', '损坏原因')->options(Order::reason)->value(function ($reason) {
                return $reason;
            });
            $form->text('deal', '维修方式');
            $form->divider('');

            $options = [];
            if ($id) {
                $modelId = Order::where('id', '=', $id)->value('model_id');
                $accessoryRelations = MachineAccessoryRelation::with('accessory')
                    ->where('model_id', '=', $modelId)->get();
                $options[0] = '请选择';
                foreach ($accessoryRelations as $key => $relation) {
                    $options[$relation->id] = $relation->accessory->title . ' | 价格 : ' . $relation->price;
                }
            }
            $form->hasMany('repair_material', '维修配件列表',
                function (Form\NestedForm $form) use ($id, $options) {
                    $modelId = Order::where('id', '=', $id)->value('model_id');
                    $pr_sn = Order::where('id', '=', $id)->value('sn');
                    $form->select('mat_id', '维修配件')->options(MachineAccessoryTree::model_options($modelId))->load('malfunction_id', admin_url('repair_check_malfunction'));
                    $form->select('malfunction_id', '配件故障')->options(function ($value) {
                        $data = MachineMalfunction::where('id', $value)->get()->toArray();
                        $ret = array();
                        if (count($data) > 0) {
                            foreach ($data as $d) {
                                $ret[$d['id']] = $d['title'];
                            }
                        }
                        return $ret;
                    });
                    $form->select('material_id', '维修物料')->options(function ($value) use ($pr_sn, $form) {
                        $data = Material::where('id', $value)->get()->toArray();
                        $ret = array();
                        if (count($data) > 0) {
                            foreach ($data as $d) {
                                $ret[$d['id']] = $d['name'] . '|价格 : ' . $d['price'];
                            }
                        }
                        return $ret;
                    });
                    $form->number('count', '数量')->default(1);
                    $form->select('is_charge', '是否收费')->options(Order::is_charge)->default(1);
                });

            $form->currency('accessory_amount', '总配件价格')->symbol('￥');
            $form->currency('accessory_cast', '实际收取配件价格')->symbol('￥');
            $form->currency('accessory_in_ar', '弃修时收取配件价格')->symbol('￥');
            $form->divider();

            if ($id && $form->model()->find($id)->in_period !== 1) {
                $rb_exp_sn = $form->model()->find($id)->rb_come_exp_sn;
                $exp_sn = $form->model()->find($id)->come_exp_sn;
                $exp_cast = Express::query_order($rb_exp_sn, $exp_sn);
                //$exp_cast = 15;
            } else {
                $exp_cast = 0;
            }
            $form->currency('exp_cast', '快递费用')->symbol('￥')->default(intval($exp_cast))->help('快递费用  ' . $exp_cast)->readOnly();
            $form->hidden('staff_cast', '检测费用')->value($exp_cast);
            //$form->display('staff_cast', '检测费用')->value($exp_cast)->help('快递费用  '.$exp_cast. '元');
            $form->currency('amount', '总计')->symbol('￥');
            $form->currency('pay_amount', '待支付金额')->symbol('￥')->help('修改前金额：');
            $form->currency('amount_in_ar', '弃修时需支付金额')->symbol('￥')->help('客户弃修时收取');
            $form->divider();

            $form->display('receive_case', '收到的配件');
            $form->text('deal_remark', '备注');
            $form->hidden('check_man');
            $form->hidden('status');
            //$form->hidden('pay_amount');
            $form->saving(function (Form $form) use ($exp_cast) {
                //$form->check_man = Admin::user()->id;
                if ($form->model()->in_period == 1) {
                    $exp_cast = '0.00';
                } else {
                    $rb_exp_sn = $form->model()->rb_come_exp_sn;
                    $exp_sn = $form->model()->come_exp_sn;
                    $exp_cast = Express::query_order($rb_exp_sn, $exp_sn);
//                    $exp_cast = 15;
                }
                $form->staff_cast = floatval($exp_cast);
//                dd($form);
                if ($form->status < 500)
                    $form->status = 500;
                //待支付金额判断：
                if (($form->in_period == 1 || $form->model()->in_period == 1) && ($form->reason == 2)) {
                    $form->pay_amount = '0.00';
                } else {
//                    $form->pay_amount = $form->amount;
                }
//                dd($form->pay_amount);
                //微信支付如果有发起过支付请求，必须保证金额等信息一致，所以有金额变动，需要去掉内部支付单号重新生成
                if ($form->pay_amount != $form->model()->pay_amount && !empty($form->model()->rb_pay_sn) && empty($form->model()->is_paid)) {
                    $data = ['rb_pay_sn' => '', 'pay_com' => 0, 'pay_sn' => ''];
                    DB::table('order')->where('sn', $form->model()->sn)->update($data);
                }
            });
            //快递费不保存,保存成staff_cast
            $form->ignore(['exp_cast']);

            $form->saved(function ($form) {
                //同步处理已使用的物料
                PostRepairUsedMaterial::where('pr_sn', $form->sn)->delete();
                $pr_material = PostRepairMaterial::where('pr_sn', $form->sn)->get();
                if (count($pr_material) > 0) {
                    foreach ($pr_material as $material) {
//                        dd($material);
                        $new_used_material = new PostRepairUsedMaterial();
                        $new_used_material->pr_sn = $material->pr_sn;
                        $new_used_material->mat_id = $material->mat_id;
                        $new_used_material->material_id = $material->material_id;
                        $new_used_material->is_charge = $material->is_charge;
                        $new_used_material->count = $material->count;
                        $new_used_material->save();
                    }
                }
//                dd(PostRepairUsedMaterial::where('pr_sn', $form->sn)->get()->toArray());
                $order = $form->model();
//                dd($order->status == Order::CHECK_FINISH && $order->in_period == 1 && $order->reason == 2 && empty(intval($order->pay_amount)));
                //处理保修期内并且是质量问题的订单，直接修改为已支付的状态
                if ($order->status == Order::CHECK_FINISH && $order->in_period == 1 && $order->reason == 2 && empty(floatval($order->pay_amount))) {
                    $save = Order::where('sn', $order->sn)->first();
                    $save->status = Order::PAY_FINISH;
                    $save->save();
                }
            });
        });
    }
}

/**
 * 订单跟踪查询
 */
class orderTrackBtn extends Grid\Tools\AbstractTool
{
    public function render()
    {
        return <<< EOT
<div class="btn-group" >
    <a href="JavaScript:void(0);" class="btn btn-sm btn-primary" id="goOrderTrack"><i class="fa fa-search"></i>&nbsp;订单跟踪查询</a>
</div>
<script>
    $('#goOrderTrack').click(function(){
        console.log($("input[name='sn']").val())
        sn = $("input[name='sn']").val();
        barcode = $("input[name='barcode']").val();
        if(!sn && !barcode){
            toastr.error('请先输入维修单号或者SN码');  
            $("input[name='sn']").focus();
            return false;  
        }

        if(sn){
            param = sn
        }

        if(barcode){
            param = barcode
        }

        layer.open({
            type: 2,
            title: '订单跟踪',
            shadeClose: true,
            shade: 0.8,
            area: ['1300px', '94%'],
            content: '/admin/post_repair/orderTrack/' + param //iframe的url
        });

    })
</script>
EOT;
    }
}