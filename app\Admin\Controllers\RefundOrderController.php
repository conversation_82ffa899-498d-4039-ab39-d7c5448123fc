<?php


namespace App\Admin\Controllers;


use App\Admin\Extensions\OrderRefund;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\PayOrder;
use App\Models\RefundOrder;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;
use http\Client\Curl\User;
use Illuminate\Support\MessageBag;

class RefundOrderController extends Controller
{
    use ModelForm;

    /**
     *
     * Index interface
     *
     *@return Content
     **/
    public function index(){
        return Admin::content(function (Content $content){
            $content->header('订单退款');
            $content->description('退款列表');
            $content->body($this->grid());
        });
    }

    /**
     *
     * Index interface
     *
     *@return Content
     **/
    public function edit($id){
        return Admin::content(function (Content $content) use ($id){
            $content->header('订单退款');
            $content->description('退款列表');
            $content->body($this->form($id)->edit($id));
        });
    }
    /**
     *
     * Make a grid Builder
     *
     * @return Grid
     **/
    public function grid(){
        return Admin::grid(RefundOrder::class,function (Grid $grid){
            $grid->disableCreation();
            $grid->disableExport();
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->where(function($query) {
                    $key = '%'.$this->input.'%';
                    $query->where('pr_sn', 'like', $key)->orwhere('readboy_sn', 'like', $key)->orwhere('refund_sn', 'like', $key);
                }, '单号搜索');
                $filter->is('com', '支付公司')->select([1 => '微信', 2 => '支付宝']);
                $filter->is('is_refund', '是否已退款')->select([1 => '已退款', 0 => '未退款']);
                $filter->between('updated_at', '退款时间')->datetime();
            });
            $grid->tools(function ($tools){

                if (Admin::user()->inRoles('post_repair_refund')){
                    $button = <<<EOF
                     <div class="btn-group pull-right" style="margin-right:0px; margin-right: 10px;">
                      <a href ="order/refund" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 退款
                      </a >
                    </div >
EOF;
                    $tools->append($button);
                }

            });
            $grid->model()->orderBy('id', 'desc');

            $grid->id('ID')->sortable();

            $grid->pr_sn('寄修单号')->sortable();
            $grid->readboy_sn('内部单号')->sortable();
            $grid->com('支付公司')->sortable()->display(function ($value) {
                return PayOrder::COM[$value];
            });
            $grid->refund_sn('退款公司单号')->sortable();
            $grid->is_refund('是否退款')->sortable()->display(function ($value) {
                return RefundOrder::IS_REFUND[$value];
            });
            $grid->column('pay_order.pay_amount', '支付金额');
            $grid->refund_amount('退款金额');
            $grid->refund_remark('退款备注')->editable();
            $grid->actions(function ($actions){
               $actions->disabledelete();
               $actions->disableEdit();
               $is_refund = $actions->row->is_refund;
               if($is_refund == 0){
                   $order_sn = $actions->row->pr_sn;
                   $out_trade_no = $actions->row->readboy_sn;
                   $com = $actions->row->com;
                   $c = 'order/refund_query?order_sn='.$order_sn.'&out_trade_no='.$out_trade_no.'&com='.$com;
                   $html = '<a href="'.$c.'">手动查询</a>';
                   $actions->append($html);
               }

            });
        });
    }

    /**
     *
     * Make a grid Builder
     *
     * @return Form
     **/
    public function form($id=null){
        return Admin::form(RefundOrder::class, function (Form $form) use ($id){
            $form->hidden('refund_remark', '退款备注');
        });
    }
    public function orderRefund(Content $content){
        if (request()->isMethod('post')) {
            $data = request()->all();
            $order_sn = $data['order_sn'];
            $refund_fee = $data['refund_fee'];
            $refund_remark = $data['refund_remark'];
            $user_id = Admin::user()->id;
            $order = Order::where('sn', $order_sn)->first();
            $pay_order = PayOrder::where([['pr_sn', $order_sn], ['is_paid', 1]])->orderby('id', 'desc')->first();

            if(empty($order)){
                $error = new MessageBag([
                    'title'=> '错误提示',
                    'message'=>'无此单号信息，请确认单号是否正确'
                ]);
                return back()->withInput()->with(compact('error'));

            }
            if(empty($pay_order)){
                $error = new MessageBag([
                    'title'=> '错误提示',
                    'message'=>'此单号未有支付信息'
                ]);
                return back()->withInput()->with(compact('error'));

            }
            $com = $pay_order->com;
            if (in_array($order->status, [600, 700, 800, 900, -800, -900] )){
                $refund = new OrderRefund();
                $ret = $refund->refund($order_sn, $refund_fee, $com);
                if ($ret == false){
                    $error = new MessageBag([
                        'title'=> '错误提示',
                        'message'=>'系统错误，退款失败'
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                if ($ret->ok == 0){
                    $error = new MessageBag([
                        'title'=> '错误提示',
                        'message'=> $ret->msg
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                $refund_order = RefundOrder::where([['pr_sn', $order_sn], ['readboy_sn', $ret->data]])->first();
//                主动查询是否已退款
                $order_query = new OrderRefund();
                $ret = $order_query->refund_query($order_sn, $refund_order->readboy_sn, $com);
                if (!empty($refund_order)){
                    $refund_order->admin = $user_id;
                    $refund_order->refund_remark = $refund_remark;
                    $refund_order->save();
                }
                admin_toastr('退款成功', 'success');
                return redirect('admin/refund');
            }else{
                $error = new MessageBag([
                    'title'=> '错误提示',
                    'message'=>'订单状态不可退款'
                ]);
                return back()->withInput()->with(compact('error'));
            }


        }
        $content->header('订单退款');
        $content->description('订单退款');
        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/order/refund');
        $form->text('order_sn', '寄修单号')->rules('required');
        $form->text('refund_fee', '退款金额')->rules('required');
        $form->text('refund_remark', '退款备注')->rules('required');
//            $form->select('trade_plat', '交易平台')->options([1=>'微信']);
        $content->body($form);
        return $content;

    }

    public function orderRefundQuery(Content $content){
        $data = request()->all();
//        dd($data);
        $order_sn = $data['order_sn'];
        $out_trade_no = $data['out_trade_no'];
        $com = $data['com'];
        $order_query = new OrderRefund();
        $ret = $order_query->refund_query($order_sn, $out_trade_no, $com);
        if ($ret == false){
            $error = new MessageBag([
               'title' => '错误提示',
               'message' => '接口连接错误，请联系相关人员'
            ]);
            return back()->withInput()->with(compact('error'));
        }elseif ($ret->ok == 0){
            $error = new  MessageBag([
                'title' => '错误提示',
                'message' => $ret->msg
            ]);
            return back()->withInput()->with(compact('error'));
        }
        admin_toastr('退款成功', 'success');
        return redirect('admin/refund');
    }
}
