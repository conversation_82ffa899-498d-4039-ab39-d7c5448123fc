<?php
/**
 * Created by PhpStorm.
 * User: Xian
 * Date: 2017/12/16
 * Time: 8:50
 */

namespace App\Admin\Extensions;


use App\Models\Agency;
use App\Models\Warranty;
use App\Services\Admin\MachineTypeService;
use App\Services\Admin\StatisticsService;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;

class EndpointWarrantyStatisticsExporter extends AbstractExporter
{
    use ExcelExportTrait;
    protected $time;

    public function __construct($time, Grid $grid = null)
    {
        parent::__construct($grid);
        $this->time = $time;
    }

    protected $statisticsService;

    public function export()
    {
        $filename = '终端保卡销售额';
        $titles = [
            '总代',
            '二代',
            '终端名称',
            '终端地址',
            '终端负责人',
            '负责人电话',
            '保卡数量',
            '销售额',
        ];
        $data = $this->getData();
        $machineTypeService = new MachineTypeService();
        $modelPrices = $machineTypeService->getCachedAllModelPrices();
        $topAgencies = Agency::where('pid', 0)->pluck('name', 'id')->toArray();
        $secondAgencies = Agency::where('pid', '!=', 0)->pluck('name', 'id')->toArray();
        $endpointIds = array_column($data, 'endpoint');
        $endpointWarrantyPrices = [];
        for ($i = 0; $i < count($endpointIds); $i = $i + 200) {
            $ids = array_slice($endpointIds, $i, 200);
            $endpointWarrantyPrices += $this->calculateEndpointWarrantyPrice($ids, $this->time, $modelPrices);
        }
        $formatData = [];
        if (!empty($data)) {
            foreach ($data as $key => $row) {
                $formatData[] = [
                    $topAgencies[$row['endpoints']['top_agency']],
                    $row['endpoints']['second_agency'] ? $secondAgencies[$row['endpoints']['second_agency']] : '无',
                    $row['endpoints']['name'],
                    $row['endpoints']['address'],
                    $row['endpoints']['manager'],
                    " " . $row['endpoints']['phone'],
                    $row['warranty_count'],
                    $endpointWarrantyPrices[$row['endpoint']],
                ];
            }
        }
        $sumPriceKey = 6;
        $formatData = $this->orderResultByPrice($formatData, $sumPriceKey);
        array_unshift($formatData, $titles);
        ExcelExportTrait::exportToExcel($filename, $formatData);
    }

    public function calculateEndpointWarrantyPrice($endpointIds, $time, $modelPrices)
    {

        $t1 = microtime(true);
        $result = Warranty::select(['id', 'model', 'endpoint'])
            ->whereIn('endpoint', $endpointIds)
            ->whereBetween('buy_date', $time)
            ->get()->toArray();
        $t2 = microtime(true);
        $endpointWarrantyPrice = [];
        foreach ($endpointIds as $key => $id) {
            $endpointWarrantyPrice[$id] = 0;

        }
        foreach ($result as $k => $warranty) {
            $endpointWarrantyPrice[$warranty['endpoint']] += $modelPrices[$warranty['model']] ?? 0;
        }

        return $endpointWarrantyPrice;
    }

    public function orderResultByPrice($data, $sumPriceKey)
    {
        foreach ($data as $k => $v) {
            $name[$k] = $v[$sumPriceKey];
        }
        array_multisort($name, SORT_REGULAR, SORT_DESC, $data);

        return $data;
    }
}