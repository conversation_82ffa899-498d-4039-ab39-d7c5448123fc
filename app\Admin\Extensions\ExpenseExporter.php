<?php

namespace App\Admin\Extensions;

use App\Models\BrokenScreenInsurance;
use App\Models\Endpoint;
use App\Models\Order;
use App\Models\PayOrder;
use App\Models\PostRepairExpense;
use App\Models\PostRepairMaterial;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;
use App\Models\Agency;
use Illuminate\Support\Facades\Request;

class ExpenseExporter extends AbstractExporter
{
    public function export()
    {
        $filename = '核销列表';
        $data = $this->getData();
        $updated_at_last = Request::get('updated_at_last');
//        dd($updated_at_last);
        if (!empty($data)) {
            $titles = [
                '支付方式',
                'id',
                '寄修单号',
                'SN码',
                '订单状态',
                '联系状态',
                '有无碎屏保',
                '是否使用碎屏保',
                '是否直营（碎屏保）',
                '寄出时间',
                '付款时间',
                '下单时间',
                '物料名称',
                '新物料编码',
                '旧物料编码',
                '数量',
                '单价',
                '是否收费',
                '收费类型',
                '总费用',
                '快递费用',
                '配件实际收费(总)',
                '实际费用（需要支付的费用）',
//                '支付方式',
                '机型',
                '保修信息',
                '备注',
                '发票抬头',
                '发票税号',
                '发票邮箱',
                '代理地区',
            ];
            $formatData = $this->getFormatData($data);
            array_unshift($formatData, $titles);
            ExcelExportTrait::exportToExcel($filename, $formatData);
        }
    }

    private function getFormatData($data)
    {
        $formatData = [];
//        dd($data);
//        $data = Order::whereBetween('pay_time', ["2021-02-01 00:00:00", "2021-03-01 00:00:00"])
//            ->whereNotBetween('updated_at_last', ["2021-02-01 00:00:00", "2021-03-01 00:00:00"])
//            ->where([['status', '>', 0]])
//            ->get()->toArray();
        foreach ($data as $d) {
            if ($d['status'] != Order::EXP_GO_SUCCESS && $d['status'] != Order::ORDER_FINISH) {
                continue;
            }
            $used_material = DB::table('pr_used_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_used_material.mat_id')
                ->leftjoin('material', 'material.id', '=', 'pr_used_material.material_id')
                ->where('pr_used_material.pr_sn', $d['sn'])
                ->select('material.name as name', 'material.price_first as price_first', 'material.price as price', 'pr_used_material.count as count',
                    'material.code as code', 'material.old_code as old_code', 'material.specification as specification',
                    'material.from as from', 'pr_used_material.is_charge as is_charge', 'pr_used_material.charge_type as charge_type')
                ->get()
                ->toArray();

            if (empty($used_material)) {
                //标核销然后跳过
                $sn = $d['sn'];
                $save['pr_sn'] = $sn;
                $save['updated_at'] = date('Y-m-d H:i:s');
                PostRepairExpense::updateOrInsert(array('pr_sn' => $sn), $save);
                continue;
            }
            $updated_at = PayOrder::where('pr_sn', '=', $d['sn'])->value('updated_at');
            $number = 1;
            $send_time = DB::table('order_log')->where('pr_sn', $d['sn'])->where('log_status', Order::EXP_GO_SUCCESS)->value('date');
            $is_direct_sales = BrokenScreenInsurance::where([['barcode', $d['barcode']], ['status', 300]])->value('is_direct_sales');
            foreach ($used_material as $m) {
                $price = $m->price;
                if ($m->charge_type == 2) {
                    $price = $m->price_first;
                }
                $amount = $d['amount'];
                if ($number != 1) {
                    $amount = '';
                }
                $pay_amount = $d['pay_amount'];
                if ($number != 1) {
                    $pay_amount = '';
                }
                $accessory_cast = $d['accessory_cast'];
                if ($number != 1) {
                    $accessory_cast = '';
                }
                $number++;
                $row = [
                    PayOrder::COM[$d['pay_com']],
                    $d['id'],
                    $d['sn'] . PayOrder::COM[$d['pay_com']],
                    $d['barcode'] . ' ',
                    Order::STATUS[$d['status']],
                    Order::CONNECT[$d['connect']],
                    Order::has_screen_insurance[$d['has_screen_insurance']],// 有无碎屏保
                    Order::used_screen_insurance[$d['used_screen_insurance']],// 是否使用碎屏保
                    !empty($is_direct_sales) ? Order::is_direct_sales[$d['used_screen_insurance']] : '无碎屏保',
                    $send_time,
                    $updated_at,
                    $d['updated_at_last'],

                    $m->name,
                    $m->code . ' ',
                    $m->old_code . ' ',
                    $m->count,
                    $price,
                    Order::is_charge[$m->is_charge],
                    Order::charge_type[$m->charge_type],

                    $amount,
                    $d['staff_cast'],
                    $accessory_cast,
                    $d['pay_amount'],
//                    PayOrder::COM[$d['pay_com']],
                    $d['model_name'],
                    Order::in_period[$d['in_period']],
                    $d['deal_remark'],
                    $d['invoice_title'],
                    $d['invoice_tax_id'],
                    $d['invoice_email'],
                    $d['agency'],
                ];
                $formatData[] = $row;
            }

            //标核销
            $sn = $d['sn'];
            $save['pr_sn'] = $sn;
            $save['updated_at'] = date('Y-m-d H:i:s');
            PostRepairExpense::updateOrInsert(array('pr_sn' => $sn), $save);
        }
//        dd($formatData);
        return $formatData;
    }
}
