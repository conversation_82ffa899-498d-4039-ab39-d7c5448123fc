<?php

use Illuminate\Routing\Router;

Admin::registerHelpersRoutes();

Route::group([
    'prefix' => config('admin.prefix'),
    'namespace' => Admin::controllerNamespace(),
    'middleware' => ['guest'],
], function (Router $router) {
    //获取手机验证码
    $router->get('users/sendPhoneCode', 'UserController@sendPhoneCode');
    $router->get('/login', 'AuthController@getLogin')->name('auth_center_login');
    $router->any('/auth/token_login', 'AuthController@tokenLogin')->name('token_login');
    $router->get('/logout/{session_id?}', 'AuthController@getLogout')->name('getlogout');
});

Route::group([
    'prefix' => config('admin.prefix'),
    'namespace' => Admin::controllerNamespace(),
    'middleware' => ['web', 'admin'],
], function (Router $router) {
    $router->get('/', 'HomeController@index');

    $router->get('/test/yz_billno', 'TestController@yz_billno');
    $router->get('/test/yz_order', 'TestController@yz_create_order');
    $router->get('/test/yz_track', 'TestController@yz_track');
    $router->get('/test/response_request', 'TestController@response_request');
    $router->post('/test/response_request', 'TestController@response_request');
    $router->get('/test/edit', 'TestController@edit_test');
    $router->get('/test/sf_test', 'TestController@sf_test');
    $router->get('/test/express_d_address_test', 'TestController@express_d_address_test');

    $router->get('agency/secondAgency', 'AgencyController@secondAgency');
    $router->get('agency/get4SEndpoint', 'AgencyController@get4SEndpoint');

    $router->get('region/region_city', 'RegionController@city');

    // 寄修服务
    $router->get('post_repair/setReapirCheckReamrk', 'PostRepairController@setReapirCheckReamrk');
    $router->get('post_repair/is_dial', 'PostRepairController@is_dial');
    $router->get('post_repair/is_dial_repair', 'PostRepairController@is_dial_repair');
    $router->get('post_repair/city', 'PostRepairController@city'); // 获取市
    $router->get('post_repair/setCompleteRemark', 'PostRepairController@setCompleteRemark'); // 保存订单完成备注
    $router->get('post_repair/orderTrack/{sn}', 'PostRepairController@orderTrack');
    $router->resource('post_repair', PostRepairController::class);

    // 云客服账号管理
    $router->get('cloud_customer/selectUser', 'CloudCustomerController@selectUser');
    $router->get('cloud_customer/setYkf', 'CloudCustomerController@setYkf');
    $router->post('cloud_customer/setYkf', 'CloudCustomerController@doSetYkf');
    $router->resource('cloud_customer', CloudCustomerController::class);

    // 寄修黑名单
    $router->get('/repair_black_list/getLog', 'RepairBlackListController@getLog'); // 获取日志信息
    $router->get('/repair_black_list/getRemarkInfo', 'RepairBlackListController@getRemarkInfo'); // 获取备注信息
    $router->get('/repair_black_list/setRemarkInfo', 'RepairBlackListController@setRemarkInfo'); // 记录备注信息
    $router->get('/repair_black_list/import', 'RepairBlackListController@import_data_view');
    $router->post('/repair_black_list/import', 'RepairBlackListController@import_data_store');
    $router->resource('repair_black_list', RepairBlackListController::class);

//    代理商寄修
    $router->post('/agent_order/cancel', 'AgentOrderPostCheckListController@cancel');
    $router->post('/agent_order/deleteAll', 'AgentOrderPostCheckListController@deleteAll');
    $router->resource('agent_order', AgentOrderPostCheckListController::class);
//    代理商审核
    $router->resource('agent_order_post_check', AgentOrderPostCheckController::class);
//    代理商签收
    $router->resource('agent_order_post_sign', AgentOrderPostSignController::class);
//    打印标签
    $router->get('/agent_order_post_sign_print', 'AgentOrderPostSignController@post_sign_print');
//    寄修总览
    $router->resource('agent_order_post_repair', AgentOrderPostRepairController::class);
    $router->resource('agent_order_go_confirm', AgentOrderGoConfirmController::class);
//    同路径的路由要放在没绑定路由的后面
    $router->get('agent_order_repair_exp_go/express_one_order', 'AgentOrderRepairExpGoController@express_one_order');
    $router->post('agent_order_repair_exp_go/express_one_order', 'AgentOrderRepairExpGoController@express_one_order');
    $router->get('agent_order_repair_exp_go/offline_express_one_order', 'AgentOrderRepairExpGoController@offline_express_one_order');
    $router->get('agent_order_repair_exp_go/yt_express_one_order', 'AgentOrderRepairExpGoController@yt_express_one_order');
    $router->post('agent_order_repair_exp_go/yt_express_one_order', 'AgentOrderRepairExpGoController@yt_express_one_order');
    $router->get('agent_order_repair_exp_go/yz_express_one_order', 'AgentOrderRepairExpGoController@yz_express_one_order');
    $router->post('agent_order_repair_exp_go/yz_express_one_order', 'AgentOrderRepairExpGoController@yz_express_one_order');
    $router->get('agent_order_repair_exp_go/yt_express/{id}', 'AgentOrderRepairExpGoController@yt_express');
    $router->post('agent_order_repair_exp_go/yt_express', 'AgentOrderRepairExpGoController@yt_express');
    $router->get('agent_order_repair_exp_go/yt_route_order', 'AgentOrderRepairExpGoController@yt_route_order');

    $router->resource('/agent_order_repair_exp_go', AgentOrderRepairExpGoController::class);

    $router->resource('pseudo_code', PseudoCodeController::class);
    $router->get('agent_order_bill/set_bill_status', 'AgentOrderBillController@set_bill_status');
    $router->resource('agent_order_bill', AgentOrderBillController::class);
    $router->resource('agent_order_bill_detail', AgentOrderBillDetailController::class);

//    $router->get('agent_order_bill', 'AgentOrderPostCheckListController@agent_order_bill');

    $router->get('/post_repair/view/{id}', 'PostRepairController@view');

    $router->get('/api/users', 'PostRepairManageController@users');
    $router->resource('order_extend', OrderExtendController::class);
    $router->resource('post_repair_manage', PostRepairManageController::class);
    $router->get('/post_repair_manage/selectUser', 'PostRepairManageController@selectUser');
    $router->post('/post_repair_manage/cancel', 'PostRepairManageController@cancel');
    $router->get('/post_repair_manage/log/{id}', 'PostRepairManageController@log');

    $router->get('/post_check/setAuditOpinion', 'PostCheckController@setAuditOpinion'); // 记录审核备注
    $router->get('/post_check/express_one_order', 'PostCheckController@express_one_order');
    $router->post('/post_check/express_one_order', 'PostCheckController@express_one_order');
    $router->resource('post_check', PostCheckController::class);
    $router->get('/post_check/view/{id}', 'PostCheckController@view');
    $router->get('/post_check/express/{id}', 'PostCheckController@express');
    $router->post('/post_check/express', 'PostCheckController@express');
    $router->post('/post_check/cancel', 'PostCheckController@cancel');
    $router->get('/post_sign_print', 'PostSignController@post_sign_print');
    $router->post('/post_sign/cancel', 'PostSignController@cancel');
    $router->post('/post_sign_in_new', 'PostSignController@post_sign_in_new');
    $router->get('/post_sign/change_status_or_remark', 'PostSignController@change_status_or_remark');
    $router->post('/post_sign/change_status_or_remark', 'PostSignController@change_status_or_remark_post');
    $router->resource('post_sign', PostSignController::class);
    $router->get('/post_sign/view/{id}', 'PostSignController@view');
    $router->get('/post_sign_in', 'PostSignController@post_sign_in'); // get 签收
    $router->get('/set_come_sure', 'PostSignController@set_come_sure');

    // 支付确认
    $router->post('/post_connect/cancel', 'PostConnectController@cancel');
    $router->get('/post_connect/getRemarkInfo', 'PostConnectController@getRemarkInfo'); // 获取备注信息
    $router->get('/post_connect/setPayRemark', 'PostConnectController@setPayRemark'); // 记录备注信息
    $router->resource('post_connect', PostConnectController::class);
    $router->get('/post_conncet/view/{id}', 'PostConnectController@view');
    $router->get('/post_connect_set', 'PostConnectController@post_connect_set');

    $router->resource('repair_staff', RepairStaffController::class);
    $router->resource('repair_allot', RepairAllotController::class);
    $router->get('post_allot/{id}', 'PostAllotController@index')->name('post_allot_index');
    $router->post('post_allot/{id}', 'PostAllotController@index')->name('post_allot_index');
    $router->get('material_search', 'MachineAccessoryTreeController@material_search');
    $router->post('machine_accessory_tree_copy', 'MachineAccessoryTreeController@machine_accessory_tree_copy');
    $router->resource('machine_accessory_tree', MachineAccessoryTreeController::class);

    $router->get('machine_accessory_tree_list/set_malfunction', 'MachineAccessoryTreeListController@setMalfunction'); // 设置症状
    $router->get('machine_accessory_tree_list/get_malfunction_info', 'MachineAccessoryTreeListController@getMalfunctionInfo'); // 获取配件症状
    $router->resource('machine_accessory_tree_list', MachineAccessoryTreeListController::class);
    $router->post('machine_quality_tree_copy', 'MachineQualityTreeController@machine_quality_tree_copy');
    $router->resource('machine_quality_tree', MachineQualityTreeController::class);
    $router->resource('damage', DamageController::class);

    $router->resource('post_order', PostOrderController::class);
    $router->get('/post_order/view/{id}', 'PostOrderController@view');
    $router->get('/post_order/print/{id}', 'PostOrderController@print_order');
    $router->post('/post_order/cancel', 'PostOrderController@cancel');

    $router->get('/accessory_price_offer/upload', 'AccessoryPriceOfferController@upload');
    $router->post('/accessory_price_offer/upload', 'AccessoryPriceOfferController@upload');
    $router->resource('/accessory_price_offer', AccessoryPriceOfferController::class);
    $router->resource('/accessory_price_offer_list', AccessoryPriceOfferListController::class);

    $router->get('repair_check/deviceUnbind', 'RepairCheckController@deviceUnbind'); // 设备解绑
    $router->get('repair_check/saveRecordTime', 'RepairCheckController@saveRecordTime'); // 保存记录时间
    $router->get('repair_check/call', 'RepairCheckController@call'); // 拨打电话
    $router->get('repair_check/hollyCall', 'RepairCheckController@hollyCall'); // 拨打电话
    $router->get('repair_check/sendMsg', 'RepairCheckController@sendMsg'); // 发送短信
    $router->get('/repair_check/repeat_remark', 'RepairCheckController@repeat_remark');
    $router->get('/repair_check/boom_list/{barcode}', 'RepairCheckController@boom_list');
    $router->get('/repair_check_material', 'RepairCheckController@repair_check_material');
    $router->get('/repair_check_malfunction', 'RepairCheckController@repair_check_malfunction');
    $router->get('/repair_check_freeze', 'RepairCheckController@freeze');
    $router->get('/repair_check/express', 'RepairCheckController@express');
    $router->get('/repair_check/discount_type', 'RepairCheckController@discount_type');
    $router->get('/repair_check/order_mark', 'RepairCheckController@order_mark');
    $router->get('/repair_check/setOrderRemark', 'RepairCheckController@setOrderRemark');
    $router->get('/repair_check/setRepairRemark', 'RepairCheckController@setRepairRemark');
    $router->get('/repair_check/batchIsTell', 'RepairCheckController@batchIsTell');
    $router->resource('repair_check', RepairCheckController::class);
    $router->get('/repair_check/view/{id}', 'RepairCheckController@view');
    $router->get('/repair_check/view2/{id}', 'RepairCheckController@view2');
    $router->get('/repair_check/print/{id}', 'RepairCheckController@print_order');
    $router->post('/repair_check/cancel', 'RepairCheckController@cancel');
    $router->post('/repair_check/accessory_lack_notice', 'RepairCheckController@accessory_lack_notice');
    $router->post('/repair_check/aging_test_notice', 'RepairCheckController@aging_test_notice');


    $router->get('/express/express_route', 'ExpressController@express_route');
    $router->get('/express/express_route_fresh', 'ExpressController@express_route_fresh');
    $router->get('/express/express_fee', 'ExpressController@express_fee');
    $router->resource('/express', ExpressController::class);
    $router->get('/pay/notify', 'PayOrderController@pay_notify');
    $router->resource('/pay', PayOrderController::class);

    $router->get('/repair_quality/quality_view/{id}', 'RepairQualityController@quality_view');
    $router->get('/repair_quality/quality_list/{id}', 'RepairQualityController@quality_list');
    $router->post('/repair_quality/quality_list', 'RepairQualityController@quality_list');
    $router->resource('/repair_quality', RepairQualityController::class);

    $router->get('repair_exp_go/express_one_order', 'RepairExpGoController@express_one_order');
    $router->post('repair_exp_go/express_one_order', 'RepairExpGoController@express_one_order');
    $router->get('repair_exp_go/express/{id}', 'RepairExpGoController@express');
    $router->post('repair_exp_go/express', 'RepairExpGoController@express');
    $router->get('repair_exp_go/yz_express/{id}', 'RepairExpGoController@yz_express');
    $router->post('repair_exp_go/yz_express', 'RepairExpGoController@yz_express');
    $router->get('repair_exp_go/yz_express_one_order', 'RepairExpGoController@yz_express_one_order');
    $router->post('repair_exp_go/yz_express_one_order', 'RepairExpGoController@yz_express_one_order');
    $router->get('repair_exp_go/cancel_express', 'RepairExpGoController@cancel_express');
    $router->get('repair_exp_go/manual_express/{id}', 'RepairExpGoController@manual_express');
    $router->post('repair_exp_go/manual_express', 'RepairExpGoController@manual_express');

    $router->get('repair_exp_go/expense', 'RepairExpGoController@expense');
    $router->get('repair_exp_go/expense_print', 'RepairExpGoController@expense_print');
    $router->resource('/repair_exp_go', RepairExpGoController::class);
    $router->resource('machine_category', MachineCategoryController::class);

    $router->resource('damage_list', DamageListController::class);
    $router->resource('machine_quality_list', MachineQualityListController::class);

    $router->get('/repair_abandon', 'RepairOrderController@repair_abandon');
    $router->get('/material_group', 'RepairOrderController@material_group');
    $router->get('/repair_sure', 'RepairOrderController@repair_sure');
    $router->resource('repair_order', RepairOrderController::class);
    $router->get('/repair_order/view/{id}', 'RepairOrderController@view');
    $router->post('/repair_order/cancel', 'RepairOrderController@cancel');
    $router->get('/repair_order/express/{id}', 'RepairOrderController@express');
    $router->post('/repair_order/express', 'RepairOrderController@express');
    $router->resource('post_endpoint', PostEndpointController::class);
    $router->resource('post_config', ConfigController::class);
    $router->post('post_config/picture_upload', 'ConfigController@uploadPicture')
        ->name('config_picture_upload');
    $router->get('category_damage', 'SelfTestController@category_damage');
    $router->resource('self_test', SelfTestController::class);
    $router->post('self_test/picture_upload', 'SelfTestController@uploadPicture')
        ->name('self_test_picture_upload');

    $router->get('/material/upload', 'MaterialController@upload');
    $router->post('/material/upload', 'MaterialController@upload');


    //机型管理
    $router->get('machine_type/category', 'MachineTypeController@machine_category');
    $router->get('machine_type/category_prepend', 'MachineTypeController@machine_category_prepend');


    $router->resource('machine_type', MachineTypeController::class);

    //物料管理
    $router->resource('material_category', MaterialCategoryController::class);
    $router->resource('material', MaterialController::class);
    $router->resource('material_message', MaterialMessageController::class);

    //视频
    $router->resource('trainvideo', TrainVideoController::class);

    //统计
    //每日订单
    $router->get('statistics/order_view', 'StatisticsController@orderView');
    $router->get('statistics/daily_order', 'StatisticsController@dailyOrder');
    $router->get('statistics/order_info', 'StatisticsController@orderInfo');
//    订单分布
    $router->get('statistics/order_distribute', 'StatisticsController@order_distribute');
//    保内保外
    $router->get('statistics/order_in_period', 'StatisticsController@order_in_period');
    $router->get('statistics/damage_accessory', 'StatisticsController@damageAccessory');
//    每日看板
//    $router->get('statistics/daily_block', 'StatisticsController@dailyBlock');
    //终端保卡数量流水
    $router->get('statistics/endpoint_warranty/flow', 'StatisticsController@endpointWarrantyFlow')
        ->name('endpoint_warranty_flow');
    $router->get('statistics/endpoint_warranty/flow/model_count', 'StatisticsController@endpointWarrantyFlowModelCount')
        ->name('endpoint_warranty_flow_model_count');
    $router->get('statistics/endpoint_warranty/model_sum_price', 'StatisticsController@endpointWarrantyModelSumPrice')
        ->name('endpoint_warranty_model_sum_price');
    //终端每日保卡
    $router->get('statistics/daily_warranty', 'StatisticsController@dailyWarranty');
    //终端地区保卡
    $router->get('statistics/agency_action', 'StatisticsController@agencyAction')
        ->name('agency_action');
    //终端销售之星
    $router->get('statistics/sales_star', 'StatisticsController@salesStar')
        ->name('sales_star');
    //终端销售之星详情
    $router->get('statistics/sales_star/detail', 'StatisticsController@salesStarDetail')
        ->name('sales_star_detail');
    $router->get('statistics/sales_star/rank', 'StatisticsController@salesStarRank')
        ->name('sales_star_rank');
    $router->get('statistics/sales_star/detail_export', 'StatisticsController@salesStarDetailExport')
        ->name('sales_star_detail_export');
    $router->get('statistics/sales_star/rank_export', 'StatisticsController@salesStarRankExport')
        ->name('sales_star_rank_export');
    $router->get('statistics/endpoint_positions', 'StatisticsController@endpointPositions')
        ->name('endpoint_positions');


    //维修单
    $router->get('repair_bill/print', 'RepairBillController@print');
    $router->get('repair_bill/search', 'RepairBillController@search');
    $router->resource('repair_bill', RepairBillController::class);


    //故障管理
    $router->get('machine_malfunction/getMachineMalfunctionByParentId',
        'MachineMalfunctionController@getMachineMalfunctionByParentId');
    $router->resource('machine_malfunction', MachineMalfunctionController::class);
    //配件管理
    $router->get('machine_accessory/calculatePrice', 'MachineAccessoryController@calculatePrice');
    $router->get('machine_accessory/getAccessoryByParentId', 'MachineAccessoryController@getAccessoryByParentId')
        ->name('getAccessoryByParentId');
    $router->resource('machine_accessory', MachineAccessoryController::class);
    //机型配件管理
    $router->get('machine_accessory_relation/getAccessoriesByModelId',
        'MachineAccessoryRelationController@getAccessoriesByModelId');
    $router->resource('machine_accessory_relation', MachineAccessoryRelationController::class);

    //维修统计相关
    $router->get('repair_statistics/summary', 'RepairStatisticsController@summary');
    $router->get('repair_statistics/summary/export', 'RepairStatisticsController@exportSummary')
        ->name('repair_statistics.export_summary');

    //维修全局统计
    $router->get('statistics/summary', 'StatisticsController@summary')
        ->name('statistics.summary');
    //维修全局统计V2版本
    $router->get('statistics/summary_v2', 'StatisticsController@summaryV2')
        ->name('statistics.summaryV2');
    $router->get('statistics/daily_statistics', 'StatisticsController@daily_statistics');

//    自选配件
    $router->resource('optional_accessory_category', OptionalAccessoryCategoryController::class);
    $router->resource('optional_accessory', OptionalAccessoryController::class);

//    常见问题
    $router->resource('faq_category', FaqCategoryController::class);
    $router->resource('faq_question', FaqQuestionController::class);
    $router->post('faq_question/picture_upload', 'ConfigController@uploadPicture')
        ->name('faq_question_config_picture_upload');
    $router->get('faq/category', 'FaqQuestionController@faq_category');

//    碎屏保
    $router->get('broken_screen_insurance/view/{id}', 'BrokenScreenInsuranceController@view');
    $router->get('broken_screen_insurance/refund', 'BrokenScreenInsuranceController@refund'); // 碎屏保退款
    $router->get('broken_screen_insurance/change_status', 'BrokenScreenInsuranceController@change_status');
    $router->get('/broken_screen_insurance_supply/standard', 'BrokenScreenInsuranceSupplyController@standard');
    $router->get('/broken_screen_insurance_supply/check', 'BrokenScreenInsuranceSupplyController@check');
    $router->resource('broken_screen_insurance_supply', BrokenScreenInsuranceSupplyController::class);

    $router->resource('broken_screen_insurance', BrokenScreenInsuranceController::class);
    $router->resource('broken_screen_insurance_standard', BrokenScreenInsuranceStandardController::class);
    $router->resource('standard_category', BrokenScreenInsuranceStandardCategoryController::class);

//    寄修订单退款
    $router->get('order/refund', 'RefundOrderController@orderRefund');
    $router->get('order/refund_query', 'RefundOrderController@orderRefundQuery');
    $router->post('order/refund', 'RefundOrderController@orderRefund');
    $router->resource('refund', RefundOrderController::class);
//    终端代寄审核
    $router->get('/endpoint_order_post_check/express_one_order', 'EndpointOrderPostCheckController@express_one_order');
    $router->post('/endpoint_order_post_check/express_one_order', 'EndpointOrderPostCheckController@express_one_order');

    $router->resource('endpoint_repair_manager', EndpointRepairManagerController::class);

    $router->resource('endpoint_order_post_check', EndpointOrderPostCheckController::class);
    $router->resource('endpoint_post_repair_order', EndpointPostRepairListController::class);

    $router->resource('export_record', ExportController::class);
    $router->get('/post_repair_manage_export', 'ExportController@post_repair_manage_export');
    $router->get('/repair_material_export_by_pay_time', 'ExportController@repair_material_export_by_pay_time');
    $router->get('/broken_screen_insurance_export', 'ExportController@broken_screen_insurance_export');

    $router->get('/customer_service_manage/push_msg_to_app', 'CustomerServiceManageController@PushMsgToApp'); // 推送消息到app
    $router->get('/customer_service_manage/batchIsTell', 'CustomerServiceManageController@batchIsTell'); // 客服知会批处理
    $router->get('/customer_service_manage/set400Remark', 'CustomerServiceManageController@set400Remark');
    $router->get('/customer_service_manage/setOrderRemark', 'CustomerServiceManageController@setOrderRemark');
    $router->resource('customer_service_manage', CustomerServiceManageController::class);
    $router->get('/customer_service_manage/view/{id}', 'CustomerServiceManageController@view');
    $router->post('/customer_service_manage/postRepairAgain', 'CustomerServiceManageController@postRepairAgain'); // 重新下单
    $router->post('/customer_service_manage/pay_notice', 'CustomerServiceManageController@pay_notice');
    $router->post('/customer_service_manage/accessory_lack_notice', 'CustomerServiceManageController@accessory_lack_notice');
    $router->post('/customer_service_manage/aging_test_notice', 'CustomerServiceManageController@aging_test_notice');
    $router->get('statistics/report', 'StatisticsController@report');

    //特批出库
    $router->get('special_sales/import', 'SpecialSalesController@import_data_view');
    $router->post('special_sales/import', 'SpecialSalesController@import_data_store');
    $router->resource('special_sales', SpecialSalesController::class);//这个需要放在最后


    $router->group(['middleware' => 'admin.permission:allow,administrator,manager'], function ($router) {
        // 新菜单管理
        $router->resource('auth/menu', NewMenuController::class);
    });
});


//用户和终端审核核销post_sign
Route::group([
    'prefix' => config('admin.prefix'),
    'namespace' => Admin::controllerNamespace(),
    'middleware' => ['web', 'admin', 'admin.permission:allow,manager,topAgency,scmanager,leader,topAgencyAfterSale'],
], function ($router) {
    $router->post('users/resetPassword', 'UserController@resetPassword');
    $router->resource('users', UserController::class);
    $router->post('endpoint/collate', 'EndpointController@collate');
    $router->resource('endpoint_audit', EndpointAuditController::class);
});

//终端管理和统计
Route::group([
    'prefix' => config('admin.prefix'),
    'namespace' => Admin::controllerNamespace(),
    'middleware' => [
        'web',
        'admin',
        'admin.permission:allow,manager,topAgency,leader,endpointManager,secondAgency,scmanager,aftersale,service,topAgencyFinance,topAgencyAfterSale,action4S',
    ],
], function ($router) {
    $router->get('endpoint/printout', 'EndpointController@printout');
    $router->resource('endpoint', EndpointController::class);

    $router->get('statistics/agency_warranty', 'StatisticsController@agencyWarranty');
    $router->get('statistics/model_warranty', 'StatisticsController@modelWarranty');
    $router->get('statistics/endpoint_warranty', 'StatisticsController@endpointWarranty')->name('endpoint_warranty');

});


//代理地区
Route::group([
    'prefix' => config('admin.prefix'),
    'namespace' => Admin::controllerNamespace(),
    'middleware' => ['web', 'admin', 'admin.permission:allow,manager,action,scmanager,leader'],
], function ($router) {
    //导出代理地区
    $router->get('agency/export', 'AgencyController@export');
    $router->post('agency/del', 'AgencyController@del');
    $router->get('agency/topAgency', 'AgencyController@topAgency');
    $router->resource('agency', AgencyController::class);


    $router->resource('partition', PartitionController::class);
    $router->resource('endpoint_channel', EndpointChannelController::class);
    $router->resource('agency_user', AgencyUserController::class);
    $router->delete('endpoint_collation/deleteAll', 'EndpointCollationController@deleteAll');
    $router->resource('endpoint_collation', EndpointCollationController::class);

});

