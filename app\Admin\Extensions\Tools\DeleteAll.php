<?php

namespace App\Admin\Extensions\Tools;

use Encore\Admin\Grid\Tools\BatchAction;

class DeleteAll extends BatchAction
{

    public function script()
    {
        return <<<EOT

$('{$this->getElementClass()}').on('click', function() {
    var res = confirm("确定要清空所有记录?");
    if (res == true)
    {
       $.ajax({
        method: 'post',
        url: '{$this->resource}/deleteAll',
        data: {
            _token:LA.token,
            ids: selectedRows()
        },
        success: function () {
            $.pjax.reload('#pjax-container');
            toastr.success('操作成功');
        }
    });
    }
    else
    {
      alert("You pressed Cancel!");
    }
});

EOT;

    }
}