<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 2017/8/17
 * Time: 10:39
 */

namespace app\Api\Transformers;

use League\Fractal\TransformerAbstract;
use App\Models\MaterialCategory;

class MaterialCategoryTransformers extends TransformerAbstract {
    public function transform(MaterialCategory $materialCategory) {
        return [
            'name' => $materialCategory['name'],
        ];
    }
}