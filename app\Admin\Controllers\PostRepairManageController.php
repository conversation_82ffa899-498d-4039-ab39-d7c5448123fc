<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\StatisticsExporter;
use App\Admin\Extensions\OrderExporter;
use App\Admin\Extensions\OrderExporter1;
use App\Admin\Extensions\Producer;
use App\Admin\Extensions\Tools\Button;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\QuickPickTool;

use App\Models\AgentOrderCorrelation;
use App\Models\BrokenScreenInsurance;
use App\Models\BrokenScreenInsuranceUsage;
use App\Models\Machine;
use App\Models\MachineAccessory;
use App\Models\MachineAccessoryRelation;
use App\Models\MachineAccessoryTree;
use App\Models\MachineMalfunction;
use App\Models\Material;
use App\Models\Order;
use App\Models\OrderExtend;
use App\Models\OrderLog;
use App\Models\PostRepairAccessory;
use App\Models\PostRepairMalfunction;
use App\Models\PostRepairMaterial;
use App\Models\PostRepairOptionalAccessory;
use App\Models\PostRepairUsedMaterial;

use App\User;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\MessageBag;

//Admin::css('/static/css/scroll_bar.css');
class PostRepairManageController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {
            $content->header('寄修订单管理');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {
            $content->header('寄修订单');
            $content->description('编辑');

            $content->body($this->form($id)->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {
            $content->header('寄修订单查看');

            $order = Order::where(['id' => $id])->first();
            $order_extend = OrderExtend::where(['sn' => $order->sn])->first();

            $pr_material = DB::table('pr_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_material.mat_id')
                ->leftjoin('machine_malfunction', 'machine_malfunction.id', '=', 'pr_material.malfunction_id')
                ->leftjoin('material', 'material.id', '=', 'pr_material.material_id')
                ->where('pr_material.pr_sn', $order['sn'])
                ->select('machine_accessory_tree.title as title', 'machine_malfunction.title as malfunction',
                    'material.price as price', 'pr_material.count as count')
                ->get()
                ->toArray();
            $pr_used_material = DB::table('pr_used_material')
                ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_used_material.mat_id')
                ->leftjoin('material', 'material.id', '=', 'pr_used_material.material_id')
                ->where('pr_used_material.pr_sn', $order['sn'])
                ->select('material.name as name', 'material.price as price', 'pr_used_material.count as count',
                    'material.code as code', 'material.old_code as old_code',
                    'material.specification as specification', 'material.from as from')
                ->get()
                ->toArray();
            $pr_oa = PostRepairOptionalAccessory::getShowListBySn($order->sn);
            $content->body(view('admin/post_repair/view',
                compact('order', 'pr_material', 'pr_used_material', 'pr_oa', 'order_extend')));
        });
    }

    public function log($id)
    {
        $data = DB::table('order_log')
            ->leftjoin('admin_users', 'order_log.admin', '=', 'admin_users.id')
            ->where('order_log.pr_sn', $id)
            ->orderby("order_log.date")
            ->get()->toArray();
        $data2 = DB::table('order_log2')
            ->leftjoin('admin_users', 'order_log2.admin', '=', 'admin_users.id')
            ->where('order_log2.pr_sn', $id)
            ->orderby("order_log2.date")
            ->get()->toArray();
        return view('admin/post_repair_manage/log', compact('data','data2'));
    }

    public function printf($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('打印检测报告');
            $order = Order::where(['id' => $id])->first();
            $post_malfunction = DB::table('pr_malfunction')
                ->join('machine_malfunction', 'machine_malfunction.id', '=', 'pr_malfunction.malfunction_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_malfunction.title')->toArray();
            $malfunction = implode('，', $post_malfunction);
            $post_accessory = DB::table('pr_accessory')
                ->join('machine_accessory', 'machine_accessory.id', '=', 'pr_accessory.mar_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_accessory.title')->toArray();
            $accessory = implode('，', $post_accessory);
            $content->body(view('admin/post_order/print',
                compact('order', 'malfunction', 'accessory')));
        });
    }

    public function cancel(Request $request)
    {
        foreach (Order::find(request()->get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修订单');
            $content->description('创建');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $action_style = <<<css
    .action_link {
        white-space: nowrap;
    }
    .action_link:hover {
        text-decoration-line: underline;
    }
css;
        $action_style_str = base64_encode($action_style);

        $script = <<<EOF

            // 设置操作链接-设置样式
            const action_style = decodeURIComponent(escape(atob('$action_style_str')));
            $('head').append('<style type="text/css">' + action_style + '</style>');

            //查看快递路由信息
            $('.express_route_fresh').click(function(){
                var id = $(this).attr('value');
                console.log('.express_route_fresh click id: ' + id);
                layer.open({
                      type: 2,
                      title: '快递路由',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['1000px', '60%'],
                      content: 'express/express_route_fresh?readboy_sn='+id //iframe的url
                });
            });

            //查看订单日志
            $('.log_view').click(function(){
                var id = $(this).attr('value');
                layer.open({
                      type: 2,
                      title: '日志',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['1000px', '60%'],
                      content: 'post_repair_manage/log/'+id //iframe的url
                });
            });
            $('#export').click(function(){
                 param = window.location.search;
                //  alert(window.location.search);
                 $.get('/admin/post_repair_manage_export'+param, function(data){
                    if(data['ok'] == 0){
                        alert(data['msg']);
                    }else{
                        alert(data['msg']);
                    }
                 });
            });
            $("input[name='sn']").focus();
            // $("input[name='come_exp_sn']").focus();

            // 导出去年寄修耗时情况
            $('.btn-repair-use-time').click(function(){
                url = window.location.href;
                    if (url.indexOf("&repair_use_time=1&_export_=1") < 0) {
                    if (url.indexOf("?") < 0) {
                        url = url + "?&repair_use_time=1&_export_=1";
                    } else {
                        url = url + "&repair_use_time=1&_export_=1";
                    }
                }
                window.open(url);
            });
EOF;
        Admin::script($script);
        // Admin::css('/static/css/scroll_bar.css');
        return Admin::grid(Order::class, function (Grid $grid) {
            // $grid->model()->where('status', '>=', 200);
            $grid->disableCreation();
            $grid->disableExport();
            // $grid->exporter(new OrderExporter());
            // if (Request::get('order_export')){
            //     $grid->exporter(new OrderExporter1());
            // }
            // 快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => [],
                ],
                1 => [
                    'name' => '待审核',
                    'param' => [['status', '=', Order::WAIT_AUDIT]],
                ],
                2 => [
                    'name' => '待检测',
                    'param' => [[DB::Raw('status in (' . Order::COME_SURE_IS_TELL . ',' . Order::NO_CHECK_IS_TELL . ')'), '1']],
                ],
                3 => [
                    'name' => '待支付',
                    'param' => [['status', '=', Order::CHECK_FINISH_IS_TELL]],
                ],
                4 => [
                    'name' => '待维修',
                    'param' => [['status', '=', Order::PAY_FINISH]],
                ],
                5 => [
                    'name' => '待发货',
                    'param' => [['status', '=', Order::REPAIR_FINISH]],
                ],
            ];
            // 筛选条数
            foreach ($option as $key => $value) {
                $option[$key]['count'] = Order::where($value['param'])->count();
            }

            // 寄修耗时统计导出
            if (Request::get('repair_use_time')) {
                $grid->exporter(new StatisticsExporter());
            }

            // 自定义状态快捷筛选按钮
            $grid->tools(function ($tools) use ($option) {
                $html = <<<EOF
                <div class="btn-group" style=" margin-top: 10px;">
                      <button id="export"  class="btn btn-sm btn-warning" >
                        <i class="fa fa-print" ></i > 导出寄修概况
                      </button >
                </div >
EOF;
                $tools->append($html);

                $html_print = <<<EOF
                <div class="btn-group" style=" margin-top: 10px;">
                      <a href ="" class="btn btn-sm btn-success btn-repair-use-time" >
                        <i class="fa fa-print" ></i > 导出去年寄修耗时情况
                      </a >
                </div >
EOF;
                if (Admin::user()->isAdministrator()) {
                    $tools->append($html_print);
                }
                $tools->append(new QuickPickTool($option));
                $tools->batch(function ($batch) {
                    // $batch->disableDelete(); 
                    $batch->add('取消订单', new Cancel());
                });
            });

            // 根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])->orderBy('id', 'desc');
            }
            $grid->model()->where($option[-1]['param'])->orderBy('id', 'desc');

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('come_exp_sn', '快递单号');
                $filter->like('sn', '维修订单号');
                $filter->like('barcode', 'S/N码');
                $filter->like('name', '联系人');
                $filter->is('status', '订单状态')
                    ->select(Order::STATUS);
                $filter->is('quality', '品检状态')->select(Order::quality);
                $filter->equal('model_name', '产品型号')->select(
                    Machine::orderBy('name')->pluck('name', 'name')->all()
                );
                //$filter->like('need_invoice','是否开具发票')->select([1=>'是',0=>'否']);
                $filter->between('created_at', '订单提交日期')->datetime();
                $filter->between('updated_at', '最后操作时间')->datetime();
                $filter->between('updated_at_last', '回寄下单日期')->datetime();
                //$filter->scope('order_log.date');
                // 多条件查询
                $filter->between('receive_time', '签收时间')->datetime();
                $filter->like('type', '寄修类型')->select(Order::post_repair_type);
                $filter->equal('in_period', '保内保外')->select([0 => '无保修信息', 1 => '保内', 2 => '保外']);
                $filter->like('auditor_user.name', '审核人');
                $filter->equal('check_man', '检测人员')
                    ->select(Order::join('admin_users as au', 'order.check_man', '=', 'au.id')
                        ->pluck('au.name', 'order.check_man'));
                $filter->is('repeat_order', '寄修情况')->select(Order::repeat_repair);
                $filter->equal('order_extend.print_man', '打印人员')->select(DB::table('order_extend as oe')
                    ->join('admin_users as au', 'oe.print_man', '=', 'au.id')->pluck('au.name', 'oe.print_man'));

            });

            //表格显示列
            $grid->id('ID')->sortable();
            //$grid->uid('UID');
            $grid->column('user.username', '用户名');
            $grid->column('user.phone', '用户手机号');
            Order::order_priority_column($grid);
            $grid->sn('寄修订单编号');
            //$grid->column('come_exp_sn', '寄来快递单号')->display(function ($value) {
            //    $html = '<a class="express_route_fresh" href="javascript:void(0);" value="' . $value . '">' . $value . '</a>';
            //    return $html;
            //});
            //$grid->column('go_exp_sn', '寄去快递单号')->display(function ($value) {
            //    $html = '<a class="express_route_fresh" href="javascript:void(0);" value="' . $value . '">' . $value . '</a>';
            //    return $html;
            //});
            $grid->column('寄来-寄去快递单号')->display(function () {
                $come = $this->come_exp_sn;
                $go = $this->go_exp_sn;
                $a_come = '<a class="express_route_fresh" href="javascript:void(0);" value="' . $come . '">' . $come . '</a>';
                $a_go = '<a class="express_route_fresh" href="javascript:void(0);" value="' . $go . '">' . $go . '</a>';
                return ($come ? $a_come : '———') . '<br/>' . ($go ? $a_go : '———');
            });
            //$grid->barcode('S/N码');
            $grid->column('S/N码-机型')->display(function () {
                return $this->barcode . '<br/>' . $this->model_name;
            });
            //$grid->name('联系人');
            //$grid->phone('用户联系方式');
            $grid->column('联系人')->display(function () {
                return $this->name . '<br/>' . $this->phone;
            });
            //$grid->endpoint()->name('寄修售后点');
            $grid->amount('总费用￥');
            $grid->pay_amount('维修时需支付金额￥');
            $grid->amount_in_ar('弃修时需支付金额￥');
            //$grid->column('支付金额￥')->display(function () {
            //    $suffix = in_array($this->connect, [3, 6]) ? '<br/>(弃修)' : NULL;
            //    return $this->pay_amount . $suffix;
            //});
            $grid->need_invoice('是否开具发票')->display(function ($value) {
                if ($value == 1) {
                    return '是';
                } else {
                    return '否';
                }
            });
            $grid->created_at('订单提交时间');
            //$grid->updated_at_last('订单回寄时间')->sortable();
            $grid->column('expense.updated_at', '核销时间')->display(function ($value) {
                if ($value) {
                    return $value;
                } else {
                    return '未核销';
                }
            });
            //$grid->connect('联系状态')->editable('select', Order::CONNECT);
            $grid->status('订单状态')->display(function ($status) {
                $s = Order::STATUS;
                if (array_key_exists($status, $s)) {
                    return $status . $s[$status];
                } else {
                    return "———";
                }
            })->sortable();
            /* $grid->connect('联系状态')->display(function ($connect) {
                $s = Order::CONNECT;
                if (array_key_exists($connect, $s)) {
                    return $connect . $s[$connect];
                } else {
                    return "———";
                }
            }); */
            $grid->check_user()->name('检测人')->display(function ($name) {
                if ($name == '')
                    return '———';
                return $name;
            });
            $grid->repair_user()->name('维修人')->display(function ($name) {
                if ($name == '')
                    return '———';
                return $name;
            });
            $grid->repeat_order('二次维修')->display(function ($value) {
                if ($value == 1) {
                    return '是';
                } else {
                    return '否';
                }
            });
            $grid->quality('品检状态')->display(function ($value) {
                return Order::quality[$value];
            });
            $grid->type('寄修类型')->display(function ($value) {
                return Order::post_repair_type[$value];
            });
            $grid->order_extend()->order_mark('订单标注')->display(function ($order_mark) {
                $str = '未知';
                if (array_key_exists($order_mark, Order::ORDER_MARK)) {
                    $str = Order::ORDER_MARK[$order_mark];
                }
                switch ($order_mark) {
                    case Order::ORDER_MARK_NORMAL:
                        return '<span>' . $str . '</span>';
                    default:
                        return '<span  style="color: red">' . $str . '</span>';
                }
            });

            $grid->actions(function ($actions) {
                $actions->disableDelete();

                $status = $actions->row->status;
                $c = 'post_repair/view/' . $actions->getKey();
                $html = '<a href="' . $c . '" class="action_link"><span style="color:blue">查看 </span></a>';
                $actions->append($html);

                $data = $actions->row;
                $c = 'post_repair_manage/log/' . $data->sn;
                $html = '<a href="javascript:void(0);" value="' . $data->sn .
                    '" class="action_link log_view"><span style="color:green">日志 </span></a>';
                $actions->append($html);

                $actions->append('<br/>');

                // 客服一级,客服二级,审核客服去掉编辑权限
                if (Admin::user()->inRoles(['customer_service_one', 'customer_service_two'])) {
                    $actions->disableEdit();
                } else {
                    if ($actions->row->order_extend['sign_in_status'] > 0) {
                        $c = 'order_extend/' . $actions->row->order_extend['id'] . '/edit';
                        $html = '<a href="' . $c . '" class="action_link"><span style="color:blue">编辑签收信息 </span></a>';
                        $actions->append($html);
                    }
                }
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id = null)
    {
        if (!Admin::user()->inroles(['Administrator', 'repair_service','customer_service_approve'])) {
            return Admin::content(function (Content $content) {
                $error = new MessageBag([
                    'title' => '无法访问',
                    'message' => '没有权限！',
                ]);
                return $content->body($error);
            });
        }

        $ar_title_js = MachineAccessoryTree::get_ar_title_js();
        $staff_default = Order::express_default();
        $oa_status_cancel = PostRepairOptionalAccessory::STATUS[PostRepairOptionalAccessory::STATUS_CANCEL];

        $optional_accessory_style = <<<css
    #optional_accessory_table_in td,th {
        padding: 10px;
    }
css;
        $optional_accessory_style_str = base64_encode($optional_accessory_style);
        $optional_accessory_script = <<<js
    // 获取定位元素
    let n_m_hr_top = $('#has-many-repair_material').prev();
    let n_m_title = $('#has-many-repair_material').prev().prev();
    let n_oa_title_in = $('#optional_accessory_title');
    let n_oa_title_raw = n_oa_title_in.parent().hide().parent().parent();
    // 设置标题行
    let n_oa_title_new = n_m_title.clone();
    let n_oa_title_text = n_oa_title_new.find('h4.pull-right');
    n_oa_title_text.parent().css('text-align', 'right');
    n_oa_title_text.html(n_oa_title_in.text());
    n_oa_title_raw.before(n_oa_title_new);
    n_oa_title_raw.before(n_m_hr_top.clone());
    n_oa_title_raw.after('<hr>');
    // 读取数据
    let n_oa_json = $('#optional_accessory_json');
    const oa_json_str = n_oa_json.text();
    const oa_list = JSON.parse(oa_json_str);
    const oa_status_cancel = '$oa_status_cancel';
    let oa_all_cancel = oa_list.every(function (d) {
        return d.status == oa_status_cancel;
    });
    let oa_some_cancel = oa_list.some(function (d) {
        return d.status == oa_status_cancel;
    });
    const oa_price_total = oa_list.reduce(function (b, d) {
        return b + parseFloat(d.price_sum);
    }, 0);
    const oa_price_normal = oa_list.reduce(function (b, d) {
        return b + (d.status == oa_status_cancel ? 0 : parseFloat(d.price_sum));
    }, 0);
    const oa_count_total = oa_list.reduce(function (b, d) {
        return b + d.count;
    }, 0);
    if (!oa_list || oa_list.length <= 0) {
        n_oa_title_raw.hide();
        oa_all_cancel = false;
        oa_some_cancel = false;
    }
    console.log({ oa_list, oa_price_total, oa_price_normal, oa_count_total, oa_all_cancel, oa_some_cancel });
    let oa_title_brackets = oa_all_cancel ? '已取消' : ('￥' + oa_price_total + '，共计' + oa_count_total + '件');
    n_oa_title_text.html(n_oa_title_in.text() + '<br/>（' + oa_title_brackets + '）');
    // 设置hidden-form
    $('.optional_accessory_amount').val(oa_price_total);
    $('.optional_accessory_cast').val(oa_price_normal);
    // 设置表格
    let n_oa_table = $('#optional_accessory_table_in');
    let n_oa_table_thead = n_oa_table.find('thead');
    let n_oa_table_thead_tr = n_oa_table_thead.find('tr');
    let n_oa_table_tbody = n_oa_table.find('tbody');
    let n_oa_table_tfoot_tr = n_oa_table.find('tfoot tr');
    let oa_table_columns = [
        { name: '配件分类', key: 'oac_name' },
        { name: '配件名称', key: 'oa_name' },
        { name: '物料编码', key: 'm_code' },
        { name: '物料名称', key: 'm_name' },
        { name: '购买数量', key: 'count' },
        { name: '购买单价', key: 'price' },
        { name: '合计', key: 'price_sum' },
        { name: '状态', key: 'status' },
    ];
    // 设置表格-设置表头
    oa_table_columns.forEach(oa_table_set_thead_th);
    function oa_table_set_thead_th(col) {
        let th_str = '<th>' + col.name + '<\/th>';
        n_oa_table_thead_tr.append(th_str);
    }
    // 设置表格-设置内容
    for (var ri in oa_list) {
        let tr_str = oa_table_get_tbody_tr_td(oa_table_columns, oa_list[ri]);
        n_oa_table_tbody.append(tr_str);
    }
    function oa_table_get_tbody_tr_td(columns, row) {
        let td_str = columns.reduce(function (b, c) {
            return b + '<td>' + row[c.key] + '<\/td>';
        }, '');
        return '<tr>' + td_str + '<\/tr>';
    }
    // 设置表格-设置合计行
    tfoot_td_price_str = oa_price_total + '元';
    tfoot_td_price_str = oa_some_cancel ?
        ('<span style="text-decoration: line-through;">' + tfoot_td_price_str + '<\/span>&nbsp;<span>' + oa_price_normal + '元<\/span>') :
        ('<span>' + tfoot_td_price_str + '<\/span>');
    tfoot_td_str = '<td colspan="' + oa_table_columns.length + '">' +
                   '自选购配件价格：' +
                   tfoot_td_price_str +
                   '<\/td>';
    n_oa_table_tfoot_tr.append(tfoot_td_str);
    // 设置表格-设置样式
    const oa_style = decodeURIComponent(escape(atob('$optional_accessory_style_str')));
    $('head').append('<style type="text/css">' + oa_style + '</style>');
js;
        Admin::script($optional_accessory_script);

        $script = <<<js

            // 配件选项变更
            $(document).on('change', ".mat_id", function () {
                // 自动变更是否收费 判断仅弃修时收费
                var mat_text = this.selectedOptions[0].text;
                var ar_title = $ar_title_js;
                var is_charge_dom = $(this).closest('.fields-group').find(".is_charge");
                if (ar_title.filter(x => mat_text.indexOf(x) != -1).length > 0) {
                    $(is_charge_dom).val(2).trigger('change');
                } else {
                    $(is_charge_dom).val(1).trigger('change');
                }
                // 自动变更物料
                var target = $(this).closest('.fields-group').find(".material_id");
                var type = $('.type').val();
                var charge_type = $(this).parent().parent().prev().find('.charge_type').val();
                $.get("/admin/repair_check_material?q="+this.value+"&type="+ type + "&charge_type="+charge_type, 
                    function (data) {
                        target.find("option").remove();
                        defaultdata = [{"id":0,"text":"请选择"}];
                        setdata = $.map(data, function (d) {
                            d.id = d.id;
                            d.text = d.text;
                            return d;
                        });
                        data = setdata;
                        $(target).select2({
                            data: data
                        }).trigger('change');
                    });
            });
            $(document).on('change', ".charge_type", function () {
                var target = $(this).closest('.fields-group').find(".material_id");
                var type = $('.type').val();
                var charge_type = $(this).val();
                var mat_id = $(this).parent().parent().next().find('.mat_id').val();

                if (mat_id != 0) {
                    $.get("/admin/repair_check_material?q=" + mat_id + "&type=" + type + "&charge_type=" + charge_type, 
                        function (data) {
                            target.find("option").remove();
                            defaultdata = [{"id":0,"text":"请选择"}];
                            setdata = $.map(data, function (d) {
                                    d.id = d.id;
                                    d.text = d.text;
                                    return d;
                                });
                            data = setdata;
                            $(target).select2({
                                data: data
                            }).trigger('change');
                        });
                }
            });

            let staff_default = $staff_default;

            //别问我为什么不直接操作模板div,是因为用了DocumentFragment,操作不了,不是我用的!
            $(document).ready(function() {
                var in_period = $('.in_period').val()
                if (in_period != '1') {
                    calculatePrice();
                }
                // calculatePrice();
                function total() {
                    var staff = Number($('#staff_cast').val().replace(/,/g, ""));
                    // var staff = 0;
                    var exp_cast = 0;
                    // var exp_cast = Number($('#exp_cast').val().replace(/,/g, ""));
                    var accessory = Number($('#accessory_cast').val().replace(/,/g, ""));
                    var accessory_amount = Number($('#accessory_amount').val().replace(/,/g, ""));
                    var accessory_in_ar = Number($('#accessory_in_ar').val().replace(/,/g, ""));

                    var staff = Number($('#staff_cast').val().replace(/,/g, ""));
                    var ar_repeat = Number($('#ar_repeat').text());
                    var amount_in_ar = 0;

                    // 多次弃修判断
                    var staff_ar_base = staff > 0 ? staff : staff_default;
                    if (ar_repeat > 0) { //弃修过
                        amount_in_ar = staff_ar_base * 2 + accessory_in_ar + exp_cast * 2;
                    } else { //没弃修过
                        amount_in_ar = staff_ar_base + accessory_in_ar + exp_cast;
                    }

                    $('#amount').val(staff + accessory_amount + exp_cast + oa_price_normal);
                    $('#pay_amount').val(staff + accessory + exp_cast + oa_price_normal);
                    $('#amount_in_ar').val(amount_in_ar);
                }

                $(document).on('change', '.staff_cast', function() {
                    total();
                });

                // 统计配件价格
                function calculatePrice() {
                    let totalPrice = 0;
                    let accessoryAmount = 0;
                    let accessoryInAr = 0;
                    $('.material_id:visible').each(function() {
                        let optionText = $(this).find("option:selected").text();
                        let price = optionText.split('价格 : ')[1];
                        let count = $(this).parent().parent().next().find('#count').val();
                        let is_charge = $(this).parent().parent().next().next().find('.is_charge').val();
                        let price_in = $(this).parent().parent().next().next().next().find('#price_in').val().replace(/,/g, "");
                        // 手动备注的价格
                        if (price_in > 0) {
                            price = price_in
                        } else {
                            $(this).parent().parent().next().next().next().find('#price_in')[0].value = price;
                        }

                        accessoryAmount += price * count;
                        if (price == null) {
                            price = 0;
                        }
                        // 判断是否收费
                        if (is_charge == '1') {
                            totalPrice += price * count;
                        } else if (is_charge == '2') {
                            accessoryInAr += price * count;
                        }
                    });
                    // 实际收取维修配件费用
                    $('#accessory_cast').val(totalPrice);
                    // 总维修配件费用
                    $('#accessory_amount').val(accessoryAmount);
                    // 弃修时需支付配件费用
                    $('#accessory_in_ar').val(accessoryInAr);

                    total();
                }

                // 下拉框变化
                $(document).on('change', '.material_id', function() {
                    $(this).parent().parent().next().next().next().find('#price_in')[0].value = 0.00;
                    calculatePrice();
                });
                // 是否收费改变
                $(document).on('change', '.is_charge', function() {
                    calculatePrice();
                });
                // 加减数量按钮点击
                $('.has-many-repair_material-forms').on('click', 'button', function() {
                    calculatePrice();
                });
                // 备注配件价格改变
                $(document).on('change', '#price_in', function() {
                    calculatePrice();
                });
                // 移除按钮点击
                $(document).on('click', '.remove', function() {
                    calculatePrice();
                });
            });
js;

        Admin::script($script);
        return Admin::form(Order::class, function (Form $form) use ($id) {

            $form->select("uid", "修改用户登录账号")->options(function ($id) {
                $user = User::find($id);
                if ($user) {
                    return [$user->id => $user->name . "-" . $user->phone];
                }
            })->help("请输入手机号")->ajax("/admin/api/users");
            $form->text('name', '寄修人');
            $form->text('phone', '联系方式');
            $form->text('province', '省');
            $form->text('city', '市');
            $form->text('district', '区');
            $form->text('address', '详细地址');
            $form->display('sn', '寄修订单号');
            $form->text('barcode', 'S/N码');
            $form->text('serial', '序列号');
            $form->text('imei', 'IMEI码');
            //$form->barcode('sn', '寄修条码条码')->options(['width'=>1,]);
            $form->display('model_name', '机器型号');
            $form->select('in_period', '保修状态')->options(Order::in_period);
            $form->hidden('sn', '寄修订单号');
            $form->divider();
            $form->select('reason', '损坏原因')->options(Order::reason)->value(function ($reason) {
                return $reason;
            });
            $form->text('deal', '维修方式');
            $form->select('repair_man', '维修人')->options(function ($id) {
                $user = User::find($id);
                if ($user) {
                    return [$user->id => $user->name];
                }
            })->ajax('/admin/post_repair_manage/selectUser');

            $form->divider();
            $form->display('type', '寄修方式')->with(function ($value) {
                $str = '未知';
                if (array_key_exists($value, Order::post_repair_type)) {
                    $str = Order::post_repair_type[$value];
                }
                return '<span style="color:red">' . $str . '</span>';
            });
            $form->hidden('type');

            $barcode = Order::where('id', '=', $id)->value('barcode');
            $bsi = BrokenScreenInsurance::firstByBarcode($barcode);
            $has_screen_insurance = Order::where('id', '=', $id)->value('has_screen_insurance');
            $screen_insurance = Order::where('id', '=', $id)->value('used_screen_insurance');
            $in_si_period_status = Order::where('id', '=', $id)->value('in_si_period');
            $form->display('has_screen_insurance', '是否有碎屏保')->with(function ($value) {
                if ($value == 1) {
                    return '<span style="color:red">有碎屏保</span>';
                }
                return '无';
            });
            if ($has_screen_insurance == 1 && $bsi) {
                $form->display('in_si_period', '碎屏保状态')->with(function ($value) {
                    return Order::in_period[$value];
                });
                $form->display('碎屏保剩余次数')->with(function ($value) use ($bsi) {
                    return $bsi->insurance_times_remain . '次';
                });

                if ($screen_insurance == 0) {
                    if ($in_si_period_status == 1) {
                        if ($bsi->insurance_times_remain > 0) {
                            $used_options = [0 => '不使用'];
                            for ($i = 1; $i <= $bsi->insurance_times_remain; $i++) {
                                $used_options[$i] = '使用 ' . $i . ' 次';
                            }
                            $form->select('used_screen_insurance', '是否使用碎屏保')
                                ->options($used_options);
                        } else {
                            $html = '<span style="color: red">此机器碎屏保次数已用完</span>';
                            $form->html($html);
                        }
                    } else {
                        $html = '<span style="color: red">此机器碎屏保已过期无法使用</span>';
                        $form->html($html);
                    }
                } else {
                    $html = '<span style="color: red">此订单或机器已使用过碎屏保</span>';
                    $form->html($html);
                }
            }

            $form->hidden('has_screen_insurance');
            $form->hidden('in_si_period');

            $form->divider();
            $form->select('repeat_order', '是否二次维修')
                ->options([0 => "首次寄修", 1 => "二次寄修", 2 => "二次返修"]);
            $form->display('ar_repeat', '该条码之前弃修订单数')->with(function ($ar_repeat) {
                if ($ar_repeat > 0) {
                    return '<span id="ar_repeat" style="color: red">' . $ar_repeat . '</span>';
                } else {
                    return '<span id="ar_repeat">' . $ar_repeat . '</span>';
                }
            });
            $form->divider();

            $sn = Order::where('id', '=', $id)->value('sn');
            $oas = $sn ? PostRepairOptionalAccessory::getShowListBySn($sn) : array();
            $oas = PostRepairOptionalAccessory::statusToTextInList($oas);
            $oas_json = json_encode($oas, JSON_UNESCAPED_UNICODE);
            $optional_accessory_html_title = '<div id="optional_accessory_title">自选配件列表</div>';
            $optional_accessory_html_oas = '<div id="optional_accessory_json">' . $oas_json . '</div>';
            $optional_accessory_html_hide = '<div id="optional_accessory_hide">' .
                $optional_accessory_html_title .
                $optional_accessory_html_oas .
                '</div>';
            $optional_accessory_html_table = <<<html
    <div id="optional_accessory_table">
        <table id="optional_accessory_table_in">
            <thead>
                <tr></tr>
            </thead>
            <tbody>
            </tbody>
            <tfoot>
                <tr></tr>
            </tfoot>
        </table>
    </div>
html;
            $form->html($optional_accessory_html_hide . $optional_accessory_html_table);

            $type = Order::where('id', '=', $id)->value('type');
            $form->hasMany('repair_material', '维修配件列表',
                function (Form\NestedForm $form) use ($id, $type) {
                    $modelId = Order::where('id', '=', $id)->value('model_id');
                    $pr_sn = Order::where('id', '=', $id)->value('sn');
                    $form->select('charge_type', '收费类型')->options(function ($value) use ($type) {
                        if ($type == 2) {
                            return Order::charge_type;
                        } else {
                            return [1 => '客户价格'];
                        }
                    });
                    $form->select('mat_id', '维修配件')
                        ->options(MachineAccessoryTree::model_options($modelId))
                        ->load('malfunction_id', admin_url('repair_check_malfunction'));
                    $form->select('malfunction_id', '配件故障')->options(function ($value) {
                        $data = MachineMalfunction::where('id', $value)->get()->toArray();
                        $ret = array();
                        if (count($data) > 0) {
                            foreach ($data as $d) {
                                $ret[$d['id']] = $d['title'];
                            }
                        }
                        return $ret;
                    });
                    $form->select('material_id', '维修物料')->options(function ($value) use ($pr_sn, $form, $type) {
                        $data = Material::where('id', $value)->get()->toArray();
                        $ret = array();
                        $charge_type = PostRepairMaterial::where([['pr_sn', '=', $pr_sn], ['material_id', '=', $value]])->value('charge_type');

                        if (count($data) > 0) {
                            foreach ($data as $d) {
                                $from = $d['from'] == 1 ? '新仓库' : '旧仓库';
                                if ($charge_type == 2) {
                                    $ret[$d['id']] = $d['name'] . $d['specification'] . '|编码:' . $d['code'] . '|旧编码:' . $d['old_code'] .
                                        '|仓库:' . $from . '|库存:' . $d['quantity'] . '|价格 : ' . $d['price_first'];
                                } else {
                                    $ret[$d['id']] = $d['name'] . $d['specification'] . '|编码:' . $d['code'] . '|旧编码:' . $d['old_code'] .
                                        '|仓库:' . $from . '|库存:' . $d['quantity'] . '|价格 : ' . $d['price'];
                                }
                            }
                        }
                        return $ret;
                    });
                    $form->number('count', '数量')->default(1);
                    $form->select('is_charge', '是否收费')->options(Order::is_charge)->default(1);
                    $form->currency('price_in', '该配件在本订单的实收价格')->symbol('￥')
                        ->help('特殊打折的配件 请到【寄修管理】处修改价格，默认使用【维修物料】顾客价格。');
                });

            $form->currency('accessory_amount', '总维修配件价格')->symbol('￥');
            $form->currency('accessory_cast', '实际收取维修配件价格')->symbol('￥');
            $form->currency('accessory_in_ar', '弃修时收取配件价格')->symbol('￥');
            $form->divider();

            if ($id && $form->model()->find($id)->in_period !== 1) {
                $rb_exp_sn = $form->model()->find($id)->rb_come_exp_sn;
                $exp_sn = $form->model()->find($id)->come_exp_sn;
                $exp_cast = Express::query_order($rb_exp_sn, $exp_sn);
                # 用户为代理商寄修时分摊快递费
                if ($form->model()->find($id)->type == 3 && $exp_cast != '无法获取') {
                    $agent_order_sn = AgentOrderCorrelation::leftJoin('order', 'order.sn', '=', 'agent_order_correlation.order_sn')
                        ->where('order.id', $id)->value('agent_order_sn');
                    $count = AgentOrderCorrelation::leftJoin('order', 'order.sn', '=', 'agent_order_correlation.order_sn')
                        ->where([['agent_order_correlation.agent_order_sn', $agent_order_sn], ['order.come_exp_sn', $exp_sn]])->count();
                    if ($count > 0) {
                        //dd(intval($exp_cast));
                        $exp_cast = floatval($exp_cast) / $count;
                    }
                }
                //$exp_cast = 15;
            } else {
                $exp_cast = 0;
            }
            $form->currency('staff_cast', '快递费用')->symbol('￥')->default(intval($exp_cast))->help('快递费用  ' . $exp_cast);
            $form->hidden('exp_coat', '快递费用')->value(0);
            //$form->display('staff_cast', '检测费用')->value($exp_cast)->help('快递费用  '.$exp_cast. '元');
            $form->currency('amount', '总费用')->symbol('￥');
            $form->currency('pay_amount', '维修时需支付金额')->symbol('￥')->help('修改前金额：');
            $form->currency('amount_in_ar', '弃修时需支付金额')->symbol('￥')->help('客户弃修时收取');

            $amount_check_tips = '<div style="color: red">请核对各项金额</div>';
            $form->html($amount_check_tips);
            $form->divider();

            $form->text('receive_case', '收到的配件');
            $form->display('order_extend.sign_in_status', '签收状态')->with(function ($value) {
                if (array_key_exists($value, Order::SIGN_IN_STATUS)) {
                    return Order::SIGN_IN_STATUS[$value];
                } else {
                    return '未知';
                }
            });

            $form->text('deal_remark', '维修备注');
            $form->hidden('check_man');

            $form->select('connect', '联系状态')->options(Order::CONNECT);
            $form->select('status', '订单状态')->options(Order::STATUS);
            $form->select('order_extend.order_mark', '订单标注')->options(Order::ORDER_MARK);
            Order::order_priority_form($form);
            $form->hidden('audit_status');

            $form->saving(function (Form $form) use ($exp_cast) {
                //$form->check_man = Admin::user()->id;
                //不记录日志
                $form->no_log = 1;
                //dd($form->pay_amount);
                //微信支付如果有发起过支付请求，必须保证金额等信息一致，所以有金额变动，需要去掉内部支付单号重新生成
                if ($form->pay_amount != $form->model()->pay_amount && !empty($form->model()->rb_pay_sn) && empty($form->model()->is_paid)) {
                    $data = ['rb_pay_sn' => '', 'pay_com' => 0, 'pay_sn' => ''];
                    DB::table('order')->where('sn', $form->model()->sn)->update($data);
                }

                if ($form->status == 800) {
                    // dd($form->status);
                    if (empty($form->model()->updated_at_last)) {
                        $data = ['go_sure' => 1, 'updated_at_last' => date('Y-m-d H:i:s')];
                    } else {
                        $data = ['go_sure' => 1];
                    }
                    DB::table('order')->where('sn', $form->model()->sn)->update($data);
                    // $form->go_sure = 1;
                    // $form->updated_at_last = date('Y-m-d H:i:s');
                }
                if ($form->status == -200) {
                    $form->audit_status = 2;
                }
                if ($form->status == 200) {
                    $form->audit_status = 1;
                }
                if ($form->repair_material) {
                    foreach ($form->repair_material as $material) {
                        if (empty($material['material_id'])) {
                            $error = new MessageBag([
                                "title" => '错误提示',
                                "message" => "有物料提交不成功，请重新提交"
                            ]);
                            return back()->withInput()->with(compact('error'));
                        }
                    }
                }

                // 有碎屏保  碎屏保在保修期内  使用碎屏保
                if ($form->has_screen_insurance == 1) {
                    $bsi = BrokenScreenInsurance::firstByBarcode($form->barcode);
                    $bsi_insurance_times_remain = $bsi ? $bsi->insurance_times_remain : 0;
                    if ($form->in_si_period == 1 && $form->used_screen_insurance > 0
                        && $bsi_insurance_times_remain > 0
                        && empty($form->model()->used_screen_insurance)) {
                        DB::table('order')->where('sn', $form->model()->sn)->update(["used_screen_insurance" => 1]);
                        $now = date('Y-m-d H:i:s');
                        $bsi_usage = array(
                            'barcode' => $form->barcode,
                            'bsi_id' => $bsi->id,
                            'standard_id' => $bsi->standard,
                            'status_before' => $bsi->status,
                            'insurance_times_before' => $bsi_insurance_times_remain,
                            'pr_sn' => $form->sn,
                            'admin_uid' => Admin::user()->id,
                            'record_effective' => BrokenScreenInsuranceUsage::RecordEffectiveEnable,
                            'created_at' => $now,
                            'updated_at' => $now,
                        );
                        if ($bsi->status == BrokenScreenInsurance::STATUS_PAY_EFFECTIVE) {
                            $bsi_remain_new = $bsi_insurance_times_remain - $form->used_screen_insurance;
                            $bsi->insurance_times_remain = $bsi_remain_new;
                            if ($bsi_remain_new <= 0) {
                                $bsi->status = BrokenScreenInsurance::STATUS_REPORTED;
                                $bsi->usage_state = 1;
                                $bsi->usaged_at = $now;
                            }
                            $bsi->updated_at = $now;
                            $bsi->save();
                        }
                        $form->used_screen_insurance = 1;
                        $bsi_usage['status_after'] = $bsi->status;
                        $bsi_usage['insurance_times_after'] = $bsi_remain_new;
                        BrokenScreenInsuranceUsage::Insert($bsi_usage);
                    }
                }
                if ($form->has_screen_insurance == 0 && $form->used_screen_insurance > 0) {
                    $form->used_screen_insurance = 0;
                }
            });

            //快递费不保存,保存成staff_cast
            $form->ignore(['exp_cast']);

            $form->saved(function ($form) {
                //同步处理已使用的物料
                PostRepairUsedMaterial::where('pr_sn', $form->sn)->delete();
                $pr_material = PostRepairMaterial::where('pr_sn', $form->sn)->get();
                $now = date('Y-m-d h:i:s');
                if (count($pr_material) > 0) {
                    foreach ($pr_material as $material) {
                        $new_used_material = new PostRepairUsedMaterial();
                        $new_used_material->pr_sn = $material->pr_sn;
                        $new_used_material->mat_id = $material->mat_id;
                        $new_used_material->material_id = $material->material_id;
                        $new_used_material->is_charge = $material->is_charge;
                        $new_used_material->charge_type = $material->charge_type;
                        $new_used_material->count = $material->count;
                        $new_used_material->price_in = $material->price_in;
                        $new_used_material->created_at = $now;
                        $new_used_material->statistic_time = $now;
                        $new_used_material->save();
                    }
                }
                //dd(PostRepairUsedMaterial::where('pr_sn', $form->sn)->get()->toArray());
                $order = $form->model();
                //dd($order->status == Order::CHECK_FINISH && $order->in_period == 1 && $order->reason == 2 && empty(intval($order->pay_amount)));
                // 处理已检测,已知会的0元订单，直接修改为已支付的状态
                if ($order->status == Order::CHECK_FINISH_IS_TELL && empty(floatval($order->pay_amount))) {
                    $save = Order::where('sn', $order->sn)->first();
                    $save->status = Order::PAY_FINISH;
                    $save->save();
                }
                if ($order->status == Order::ORDER_FINISH || $order->status == Order::EXP_GO_SUCCESS) {
                    $order_extend = OrderExtend::where(['sn' => $order['sn']])->first();
                    if ($order_extend->exchange_number) {
                        $producer = new Producer();
                        $category_id = Machine::where(['model_id' => $order['model_id']])->value('category_id');
                        $content_data = array([
                            'category_id' => $category_id,
                            'sn' => $order->sn,
                            'model_id' => $order->model_id,
                            'model_name' => $order->model_name,
                            'serial' => $order->serial,
                            'barcode' => $order->barcode,
                            'imei' => $order->imei,
                            'status' => $order->status,
                            'updated_at_last' => $order->updated_at_last,
                            'exchange_number' => $order_extend->exchange_number
                        ]);
                        //dd($content_data);
                        $content_data = json_encode($content_data);
                        $producer->warranty_return_or_exchange_producer($content_data);
                    }
                }
            });
        });
    }

    public function users()
    {
        $q = \request()->get("q");
        return User::where('phone', 'like', "%$q%")->selectRaw("id, concat_ws('-',name, phone) as text")->paginate(10);
    }

    /**
     * 搜索用户姓名
     */
    public function selectUser()
    {
        //select框输入内容（用户姓名），q是固定值不需要变

        $name = \request()->get("q");
        return User::where('id', '!=', 1)
            ->where(function ($query) use ($name) {
                $query->where('name', 'like', '%' . $name . '%')
                    ->orWhere('username', 'like', '%' . $name . '%')
                    ->orWhere('phone', 'like', '%' . $name . '%');
            })
            ->selectRaw('id,name as text')
            ->paginate(10);
    }
}
