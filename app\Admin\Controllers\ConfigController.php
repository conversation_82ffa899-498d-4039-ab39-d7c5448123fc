<?php

namespace App\Admin\Controllers;

use App\Models\Config;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Http\Request;

class ConfigController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('配置说明');
            $content->description('description');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('配置说明');
            $content->description('description');

            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(Config::class, function (Grid $grid) {

            $grid->id('ID')->sortable();
            $grid->key('配置索引');
            $grid->name('配置名称');
//            $grid->value('配置内容');
            $grid->visible('是否可见')->switch();
            $grid->only_text('是否纯文本保存')->switch();
            $grid->editable('是否可编辑')->switch();
            $grid->created_at('创建时间');
            $grid->updated_at('更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id = null)
    {
        return Admin::form(Config::class, function (Form $form) use ($id) {

            $form->display('id', 'ID');
            $form->text('key', '配置索引');
            $form->text('name', '配置名称');
            $form->editor('value', '配置内容')->attribute(['route' => 'config_picture_upload']);
            $form->switch('visible', '是否可见')->states(['on' => ['text' => '可见'], 'off' => ['text' => '不可见']]);
            $form->switch('editable', '是否可编辑')->states(['on' => ['text' => '可编辑'], 'off' => ['text' => '不可编辑']]);
            $form->switch('only_text', '是否纯文本保存')->states(['on' => ['text' => '纯文本'], 'off' => ['text' => '不纯文本']]);
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
            $form->saving(function (Form $form) {
                if ($form->only_text == 'on' && ($form->value || $form->model()->value)) {
                    $value = $form->value ? $form->value : $form->model()->value;
                    $value = htmlspecialchars_decode($value);
                    $value = str_replace("&nbsp;", "", $value);
                    $value = strip_tags($value);
                    $form->value = $value;
                }
                if (request()->input('key') == null)
                    return;
//                $form->name = (Config::key)[$form->key];
            });
        });
    }


    public function uploadPicture(Request $request)
    {
        $pathList = [];
        foreach ($request->file('images') as $key => $value) {
            $path = $value->store('rbcare/repair/explain', 'oss');
            $pathList[] = config('admin.upload.host') . $path;
        }
        //这个是专门提供给富文本编辑器的json响应,要这样子的格式前端编辑器才会认为你成功上传了图片
        $responseData = ['errno' => 0, 'data' => $pathList];

        return response()->json($responseData);
    }
}
