<?php

namespace App\Admin\Extensions;

use App\Models\Endpoint;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;
use App\Models\Agency;

class PaperSalesmanExporter extends AbstractExporter
{
    public function export()
    {
        $filename = '导购答题列表';
        $data = $this->getData();
        if (!empty($data)) {
            $titles = [
                '答卷id',
                '一级代理',
                '二级代理',
                '姓名',
                '手机号',
                '耗时（秒）',
                '得分',
                '作答次数',
                '考试时间',
            ];
            $formatData = $this->getFormatData($data);
            array_unshift($formatData, $titles);
            ExcelExportTrait::exportToExcel($filename, $formatData);
        }
    }

    private function getFormatData($data)
    {
        foreach ($data as $row) {
            $row = [
                $row['id'],
                $row['top_name'],
                $row['second_name'],
                $row['name'],
                $row['phone'],
                $row['duration'],
                $row['score'],
                $row['response_times'],
                $row['updated_at'],
            ];
            $formatData[] = $row;
        }

        return $formatData;
    }


}