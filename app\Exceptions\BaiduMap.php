<?php

namespace App\Admin\Extensions;

use Encore\Admin\Form\Field;

class BaiduMap extends Field {

    protected $view = 'admin.baidu-map';

    protected static $css = [
    ];

    protected static $js = [
        //        '/packages/baidu-map/js/map.js',
        //        '/packages/baidu-map/js/artDialog/jquery.artDialog.js?skin=default',
        //        '/packages/baidu-map/js/artDialog/plugins/iframeTools.js',
        'https://api.map.baidu.com/api?v=2.0&ak={$baidu_ak}\"',
    ];

    public function render() {
        $this->script = <<<EOT

            function initBaiduMap(longitude,latitude){
                 var map = new BMap.Map("l-map");
	             var point = new BMap.Point(116.400244,39.92556);
	             map.centerAndZoom(point, 12);
	             var marker = new BMap.Marker(point);// 创建标注
	             var marker = new BMap.Marker(point);// 创建标注
	             map.addOverlay(marker);             // 将标注添加到地图中
	             marker.enableDragging();           // 不可拖拽
                 marker.addEventListener("dragend", function (e) {
                     alert(e.point.lng);
                     alert(e.point.lat);
        })
}
EOT;
        return parent::render();

    }
}