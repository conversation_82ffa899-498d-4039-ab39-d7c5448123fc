<?php


namespace App\Admin\Extensions\Tools;


use Encore\Admin\Grid\Tools\BatchAction;

class PostCheckOneOrder extends BatchAction
{
    public function script()
    {
        return <<<EOT
        $('{$this->getElementClass()}').on('click', function(){
            var res = confirm("确定要下同一个快递订单?");
            if(res == true){
                var data = {
                        _token:LA.token,
                        ids: selectedRows()
                    }
//                    alert(selectedRows());
//                  alert(data);
                 var urlEncode = function (param, key, encode) {
                  if(param==null) return '';
                  var paramStr = '';
                  var t = typeof (param);
                  
                  if (t == 'string' || t == 'number' || t == 'boolean') {
                    paramStr += '&' + key + '=' + ((encode==null||encode) ? encodeURIComponent(param) : param);
                  } else {
                    for (var i in param) {
                      var k = key == null ? i : key + (param instanceof Array ? '[' + i + ']' : '.' + i);
                      paramStr += urlEncode(param[i], k, encode);
                    }
                  }
                  return paramStr;
                };
                var params = urlEncode(data);
//                alert(params);
                window.location.href = '{$this->resource}/express_one_order?'+ params;   
                
                 
            }else{
              alert("You pressed Cancel!");
            }
                
           });
EOT;

    }
}