<?php

namespace App\Admin\Extensions;

use App\Models\Agency;
use App\Models\BrokenScreenInsurance;
use App\Models\BrokenScreenInsuranceStandard;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;

class BrokenScreenInsuranceExporter extends AbstractExporter
{

    /**
     * {@inheritdoc}
     */
    public function export()
    {
        $filename = '碎屏保报表' . '_' . date('Ymd');
        $data = $this->getData();
        if (!empty($data)) {
            $titles = [
                '保单编号',
                '条码',
                '机型',
                '投保类型',
                '保险价格',
                '购买时间',
                '一级代理',
                '购买终端',
                '是否使用',
                '保险到期时间',
                '支付方式',
                '支付单号',
                '退款单号',
                '购买类型',
                '保单状态',
            ];
            $formatData = $this->getFormatData($data);
            array_unshift($formatData, $titles);
            ExcelExportTrait::exportToExcel($filename, $formatData);
        }
    }

    private function getFormatData($data_list): array
    {
        $formatData = [];
        foreach ($data_list as $data) {
            //$standard = BrokenScreenInsuranceStandard::where('id', $data['standard'])->pluck('name','month');
            $created_at = $data['created_at'];
            $month = '+ ' . $data['standard_name']['month'] . 'months';
            $date = strtotime('-1 day', strtotime($month, strtotime(date('Y-m-d', strtotime($created_at)))));
            $end_time = date('Y-m-d', $date);

            $agency = Agency::where('id', $data['top_agency'])->value('name');

            $trade_plat = '未知';
            if (array_key_exists($data['trade_plat'], BrokenScreenInsurance::TRADE_PLAT)) {
                $trade_plat = BrokenScreenInsurance::TRADE_PLAT[$data['trade_plat']];
            }

            $buy_type = '未知';
            if (array_key_exists($data['type'], BrokenScreenInsurance::BUY_TYPE)) {
                $buy_type = BrokenScreenInsurance::BUY_TYPE[$data['type']];
            }

            $row = [
                $data['sn'],
                ' ' . $data['barcode'],
                $data['model_name'],
                $data['standard_name']['name'],
                $data['pay_amount'],
                $created_at,
                $agency,
                $data['endpoint_name'],
                BrokenScreenInsurance::USAGE_STATE[$data['usage_state']],
                $end_time,
                $trade_plat,
                $data['pay_id'],
                $data['refund_id'],
                $buy_type,
                BrokenScreenInsurance::STATUS[$data['status']]
            ];
            $formatData[] = $row;
        }
        return $formatData;
    }
}
