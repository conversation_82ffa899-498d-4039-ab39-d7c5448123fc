<?php
/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 2019/7/30
 * Time: 15:42
 */

namespace App\Admin\Extensions;

use App\Models\PostExpress;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use SimpleXMLElement;
use function GuzzleHttp\Psr7\str;

class Express
{
    public function create_exp_sn($sn = null)
    {
//        return '201908020903371884071774jxkd4159';
        if (!$sn) {
            $d = date('YmdHis');
            $r = random_int(100000, 999999);
            $sn = $d . strval($r);
        }
        $pre = 'jxkd';
        $rand = strval(random_int(1000, 9999));
        $ret = $sn . $pre . $rand;
        return $ret;
    }

    public function update_express($data)
    {

    }

    public function store_express($data = null)
    {
        $sns = explode(',', $data['sn']);
        foreach ($sns as $sn) {
            $express = new PostExpress();
            $express->pr_sn = $sn;
            $express->com = '顺丰';
            $express->readboy_sn = $data['orderid'];
            $express->type = isset($data['type']) ? $data['type'] : 0;
            $express->pay_method = isset($data['pay_method']) ? $data['pay_method'] : 0;
            $express->data = $data;
            $express->save();
        }
    }


    public function yt_store_express($data = null)
    {
        $sns = explode(',', $data['sn']);
        foreach ($sns as $sn) {
            $express = new PostExpress();
            $express->pr_sn = $sn;
            $express->com = '圆通';
            $express->readboy_sn = $data['orderid'];
            $express->type = isset($data['type']) ? $data['type'] : 0;
            $express->pay_method = isset($data['pay_method']) ? $data['pay_method'] : 0;
            $express->data = $data;
            $express->com_type = 2;
            $express->save();
        }
    }

    public function create_express_xml($data = null)
    {
        //处理数据
        //$data['express_type'] = 1;
        //$data['need_return_tracking_no'] = 1;
        if (isset($data['d_address'])) {
            $data['d_address'] = str_ireplace('寄修服务中心', '寄修服务中心【系统下单】', $data['d_address']);
        }
        $data['orderid'] = self::create_exp_sn(explode(',', $data['sn'])[0]);
        //保存快递下单数据
        self::store_express($data);
        unset($data['_token']);
        unset($data['sn']);
        unset($data['type']);
        $xmlStr = <<<XML
<?xml version="1.0" encoding="UTF-8"?>
<Request service="OrderService" lang="zh-CN"></Request>
XML;
        $xml = new SimpleXMLElement($xmlStr);
        $xml->addChild('Head', env('SF_HEAD'));
        $body = $xml->addChild('Body');
        $order = $body->addChild('Order');
        foreach ($data as $k => $v) {
            $order->addAttribute($k, $v);
        }
        $cargo = $order->addChild('Cargo');
        $cargo->addAttribute('name', '读书郎教育产品（含锂电池）');
        return $xml->asXML();
    }


    public function yx_create_express_xml($data)
    {
        //处理数据
        $data['orderid'] = self::create_exp_sn(explode(',', $data['sn'])[0]);
//        //保存快递下单数据
        self::yt_store_express($data);
//        $xmlStr = <<<XML
        /*<?xml version="1.0" encoding="UTF-8"?><RequestOrder></RequestOrder>*/
//XML;
//        $xml = new SimpleXMLElement($xmlStr);
//        $xml->addChild("logistics_interface");
//        $xml->addChild('clientID', 'K76044191');
//        $xml->addChild('logisticProviderID', 'YTO');
//        $xml->addChild('customerId', 'K76044191');
//        $xml->addChild('txLogisticID', $data['orderid']);
//        $xml->addChild('tradeNo', 'K76044191');
//        $xml->addChild('orderType', 1);
//        $xml->addChild('serviceType', 1);
//        $xml->addChild('itemName', '读书郎教育产品（含锂电池）');
//        $xml->addChild('number', $data['number']);
//
//        $sender = $xml->addChild('sender');
//        $sender->addChild('name', $data['j_contact']);
//        $sender->addChild('mobile', $data['j_tel']);
//        $sender->addChild('prov', $data['j_province']);
//        $sender->addChild('city', $data['j_city'].','.$data['j_county']);
//        $sender->addChild('address', $data['j_address']);

//        $receiver = $xml->addChild('receiver');
//        $receiver->addChild('name', $data['d_contact']);
//        $receiver->addChild('mobile', $data['d_tel']);
//        $receiver->addChild('prov', $data['d_province']);
//        $receiver->addChild('city', $data['d_city'].','.$data['d_county']);
//        $receiver->addChild('address', $data['d_address']);
        $data = [
            "logistics_interface" => json_encode([
                "clientID" => "K76044191",
                "logisticProviderID" => "YTO",
                "customerId" => "K76044191",
                "txLogisticID" => $data['orderid'],
                "tradeNo" => "K76044191",
                "orderType" => 1,
                "serviceType" => 1,
                "itemName" => "读书郎教育产品（含锂电池）",
                "number" => $data['number'],
                "sender" => [
                    "name" => $data['j_contact'],
                    "mobile" => $data['j_tel'],
                    "prov" => $data['j_province'],
                    "city" => $data['j_city'] . ',' . $data['j_county'],
                    "address" => $data['j_address'],
                ],
                "receiver" => [
                    "name" => $data['d_contact'],
                    "mobile" => $data['d_tel'],
                    "prov" => $data['d_province'],
                    "city" => $data['d_city'] . ',' . $data['d_county'],
                    "address" => $data['d_address'],
                ],
            ]),
            "clientId" => "K76044191",
            "type" => "offline",
        ];

//        return $xml->asXML();
        return $data;
    }

    public function create_express_order($info)
    {
        // $url = env('SF_HOST') . '/api/order/placeOrder';
        $url = 'http://bsp-oisp.sf-express.com/bsp-oisp/sfexpressService';
//        $custid = '7551234567';
        // $verify_word = env('SF_VERIFY_CODE');
        // $check_code = env('SF_CHECK_CODE');
        $check_code = 'nopDeBdqCQp0uUszvuK5jIWhOwovmK7K';
        $xml = self::create_express_xml($info);
        // $verify_code = $verify_word;// base64_encode(md5($xml . $verify_word, true));
        $verify_code = base64_encode(md5($xml . $check_code, true));
        $param = [
            'xml' => $xml,
            'verifyCode' => $verify_code,
            // 'checkCode' => $check_code,
        ];
        $data = http_build_query($param);
        $context = stream_context_create([
            'http' => array(
                'method' => 'POST',
                'header' => 'Content-type:application/x-www-form-urlencoded',
                'content' => $data,
                'timeout' => 30 // 超时时间（单位:s）
            )
        ]);
        $response = file_get_contents($url, false, $context);

        $resp = simplexml_load_string($response);
//        dd($resp);
        $result = json_decode(json_encode($resp), TRUE);
//        dd($result);
        return $result;
    }


    public function yt_create_express_order($data)
    {
        $url = 'http://openapi.yto.net.cn/open/waybill_internal_adapter/v1/CEFObV/K76044191';
        $param = self::yx_create_express_xml($data);
//        $data_digest=$xml.'5YRbj2uZ';
//        $data_digest = md5($data_digest, true);
//        $data_digest = base64_encode("$data_digest");
//        $data_digest = urlencode($data_digest);
//        $param = [
//            'logistics_interface' => $xml,
////            'data_digest' => $data_digest,
//            'clientId' => 'K76044191',
//            'type' => 'offline'
//        ];
//        dd($param);
//        ksort($param);
        $sign = json_encode($param) . "waybill_internal_adapter" . "v1" . '5YRbj2uZ';
//        $sign = "opentest123456";
        $sign = md5($sign, true);
        $sign = base64_encode("$sign");
//        $sign = urlencode($sign);
//        dd($sign);
        $data = [
            'timestamp' => strval(time()),
            "param" => json_encode($param),
            "sign" => $sign,
            "format" => "JSON",
        ];
//        dd($data);
//        $url = $url."?"."timestamp=".date('Y-m-d H:i:s')."&param=".json_encode($param)."&sign=".$sign."&format=JSON";
//        $context = stream_context_create([
//            'http' => array(
//                'method' => 'POST',
//                'header' => 'Content-type:application/x-www-form-urlencoded; charset=UTF-8',
//                'content' => $data,
//                'timeout' => 30 // 超时时间（单位:s）
//            )
//        ]);
//        $response = file_get_contents($url, false, $context);
        $client = new Client();
        $resp = $client->request('post', $url, [
            'headers' => [
                'Content-type' => 'application/x-www-form-urlencoded'
            ],
            'form_params' => $data
        ]);

        if ($resp->getStatusCode() !== 200) {
            dd(false);
            return false;
        }
        $response = json_decode($resp->getBody()->getContents(), true);
//        dd($response);
//        return $resp;
//        $resp = simplexml_load_string($response);
        return json_decode(json_encode($response), TRUE);
    }

    public function create_route_xml($tracking_number = null)
    {
        $xmlStr = <<<XML
<?xml version="1.0" encoding="UTF-8"?>
<Request service="RouteService" lang="zh-CN"></Request>
XML;
        $xml = new SimpleXMLElement($xmlStr);
        $xml->addChild('Head', env('SF_HEAD'));
        $body = $xml->addChild('Body');
        $route = $body->addChild('RouteRequest');
        $route->addAttribute('tracking_type', 2);
        $route->addAttribute('tracking_number', $tracking_number);
        $route->addAttribute('method_type', 1);
        return $xml->asXML();
    }


    public function route_order($tracking_number = null)
    {
        // $url = env('SF_HOST') . '/api/order/getRoute';
        $url = 'http://bsp-oisp.sf-express.com/bsp-oisp/sfexpressService';
        // $verify_word = env('SF_VERIFY_CODE');
        // $check_code = env('SF_CHECK_CODE');
        $check_code = 'nopDeBdqCQp0uUszvuK5jIWhOwovmK7K';
        $xml = self::create_route_xml($tracking_number);
        // $verify_code = $verify_word;//base64_encode(md5($xml . $verify_word, true));
        $verify_code = base64_encode(md5($xml . $check_code, true));
        $param = [
            'xml' => $xml,
            'verifyCode' => $verify_code,
            // 'checkCode' => $check_code,
        ];
        $data = http_build_query($param);
        $context = stream_context_create([
            'http' => array(
                'method' => 'POST',
                'header' => 'Content-type:application/x-www-form-urlencoded',
                'content' => $data,
                'timeout' => 15 // 超时时间（单位:s）
            )
        ]);
        $response = file_get_contents($url, false, $context);
        $resp = simplexml_load_string($response);
        $result = json_decode(json_encode($resp), TRUE);
        return $result;
    }

    public function yt_route_order($tracking_number = null)
    {
//        $tracking_number = 'YT3155661629044';
        $url = 'http://openapi.yto.net.cn/service/waybill_query/v1/CEFObV';
        $param = [
//            'sign' => 's0sZY6',
            'app_key' => '4QYcXM',
            'method' => 'yto.Marketing.WaybillTrace',
            'timestamp' => date('Y-m-d H:i:s'),
            'user_id' => 'CEFObV',
            'v' => '1.01',
//            'param'=>[['number'=>$tracking_number]],
            'format' => 'JSON'
        ];
        ksort($param);
        $sign = '';
        $sign1 = '';
        foreach ($param as $k => $v) {
            $sign = $sign . $k . $v;
            $sign1 = $sign1 . '&' . $k . '=' . $v;
        }
        $sign = strtoupper(md5('s0sZY6' . $sign));

        $param['sign'] = $sign;
        $param['param'] = json_encode([['number' => $tracking_number]]);
//        $sign1 = 'sign='.$sign.$sign1.'&param='.json_encode([['number'=>$tracking_number]]);
        $client = new Client();
        $resp = $client->request('post', $url, [
            'headers' => [
                'Content-type' => 'application/x-www-form-urlencoded'
            ],
            'form_params' => $param
        ]);
        if ($resp->getStatusCode() !== 200) {
            return false;
        }
        $resp = json_decode($resp->getBody()->getContents(), true);
//        dd($resp);
        return $resp;
    }

    public function yz_route_order($tracking_number = null)
    {
        $app_key = "E29F083084F6AE6E"; // "0D4147E80EE0498C"; 
        $tracking_number = '9892019112608';
        $url = 'http://211.156.197.242:8080/querypush-gjcx/mailTrackGjcx/mailTrackGjcxTwswn/plus';
        $msgBody = json_encode(["traceNo" => $tracking_number]);
//        $msgBody = "{\"traceNo\":\"9892019112608\"}"."0D4147E80EE0498C";
        // dump($msgBody);
        $dataDigest = md5($msgBody . $app_key, false);
        // dump($dataDigest);
        $dataDigest = base64_encode($dataDigest);
        // dump(base64_encode('a9fb35a79d615fe659063466fe33d35e'));
        // dump($dataDigest);
        $param = [
//            'sign' => 's0sZY6',
            'sendID' => 'ZSDSL',
            'proviceNo' => '99',
            'serialNo' => date('YmdHis') . $tracking_number,
            'msgKind' => 'ZSDSL_JDPT_TRACE',
            'sendDate' => date('YmdHis'),
            'receiveID' => 'JDPT',
//            'param'=>[['number'=>$tracking_number]],
            'batchNo' => 999, // $tracking_number,
            'dataType' => 1,
            'dataDigest' => $dataDigest,
            'msgBody' => $msgBody
        ];
//        $url = $url."?".http_build_query($param);
        // dump($param);
        $client = new Client();
        $resp = $client->request('post', $url, [
            'headers' => [
                'Content-type' => 'application/x-www-form-urlencoded'
            ],
            'form_params' => $param
        ]);
        if ($resp->getStatusCode() !== 200) {
            return false;
        }
        $resp = json_decode($resp->getBody()->getContents(), true);
//        dd($resp);
        return $resp;
    }

    public static function create_query_order_xml($rb_exp_sn, $exp_sn)
    {
        $xmlStr = <<<XML
<?xml version="1.0" encoding="UTF-8"?>
<Request service="QuerySFWaybillService" lang="zh-CN"></Request>
XML;
        $xml = new SimpleXMLElement($xmlStr);
        $xml->addChild('Head', env('SF_HEAD'));
        $body = $xml->addChild('Body');
        $waybill = $body->addChild('Waybill');
        $waybill->addAttribute('type', 1);
        $waybill->addAttribute('waybillNo', $exp_sn);
        $waybill->addAttribute('orderId', $rb_exp_sn);
        return $xml->asXML();
    }

    public static function query_order($rb_exp_sn, $exp_sn)
    {
//        $rb_exp_sn = '20191102091908344088jxkd4440';
//        $exp_sn = '288226293207';
//        $rb_exp_sn = '20191104100457766392jxkd1727';
//        $exp_sn = '288272800208';
        // $url = env('SF_HOST') . '/api/order/querySFWaybill?';
        // $param = [
        //     'waybillNo' => $exp_sn,
        //     'orderId' => $rb_exp_sn,
        // ];
        // $data = http_build_query($param);
        // $url .= $data;
        $url = 'http://bsp-oisp.sf-express.com/bsp-oisp/sfexpressService';
        $check_code = 'nopDeBdqCQp0uUszvuK5jIWhOwovmK7K';
        $xml = self::create_query_order_xml($rb_exp_sn, $exp_sn);
        $verify_code = base64_encode(md5($xml . $check_code, true));
        $param = [
            'xml' => $xml,
            'verifyCode' => $verify_code,
            // 'checkCode' => $check_code,
        ];
        $data = http_build_query($param);
        $context = stream_context_create([
            'http' => array(
                'method' => 'POST',
                'header' => 'Content-type:application/x-www-form-urlencoded',
                'content' => $data,
                'timeout' => 15 // 超时时间（单位:s）
            )
        ]);
        try {
            $response = file_get_contents($url, false, $context);
            $resp = simplexml_load_string($response);
        } catch (\Exception $e) {
            \Log::info('query exp order error:rb_exp_sn=' . $rb_exp_sn . ',exp_sn=' . $exp_sn);
            return '无法获取';
        }
        $result = json_decode(json_encode($resp), TRUE);
//        dd(($result['Head'] == 'OK' && isset($result['Body']['Waybill']['Fee']['@attributes']['value'])));
        if ($result['Head'] == 'OK' && isset($result['Body']['Waybill']['Fee']['@attributes']['value'])) {
            $ret = $result['Body']['Waybill']['Fee']['@attributes']['value'];
            self::store_fee($rb_exp_sn, $exp_sn, $ret);
            return $ret;
        } else if ($result['Head'] == 'OK' && isset($result['Body']['Waybill']['Fee'])) {
            $ret = 0.0;
            foreach ($result['Body']['Waybill']['Fee'] as $fee) {
                $ret += $fee['@attributes']['value'];
            }
            self::store_fee($rb_exp_sn, $exp_sn, $ret);
            return floatval($ret);
        } else {
            return 0;
        }
    }

    public static function store_fee($rb_exp_sn, $exp_sn, $fee)
    {
        return DB::table('pr_express')->where('readboy_sn', $rb_exp_sn)->where('exp_sn', $exp_sn)->update(['fee' => $fee]);
    }


    public function yz_create_express_order($data)
    {
        $ret = self::yz_batch_get_waybillNo_order();
        if (!$ret) {
            return ['ok' => 0, "msg" => "访问外部接口出错"];
        }

        if ($ret['result'] == "false") {
            return ['ok' => 0, "msg" => $ret['errorMsg'][1]];
        }

        $waybill_no = explode(",", $ret['waybill_no'])[0];
        $data['waybill_no'] = $waybill_no;
        $data['one_bill_flag'] = 0;
        // 正式
        $url = 'https://39.108.232.57:8080/interface';
        // 测试
        $url = 'https://211.156.197.233/ebiistandard-job/a/ChinaPostApi/OrderCreate';
        $xml = self::yz_create_express_xml($data);
//        dd($xml);
        $data_digest = $xml . 'key123xydJDPT';
        $data_digest = md5($data_digest, true);
        $data_digest = base64_encode("$data_digest");

        $param = [
            'logistics_interface' => $xml,
            'data_digest' => $data_digest,
            'msg_type' => 'ORDERCREATE',
            'ecCompanyId' => 'SDDM-LOGISTICS'
        ];
        // dump($param);
//
        $client = new Client([
            'verify' => false,
        ]);
        // $url = $url."?".http_build_query($param);
        $resp = $client->request('post', $url, [
            'headers' => [
                'Content-type' => 'application/x-www-form-urlencoded'
            ],
            'form_params' => $param
        ]);
        if ($resp->getStatusCode() !== 200) {
            return ["ok" => 0, 'msg' => "访问第三方接口失败"];
        }
        $raws = $resp->getBody()->getContents();
        // dump($raws);
        $resp = simplexml_load_string($raws);
        $order = json_decode(json_encode($resp), TRUE);
//        dd($order);
        if ($order['responseItems']['response']["success"] == "false") {
            return ['ok' => 0, "msg" => $order['responseItems']['response']['reason']];
        }
        return ['ok' => 1, "readboy_sn" => $order['responseItems']['response']['txLogisticID'], 'exp_sn' => $waybill_no];
    }


    public function yz_batch_get_waybillNo_order()
    {
        // 正式
        $url = 'https://39.108.232.57:8080/interface';
        // 测试
        $url = 'https://211.156.197.233/iwaybillno-web/a/iwaybillBatch/receive';
        $xml = self::yz_batch_get_waybillNo_xml();
        $data_digest = $xml . 'key123xydJDPT';
        $data_digest = md5($data_digest, true);
        $data_digest = base64_encode("$data_digest");
//        $data_digest = urlencode($data_digest);
//        dd($data_digest);
        $param = [
            'logistics_interface' => $xml,
            'data_digest' => $data_digest,
            'msg_type' => 'BatchGetWaybillNo',
            'ecCompanyId' => 'SDDM-LOGISTICS'
        ];


        $client = new Client([
            'verify' => false,
        ]);
        // $url = $url."?".http_build_query($param);
        // dump($url);
        $resp = $client->request('post', $url, [
            'headers' => [
                'Content-type' => 'application/x-www-form-urlencoded'
            ],
            'form_params' => $param
        ]);
        if ($resp->getStatusCode() !== 200) {
            return false;
        }
//        dump($resp->getBody()->getContents());
//        $response = json_decode($resp->getBody()->getContents(), true);
//        dump($response);
//        return $resp;
        $resp = simplexml_load_string($resp->getBody()->getContents());
        // dump($resp);
        return json_decode(json_encode($resp), TRUE);
    }

    public function yz_create_express_xml($data)
    {
        //处理数据
        $data['orderid'] = self::create_exp_sn(explode(',', $data['sn'])[0]);
//        //保存快递下单数据
        self::yz_store_express($data);
        $xmlStr = <<<XML
<?xml version="1.0" encoding="UTF-8"?><OrderNormals></OrderNormals>
XML;
        $xml = new SimpleXMLElement($xmlStr);
        $OrderNormal = $xml->addChild("OrderNormal");
        $OrderNormal->addChild('created_time', date('Y-m-d H:i:s'));
        $OrderNormal->addChild('ecommerce_no', 'SDDM-LOGISTICS');
        $OrderNormal->addChild('ecommerce_user_id', 'SDDM-LOGISTICS');
        $OrderNormal->addChild('sender_type', 1);
        $OrderNormal->addChild('sender_no', '1100094212134');
        $OrderNormal->addChild('inner_channel', 0);
        $OrderNormal->addChild('logistics_order_no', $data['orderid']);
        $OrderNormal->addChild('waybill_no', $data['waybill_no']);
        $OrderNormal->addChild('one_bill_flag', $data['one_bill_flag']);
        $OrderNormal->addChild('contents_attribute', 3);
        $OrderNormal->addChild('product_type', 1);
        $OrderNormal->addChild('none', "");
        $OrderNormal->addChild('project_id', "SDDM-LOGISTICS");
        $OrderNormal->addChild('none', "");
        $sender = $OrderNormal->addChild('sender');
        $sender->addChild('name', $data['j_contact']);
        $sender->addChild('mobile', $data['j_tel']);
        $sender->addChild('prov', $data['j_province']);
        $sender->addChild('city', $data['j_city']);
        $sender->addChild('county', $data['j_county']);
        $sender->addChild('address', $data['j_address']);

        $receiver = $OrderNormal->addChild('receiver');
        $receiver->addChild('name', $data['d_contact']);
        $receiver->addChild('mobile', $data['d_tel']);
        $receiver->addChild('prov', $data['d_province']);
        $receiver->addChild('city', $data['d_city']);
        $receiver->addChild('county', $data['d_county']);
        $receiver->addChild('address', $data['d_address']);
        $cargos = $OrderNormal->addChild("cargos");
        $Cargo = $cargos->addChild("Cargo");
        $Cargo->addChild("cargo_name", '读书郎教育产品（含锂电池）');
        $OrderNormal->addChild("logistics_provider", "B");
        return $xml->asXML();
    }

    public function yz_batch_get_waybillNo_xml()
    {
        $xmlStr = <<<XML
<?xml version="1.0" encoding="UTF-8"?><BatchGetWaybillNo></BatchGetWaybillNo>
XML;

        $xml = new SimpleXMLElement($xmlStr);
        // 正式
//        $xml->addChild('CreatedTime', date('Y-m-d H:i:s'));
//        $xml->addChild('CustomerNo', '1100094212134');
//        $xml->addChild('MailType', 6);
//        $xml->addChild('count', 1);
//        $xml->addChild('eventSource', 'ZSDSL');

        // 测试
        $xml->addChild('CreatedTime', date('Y-m-d H:i:s'));
        $xml->addChild('CustomerNo', '90000001885440');
        $xml->addChild('MailType', 6);
        $xml->addChild('count', 1);
        $xml->addChild('eventSource', 'SDDM-LOGISTICS');
        return $xml->asXML();
    }

    public function yz_store_express($data = null)
    {
        $sns = explode(',', $data['sn']);
        foreach ($sns as $sn) {
            $express = new PostExpress();
            $express->exp_sn = $data['waybill_no'];
            $express->pr_sn = $sn;
            $express->com = '邮政';
            $express->readboy_sn = $data['orderid'];
            $express->type = isset($data['type']) ? $data['type'] : 0;
            $express->pay_method = isset($data['pay_method']) ? $data['pay_method'] : 0;
            $express->data = $data;
            $express->com_type = 3;
            $express->save();
        }
    }
}

