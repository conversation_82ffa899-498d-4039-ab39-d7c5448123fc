<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\AgencyPostRepairAmountExporter;
use App\Admin\Extensions\AgencyPostRepairRegionExporter;
use App\Admin\Extensions\ExpenseExporter;
use App\Admin\Extensions\Express;
use App\Admin\Extensions\Tools\Button;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\ExpGoOneOrder;
use App\Admin\Extensions\Tools\offlineExpGoOneOrder;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Admin\Extensions\Tools\YtExpGoOneOrder;
use App\Models\ChinaArea;
use App\Models\Endpoint;
use App\Models\Machine;
use App\Models\Order;

use App\Models\PostExpress;
use App\Models\PostRepairEndpoint;
use App\Models\PostRepairExpense;
use App\User;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\MessageBag;
use function PHPSTORM_META\type;

class AgentOrderRepairExpGoController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('回寄产品');
            $content->description('经销商寄修');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('回寄产品');
            $content->description('');

            $content->body($this->form()->edit($id));
        });
    }

    public function view($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('回寄产品--查看');
            $order = Order::where(['id' => $id])->first();
            $post_malfunction = DB::table('post_repair_malfunction')
                ->join('machine_malfunction', 'machine_malfunction.id', '=', 'post_repair_malfunction.malfunction_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_malfunction.title')->toArray();
            $malfunction = implode('，', $post_malfunction);
            $post_accessory = DB::table('pr_accessory')
                ->join('machine_accessory', 'machine_accessory.id', '=', 'pr_accessory.mar_id')
                ->where('pr_sn', '=', $order['sn'])->pluck('machine_accessory.title')->toArray();
            $accessory = implode('，', $post_accessory);
            $check_man = DB::table('admin_users')
                ->join('order', 'order.check_man', '=', 'admin_users.id')
                ->where('order.check_man', '=', $order['check_man'])->pluck('admin_users.name')->toArray();
            $check_man = implode('', $check_man);
            $repair_man = DB::table('admin_users')
                ->join('order', 'order.repair_man', '=', 'admin_users.id')
                ->where('order.repair_man', '=', $order['repair_man'])->pluck('admin_users.name')->toArray();
            $repair_man = implode('', $repair_man);
            $content->body(view('admin/repair_order/view', compact('order', 'malfunction', 'accessory', 'check_man', 'repair_man')));

        });
    }

    public function express(Content $content, $id = null)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
            $data['custid'] = env('SF_CUSTID');
            $data['template'] = '思为滕-下单';
            $data['express_type'] = 103;
            $data['pay_method'] = 1;
            $data['type'] = 2;
            $id = $data['id'];
            unset($data['id']);
//            dd($data);
            $express = new Express();
            $result = $express->create_express_order($data);
//            $result = array();
//            $result['Head'] = 'a';
//            $result['ERROR'] = '1111';

            if ($result['Head'] == 'OK' && $result['Body']['OrderResponse']['@attributes']['filter_result'] < 3) {
                //更新快递信息
                PostExpress::where(['readboy_sn' => $result['Body']['OrderResponse']['@attributes']['orderid']])->update([
                    'status' => 1,
                    'exp_sn' => $result['Body']['OrderResponse']['@attributes']['mailno'],
                ]);
                //写入订单信息
                $order = Order::where(['id' => $id])->first();
//                dd($order);
                $order->status = Order::EXP_GO_SUCCESS;
                $order->rb_go_exp_sn = $result['Body']['OrderResponse']['@attributes']['orderid'];
                $order->go_exp_sn = $result['Body']['OrderResponse']['@attributes']['mailno'];
                $order->go_exp_com = '顺丰快递';
                $order->go_sure = 1;
                $order->updated_at_last = date('Y-m-d H:i:s');
                $order->save();
                return redirect('/admin/repair_exp_go');
            } else if ($result['Head'] == 'OK' && $result['Body']['OrderResponse']['@attributes']['filter_result'] == 3) {
//                dd($result);
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => "地区无法到达",
//                    'message' => $result['ERROR'],
                ]);
                Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                return back()->withInput()->with(compact('error'));
            } else if ($result['Head'] == 'ERR') {
                $error = new MessageBag([
                    'title' => '错误提示',
//                    'message' => "内部错误",
                    'message' => $result['ERROR'],
                ]);
                Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                return back()->withInput()->with(compact('error'));
            } else {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => "内部错误",
//                    'message' => $result['ERROR'],
                ]);
                Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                return back()->withInput()->with(compact('error'));
            }
        }

        $order = Order::where(['id' => $id])->first();
        $endpoint = PostRepairEndpoint::where(['id' => $order->repair_endpoint])->first();
//        $endpoint = Endpoint::where(['name' => $repair_endpoint->name])->first();
        $province = ChinaArea::where(['region_id' => $endpoint->province])->first();
        $city = ChinaArea::where(['region_id' => $endpoint->city])->first();
        $district = ChinaArea::where(['region_id' => $endpoint->district])->first();
        $content->header('回寄-快递下单');

        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/repair_exp_go/express');
        $form->hidden('id')->default($id);
        $form->hidden('sn')->default($order->sn);
        $form->text('j_contact', '终端-姓名')->default($endpoint->name)->rules('required');
        $form->text('j_tel', '终端-联系电话')->default($endpoint->phone)->rules('required');
        $form->text('j_province', '终端-省')->default($province->region_name)->rules('required');
        $form->text('j_city', '终端-市')->default($city->region_name)->rules('required');
        $form->text('j_county', '终端-区')->default($district->region_name)->rules('required');
        $form->text('j_address', '终端-地址')->default($endpoint->address)->rules('required');
        $form->divide();
        $form->text('d_contact', '用户-姓名')->default($order->name)->rules('required');
        $form->text('d_tel', '用户-联系电话')->default($order->phone)->rules('required');
        $form->text('d_province', '用户-省')->default($order->province)->rules('required');
        $form->text('d_city', '用户-市')->default($order->city)->rules('required');
        $form->text('d_county', '用户-区')->default($order->district)->rules('required');
        $form->text('d_address', '用户-地址')->default($order->address)->rules('required');
        $content->body($form);
        return $content;
    }


    public function express_one_order(Content $content)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
            $data['custid'] = env('SF_CUSTID');
            $data['template'] = '思为滕-下单';
            $data['express_type'] = 103;
            $data['pay_method'] = 1;
            $data['type'] = 2;
            $ids = explode(',', $data['ids']);
            unset($data['ids']);
            $express = new Express();
            $result = $express->create_express_order($data);
            $ret = false;
            foreach ($ids as $id) {
                if ($result['Head'] == 'OK' && $result['Body']['OrderResponse']['@attributes']['filter_result'] < 3) {
                    //更新快递信息
                    PostExpress::where(['readboy_sn' => $result['Body']['OrderResponse']['@attributes']['orderid']])->update([
                        'status' => 1,
                        'exp_sn' => $result['Body']['OrderResponse']['@attributes']['mailno'],
                    ]);
                    //写入订单信息
                    $order = Order::where(['id' => $id])->first();
//                dd($order);
                    $order->status = Order::EXP_GO_SUCCESS;
                    $order->rb_go_exp_sn = $result['Body']['OrderResponse']['@attributes']['orderid'];
                    $order->go_exp_sn = $result['Body']['OrderResponse']['@attributes']['mailno'];
                    $order->go_exp_com = '顺丰快递';
                    $order->go_sure = 1;
                    $order->updated_at_last = date('Y-m-d H:i:s');
                    $order->save();
                    $ret = true;
                } else {
                    Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                }
            }
            if ($ret) {
                return redirect('/admin/agent_order_repair_exp_go');
            } else {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => $result['ERROR'],
                ]);
                return back()->withInput()->with(compact('error'));
            }
        }

        $ids = Request::get('ids');
        $sns = array();
        $orders = Order::whereIn('id', $ids)->get();
//        dd($orders);
        //比对收货地址
        $cmp_order = $orders[0];
        foreach ($orders as $order) {
            if ($order->name != $cmp_order->name
                || $order->phone != $cmp_order->phone
                || $order->uid != $cmp_order->uid
                || $order->province != $cmp_order->province
                || $order->city != $cmp_order->city
                || $order->district != $cmp_order->district
                || $order->address != $cmp_order->address) {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => '快递订单信息不相同',
                ]);
                return back()->withInput()->with(compact('error'));
            }
            $sns[] = $order->sn;
        }
        $order = $cmp_order;
        $endpoint = PostRepairEndpoint::where(['id' => $order->repair_endpoint])->first();
//        $endpoint = Endpoint::where(['name' => $repair_endpoint->name])->first();
        $province = ChinaArea::where(['region_id' => $endpoint->province])->first();
        $city = ChinaArea::where(['region_id' => $endpoint->city])->first();
        $district = ChinaArea::where(['region_id' => $endpoint->district])->first();
        $content->header('回寄-多订单回寄同一个快递');

        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/agent_order_repair_exp_go/express_one_order');
        $form->hidden('ids')->default(implode(',', $ids));
        $form->hidden('sn')->default(implode(',', $sns));
        $form->text('j_contact', '终端-姓名')->default($endpoint->name)->rules('required');
        $form->text('j_tel', '终端-联系电话')->default($endpoint->phone)->rules('required');
        $form->text('j_province', '终端-省')->default($province->region_name)->rules('required');
        $form->text('j_city', '终端-市')->default($city->region_name)->rules('required');
        $form->text('j_county', '终端-区')->default($district->region_name)->rules('required');
        $form->text('j_address', '终端-地址')->default($endpoint->address)->rules('required');
        $form->divide();
        $form->text('d_contact', '用户-姓名')->default($order->name)->rules('required');
        $form->text('d_tel', '用户-联系电话')->default($order->phone)->rules('required');
        $form->text('d_province', '用户-省')->default($order->province)->rules('required');
        $form->text('d_city', '用户-市')->default($order->city)->rules('required');
        $form->text('d_county', '用户-区')->default($order->district)->rules('required');
        $form->text('d_address', '用户-地址')->default($order->address)->rules('required');
        $content->body($form);
        return $content;
    }

    public function yt_route_order($tracking_number = null)
    {
        $tracking_number = 'YT3155661629044';
        $url = 'http://openapi.yto.net.cn/service/waybill_query/v1/CEFObV';
        $param = [
            'sign' => 's0sZY6',
            'app_key' => '4QYcXM',
            'method' => 'yto.Marketing.WaybillTrace',
            'timestamp' => date('Y-m-d H:i:s'),
            'user_id' => 'CEFObV',
            'v' => '1.01',
            'param' => [['number' => $tracking_number]],
            'format' => 'JSON'
        ];
        $data = http_build_query($param);
        $url = $url . "?" . $data;
        $context = stream_context_create([
            'http' => array(
                'method' => 'get',
                'header' => 'Content-type:application/x-www-form-urlencoded',
//                'content' => $data,
                'timeout' => 15 // 超时时间（单位:s）
            )
        ]);
        $response = file_get_contents($url, false, $context);
        $resp = simplexml_load_string($response);
        $result = json_decode(json_encode($resp), TRUE);
        dd($result);
        return $result;
    }

    public function yt_express_one_order(Content $content)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
            $data['pay_method'] = 1;  // 寄付
            $data['type'] = 2;  // 寄走
            $ids = explode(',', $data['ids']);
            $data['number'] = count($ids);
            unset($data['ids']);

//            dd($receiver);
            $express = new Express();
            $result = $express->yt_create_express_order($data);
//            dd($result);
            $ret = false;
            if ($result['success'] == 'true') {
                PostExpress::where(['readboy_sn' => $result['txLogisticID']])->update([
                    'status' => 1,
                    'exp_sn' => $result['mailNo'],
                ]);
                foreach ($ids as $id) {
                    $order = Order::where(['id' => $id])->first();
                    $order->status = Order::EXP_GO_SUCCESS;
                    $order->rb_go_exp_sn = $result['txLogisticID'];
                    $order->go_exp_sn = $result['mailNo'];
                    $order->go_exp_com = '圆通快递';
                    $order->go_sure = 1;
                    $order->updated_at_last = date('Y-m-d H:i:s');
                    $order->save();
                    $ret = true;
                }
            } else {
                foreach ($ids as $id) {
                    Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                }
            }
            if ($ret) {
                return redirect('/admin/agent_order_repair_exp_go');
            } else {
                if ($result['success'] == 'false') {
                    $error = new MessageBag([
                        'title' => '错误提示',
                        'message' => $result['reason'],
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => '系统出错',
                ]);
                return back()->withInput()->with(compact('error'));
            }
        }

        $ids = Request::get('ids');
        $sns = array();
        $orders = Order::whereIn('id', $ids)->get();
//        dd($orders);
        //比对收货地址
        $cmp_order = $orders[0];
        foreach ($orders as $order) {
            if ($order->name != $cmp_order->name
                || $order->phone != $cmp_order->phone
                || $order->uid != $cmp_order->uid
                || $order->province != $cmp_order->province
                || $order->city != $cmp_order->city
                || $order->district != $cmp_order->district
                || $order->address != $cmp_order->address) {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => '快递订单信息不相同',
                ]);
                return back()->withInput()->with(compact('error'));
            }
            $sns[] = $order->sn;
        }
        $order = $cmp_order;
        $endpoint = PostRepairEndpoint::where(['id' => $order->repair_endpoint])->first();
//        $endpoint = Endpoint::where(['name' => $repair_endpoint->name])->first();
        $province = ChinaArea::where(['region_id' => $endpoint->province])->first();
        $city = ChinaArea::where(['region_id' => $endpoint->city])->first();
        $district = ChinaArea::where(['region_id' => $endpoint->district])->first();
        $content->header('回寄-多订单回寄同一个快递');

        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/agent_order_repair_exp_go/yt_express_one_order');
        $form->hidden('ids')->default(implode(',', $ids));
        $form->hidden('sn')->default(implode(',', $sns));
        $form->text('j_contact', '终端-姓名')->default($endpoint->name)->rules('required');
        $form->text('j_tel', '终端-联系电话')->default($endpoint->phone)->rules('required');
        $form->text('j_province', '终端-省')->default($province->region_name)->rules('required');
        $form->text('j_city', '终端-市')->default($city->region_name)->rules('required');
        $form->text('j_county', '终端-区')->default($district->region_name)->rules('required');
        $form->text('j_address', '终端-地址')->default($endpoint->address)->rules('required');
        $form->divide();
        $form->text('d_contact', '用户-姓名')->default($order->name)->rules('required');
        $form->text('d_tel', '用户-联系电话')->default($order->phone)->rules('required');
        $form->text('d_province', '用户-省')->default($order->province)->rules('required');
        $form->text('d_city', '用户-市')->default($order->city)->rules('required');
        $form->text('d_county', '用户-区')->default($order->district)->rules('required');
        $form->text('d_address', '用户-地址')->default($order->address)->rules('required');
        $content->body($form);
        return $content;
    }

    public function yt_express(Content $content, $id = null)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
            $data['pay_method'] = 1;  // 寄付
            $data['type'] = 2; //  寄走
            $data['number'] = 1;
            $id = $data['id'];
            unset($data['id']);
//            dd($data);
            $express = new Express();
            $result = $express->yt_create_express_order($data);

            if ($result['success'] == 'true') {
                //更新快递信息
                PostExpress::where(['readboy_sn' => $result['txLogisticID']])->update([
                    'status' => 1,
                    'exp_sn' => $result['mailNo'],
                ]);
                //写入订单信息
                $order = Order::where(['id' => $id])->first();
                $order->status = Order::EXP_GO_SUCCESS;
                $order->rb_go_exp_sn = $result['txLogisticID'];
                $order->go_exp_sn = $result['mailNo'];
                $order->go_exp_com = '圆通快递';
                $order->go_sure = 1;
                $order->updated_at_last = date('Y-m-d H:i:s');
                $order->save();
                return redirect('/admin/repair_exp_go');
            } else if ($result['success'] == 'false') {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => $result['reason'],
//                    'message' => $result['ERROR'],
                ]);
                Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                return back()->withInput()->with(compact('error'));
            } else {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => "内部错误",
//                    'message' => $result['ERROR'],
                ]);
                Order::where(['id' => $id])->update(['status' => Order::EXP_GO_FAIL]);
                return back()->withInput()->with(compact('error'));
            }
        }

        $order = Order::where(['id' => $id])->first();
        $endpoint = PostRepairEndpoint::where(['id' => $order->repair_endpoint])->first();
//        $endpoint = Endpoint::where(['name' => $repair_endpoint->name])->first();
        $province = ChinaArea::where(['region_id' => $endpoint->province])->first();
        $city = ChinaArea::where(['region_id' => $endpoint->city])->first();
        $district = ChinaArea::where(['region_id' => $endpoint->district])->first();
        $content->header('回寄-快递下单');

        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/agent_order_repair_exp_go/yt_express');
        $form->hidden('id')->default($id);
        $form->hidden('sn')->default($order->sn);
        $form->text('j_contact', '终端-姓名')->default($endpoint->name)->rules('required');
        $form->text('j_tel', '终端-联系电话')->default($endpoint->phone)->rules('required');
        $form->text('j_province', '终端-省')->default($province->region_name)->rules('required');
        $form->text('j_city', '终端-市')->default($city->region_name)->rules('required');
        $form->text('j_county', '终端-区')->default($district->region_name)->rules('required');
        $form->text('j_address', '终端-地址')->default($endpoint->address)->rules('required');
        $form->divide();
        $form->text('d_contact', '用户-姓名')->default($order->name)->rules('required');
        $form->text('d_tel', '用户-联系电话')->default($order->phone)->rules('required');
        $form->text('d_province', '用户-省')->default($order->province)->rules('required');
        $form->text('d_city', '用户-市')->default($order->city)->rules('required');
        $form->text('d_county', '用户-区')->default($order->district)->rules('required');
        $form->text('d_address', '用户-地址')->default($order->address)->rules('required');
        $content->body($form);
        return $content;
    }

    public function offline_express_one_order()
    {
        $data = request()->all();
        $ids = $data['ids'];
        foreach ($ids as $id) {
            //写入订单信息
            $order = Order::where(['id' => $id])->first();
            $order->status = Order::EXP_GO_SUCCESS;
            $order->go_exp_sn = $data['go_exp_sn'];
            $order->go_exp_com = $data['go_exp_com'];
            $order->go_sure = 1;
            $order->updated_at_last = date('Y-m-d H:i:s');
            $order->save();
        }
    }


    public function cancel(Request $request)
    {
        foreach (Order::find($request->get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form());
        });
    }

    public function expense(Request $request)
    {
        $sn = Request::get('sn');
        $save['pr_sn'] = $sn;
        $save['updated_at'] = date('Y-m-d H:i:s');
        PostRepairExpense::updateOrInsert(array('pr_sn' => $sn), $save);
    }

    public function expense_print()
    {
        $sn = Request::get('sn');
        $order = Order::where(['sn' => $sn])->first();
        $post_malfunction = DB::table('pr_malfunction')
            ->join('machine_malfunction', 'machine_malfunction.id', '=', 'pr_malfunction.malfunction_id')
            ->where('pr_sn', '=', $order['sn'])->pluck('machine_malfunction.title')->toArray();
        $malfunction = implode('，', $post_malfunction);
        $post_accessory = DB::table('pr_accessory')
            ->join('machine_accessory_relation', 'machine_accessory_relation.id', '=', 'pr_accessory.mar_id')
            ->join('machine_accessory', 'machine_accessory.id', '=', 'machine_accessory_relation.accessory_id')
            ->where('pr_sn', '=', $order['sn'])->pluck('machine_accessory.title')->toArray();
        $accessory = implode('，', $post_accessory);

        $pr_material = DB::table('pr_used_material')
            ->leftjoin('machine_accessory_tree', 'machine_accessory_tree.id', '=', 'pr_used_material.mat_id')
            ->leftjoin('material', 'material.id', '=', 'pr_used_material.material_id')
            ->where('pr_used_material.pr_sn', $order['sn'])
            ->select('material.name as name', 'material.price as price', 'pr_used_material.count as count',
                'material.code as code', 'material.old_code as old_code', 'material.specification as specification', 'material.from as from')
            ->get()
            ->toArray();
//            dd($pr_material);
        return view('admin/repair_exp_go/expense_print', compact('order', 'malfunction', 'accessory', 'pr_material'));
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOT
            //条码
            $('input[name="sn"]').bind('mouseover', function(){this.select();});
            $('input[name="sn"]').bind('click', function(){this.select();});
            $('input[name="sn"]').click();
            //打印物料损耗报告
            $('.print_expense').click(function(){
                var id = $(this).attr('value');
                $.get('/admin/repair_exp_go/expense?sn='+id, function(result){
                    layer.open({
                        type: 2,
                        title: '打印损耗报告',
                        shadeClose: true,
                        shade: 0.8,
                        area: ['600px', '90%'],
                        content: 'repair_exp_go/expense_print?sn='+id //iframe的url
                    });
                });
            });
            //打印检测报告
            $('.print_check').click(function(){
                var id = $(this).attr('value');
                layer.open({
                      type: 2,
                      title: '打印检测报告',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['600px', '90%'],
                      content: 'repair_check/print/'+id //iframe的url
                });
            });
            $('.btn-order-export').click(function(){
                 url = window.location.href;
                 if (url.indexOf("&order_export=1&_export_=1") < 0) {
                    if (url.indexOf("?") < 0) {
                        url = url + "?&order_export=1&_export_=1";
                    } else {
                        url = url + "&order_export=1&_export_=1";
                    }
                }
                window.open(url);
            });
            $('.btn-order-amount-export').click(function(){
                 url = window.location.href;
                 if (url.indexOf("&order_amount_export=1&_export_=1") < 0) {
                    if (url.indexOf("?") < 0) {
                        url = url + "?&order_amount_export=1&_export_=1";
                    } else {
                        url = url + "&order_amount_export=1&_export_=1";
                    }
                }
                window.open(url);
            });
            //打印快递条
            $('.print').click(function(){
                var id = $(this).attr('value');
//                LODOP=getLodop(document.getElementById('LODOP1'),document.getElementById('LODOP_EM1'));
//                LODOP.SET_PRINT_PAGESIZE(2,'55mm','80mm', '');//设定纸张方向和尺寸
//                LODOP.SET_PRINTER_INDEXA('Deli DL-888D');//选择打印机
//                LODOP.SET_PRINT_STYLE("FontSize",20);
//                LODOP.SET_PRINT_STYLE("Bold",1);
//                LODOP.ADD_PRINT_BARCODE('15mm','15mm','54mm','13mm',"128B",id);
//                LODOP.SET_PRINT_STYLEA(0,"AlignJustify",3);
//                LODOP.SET_PRINT_STYLEA(0,"ShowBarText",0);
////                LODOP.ADD_PRINT_TEXT('30mm','10mm','40mm','10mm','顺丰单号：'+id);
//                LODOP.ADD_PRINT_TEXT('30mm','10mm','80mm','20mm',id);
////                        LODOP.SET_PRINT_STYLEA(4,"TextFrame",2);
////                LODOP.PREVIEW();
            
                //横向打印
                LODOP=getLodop(document.getElementById('LODOP1'),document.getElementById('LODOP_EM1'));
                LODOP.SET_PRINT_PAGESIZE(1,'60mm','90mm', '');//设定纸张方向和尺寸
                LODOP.SET_PRINTER_INDEXA('Deli DL-888D');//选择打印机
                LODOP.SET_PRINT_STYLE("Bold",1);
                LODOP.SET_PRINT_STYLE("FontSize",11);
                LODOP.ADD_PRINT_BARCODE('10mm','1mm','58mm','15mm',"128Auto",id);
                LODOP.SET_PRINT_STYLEA(0,"AlignJustify",3);
                LODOP.SET_PRINT_STYLEA(0,"ShowBarText",0);
//                LODOP.ADD_PRINT_TEXT('20mm','20mm','40mm','10mm','顺丰单号：'+id);
                LODOP.ADD_PRINT_TEXT('25mm','1mm','55mm','10mm',id);
//                LODOP.ADD_PRINT_SHAPE(4,'35mm','0mm','80mm','5mm',0,1,"#000000");
//                        LODOP.SET_PRINT_STYLEA(4,"TextFrame",2);
//                LODOP.PREVIEW();
//                LODOP.PRINT_DESIGN();

                LODOP.PRINT();
            });
EOT;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
            //快捷筛选条
            $agent_order_sn = Input::get('agent_order_sn');
            if ($agent_order_sn != null) {
                $option = [
                    -1 => [
                        'name' => '全部',
                        'param' => function ($query) use ($agent_order_sn) {
                            $query->orwhere([['status', '=', Order::REPAIR_FINISH], ['quality', '=', 1], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn], ['go_confirm', '=', 1]])
                                ->orwhere([['status', '=', Order::REPAIR_REFUSE], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn], ['go_confirm', '=', 1]])
                                ->orwhere([['go_sure', '=', 1], ['status', '=', Order::EXP_GO_SUCCESS], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]])
                                ->orwhere([['status', '=', Order::EXP_GO_FAIL], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]])
                                ->orwhere([['status', '=', Order::ORDER_FINISH], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]]);
                        },
                    ],
                    1 => [
                        'name' => '未回寄',
                        'param' => function ($query) use ($agent_order_sn) {
                            $query->orwhere([['quality', '=', 1], ['status', '=', Order::REPAIR_FINISH], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]])
                                ->orwhere([['status', '=', Order::REPAIR_REFUSE], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn], ['go_confirm', '=', 1]]);
                        },
                    ],
                    2 => [
                        'name' => '回寄成功',
                        'param' => [['order.go_sure', '=', 1], ['order.status', '=', Order::EXP_GO_SUCCESS], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                    ],
                    3 => [
                        'name' => '回寄失败',
                        'param' => [['order.status', '=', Order::EXP_GO_FAIL], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                    ],
                    4 => [
                        'name' => '已完成',
                        'param' => [['order.status', '=', Order::ORDER_FINISH], ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $agent_order_sn]],
                    ],
                ];
//            dd(var_dump($option[2]['param']));
                //筛选条数
                foreach ($option as $key => $value) {
                    $option[$key]['count'] = Order::where($value['param'])
                        ->rightjoin('agent_order_correlation as aoc', 'order.sn', '=', 'aoc.order_sn')
                        ->count();
                }
//            dd($option);
                //根据自定义状态按钮搜索数据
                if (Request::get('quick_pick')) {
                    $quick_pick = Request::get('quick_pick');
                    $grid->model()->where($option[$quick_pick]['param'])
                        ->rightjoin('agent_order_correlation as aoc', 'order.sn', '=', 'aoc.order_sn')
                        ->orderBy('updated_at', 'desc');
                }
                $grid->model()
                    ->select('order.*')
                    ->where($option[-1]['param'])
                    ->rightjoin('agent_order_correlation as aoc', 'order.sn', '=', 'aoc.order_sn')
                    ->orderBy('updated_at', 'desc');
            } else {
                $option = [
                    -1 => [
                        'name' => '全部',
                        'param' => function ($query) use ($agent_order_sn) {
                            $query->orwhere([['status', '=', Order::REPAIR_FINISH], ['quality', '=', 1], ['order.type', '=', '2'], ['go_confirm', '=', 1]])
                                ->orwhere([['status', '=', Order::REPAIR_REFUSE], ['order.type', '=', '2'], ['go_confirm', '=', 1]])
                                ->orwhere([['go_sure', '=', 1], ['status', '=', Order::EXP_GO_SUCCESS], ['order.type', '=', '2']])
                                ->orwhere([['status', '=', Order::EXP_GO_FAIL], ['order.type', '=', '2']])
                                ->orwhere([['status', '=', Order::ORDER_FINISH], ['order.type', '=', '2']]);
                        },
                    ],
                    1 => [
                        'name' => '未回寄',
                        'param' => function ($query) use ($agent_order_sn) {
                            $query->orwhere([['quality', '=', 1], ['status', '=', Order::REPAIR_FINISH], ['order.type', '=', '2'], ['go_confirm', '=', 1]])
                                ->orwhere([['status', '=', Order::REPAIR_REFUSE], ['order.type', '=', '2'], ['go_confirm', '=', 1]]);
                        },
                    ],
                    2 => [
                        'name' => '回寄成功',
                        'param' => [['order.go_sure', '=', 1], ['order.status', '=', Order::EXP_GO_SUCCESS], ['order.type', '=', '2']],
                    ],
                    3 => [
                        'name' => '回寄失败',
                        'param' => [['order.status', '=', Order::EXP_GO_FAIL], ['order.type', '=', '2']],
                    ],
                    4 => [
                        'name' => '已完成',
                        'param' => [['order.status', '=', Order::ORDER_FINISH], ['order.type', '=', '2']],
                    ],
                ];
//            dd(var_dump($option[2]['param']));
                //筛选条数
                foreach ($option as $key => $value) {
                    $option[$key]['count'] = Order::where($value['param'])
//                        ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                        ->count();
                }
//            dd($option);
                //根据自定义状态按钮搜索数据
                if (Request::get('quick_pick')) {
                    $quick_pick = Request::get('quick_pick');
                    $grid->model()->where($option[$quick_pick]['param'])
//                        ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                        ->orderBy('updated_at', 'desc');
                }
                $grid->model()
                    ->select('order.*')
                    ->where($option[-1]['param'])
//                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->orderBy('updated_at', 'desc');
            }
            //同源快递筛选
            if (Request::get('check_repeat_exp')) {
                $check_id = Request::get('check_repeat_exp');
                $order = Order::where('id', $check_id)->first();
                $where = array();
                $where[] = array('uid', $order->uid);
                $where[] = array('name', $order->name);
                $where[] = array('phone', $order->phone);
                $where[] = array('province', $order->province);
                $where[] = array('city', $order->city);
                $where[] = array('district', $order->district);
                $where[] = array('address', $order->address);
                $grid->model()->where($where);
            }

            $grid->disableCreation();

            //$grid->disableExport();
            $grid->exporter(new ExpenseExporter());
            if (Request::get('order_export')) {
                $grid->exporter(new AgencyPostRepairRegionExporter());
            }
            if (Request::get('order_amount_export')) {
                $grid->exporter(new AgencyPostRepairAmountExporter());
            }

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('come_exp_sn', '寄来快递单号');
                $filter->like('go_exp_sn', '寄去快递单号');
                $filter->like('sn', '维修订单号');
                $filter->like('barcode', 'S/N码');
                $filter->like('name', '联系人');
                $filter->equal('status', '订单状态')
                    ->select(Order::STATUS);
                $a = User::join('order as o', 'o.repair_man', '=', 'admin_users.id')->pluck('admin_users.name', 'admin_users.id')->all();
                $filter->equal('repair_man', '维修人')->select(
                    $a

                );

//                $filter->like('repair_user.name','维修人');
//                $filter->equal('expense.status', '有没有核销')->select(['0' => '未核销', 'null' => '已核销']);
                $filter->between('updated_at', '最后操作时间')->datetime();
                $filter->between('updated_at_last', '下单时间')->datetime();
            });

            //工具条
            $grid->tools(function ($tools) use ($option) {
                //自定义状态快捷筛选按钮
                $tools->append(new QuickPickTool($option));

                $tools->batch(function ($batch) {
                    $batch->add('圆通快递同一订单', new YtExpGoOneOrder());
                    $batch->add('顺丰快递同一订单', new ExpGoOneOrder());
                    $batch->add('线下下单', new offlineExpGoOneOrder());
                });
                $html = <<<EOF
                <div class="btn-group" style=" margin-top: 10px;">
                      <a href ="" class="btn btn-sm btn-success btn-order-export" >
                        <i class="fa fa-print" ></i > 导出寄修概况
                      </a >
                </div >
EOF;
                $tools->append($html);
                $html_print = <<<EOF
                <div class="btn-group" style=" margin-top: 10px;">
                      <a href ="" class="btn btn-sm btn-success btn-order-amount-export" >
                        <i class="fa fa-print" ></i > 导出寄修费用概况
                      </a >
                </div >
EOF;
                $tools->append($html_print);
                $button = <<<EOF
                     <div class="btn-group pull-right" style="margin-right:15px; margin-top: 10px;">
                      <a href ="agent_order" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 返回
                      </a >
                    </div >
                    <div class="btn-group pull-right" style="margin-right:15px; margin-top: 10px;">
                      <a href ="agent_order_repair_exp_go" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 代理商总单
                      </a >
                    </div >
EOF;
                $tools->append($button);
            });

            //表格显示列
            $grid->id('ID')->sortable();
            $grid->sn('维修订单号');
            $grid->barcode('S/N码');
            $grid->name('联系人');
            $grid->phone('用户联系方式');
            $grid->status('订单状态')->display(function ($status) {
                $s = Order::STATUS;
                if (array_key_exists($status, $s)) {
                    return $s[$status];
                } else {
                    return "————";
                }
            });
            $grid->pay_amount('支付费用')->display(function ($amount) {
                return '￥' . $amount;
            });
            $grid->repair_user()->name('维修人')->display(function ($name) {
                if ($name == '')
                    return '———';
                return $name;
            });
//            $grid->column('维修完成时间');
//            $grid->go_exp_type('支付方式')->display(function ($go_exp_type) {
//                if ($go_exp_type == 1) {
//                    return '月结';
//                } elseif ($go_exp_type == 2) {
//                    return '到付';
//                } else {
//                    return '';
//                }
//            });
//            $grid->go_sure('快递状态')->display(function ($go_sure) {
//                if ($go_sure == 0) {
//                    return '未下单';
//                } elseif ($go_sure == 1) {
//                    return '已下单';
//                } else {
//                    return '下单失败';
//                }
//            });
            $grid->go_exp_sn('快递单号');
            $grid->expense()->updated_at('核销时间');
            $grid->updated_at('最后操作时间');
            $grid->updated_at_last('下单时间')->sortable();

            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $status = $actions->row->status;
                $c = 'post_repair/view/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">查看</span></a>';
                $actions->append($html);
                if ($status == Order::REPAIR_FINISH || $status == Order::REPAIR_REFUSE || $status == Order::EXP_GO_FAIL) {
                    $c = 'repair_exp_go/express/' . $actions->getKey();
                    $html = '<a href="' . $c . '"><span style="color:orange">顺丰下单 </span></a>';
                    $actions->append($html);
                }
                if ($status == Order::REPAIR_FINISH || $status == Order::REPAIR_REFUSE || $status == Order::EXP_GO_FAIL) {
                    $c = 'agent_order_repair_exp_go/yt_express/' . $actions->getKey();
                    $html = '<a href="' . $c . '"><span style="color:orange">圆通下单 </span></a>';
                    $actions->append($html);
                }
//                $c = 'agent_order_repair_exp_go/yt_route_order';
//                $html = '<a href="' . $c . '"><span style="color:orange">测试 </span></a>';
//                $actions->append($html);
                if ($status == Order::REPAIR_FINISH || $status == Order::REPAIR_REFUSE) {
                    $c = 'agent_order_repair_exp_go?check_repeat_exp=' . $actions->getKey();
                    $html = '<a href="' . $c . '"><span style="color:orange"> 快递筛选 </span></a>';
                    $actions->append($html);
                }
                if ($status == Order::EXP_GO_SUCCESS) {
                    $v = $actions->row->id;
                    $html = '<a href="javascript:void(0);" class="print_check" value="' . $v . '"><span style="color:blue"> 【打印检测报告】 </span></a>';
                    $actions->append($html);
                    $expense = PostRepairExpense::where('pr_sn', $actions->row->sn)->first();
                    if ($expense) {
                        $v = $actions->row->sn;
                        $html = '<a href="javascript:void(0);" class="print_expense" value="' . $v . '"><span style="color:green"> 【打印物料损耗报告】 </span></a>';
                        $actions->append($html);
                    } else {
                        $v = $actions->row->sn;
                        $html = '<a href="javascript:void(0);" class="print_expense" value="' . $v . '"><span style="color:red"> 【打印物料损耗报告】 </span></a>';
                        $actions->append($html);
                    }
                    $exp_sn = $actions->row->go_exp_sn;
                    $html = '<a href="javascript:void(0)" value="' . $exp_sn . '" class="print"><span style="color:orange"> 打印快递条 </span></a>';
                    $actions->append($html);
                }
                if ($status == Order::ORDER_FINISH) {
                    $expense = PostRepairExpense::where('pr_sn', $actions->row->sn)->first();
                    if ($expense) {
                        $v = $actions->row->sn;
                        $html = '<a href="javascript:void(0);" class="print_expense" value="' . $v . '"><span style="color:green"> 【打印物料损耗报告】 </span></a>';
                        $actions->append($html);
                    } else {
                        $v = $actions->row->sn;
                        $html = '<a href="javascript:void(0);" class="print_expense" value="' . $v . '"><span style="color:red"> 【打印物料损耗报告】 </span></a>';
                        $actions->append($html);
                    }
                }
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Admin::form(Order::class, function (Form $form) {

            $form->select('repair_status', '维修状态')
                ->options(Order::repair_status);
            $form->hidden('repair_man');
            $form->hidden('status');
            $form->saving(function (Form $form) {
                $form->repair_man = Admin::user()->id;
                if ($form->repair_status == 1)
                    $form->status = Order::REPAIR_FINISH;
            });
        });
    }
}
