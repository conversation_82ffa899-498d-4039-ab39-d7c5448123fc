<?php
/**
 * Created by PhpStorm.
 * User: 1
 * Date: 2017/5/15
 * Time: 14:02
 */

namespace App\Admin\Extensions\Tools;


use Encore\Admin\Grid\Tools\AbstractTool;
use Encore\Admin\Admin;
use Illuminate\Support\Facades\Request;

class EndpointSupportStatus extends AbstractTool
{
    protected function script()
    {
        $url = Request::fullUrlWithQuery(['status' => '_status_']);

        return <<<EOT

$('input:radio.status-gender').change(function () {

    var url = "$url".replace('_status_', $(this).val());

    $.pjax({container:'#pjax-container', url: url });

});

EOT;
    }

    public function render()
    {
        Admin::script($this->script());

        $options = [
            10 => '全部',
            0 => '等待审核',
            1 => '申请通过',
            2 => '已回传',
            3 => '核销成功',
            -1 => '申请失败',
            -2 => '未回传',
            -3 => '核销失败',
        ];

        return view('admin.tools.endpoint_support_status', compact('options'));
    }
}