<?php

namespace App\Admin\Controllers;

use App\Models\AdminUsersYkf;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;

use App\Http\Controllers\Controller;

use Encore\Admin\Controllers\ModelForm;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\MessageBag;

/**
 * 云客服管理
 * Create By Su Guanghua 2021/11/04 09:45
 */
class CloudCustomerController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('云客服管理');
            $content->description('云客服账号列表');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('云客服管理');
            $content->description('编辑');
            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('云客服管理');
            $content->description('添加');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(AdminUsersYkf::class, function (Grid $grid) {

            $grid->disableExport();
            $grid->disableCreation();

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('admin_user.username', '用户名');
                $filter->like('admin_user.name', '名称');

            });

            // 自定义按钮
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append(new setYkfBtn());
            });

            // $grid->actions(function ($actions) {
            //     $actions->disableDelete();
            // });

            $grid->model()->orderBy('created_at', 'desc');

            $grid->admin_user()->username('用户名');
            $grid->admin_user()->name('名称');
            $grid->seat_number( '坐席工号');
            $grid->ykf_account( '云客服账号');
            $grid->created_at('创建时间');
            $grid->updated_at('更新时间');

        });
    }

    /**
     * 添加云客服账号
     */
    public function setYkf()
    {
        $form = Admin::form(AdminUsersYkf::class, function (Form $form) {

            $form->setAction('/admin/cloud_customer/setYkf');

            $form->select('uid', '用户')
                ->ajax('/admin/cloud_customer/selectUser')
                ->rules('required')
                ->help('请选择一个用户');

            $form->text('seat_number', '坐席工号')->help('必填;例:8000')->rules('max:50|required');
            $form->text('ykf_account', '云客服账号')->help('必填')->rules('max:50|required');
            $form->password('ykf_password', '云客服密码')->help('必填')->rules('confirmed|required');
            $form->password('ykf_password_confirmation', '重复密码')->help('必填')->rules('required');
        });

        return Admin::content(function (Content $content) use ($form) {
            $content->header('添加云客服账号');
            $content->description('添加云客服账号');
            $content->body($form);
        });
    }

    /**
     * 执行添加云客服账号
     */
    public function doSetYkf(){

        $uid = trim(request()->input('uid'));
        $seat_number = trim(request()->input('seat_number'));
        $ykf_account = trim(request()->input('ykf_account'));
        $ykf_password = trim(request()->input('ykf_password'));
        $ykf_password_confirmation = trim(request()->input('ykf_password_confirmation'));

        if(empty($uid)){
            return $this->returnImportError('用户不能为空');
        }

        if(empty($seat_number)){
            return $this->returnImportError('坐席工号不能为空');
        }

        if(empty($ykf_account)){
            return $this->returnImportError('云客服账号不能为空');
        }

        if($ykf_password != $ykf_password_confirmation){
            return $this->returnImportError('两次输入密码不一致');
        } 

        // 存在无法添加
        if(AdminUsersYkf::where('uid' , $uid)->first()){
            return $this->returnImportError('该用户已经设置');
        } 

        $insertData = array(
            'uid' => $uid,
            'seat_number' => $seat_number,
            'ykf_account' => $ykf_account,
            'ykf_password' => $ykf_password,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        );

        if(!AdminUsersYkf::insert($insertData)){
            return $this->returnImportError('设置失败');
        } 
        
        admin_toastr('设置成功');
        return redirect('/admin/cloud_customer');
    }

    /**
     * 错误返回
     */
    private function returnImportError($message)
    {
        $error = new MessageBag([
            'title'     => '参数错误',
            'message'   => $message
        ]);
        return back()->with(compact('error'))->withInput();
    }

    /**
     * 搜索用户姓名
     */
    public function selectUser() {
    	// select框输入内容（用户姓名），q是固定值不需要变
        $name = request()->get('q');
        return DB::table('admin_users')
            // ->where('id' , '!=' , 1)
            ->where('status' , 1)
            ->where(function ($query) use($name) {
                $query->where('name' , 'like' , '%'. $name . '%')
                    ->orWhere('username' , 'like' , '%'. $name . '%')
                    ->orWhere('phone' , 'like' , '%'. $name . '%');
            })
            ->selectRaw('id,name as text')
            ->paginate(10);
    }

    /**
     * Make a form builder.
     * @param $id
     * @return Form
     */
    protected function form($id = null)
    {
        $js = <<<EOF
          $("form").submit(function(e){
            $('.form-control').attr('disabled',false);
          });
EOF;

        Admin::script($js);

        return Admin::form(AdminUsersYkf::class, function (Form $form) use ($id) {

            $form->text('admin_user.name', '用户名')->readOnly();
            $form->text('seat_number', '坐席工号')->help('必填')->rules('max:50|required');
            $form->text('ykf_account', '云客服账号')->help('必填')->rules('max:50|required');

            $form->password('ykf_password', '云客服密码')->help('必填')->rules('confirmed|required')
                ->default(function ($form) use ($id) {
                    return $id ? $form->model()->ykf_password : '';
                });
            $form->password('ykf_password_confirmation', '重复密码')->help('必填')->rules('required')
                ->default(function ($form) use ($id) {
                    return $id ? $form->model()->ykf_password : '';
                });

            $form->ignore(['ykf_password_confirmation']);
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}

/**
 * 自定义按钮
 */
class setYkfBtn extends Grid\Tools\AbstractTool
{
    public function render()
    {
        return <<< EOT
<div class="btn-group pull-right" style="margin-right: 10px">
    <a href="/admin/cloud_customer/setYkf" class="btn btn-sm btn-primary"><i class="fa fa-user"></i>&nbsp;添加云客服账号</a>
</div>
EOT;
    }
}