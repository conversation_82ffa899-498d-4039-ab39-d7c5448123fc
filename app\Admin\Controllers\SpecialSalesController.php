<?php

namespace App\Admin\Controllers;

use App\Models\OptionalAccessory;
use App\Models\OptionalAccessoryCategory;

use App\Models\SpecialSales;
use App\Services\Admin\YxApiService;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\MessageBag;

class SpecialSalesController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('特批出库条码');
            $content->description('特批出库条码，不保修');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        if (!Admin::user()->inroles(['manager'])) {
            return $this->returnImportError('无权操作');
        }
        return Admin::content(function (Content $content) use ($id) {

            $content->header('特批出库条码');
            $content->description('特批出库条码，不保修');

            $content->body($this->form()->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        if (!Admin::user()->inroles(['Administrator','black_list_manager'])) {
            return $this->returnImportError('无权操作');
        }
        return Admin::content(function (Content $content) {

            $content->header('特批出库条码');
            $content->description('特批出库条码，不保修');

            $content->body($this->form());
        });
    }

    public function import_data_view()
    {
        if (!Admin::user()->inroles(['Administrator','black_list_manager'])) {
            return $this->returnImportError('无权操作');
        }
        $form = Admin::form(SpecialSales::class, function (Form $form) {
            $form->tools(function (Form\Tools $tools) {
                $tools->disableBackButton();
                $tools->add('<a style="margin-right: 10px;" href="/storage/特批出库模板.xlsx" target="_blank" class="btn btn-sm btn-twitter"><i class="fa fa-download"></i>&nbsp;&nbsp;模板下载</a>');
            });

            $form->setAction('/admin/special_sales/import');
            $form->file('file', 'Excel文件：')
                ->rules('required')
                ->options([
                    'maxFileSize' => 2048,
                    'msgSizeTooLarge' => '文件 "{name}" (<b>{size} KB</b>) 超过了最大允许上传大小 <b>{maxSize} KB</b>.',
                    'allowedFileExtensions' => ['xls', 'xlsx'],
                ]);
        });

        return Admin::content(function (Content $content) use ($form) {
            $content->header('特批出库条码');
            $content->description('特批出库条码导入');
            $content->body($form);
        });
    }

    public function import_data_store(Request $request)
    {
        // 用户相关数组
        $this->validate($request, [
            'file' => 'required|file|max:2048'
        ]);

        if (!Admin::user()->inroles(['Administrator','black_list_manager'])) {
            return $this->returnImportError('无权操作');
        }


        $file = $request->file('file')->path();

        try {
            $reader = \PHPExcel_IOFactory::createReaderForFile($file);
            $reader->setReadDataOnly(true);
            $spreadSheet = $reader->load($file);
            $data = $spreadSheet->getSheet(0)->toArray();

            if (empty($data)) {
                throw new \Exception('没有数据');
            }

            $header = $data[0];
            $validHeader = [
                '物料编码',        //0
                '物料名称',        //1
                '规格型号',        //2
                '序列号',        //3
            ];
            $columns = count($validHeader);
            if (count($header) < $columns) {
                throw new \Exception('数据至少应有' . $columns . '列');
            }

            foreach ($validHeader as $i => $valid) {
                if ($header[$i] != $valid) {
                    throw new \Exception(chr($i + 65) . '1应为“' . $valid . '”');
                }
            }

            $rules = [
                2 => 'required',
                3 => 'required',
            ];

            $messages = [
                2 => ['required' => ':attribute机型不能为空'],
                3 => ['required' => ':attribute序列号不能为空'],
            ];

            $validData = [];
            $data = array_slice($data, 1);
            $count = count($data);
            $errMessages = [];
            for ($row = 0; $row < $count; $row++) {
                $item = array_map('trim', $data[$row]);

                if (!$item[3]) {
                    continue;
                }

                // 所有单元格都为空的话忽略
                if (empty(array_filter($item, function ($v) {
                    return !empty($v);
                }))) {
                    continue;
                }

                if (count($item) < $columns) {
                    throw new \Exception('第' . ($row + 2) . '行数据不足' . $columns . '列');
                }

                for ($col = 0; $col < $columns; $col++) {
                    if (isset($rules[$col])) {
                        $key = chr($col + 65) . ($row + 2);
                        $validator = Validator::make([$key => $item[$col]], [$key => $rules[$col]], $messages[$col]);
                        if ($validator->fails()) {
                            $errMessages[] = $validator->errors()->first();
                        }
                    }
                }


                $validData[] = [
                    'sn' => $item[3],
                    'model' => $item[2],
                    'remarks' => $item[1],
                    'created_at' => date('Y-m-d H:i:s'),
                ];
            }

            if ($errMessages) {
                throw new \Exception(implode('<br/>', $errMessages));
            }
            $batchSize = 500;
            DB::transaction(function () use ($validData, $batchSize) {
                foreach (array_chunk($validData, $batchSize) as $batch) {
                    SpecialSales::insert($batch);
                }
            });

        } catch (\Exception $ex) {
            return $this->returnImportError($ex->getMessage());
        }

        admin_toastr('导入成功');
        return redirect('/admin/special_sales');
    }

    private function returnImportError($message)
    {
        $error = new MessageBag([
            'title' => '导入出错',
            'message' => $message
        ]);
        return back()->with(compact('error'))->withInput();
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(SpecialSales::class, function (Grid $grid) {
            $grid->disableExport();
            $isAdmin = Admin::user()->inroles([ 'Administrator','black_list_manager']);
            if (!$isAdmin){
                $grid->disableCreation();
            }
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('sn', '条码');
                $filter->like('model', '机型');
            });
            $grid->tools(function ($tools) use ($isAdmin) {
                if (!$isAdmin) {
                    $tools->batch(function ($batch) {
                        $batch->disableDelete();
                    });
                }
                if ($isAdmin) {
                    $tools->append(new ImportBtnData());
                }
            });

            $grid->model()->orderBy('id', 'desc');
            $grid->id('ID')->sortable();
            $grid->sn('序列号');
            $grid->column('model', '机型名称');
            $grid->date('销售日期');
            $grid->remarks('备注');
            $grid->created_at('创建日期');
            $grid->actions(function ($actions) use ($isAdmin) {
                $actions->disableEdit();
                if (!$isAdmin) {
                    $actions->disableDelete();
                }
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Admin::form(SpecialSales::class, function (Form $form) {

            $form->text('sn', '序列号')->rules('required');
            $form->text('model', '机型')->rules('required');
            $form->date('date', '销售日期');
            $form->text('remarks', '备注');
        });
    }
}


class ImportBtnData extends Grid\Tools\AbstractTool
{
    public function render()
    {
        return <<< EOT
    <a href="/admin/special_sales/import" class="btn btn-sm btn-primary grid-refresh"><i class="fa fa-upload"></i>条码导入</a>
EOT;
    }
}