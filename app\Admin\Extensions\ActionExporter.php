<?php

namespace App\Admin\Extensions;

use Encore\Admin\Grid\Exporters\AbstractExporter;

class ActionExporter extends AbstractExporter
{
    public function export()
    {
        $filename = '活动.csv';

        // 这里获取数据
//        dd($this->getData());
        $data = $this->getData();
        // 根据上面的数据拼接出导出数据，
        $output = '';
        $level_array = [
            1 => '省级',
            2 => '市级',
            3 => '县级',
            4 => '乡镇',
            0 => '未知',
        ];
        $status_array = [
            1 => '等待审批',
            2 => '大区审批通过',
            3 => '审批通过',
            4 => '已完成',
            5 => '已核销',
            6 => '审批未通过',
            7 => '未完成',
            8 => '核销未通过',
        ];
        if (!empty($data)) {
            $titles = ['编号','活动名称','终端名称','终端地址','活动开始时间','活动结束时间','负责人','电话','级别','状态'];
            $output = implode(',', $titles) . "\n";
            foreach ($data as $row) {
                $row = [
                    $row['number'],
                    $row['name'],
                    $row['endpoint']['name'],
                    $row['endpoint']['address'],
                    strval($row['date_start']),
                    strval($row['date_end']),
                    $row['principal'],
                    $row['phone'],
                    $level_array[$row['level']],
                    $status_array[$row['status']] . "\t",
                ];
                $output .= implode(',', array_dot($row)) . "\n";
            }
        }

        // 在这里控制你想输出的格式,或者使用第三方库导出Excel文件
        $headers = [
            'Content-Encoding'    => 'UTF-8',
            'Content-Type'        => 'text/csv;charset=UTF-8',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        echo "\xEF\xBB\xBF";

        // 导出文件，
        response(rtrim($output, "\n"), 200, $headers)->send();

        exit;
    }
}