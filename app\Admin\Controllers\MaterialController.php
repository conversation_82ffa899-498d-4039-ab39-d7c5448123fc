<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\MaterialExporter;
use App\Models\Material;

use App\Models\MaterialCategory;
use App\Services\Admin\MaterialCategoryService;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Widgets\Table;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\MessageBag;
use Maatwebsite\Excel\Facades\Excel;

//物料管理
class MaterialController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('物料管理');
            $content->description('物料列表');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('物料管理');
            $content->description('物料编辑');

            $content->body($this->form()->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('物料管理');
            $content->description('物料添加');

            $content->body($this->form());
        });
    }

    public function upload(Request $request)
    {
        if (request()->isMethod('post')) {
            $path = $request->file('excel')->getRealPath();
            $res = [];
            Excel::load($path, function ($reader) use (&$res) {
                $reader = $reader->getSheet(0);
                $res = $reader->toArray();
            });
            if (count($res) < 2) {
                $error = new MessageBag([
                    'title' => '文件错误',
                    'message' => '不是Excel文件或表格不符合要求',
                ]);
                return back()->with(compact('error'));
            }
            $head = $res[0];
            $data = array_slice($res, 1);
//            dd($res);
            $toSave = [];
            $diferent = array();
            $diferent2 = array();
            foreach ($data as $k => $v) {
                foreach ($head as $k1 => $name) {
                    if ($name == null || $name == '') {
                        continue;
                    }
                    $toSave[$name] = $v[$k1];
                }
                if (!array_key_exists("code_new", $toSave)) {
                    $error = new MessageBag([
                        'title' => '文件错误',
                        'message' => '不是Excel文件或没有表头 code_new',
                    ]);
                    return back()->with(compact('error'));
                }
                if (!array_key_exists("code_old", $toSave)) {
                    $error = new MessageBag([
                        'title' => '文件错误',
                        'message' => '不是Excel文件或没有表头 code_old',
                    ]);
                    return back()->with(compact('error'));
                }
                if (!array_key_exists("price_user", $toSave)) {
                    $error = new MessageBag([
                        'title' => '文件错误',
                        'message' => '不是Excel文件或没有表头 price_user',
                    ]);
                    return back()->with(compact('error'));
                }
                $has_dif = false;
//                dump($toSave);
                $db = DB::table('material')->where([['code', $toSave['code_old']]])->first();
                if ($db && empty(floatval($db->price)) && empty(floatval($db->price_first)) && empty(floatval($db->price_second))) {
                    $update = ['price' => $toSave['price_user'],'price_first' => $toSave['price_first'],'price_second' => $toSave['price_second']];
                    $update['updated_at'] = date('Y-m-d H:i:s');
                    DB::table('material')->where('id', $db->id)->update($update);
                } else {
                    $has_dif = true;
                }
                if ($has_dif){
                    $db = DB::table('material')->where([['old_code', $toSave['code_old']]])->first();
                    if ($db && empty(floatval($db->price)) && empty(floatval($db->price_first)) && empty(floatval($db->price_second))) {
                        $update = ['price' => $toSave['price_user'],'price_first' => $toSave['price_first'],'price_second' => $toSave['price_second']];
                        $update['updated_at'] = date('Y-m-d H:i:s');
                        DB::table('material')->where('id', $db->id)->update($update);
                        $has_dif = false;
                    }
                }
                if ($has_dif){
                    $db = DB::table('material')->where([['code', $toSave['code_new']]])->first();
                    if ($db && empty(floatval($db->price)) && empty(floatval($db->price_first)) && empty(floatval($db->price_second))) {
                        $update = ['price' => $toSave['price_user'],'price_first' => $toSave['price_first'],'price_second' => $toSave['price_second']];
                        $update['updated_at'] = date('Y-m-d H:i:s');
                        DB::table('material')->where('id', $db->id)->update($update);
                        $has_dif = false;
                    } else {
                        $has_dif = true;
                    }
                }
                if ($has_dif) {
                    $diferent[] = $toSave;
                }else{
                    $diferent2[] = $toSave;
                }

//                $arr = [$db->code, $db->name, $db->price];
//                $deferent[] = $arr;
            }
            if ($diferent && $diferent2) {
                return Admin::content(function(Content $content) use ($head, $diferent, $diferent2) {
                    $content->header('导入物料顾客价格已存在(上表)，未存在（下表）');
                    $table = new Table($head, $diferent);
                    $table2 = new Table($head, $diferent2);
                    $content->body($table);
                    $content->body($table2);
                });
            }elseif ($diferent){
                return Admin::content(function(Content $content) use ($head, $diferent) {
                    $content->header('导入物料顾客价格已存在');
                    $table = new Table($head, $diferent);
                    $content->body($table);
                });
            } else {
                return redirect(admin_url('material'));
            }
        }
        return Admin::content(function (Content $content) {
            $content->header("导入物料顾客价格");
            $content->description('');
            $form = new \Encore\Admin\Widgets\Form();
            $form->action('/admin/material/upload');
            $form->file("excel", "请选择Excel文件");
            $content->body($form);
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<END
            //图片弹出层
            $('.img-thumbnail').click(
                 function(){
                     let src = $(this).attr('src');
                     let html = '<div class="message"><img style="max-width:900px;max-height:900px;" src="'+ src +'"><//div>'
                     $.fancybox.open(html);
                 }
             );
END;
        Admin::script($script);

        return Admin::grid(Material::class, function (Grid $grid) {
//            $grid->disableExport();
            $grid->exporter(new MaterialExporter());
//            $grid->disableCreation();
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->where(function($query){
                    $q = '%'. $this->input . '%';
                    $query->where('code', 'like', $q)->orwhere('old_code', 'like', $q)->orwhere('name', 'like', $q)->orwhere('specification', 'like', $q);
                }, '关键词');
                $filter->where(function($query){
                    if ($this->input == 1) {
                        $query->where('price')->orwhere('price_first')->orwhere('price_second');
                    }elseif ($this->input == 2){
                        $query->where('price','<>', 'null')->orwhere('price_first','<>', 'null')->orwhere('price_second','<>', 'null');
                    }else {

                    }
                }, '价格')->select([1 => '没有价格',2 => '有价格']);
                $filter->between('price', '顾客价格');
                $filter->between('updated_at', '更新时间')->datetime();
            });
            $grid->model()->orderBy('id', 'desc');
            if (Admin::user()->inRoles(['administrator', 'manager', 'pr_operator'])) {
                $grid->code('物料编码')->sortable()->editable();
                $grid->old_code('旧的物料编码')->sortable()->editable();
                $grid->name('物料名称')->sortable()->editable();
                $grid->specification('物料规格')->sortable()->editable();
                $grid->price_first('总代价格')->sortable()->editable();
                $grid->price_second('二代价格')->sortable()->editable();
                $grid->price('顾客价格')->sortable()->editable();
                $grid->quantity('库存')->sortable()->editable();
                $grid->from('仓库')->sortable()->editable('select', [0 => '旧仓库', 1 => '新仓库']);
                $grid->status('是否可用')->sortable()->editable('select', [0 => '不可用', 1 => '可用']);
                $grid->updated_at('最后更新')->sortable();
                $grid->tools(function($tools){
                    $html = <<<EOF
                    <div class="btn-group pull-right" style="margin-right:10px">
                        <a href ="material/upload" class="btn btn-sm btn-success" >
                            <i class="fa fa-print" ></i > 导入顾客价格
                        </a >
                    </div >
EOF;
                    $tools->append($html);
                });
            } else {
                $grid->tools(function ($tools) {
                    $tools->batch(function ($batch) {
                        $batch->disableDelete();
                    });

                });
                $grid->disableCreation();
                $grid->code('物料编码')->sortable();
                $grid->old_code('旧的物料编码')->sortable();
                $grid->name('物料名称')->sortable();
                $grid->specification('物料规格')->sortable();
                $grid->price_first('总代价格')->sortable();
                $grid->price_second('二代价格')->sortable();
                $grid->price('顾客价格')->sortable();
                $grid->quantity('库存')->sortable();
                $grid->from('仓库')->display(function ($value) {
                    $data = [0 => '旧仓库', 1 => '新仓库'];
                    return $data[$value];
                });
                $grid->updated_at('最后更新')->sortable();
                $grid->actions(function ($actions) {
                    $actions->disableDelete();
                    $actions->disableEdit();
                });
            }
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Admin::form(Material::class, function (Form $form) {

            $form->text('code', '物料编号');
            $form->text('name', '物料名称');
            $form->text('specification', '物料规格');
            $form->text('price', '顾客价格');
            $form->text('price_first', '总代价格');
            $form->text('price_second', '二代价格');
            $form->text('quantity', '库存数量');
            $form->select('from', '仓库来源')->options([0 => '旧仓库', 1 => '新仓库']);
            $form->select('status', '是否可用')->options([0 => '不可用', 1 => '可用']);
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
