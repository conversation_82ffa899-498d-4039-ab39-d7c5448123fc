<?php

namespace App\Admin\Extensions;

use App\Models\Machine;
use App\Models\MachineAccessory;
use App\Models\MachineAccessoryRelation;
use App\Models\MachineMalfunction;
use App\Models\RepairBill;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\Input;

class RepairBillExporter extends AbstractExporter
{
    use ExcelExportTrait;

    public function export()
    {
        $filename = '售后工单导出.csv';
        $titles = [
            '维修单号',
            '机身S/N码',
            '购机日期',
            '客户姓名',
            '客户联系方式',
            '机型',
            '维修收机日期',
            '维修来源',
            '服务请求(维修描述)',
            '维修方式',
            '维修原因',
            '处理信息',
            '快递寄出日期',
            '快递单号',
            '备注',
            //            '配件列表',
            //            '故障列表',
            //            '配件以及价格列表',
            '维修费用总计',
            '是否出单',
        ];
        $createdAt = Input::get('created_at');
        $query = RepairBill::with('machine_malfunction_relation')
            ->with('machine_accessory_relation');

        if ($createdAt) {
            $query->whereBetween('created_at', array_values($createdAt));
        }
        //判断当前角色,如果是技工的话导出所属技工的,不是的话导出所有
        if (Admin::user()->inRoles('afterSalesEngineer')) {
            $query->where('create_uid', '=', Admin::user()->id);
        }
        //        $data = $query->get();
        $data = $this->getData();

        $machineModel = Machine::all()->pluck('name', 'model_id');
        //        $machineMalfunctions = MachineMalfunction::all()->pluck('title', 'id');
        //        $machineAccessories = MachineAccessory::all()->pluck('title', 'id');
        //        $machineAccessoryRelationPrices = MachineAccessoryRelation::all()
        //            ->pluck('price', 'id');
        //        $machineAccessoryRelationAccessoryIds = MachineAccessoryRelation::all()
        //            ->pluck('accessory_id', 'id');
        $formatData = [];
        if (!empty($data)) {
            foreach ($data as $key => $row) {
                //过滤删除的
                if ($row['deleted_at']) {
                    continue;
                }
                //                //获取对应的故障
                //                $malfunctions = '';
                //                foreach ($row->machine_malfunction_relation as $k => $v) {
                //                    $malfunctions .= ($k + 1) . '.' . $machineMalfunctions[$v->malfunction_id] . ' ';
                //                }
                //                //获取对应的配件(因为性能问题不用laravel的集合查找)
                //                $accessories = '';
                //                foreach ($row->machine_accessory_relation as $k => $v) {
                //                    $filterId = $v->machine_accessory_relation_id;
                //                    $accessories .= ($k + 1) . '.' . $machineAccessories[$machineAccessoryRelationAccessoryIds[$filterId]]
                //                        . '|' . ($machineAccessoryRelationPrices[$filterId] ?: '未录入价格') . '元  ';
                //                }
                //加一个空字符串是为了导出的格式整洁
                $formatData[] = [
                    $row['bill_number'] . "\t",
                    $row['sn_code'] . "\t",
                    $row['buy_date'],
                    $row['customer_name'],
                    $row['customer_phone'],
                    $row['model_id'] ? $machineModel[$row['model_id']] : '未选择机型',
                    $row['receive_machine_date'],
                    $row['repair_source'],
                    $row['description'],
                    $row['repair_type'] === 1 ? '保内' : '保外',
                    $row['repair_reason_type'] === 1 ? '人为原因' : '质量原因',
                    $row['repair_deal'],
                    $row['express_date'],
                    $row['express_number'],
                    $row['remark'],
                    //                    $row['repair_cost,
                    //                    $malfunctions,
                    //                    $accessories,
                    $row['repair_cost'],
                    $row['is_checked'] ? '已出单' : '未出单',
                ];
            }

        }
        array_unshift($formatData, $titles);
        $output = '';
        foreach ($formatData as $value) {
            $output .= implode(',', $value) . "\t\n";
        }

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        //转换成gbk,要不然excel显示会乱码
        $output = mb_convert_encoding($output, 'gbk', 'utf-8');
        // 导出文件，
        response(rtrim($output, "\n"), 200, $headers)->send();
        exit();
    }
}