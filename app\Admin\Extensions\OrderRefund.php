<?php


namespace App\Admin\Extensions;
use GuzzleHttp\Client;

class OrderRefund
{
    const host = 'https://api-repair-hub.readboy.com';
//    const host = 'http://localhost:8010';
    const app_id = 'com.readboy.rbmanager';
    const app_key = 'c1931fd0714859df1411e42685781902';


    public function refund($order_sn, $refund_fee, $com){
        $client = new Client();
        if ($com == 1){
            $url = self::host .'/repair/refund';
        }else{
            $url = self::host .'/repair/alirefund';
        }
        $device_item = ['1', '', '', self::app_id, '', ''];
        $device_id = join('/', $device_item);
        $t = time();
        $sn = md5($device_id . self::app_key . $t);
        $param = [
            'form_params' => [
                't'=> $t,
                'ua' => $device_id,
                'sn' => $sn,
                'order_sn' => $order_sn,
                'refund_fee' => $refund_fee,
            ]
        ];
        $res = $client->request('post', $url, $param);
        if ($res->getStatusCode() != '200'){
            return false;
        }
        $body = $res->getBody();
        $content = json_decode($body->getContents());
        return $content;

    }
    public function refund_query($order_sn, $out_trade_no, $com){
        $client = new Client();
        if ($com == 1){
            $url = self::host .'/repair/wx_refund_query';
        }else{
            $url = self::host .'/repair/ali_refund_query';
        }
        $device_item = ['1', '', '', self::app_id, '', ''];
        $device_id = join('/', $device_item);
        $t = time();
        $sn = md5($device_id . self::app_key . $t);
        $param = [
            'form_params' => [
                't'=> $t,
                'ua' => $device_id,
                'sn' => $sn,
                'order_sn' => $order_sn,
                'out_trade_no' => $out_trade_no,
            ]
        ];
        $res = $client->request('get', $url, $param);
        if ($res->getStatusCode() != '200'){
            return false;
        }
        $body = $res->getBody();
        $content = json_decode($body->getContents());
        return $content;

    }
}