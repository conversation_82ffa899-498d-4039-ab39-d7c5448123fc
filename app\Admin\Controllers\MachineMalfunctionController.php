<?php

namespace App\Admin\Controllers;

use App\Models\MachineMalfunction;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Tree;
use Encore\Admin\Widgets\Box;
use Encore\Admin\Layout\Row;
use Encore\Admin\Layout\Column;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\DB;

class MachineMalfunctionController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {
            $content->header('配件症状列表');
            $content->description('');
            //            $content->body($this->treeView());

            //
            $content->row(function (Row $row) {
                $row->column(6, $this->treeView()->render());

                $row->column(6, function (Column $column) {
                    $form = new \Encore\Admin\Widgets\Form();
                    $form->action(admin_url('machine_malfunction'));
                    $form->select('parent_id', '父级')->options(MachineMalfunction::selectOptions());
                    $form->text('title', '名称')->rules('required');
                    $form->textarea('description', '具体描述');
                    $column->append((new Box('新增配件症状', $form))->style('success'));
                });
            });
        });
    }

    /**
     * @return \Encore\Admin\Tree
     */
    protected function treeView()
    {
        return MachineMalfunction::tree(function (Tree $tree) {
            $tree->disableCreate();

            $tree->branch(function ($branch) {
                $payload = "<strong>{$branch['title']}</strong>";

                return $payload;
            });

            $script = <<<SCRIPT
            var data = $('.dd-item');
            data.each(function(){
            var dataId = $(this).attr('data-id');
                $("a[data-id='"+dataId+"']").remove();
            });
SCRIPT;
//            Admin::script($script);
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('配件症状');
            $content->description('');

            $content->body($this->form()->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('故障');
            $content->description('故障');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(MachineMalfunction::class, function (Grid $grid) {

            $grid->id('ID')->sortable();

            $grid->created_at();
            $grid->updated_at();
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Admin::form(MachineMalfunction::class, function (Form $form) {

            $form->select('parent_id', '父级')->options(MachineMalfunction::selectOptions());
            $form->text('title', trans('配件症状名称'))->rules('required');
            $form->textarea('description', '具体描述');
            $form->display('created_at', 'Created At');
            $form->display('updated_at', 'Updated At');
        });
    }

    public function getMachineMalfunctionByParentId()
    {
        $parentId = Input::get('q');

        //为什么要改成text,因为前端组件的js操作的是text字段
        return MachineMalfunction::where('parent_id', '=', $parentId) ->get([DB::raw('id as id'), DB::raw('title as text')]);

    }
}
