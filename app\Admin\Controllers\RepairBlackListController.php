<?php

namespace App\Admin\Controllers;

use App\Models\RepairBlackList;
use App\Models\RepairBlackListRemark;
use App\Models\Order;
use App\Models\OrderLog;

use App\Http\Controllers\Controller;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\MessageBag;
use Illuminate\Support\Facades\Log;

use Illuminate\Support\Facades\DB;

/**
 * 寄修黑名单
 * Create By Su Guanghua 2021/12/01 14:45
 */
class RepairBlackListController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修黑名单');
            $content->description('黑名单列表');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修黑名单');
            $content->description('编辑');
            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修黑名单');
            $content->description('添加');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOF

        // 黑名单备注
        $('.black_list_remark').click(function(e){
            id = e.currentTarget.dataset.id
            var html = '<div class="box box-info">'+
                            '<div class="box-header with-border">黑名单备注<\/div>'+
                            '<div class="box-body" style="min-height:300px;min-width:500px" >'+
                                '<table class="table">'+
                                    '<tr>'+
                                        '<th style="text-align: center;" >时间<\/th>'+
                                        '<th style="text-align: center;" >订单状态<\/th>'+
                                        '<th style="text-align: center;" >操作人<\/th>'+
                                        '<th style="text-align: center;" >备注<\/th>'+
                                    '</tr>'+
                                    '<tbody id="tbody"><\/tbody>'+
                                '<\/table>'+
                            '<\/div>'+
                        '<\/div>'+
                        '<div style="display:flex">'+
                            '<input id="remarkInput" placeholder="请输入备注,不超过100字" size="70">'+
                            '<a class="btn btn-sm btn-primary" type="button" id="saveRemark" >确定<\/a>'+
                        '<\/div>';


            // 获取备注内容
            $.ajax({
                url: '/admin/repair_black_list/getRemarkInfo',
                data: {
                    id: id,
                },
                type: 'get',
                dataType: 'json',
                success: function(res){
                    var tbodyHtml = '';
                    $('#tbody').html('');
                    $.each(res , function(i , v){
                        tbodyHtml +='<tr>'+
                                        '<td style="text-align: center;">'+v.created_at+'</td>'+
                                        '<td style="text-align: center;">'+v.status_name+'</td>'+
                                        '<td style="text-align: center;">'+v.name+'</td>'+
                                        '<td style="text-align: center;">'+v.remark+'</td>'+
                                    '</tr>';
                    })

                    $('#tbody').append(tbodyHtml);
                }
            })
            $.fancybox.open(html);

            $('#saveRemark').click(function(){
                remark = $('#remarkInput').val()
                if(!remark){
                    layer.msg('备注不能为空');
                    return false;
                }
                $.ajax({
                    url: '/admin/repair_black_list/setRemarkInfo',
                    data: {
                        id: id,
                        remark: remark
                    },
                    type: 'get',
                    dataType: 'json',
                    success: function(res){
                        layer.msg(res.info);
                        if(res.status == 1){
                            var newTbodyHtml = '<tr>'+
                                                    '<td style="text-align: center;color:#29db6f">'+res.data.created_at+'<\/td>'+
                                                    '<td style="text-align: center;color:#29db6f">'+res.data.status_name+'<\/td>'+
                                                    '<td style="text-align: center;color:#29db6f">'+res.data.name+'<\/td>'+
                                                    '<td style="text-align: center;color:#29db6f">'+res.data.remark+'<\/td>'+
                                                '<\/tr>';

                            $('#tbody').append(newTbodyHtml);
                            $('.grid-refresh').click();
                        }
                    }
                })
            })
        })
        
        // 查看日志
        $('.check_log').click(function(e){
            id = e.currentTarget.dataset.id
            var html = '<div class="box box-info">'+
                            '<div class="box-header with-border">日志<\/div>'+
                            '<div class="box-body" style="min-height:300px;min-width:500px" >'+
                                '<table class="table">'+
                                    '<tr>'+
                                        '<th style="text-align: center;" >时间<\/th>'+
                                        '<th style="text-align: center;" >订单状态<\/th>'+
                                        '<th style="text-align: center;" >日志状态<\/th>'+
                                        '<th style="text-align: center;" >操作人<\/th>'+
                                        '<th style="text-align: center;" >来源<\/th>'+
                                    '</tr>'+
                                    '<tbody id="tbody"><\/tbody>'+
                                '<\/table>'+
                            '<\/div>'+
                        '<\/div>';

            // 获取日志内容
            $.ajax({
                url: '/admin/repair_black_list/getLog',
                data: {
                    id: id,
                },
                type: 'get',
                dataType: 'json',
                success: function(res){
                    var tbodyHtml = '';
                    $('#tbody').html('');
                    $.each(res , function(i , v){
                        tbodyHtml +='<tr>'+
                                        '<td style="text-align: center;">'+v.date+'</td>'+
                                        '<td style="text-align: center;">'+v.pr_status_name+'</td>'+
                                        '<td style="text-align: center;">'+v.log_status_name+'</td>'+
                                        '<td style="text-align: center;">'+v.name+'</td>'+
                                        '<td style="text-align: center;">'+v.log_from+'</td>'+
                                    '</tr>';
                    })

                    $('#tbody').append(tbodyHtml);
                },error:function(res){
                    console.log(res)
                }
            })
            $.fancybox.open(html);
        })
EOF;
        Admin::script($script);
        return Admin::grid(RepairBlackList::class, function (Grid $grid) {

            $grid->disableExport();
            $isAdmin = Admin::user()->inroles([ 'Administrator','black_list_manager']);

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('barcode', '条码');
                $filter->like('model', '机型');
                $filter->like('phone', '联系人手机');

            });

            $grid->tools(function ($tools) use ($isAdmin) {

               // 批量操作按钮
                $tools->batch(function ($batch) {
                });
                if ($isAdmin) {
                    $tools->append(new ImportBtnData());
                }
            });

            $grid->model()->orderBy('created_at', 'desc');

            $grid->barcode('条码');
            $grid->column('model' ,'产品型号');
            $grid->phone('联系方式');
            $grid->name('联系人');
            $grid->type('禁用类型')->display(function ($type) {
                return $type ? '手机' : '条码';
            });;
            $grid->created_at('添加时间');
            $grid->remark('备注')->display(function ($value){
                $remark = DB::table('repair_black_list_remark')->where('black_list_id' , $this->id)->orderBy('created_at', 'desc')->value('remark');
                $remark = $remark ? '<u>'.$remark.'</u>' : '<u style="color:#dd1144">Empty</u>';
                return '<a href="JavaScript:void(0);" data-id="'.$this->id.'" class="black_list_remark">'.$remark.'</a>';
            });

            $grid->actions(function ($actions) {
                $html = '<a href="JavaScript:void(0);" data-id="'.$actions->getKey().'" class="check_log">日志</a>';
                $actions->append($html);
                
                $actions->disableEdit();
            });

        });
    }

    /**
     * Make a form builder.
     * @param $id
     * @return Form
     */
    protected function form($id = null)
    {
        $js = <<<EOF
          $("form").submit(function(e){
            $('.form-control').attr('disabled',false);
          });

          const \$type = $('.type'); 
          \$type.off('change').on('change', function(e) { 
           
            if (\$type.val() =='1') {
                \$('.barcode').closest('.form-group').hide();
                \$('.model').closest('.form-group').hide();
            }else{
                \$('.barcode').closest('.form-group').show();
                \$('.model').closest('.form-group').show();
            }
   
          }).trigger('change')
EOF;

        Admin::script($js);

        return Admin::form(RepairBlackList::class, function (Form $form) use ($id) {

            $form->select('type', '禁用类型')->help('必填')->options(['0'=>'条码','1'=>'手机'])->default('0');
            $form->text('barcode', '条码')->help('必填')->rules('max:50');
            $form->text('model', '产品型号')->help('必填')->rules('max:50');
            $form->text('phone', '联系方式')->help('必填')->rules('max:50|required');
            $form->text('name', '联系人');
            $form->text('remark', '备注说明');

            $form->ignore(['remark']);

            $form->saved(function($form) {
                // 获取条码关联的最新订单信息
                $lastOrderInfo = Order::where('barcode' , $form->barcode)->orderBy('id' , 'desc')->first();

                $remarkData = array(
                    'black_list_id' => $form->model()->id,
                    'order_status' => !empty($lastOrderInfo->status) ? $lastOrderInfo->status : '',
                    'remark' => \request('remark'),
                    'uid' => Admin::user()->id,
                    'created_at' => date('Y-m-d H:i:s')
                );

                DB::table('repair_black_list_remark')->insert($remarkData);

            });
        });
    }

    /**
     * 保存备注信息
     */
    public function setRemarkInfo(){
        $id = request()->input('id');
        $remark = request()->input('remark');

        $blackListInfo = RepairBlackList::where('id' , $id)->first();
        $orderInfo = Order::where('barcode' , $blackListInfo->barcode)->orderBy('id' , 'desc')->first();

        $data = array(
            'black_list_id' => $id,
            'uid' => Admin::user()->id,
            'order_status' => !empty($orderInfo['status']) ? $orderInfo['status'] : '',
            'created_at' => date('Y-m-d H:i:s'),
            'remark' => $remark
        );

        if(DB::table('repair_black_list_remark')->insert($data)){

            $data['status_name'] = !empty(Order::STATUS[$data['order_status']]) ? Order::STATUS[$data['order_status']] : '<span style="color:#ff1c1f">无订单</span>';
            $data['name'] =  Admin::user()->name;

            return array('status' => 1 , 'info' => '添加成功' , 'data' => $data);
        } else {
            return array('status' => 0 , 'info' => '添加失败');
        }
    }

    /**
     * 获取备注信息
     */
    public function getRemarkInfo(){
        $id = request()->input('id');

        $remarkInfo = RepairBlackListRemark::join('admin_users as au' , 'repair_black_list_remark.uid' , 'au.id')
        ->where('repair_black_list_remark.black_list_id' , $id)
        ->orderBy('repair_black_list_remark.log_id' , 'desc')
        ->select('au.name','repair_black_list_remark.*')
        ->get();

        foreach($remarkInfo as $key => $value){
            $remarkInfo[$key]['status_name'] = !empty(Order::STATUS[$value['order_status']]) ? Order::STATUS[$value['order_status']] : '<span style="color:#ff1c1f">无订单</span>';
        }

        return $remarkInfo;
    }

    /**
     * 获取日志信息
     */
    public function getLog(){
        $id = request()->input('id');

        // 获取所有与条码相关的订单号
        $barcode = RepairBlackList::where('id' , $id)->value('barcode');
        $sns = Order::where('barcode', $barcode)->pluck('sn')->toArray();
        
        $logList = OrderLog::join('admin_users as au' , 'order_log.uid' , 'au.id')
            ->whereIn('order_log.pr_sn' , $sns)
            ->orderBy('order_log.date' , 'desc')
            ->select('au.name','order_log.*')
            ->get();
        foreach($logList as $key => $value){
            $logList[$key]['pr_status_name'] = Order::STATUS[$value['pr_status']];
            $logList[$key]['log_status_name'] = Order::STATUS[$value['log_status']];
        }

        return $logList;
    }

    public function import_data_view()
    {
        if (!Admin::user()->inroles(['Administrator','black_list_manager'])) {
            return $this->returnImportError('无权操作');
        }
        $form = Admin::form(RepairBlackList::class, function (Form $form) {
            $form->tools(function (Form\Tools $tools) {
                $tools->disableBackButton();
                $tools->add('<a style="margin-right: 10px;" href="/storage/黑名单模板.xlsx" target="_blank" class="btn btn-sm btn-twitter"><i class="fa fa-download"></i>&nbsp;&nbsp;模板下载</a>');
            });

            $form->setAction('/admin/repair_black_list/import');
            $form->file('file', 'Excel文件：')
                ->rules('required')
                ->options([
                    'maxFileSize' => 2048,
                    'msgSizeTooLarge' => '文件 "{name}" (<b>{size} KB</b>) 超过了最大允许上传大小 <b>{maxSize} KB</b>.',
                    'allowedFileExtensions' => ['xls', 'xlsx'],
                ]);
        });

        return Admin::content(function (Content $content) use ($form) {
            $content->header('黑名单');
            $content->description('黑名单导入');
            $content->body($form);
        });
    }

    public function import_data_store(Request $request)
    {
        // 用户相关数组
        $this->validate($request, [
            'file' => 'required|file|max:2048'
        ]);
        if (!Admin::user()->inroles(['Administrator','black_list_manager'])) {
            return $this->returnImportError('无权操作');
        }


        $file = $request->file('file')->path();

        try {
            $reader = \PHPExcel_IOFactory::createReaderForFile($file);
            $reader->setReadDataOnly(true);
            $spreadSheet = $reader->load($file);
            $data = $spreadSheet->getSheet(0)->toArray();

            if (empty($data)) {
                return $this->returnImportError('没有数据');
            }

            $header = $data[0];
            $validHeader = [
                '物料编码',        //0
                '物料名称',        //1
                '规格型号',        //2
                '条码',         //3
                '备注',         //3
            ];

            $columns = count($validHeader);
            if (count($header) < $columns) {
                throw new \Exception('数据至少应有' . $columns . '列');
            }

            foreach ($validHeader as $i => $valid) {
                if (trim($header[$i]) != $valid) {
                    throw new \Exception(chr($i + 65) . '1应为“' . $valid . '”');
                }
            }

            $rules = [
                2 => 'required',
                3 => 'required',
            ];

            $messages = [
                2 => ['required' => ':attribute机型不能为空'],
                3 => ['required' => ':attribute序列号不能为空'],
            ];

            $validData = [];
            $remarkData = [];
            $data = array_slice($data, 1);
            $count = count($data);
            $errMessages = [];
            for ($row = 0; $row < $count; $row++) {
                $item = array_map('trim', $data[$row]);

                if (!$item[1]) {
                    continue;
                }

                // 所有单元格都为空的话忽略
                if (empty(array_filter($item, function ($v) {
                    return !empty($v);
                }))) {
                    continue;
                }

                if (count($item) < $columns) {
                    throw new \Exception('第' . ($row + 2) . '行数据不足' . $columns . '列');
                }

                for ($col = 0; $col < $columns; $col++) {
                    if (isset($rules[$col])) {
                        $key = chr($col + 65) . ($row + 2);
                        $validator = Validator::make([$key => $item[$col]], [$key => $rules[$col]], $messages[$col]);
                        if ($validator->fails()) {
                            $errMessages[] = $validator->errors()->first();
                        }
                    }
                }

                $validData[] = [
                    'barcode' => trim($item[3]),
                    'model' => $item[2],
                    'created_at' => date('Y-m-d H:i:s'),
                ];
                $remarkData = [
                    'remark' => trim($item[4]),
                    'uid' => Admin::user()->id,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $barcodes[] = trim($item[3]);
            }


            if ($errMessages) {
                throw new \Exception(implode('<br/>', $errMessages));
            }

            $batchSize = 500;
            DB::transaction(function () use ($validData, $batchSize,$remarkData,$barcodes) {
                foreach (array_chunk($validData, $batchSize) as $batch) {
                    RepairBlackList::insert($batch);
                }
                //重新查id
                $ids = RepairBlackList::whereIn('barcode', $barcodes)->pluck('id')->toArray();
                $remarkDataArray = [];
                foreach ($ids as $list_id) {
                    $remarkStr = $remarkData['remark'];
                    if ($remarkStr == "") {
                        $remarkStr = "后台批量导入";
                    }
                    $temp = $remarkData;
                    $temp['remark'] = $remarkStr;
                    $temp['black_list_id'] = $list_id;
                    $remarkDataArray[] = $temp;
                }
                foreach (array_chunk($remarkDataArray, $batchSize) as $batch) {
                    RepairBlackListRemark::insert($batch);
                }
            });


        } catch (\Exception $ex) {
            return $this->returnImportError($ex->getMessage());
        }

        admin_toastr('导入成功');
        return redirect('/admin/repair_black_list');
    }

    private function returnImportError($message)
    {
        $error = new MessageBag([
            'title' => '导入出错',
            'message' => $message
        ]);
        return back()->with(compact('error'))->withInput();
    }
    
}

class ImportBtnData extends Grid\Tools\AbstractTool
{
    public function render()
    {
        return <<< EOT
    <a href="/admin/repair_black_list/import" class="btn btn-sm btn-primary grid-refresh"><i class="fa fa-upload"></i>批量导入</a>
EOT;
    }
}