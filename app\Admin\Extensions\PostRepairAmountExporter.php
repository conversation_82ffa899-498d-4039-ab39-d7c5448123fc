<?php


namespace App\Admin\Extensions;


use App\Models\Order;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

class PostRepairAmountExporter extends AbstractExporter
{
    public function export()
    {
        // TODO: Implement export() method.
        $start = date('Y-m-d 00:00:00', strtotime(date('Y-m-01 00:00:00'). '-1 month'));
        $end = date('Y-m-01 00:00:00');

        $updated_at_last = Request::get('updated_at_last');
        if ($updated_at_last){
            $start = $updated_at_last['start'];
            $end = $updated_at_last['end'];
        }
        $start_str = date('Y-m-d', strtotime($start));
        $end_str = date('Y-m-d', strtotime($end));
        $filename = '寄修费用报表'.$start_str.'-'.$end_str;
        $title = [
            '寄修类型',
            '数量（台）',
            '金额（元）',
            '微信支付',
            '支付宝支付'
        ];

        $formatData = $this->formatData($start, $end);
        array_unshift($formatData, $title);
        ExcelExportTrait::exportToExcel($filename, $formatData);
    }

    private function formatData($start, $end){
        $datas = Order::where('type', '<>', 2)
            ->whereIn('status', [800, 900])
            ->whereBetween('updated_at_last', [$start, $end])
            ->select(DB::raw('SUM(type != 2 AND pay_amount = 0) in_period, 
             SUM(type != 2 AND pay_amount > 0) no_in_period, 
             SUM(IF((type != 2 AND pay_amount = 0),pay_amount,0)) total_amount1, 
             SUM(IF((type != 2 AND pay_amount > 0),pay_amount,0)) total_amount2, 
             SUM(IF((type != 2 AND pay_amount > 0 AND pay_com = 1),pay_amount,0)) wechat_total_amount, 
             SUM(IF((type != 2 AND pay_amount > 0 AND pay_com = 2),pay_amount,0)) ali_total_amount, 
             SUM(IF(type != 2 AND pay_amount > 0,staff_cast,0)) staff_cast'))
            ->get()->first();
        $formatData = [];
        if ($datas){
            $formatData[] = ['保内维修', $datas->in_period, 0.00, 0.00, 0.00];
            $formatData[] = ['保外维修', $datas->no_in_period, $datas->total_amount2,
                $datas->wechat_total_amount,$datas->ali_total_amount,];
            $formatData[] = ['合计收款', $datas->in_period+$datas->no_in_period, $datas->total_amount2,
                $datas->wechat_total_amount,$datas->ali_total_amount,];
            $formatData[] = ['用户支付快递费', '', $datas->staff_cast, '', ''];
        }else{
            $formatData[] = ['保内维修', '', '', '', ''];
            $formatData[] = ['保外维修', '', '','',''];
            $formatData[] = ['合计收款', '', '','',''];
            $formatData[] = ['用户支付快递费', '', '', '', ''];
        }

        return $formatData;
    }
}
