<?php

/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 2019/7/8
 * Time: 20:12
 */

namespace App\Models;

use Encore\Admin\Facades\Admin;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\User;


class Order extends Model
{
    protected $table = "order";

    // public $fillable = ['status'];

    const STATUS = [
        100 => '待审核',
        200 => '审核通过',
        -200 => '审核不通过',
        300 => '用户已发货',
        -300 => '用户发货失败',
        400 => '已收货,未知会',
        410 => '已收货,已知会', // 等同于:未检测,未知会
        480 => '未检测,已知会',
        490 => '已检测,未知会',
        500 => '已检测,已知会',
        600 => '用户已支付',
        700 => '已维修',
        -700 => '已弃修',
        800 => '已回寄',
        -800 => '回寄失败',
        900 => '订单完成',
        -900 => '已取消'
    ];

    const title = [
        200 => '审核通过，等待用户发货',
        -200 => '审核不通过',
        300 => '寄修产品发货成功，维修中心等待接收中',
        400 => '维修终端已收货，等待客服知会',
        410 => '客服知会完成，等待检测', // 等同于:未检测,未知会客户
        480 => '维修知会完成，等待检测',
        490 => '已检测，等待维修知会',
        500 => '已检测，等待用户支付',
        600 => '维修费用已支付，正在维修中',
        700 => '已维修，等待回寄',
        -700 => '已弃修，等待回寄',
        800 => '回寄已发货，等待用户签收',
        900 => '产品已被签收，服务已完成',
        -900 => '寄修服务已取消'
    ];

    const in_period_null = 0;
    const in_period_in = 1;
    const in_period_out = 2;
    const in_period = [
        self::in_period_null => '无保修信息',
        self::in_period_in => '保修期内',
        self::in_period_out => '保修期外'
    ];

    const charge_type = [1 => '客户价格', 2 => '一代价格', 3 => '特殊政策（免费）'];
    const reason = [1 => '人为损坏', 2 => '元件损坏', 3 => '全面检测，客户反应故障未复现', 4 => '重新对机器进行系统升级', 5 => '屏幕脱胶'];
    const come_exp_type = [0 => '未选择邮寄方式', 1 => '上门服务', 2 => '自主寄件'];
    const has_warranty = [1 => '有保卡', 0 => '没有保卡'];
    const repair_status = [0 => '未维修', 1 => '维修完成', 2 => '用户弃修'];
    const is_agency = [0 => '否', 1 => '是'];
    const is_charge = [0 => '不收费', 1 => '收费', 2 => '仅弃修时收费'];
    const has_screen_insurance = [1 => '有碎屏保', 0 => '没有碎屏保'];
    const post_repair_type = [1 => '用户寄修', 2 => '代理商寄修', 3 => '终端代寄'];
    const quality = [1 => '通过', 2 => '打回', 0 => '未品检'];
    const used_screen_insurance = [0 => '否', 1 => '是'];
    const is_direct_sales = [0 => '否', 1 => '是'];
    const is_send_back = [0 => '否', 1 => '是'];
    const repeat_repair = [0 => "首次寄修", 1 => "二次寄修", 2 => "二次返修"];
    const deal_remark = ["屏幕返回" => "屏幕返回", "主板返回" => "主板返回", "主板、屏幕返回" => "主板、屏幕返回",
        "经检测未发现异常，老化压力检测48小时机器正常，建议暂先使用观察" => "经检测未发现异常，老化压力检测48小时机器正常，建议暂先使用观察"];
    const is_exchange_for_repair = [0 => '否', 1 => '以换代修'];
    const warehouse_type = [0 => '请选择', 1 => '保修成品仓', 2 => '维修售后仓'];

    // 签收状态
    const SIGN_IN_STATUS = [
        0 => '--',
        1 => '正常',
        2 => '外观有瑕疵轻微',
        3 => '外观有瑕疵严重',
        4 => '外观有损坏，与描述符合',
        5 => '外观有损坏，与描述不符合',
        6 => '条码不一致已核实',
    ];

    const WAIT_AUDIT = 100;
    const AUDIT_PASS = 200;
    const AUDIT_NO_PASS = -200;
    const EXP_COME_SUCCESS = 300;
    const EXP_COME_FAIL = -300;
    const COME_SURE = 400;
    const COME_SURE_IS_TELL = 410; // 已收货，已知会
    const NO_CHECK_IS_TELL = 480; // 未检测,已知会
    const CHECK_FINISH = 490; // 已检测,未知会
    const CHECK_FINISH_IS_TELL = 500; // 已检测,已知会
    const PAY_FINISH = 600;
    const REPAIR_FINISH = 700;
    const REPAIR_REFUSE = -700;
    const EXP_GO_SUCCESS = 800;
    const EXP_GO_FAIL = -800;
    const ORDER_FINISH = 900;
    const ORDER_CANCEL = -900;

    const CONNECT = [0 => '未联系', 1 => '联系成功', 2 => '联系失败', 3 => '已弃修', 4 => '专柜与用户确认中', 5 => '用户考虑中', 6 => '已弃修，联系成功'];
    const REPAIR_CHECK_STATUS = [0 => '---', 1 => '配件不足', 2 => '老化检测中', 3 => '正在等待检测'];
    const REPAIR_CHECK_STATUS2 = [0 => '---', 1 => '配件不足', 2 => '老化检测中', 3 => '正在等待检测', 4 => '已检测'];

    const ORDER_MARK_NORMAL = 0;
    const ORDER_MARK_WAITING_NOTICE = 1;
    const ORDER_MARK_MISSING_ACCESSORY = 2;
    const ORDER_MARK_UNSTABLE_FAULT = 3;
    const ORDER_MARK_INFORM_ABNORMAL = 4;
    const ORDER_MARK_PAY_ABNORMAL = 5;
    const ORDER_MARK_AUDIT_ABNORMAL = 6;
    const ORDER_MARK = [
        self::ORDER_MARK_NORMAL => '正常',
        self::ORDER_MARK_WAITING_NOTICE => '等待通知',
        self::ORDER_MARK_MISSING_ACCESSORY => '缺少配件',
        self::ORDER_MARK_UNSTABLE_FAULT => '故障不稳定',
        self::ORDER_MARK_INFORM_ABNORMAL => '知会异常',
        self::ORDER_MARK_PAY_ABNORMAL => '支付异常',
        self::ORDER_MARK_AUDIT_ABNORMAL => '审核异常',
    ];

    const OPTIONAL_ACCESSORY_STATUS_NORMAL = 1;
    const OPTIONAL_ACCESSORY_STATUS_CANCEL_PARTLY = -1;
    const OPTIONAL_ACCESSORY_STATUS_CANCEL_TOTAL = -2;
    const OPTIONAL_ACCESSORY_STATUS = [
        self::OPTIONAL_ACCESSORY_STATUS_NORMAL => '正常',
        self::OPTIONAL_ACCESSORY_STATUS_CANCEL_PARTLY => '部分取消',
        self::OPTIONAL_ACCESSORY_STATUS_CANCEL_TOTAL => '已取消',
    ];

    const ORDER_PRIORITY_NORMAL = 0;
    const ORDER_PRIORITY_URGENT = 1;
    const ORDER_PRIORITY = [
        self::ORDER_PRIORITY_NORMAL => '正常',
        self::ORDER_PRIORITY_URGENT => '加急',
    ];
    const ORDER_PRIORITY_COLOR = [
        self::ORDER_PRIORITY_NORMAL => '',
        self::ORDER_PRIORITY_URGENT => 'red',
    ];

    public function machine_type()
    {
        return $this->hasOne(Machine::class, 'model_id', 'model_id');
    }

    public function check_user()
    {
        return $this->hasOne(User::class, 'id', 'check_man');
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'uid');
    }

    public function repair_user()
    {
        return $this->hasOne(User::class, 'id', 'repair_man');
    }

    public function auditor_user()
    {
        return $this->hasOne(User::class, 'id', 'auditor');
    }

    public function endpoint()
    {
        return $this->hasOne(PostRepairEndpoint::class, "id", "repair_endpoint");
    }

    public function repair_malfunction()
    {
        return $this->hasMany(PostRepairMalfunction::class, 'pr_sn', 'sn');
    }

    public function repair_accessory()
    {
        return $this->hasMany(PostRepairAccessory::class, 'pr_sn', 'sn');
    }

    public function repair_material()
    {
        return $this->hasMany(PostRepairMaterial::class, 'pr_sn', 'sn');
    }

    public function order_log()
    {
        return $this->hasMany(OrderLog::class, 'pr_sn', 'sn');
    }

    public function used_material()
    {
        return $this->hasMany(PostRepairUsedMaterial::class, 'pr_sn', 'sn');
    }

    public function expense()
    {
        return $this->hasOne(PostRepairExpense::class, 'pr_sn', 'sn');
    }

    public function agent_sn()
    {
        return $this->hasOne(AgentOrderCorrelation::class, 'order_sn', 'sn');
    }

    public function order_extend()
    {
        return $this->hasOne(OrderExtend::class, 'sn', 'sn');
    }


    public function damages()
    {
        return $this->belongsToMany(Damage::class);
    }

    public function setPeriodFileAttribute($value)
    {
        if (is_array($value)) {
            $this->attributes['period_file'] = json_encode($value, JSON_UNESCAPED_UNICODE);
        }
    }

    public function getPeriodFileAttribute($value)
    {
        return json_decode($value, true);
    }

    public function setUploadFileAttribute($value)
    {
        if (is_array($value)) {
            $this->attributes['upload_file'] = json_encode($value, JSON_UNESCAPED_UNICODE);
        }
    }

    public function getUploadFileAttribute($value)
    {
        return json_decode($value, true);
    }

    public function setRepairImageAttribute($repair_image)
    {
        if (is_array($repair_image)) {
            $this->attributes['repair_image'] = json_encode($repair_image, JSON_UNESCAPED_UNICODE);
        }
    }

    public function getRepairImageAttribute($repair_image)
    {
        return json_decode($repair_image, true);
    }

    public function setVideoFileAttribute($value)
    {
        if (is_array($value)) {
            $this->attributes['video_file'] = json_encode($value, JSON_UNESCAPED_UNICODE);
        }
    }

    public function getVideoFileAttribute($value)
    {
        return json_decode($value, true);
    }


    public function getRepeatRemarkAttribute($value)
    {
        return explode(',', $value);
    }

    public function setRepeatRemarkAttribute($value)
    {
        $this->attributes['repeat_remark'] = implode(',', $value);
    }


    public static function boot()
    {
        parent::boot();
        static::saving(function ($model) {
            // $model->used_material = $model->repair_material;
        });

        static::updated(function ($model) {

            // $result = OrderLog::where(['pr_sn'=>$model->sn,'pr_status'=>$model->status])->first();
            // if (!$result){
            //     $log['pr_sn'] = $model->sn;
            //     $log['pr_status'] = $model->status;
            //     $log['log_from'] = 'web';
            //     $log['uid'] = $model->uid;
            //     $log['date'] = date('Y-m-d H:i:s', time());
            //     OrderLog::insert($log);
            // }

            if (!$model->no_log) {
                // $log['pr_sn'] = $model->sn;
                $log['pr_status'] = $model->status;
                $log['log_from'] = 'web';
                if (!empty($model->log_status)) {
                    $log['log_status'] = $model->log_status;
                } else {
                    $log['log_status'] = $model->status;
                }
                $log['uid'] = $model->uid;
                $log['title'] = self::title[$model->status];
                $log['admin'] = Admin::user()->id;
                if ($model->status == 490) {
                    $log['admin'] = $model->check_man;
                }
                if ($model->status == 700) {
                    $log['admin'] = $model->repair_man;
                }
                $log['date'] = date('Y-m-d H:i:s', time());
                // 判断是否已有已支付的记录
                $pay_exist = false;
                if ($log['log_status'] == self::PAY_FINISH) {
                    $log_pay = OrderLog::where([['pr_sn', '=', $model->sn], ['pr_status', '=', self::PAY_FINISH]])->first();
                    if ($log_pay && $log_pay->id > 0) {
                        $pay_exist = true;
                    }
                }
                if (!$pay_exist) {
                    OrderLog::updateOrInsert(array('pr_sn' => $model->sn, 'log_status' => $log['log_status']), $log);
                }
            }
        });

        static::updated(function ($model) {
            if (!$model->no_log) {
                $log['pr_sn'] = $model->sn;
                $log['pr_status'] = $model->status;
                $log['log_from'] = 'web';
                if (!empty($model->log_status)) {
                    $log['log_status'] = $model->log_status;
                } else {
                    $log['log_status'] = $model->status;
                }
                $log['uid'] = $model->uid;
                $log['title'] = self::title[$model->status];
                $log['admin'] = Admin::user()->id;
                if ($model->status == 490) {
                    $log['admin'] = $model->check_man;
                }
                if ($model->status == 700) {
                    $log['admin'] = $model->repair_man;
                }
                $log['date'] = date('Y-m-d H:i:s', time());
                OrderLog2::Insert($log);
            }
        });
    }

    public static function get_reason($reason): string
    {
        return array_key_exists($reason, self::reason) ? self::reason[$reason] : "未知({$reason})";
    }

    public static function get_buy_date($barcode)
    {
        $data = DB::connection('mysql2')
            ->table('warranty')
            ->where([['barcode', '=', $barcode], ['status', '=', '1']])
            ->select('buy_date')->first();
        if (empty($data)) {
            return null;
        }
        return $data->buy_date;
    }

    public static function get_discount_value($buy_date, $now)
    {
        if (empty($buy_date)) {
            return 1;
        }
        $time1 = date('Y-m-d', strtotime(' + 13 months', strtotime($buy_date)));
        $time2 = date('Y-m-d', strtotime(' + 14 months', strtotime($buy_date)));
        $time = date('Y-m-d', strtotime(' + 12 months', strtotime($buy_date)));
        $now = $now ?? date('Y-m-d');
        if ($now > $time2) {
            return 1;  // 过保两个月以上
        } elseif ($now < $time) {
            return 0.8;  // 在保修期内8折
        } elseif ($now < $time1 && $now > $time) {
            return 0.8;     // 一个月内8折
        } elseif ($now < $time2 && $now > $time1) {
            return 0.85;     // 两个月内85折
        } else {
            return 1;
        }
    }

    public static function express_default()
    {
        return 18;
    }

    public static function order_priority_column($grid)
    {
        $grid->column('order_extend.order_priority', '优先级')->display(function ($value) {
            return self::order_priority_span($value);
        });
    }

    public static function order_priority_span($value): string
    {
        $text = '未知';
        if (array_key_exists($value, Order::ORDER_PRIORITY)) {
            $text = Order::ORDER_PRIORITY[$value];
        }
        $style = '';
        if (array_key_exists($value, Order::ORDER_PRIORITY_COLOR)) {
            $color = Order::ORDER_PRIORITY_COLOR[$value] ? 'color:' . Order::ORDER_PRIORITY_COLOR[$value] . ';' : '';
            $style = $color ? ('style="' . $color . '"') : '';
        }
        return "<span $style>$text</span>";
    }

    public static function order_priority_form($form)
    {
        $form->select('order_extend.order_priority', '订单优先级')->options(self::ORDER_PRIORITY);
    }
}
