<?php

namespace App\Admin\Controllers;

use App\Models\Machine;
use App\Models\MachineAccessoryTree;

use App\Models\MachineMalfunction;
use App\Models\Material;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Tree;
use Encore\Admin\Widgets\Box;
use Encore\Admin\Layout\Row;
use Encore\Admin\Layout\Column;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Symfony\Component\HttpFoundation\Request;

//机器配件管理
class MachineAccessoryTreeController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content){
            $model_id = Input::get('model_id');
            if (empty($model_id)) {
                return back()->withInput();
            }
            $machine_name = Machine::where('model_id', $model_id)->value('name');
            $content->header("$machine_name 配件列表");
            $content->description();
            $content->row(function (Row $row) use ($model_id) {
                $row->column(6, $this->treeView($model_id)->render());

                $row->column(6, function (Column $column) use ($model_id) {
                    $form = new \Encore\Admin\Widgets\Form();
                    $form->action(admin_url('machine_accessory_tree_copy').'?model_id='.$model_id);
                    $form->select('copy_model_id', '复制机型')->options(Machine::where('visibility',1)->where('model_id','<>', $model_id)->get()->pluck('name', 'model_id'));
                    $form->hidden('model_id')->default($model_id);
                    $column->append((new Box('复制配件(最多复制3层)', $form))->style('success'));
                    $form = new \Encore\Admin\Widgets\Form();

                    $form->action(admin_url('machine_accessory_tree').'?model_id='.$model_id);
                    $form->select('parent_id', '父级')->options(MachineAccessoryTree::model_options($model_id));
                    $form->text('title', '名称')->rules('required');
                    $form->multipleSelect('materials', '物料')->options(function($ids){
                        if ($ids) {
                            return Material::find($ids)->pluck('name', 'id');
                        }
                    })->ajax('/admin/material_search')->placeholder('输入物料的名称或规格或编码');;
                    $form->multipleSelect('malfunctions', '症状')->options(MachineMalfunction::selectOptions());
                    $form->textarea('description', '具体描述');
                    $states = [
                        'on'  => ['value' => 1, 'text' => '是', 'color' => 'success'],
                        'off' => ['value' => 0, 'text' => '否', 'color' => 'danger'],
                    ];
                    $form->switch('discount', '是否参与折扣')->states($states);
                    $form->switch('accessory_mark', '是否更换主板强制填写序列号')->states($states);
                    $form->hidden('model_id')->default($model_id);
                    $column->append((new Box('新增配件', $form))->style('success'));
                });
            });
        });
    }

    public function machine_accessory_tree_copy(Request $request) {
        $data = $request->all();
//        dd($data);
        $model_id = $data['model_id'];
        $copy_mat = MachineAccessoryTree::model_tree($data['copy_model_id']);
//        dd($copy_mat);
        $now = date('Y-m-d H:i:s');
        if ($copy_mat) {
            foreach ($copy_mat as $v) {
                $deal = $v;
                $parent_id = $deal['parent_id'];
                $save = [
                    "model_id" => $data['model_id'],
                    "title" => $deal['title'],
                    "description" => $deal['description'],
                    "parent_id" => $parent_id,
                    "order" => $deal['order'],
                    "created_at" => $now,
                    "updated_at" => $now,
                ];
                $id = MachineAccessoryTree::insertGetId($save);
                if (!empty($deal['material'])) {
                    foreach ($deal['material'] as $m_id) {
                        DB::table('machine_accessory_material_relation')->insert(['mat_id' => $id, 'material_id' => $m_id]);
                    }
                }
                if (!empty($deal['malfunction'])) {
                    foreach ($deal['malfunction'] as $m_id) {
                        DB::table('machine_accessory_malfunction_relation')->insert(['mat_id' => $id, 'malfunction_id' => $m_id]);
                    }
                }
                if (!empty($deal['children'])) {
                    $parent_id = $id;
                    foreach ($deal['children'] as $vv) {
                        $deal = $vv;
                        $save = [
                            "model_id" => $data['model_id'],
                            "title" => $deal['title'],
                            "description" => $deal['description'],
                            "parent_id" => $parent_id,
                            "order" => $deal['order'],
                            "created_at" => $now,
                            "updated_at" => $now,
                        ];
//                        dd($deal);
                        $id = MachineAccessoryTree::insertGetId($save);
                        if (!empty($deal['material'])) {
                            foreach ($deal['material'] as $m_id) {
//                                dd($m_id);
                                DB::table('machine_accessory_material_relation')->insert(['mat_id' => $id, 'material_id' => $m_id]);
                            }
                        }
                        if (!empty($deal['malfunction'])) {
                            foreach ($deal['malfunction'] as $m_id) {
                                DB::table('machine_accessory_malfunction_relation')->insert(['mat_id' => $id, 'malfunction_id' => $m_id]);
                            }
                        }
                        if (!empty($deal['children'])) {
                            $parent_id = $id;
                            foreach ($deal['children'] as $vvv) {
                                $deal = $vvv;
                                $save = [
                                    "model_id" => $data['model_id'],
                                    "title" => $deal['title'],
                                    "description" => $deal['description'],
                                    "parent_id" => $parent_id,
                                    "order" => $deal['order'],
                                    "created_at" => $now,
                                    "updated_at" => $now,
                                ];
                                $id = MachineAccessoryTree::insertGetId($save);
                                if (!empty($deal['material'])) {
                                    foreach ($deal['material'] as $m_id) {
                                        DB::table('machine_accessory_material_relation')->insert(['mat_id' => $id, 'material_id' => $m_id]);
                                    }
                                }
                                if (!empty($deal['malfunction'])) {
                                    foreach ($deal['malfunction'] as $m_id) {
                                        DB::table('machine_accessory_malfunction_relation')->insert(['mat_id' => $id, 'malfunction_id' => $m_id]);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return redirect(admin_url('machine_accessory_tree').'?model_id='.$model_id);
    }

    /**
     * @return \Encore\Admin\Tree
     */
    protected function treeView($id)
    {
        return MachineAccessoryTree::tree(function (Tree $tree) use ($id) {
            $tree->disableCreate();

            $tree->query(function ($model) use ($id) {
                return $model->where('model_id', $id);
            });
            $tree->branch(function ($branch) {
                $material = MachineAccessoryTree::find($branch['id'])->materials;
                $d_total = count($material);
                $malfunction = MachineAccessoryTree::find($branch['id'])->malfunctions;
                $m_total = count($malfunction);
                $payload =<<<EOT
<strong>{$branch['title']}</strong>
<span class='pull-right'>&nbsp;&nbsp;物料数量：{$d_total}</span>
<span class='pull-right'>&nbsp;&nbsp;故障数量：{$m_total}</span>
EOT;
                return $payload;
            });
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('配件');
            $content->description('配件');

            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('配件');
            $content->description('配件');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(MachineAccessoryTree::class, function (Grid $grid) {

            $grid->id('ID')->sortable();

            $grid->created_at();
            $grid->updated_at();
        });
    }

    public function material_search(Request $request)
    {
        $q = $request->get('q');
        $page = $request->get('page');
        return Material::where('name', 'like', "%$q%")
            ->orwhere('specification', 'like', "%$q%")
            ->orwhere('code', 'like', "%$q%")
            ->orwhere('old_code', 'like', "%$q%")
            ->select('id',DB::raw('concat_ws("","【",name,"】【规格:",specification,"】【编码",code,"】【旧编码",old_code,"】【来源：",`from`,"】【库存:",quantity,"】【价格￥",price,"】") as text'))
            ->paginate($page);

//        return Material::where('name', 'like', "%$q%")->orwhere('specification', 'like', "%$q%")->paginate(null, ['id', 'name, specification CONCAT(name,specification) as text']);
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id=null)
    {
        return Admin::form(MachineAccessoryTree::class, function (Form $form) use ($id) {
            $model_id = Input::get('model_id');
            if (empty($model_id) && $id) {
                $model_id = MachineAccessoryTree::find($id)->model_id;
            }
            $form->select('parent_id', '父级')->options(MachineAccessoryTree::model_options($model_id));
            $form->text('title', trans('配件名称'))->rules('required');
            $form->textarea('description', '具体描述');
            $form->multipleSelect('materials', '物料')->options(function($ids){
                if ($ids) {
                    return Material::whereIn('id', $ids)->select(DB::raw('concat_ws("","【",name,"】【规格:",specification,"】【编码",code,"】【旧编码",old_code,"】【来源：",`from`,"】【库存:",quantity,"】【价格￥",price,"】") as text'), 'id')->pluck('text', 'id');
                }
            })->ajax('/admin/material_search')->placeholder('输入物料的名称或规格或编码');
            $form->multipleSelect('malfunctions', '症状')->options(MachineMalfunction::selectOptions());
            $states = [
                'on'  => ['value' => 1, 'text' => '是', 'color' => 'success'],
                'off' => ['value' => 0, 'text' => '否', 'color' => 'danger'],
            ];
            $form->switch('discount', '是否参与折扣')->states($states);
            $form->switch('accessory_mark', '是否更换主板强制填写序列号')->states($states);

            $form->hidden('model_id', '机型id')->default($model_id);
            $form->display('created_at', 'Created At');
            $form->display('updated_at', 'Updated At');
            $form->tools(function (Form\Tools $tools) {
                // 去掉`列表`按钮
                $tools->disableListButton();

                // 添加一个按钮, 参数可以是字符串, 或者实现了Renderable或Htmlable接口的对象实例
//                $tools->add('<a class="btn btn-sm btn-danger"><i class="fa fa-trash"></i>&nbsp;&nbsp;delete</a>');
            });
        });
    }

    public function getAccessoryByParentId(Request $request) {
        $parentId = $request->get('q');
        return MachineAccessoryTree::where('parent_id', $parentId)
            ->get([DB::raw('id as id'), DB::raw('title as text')]);
    }

    public function calculatePrice(){

        return Admin::content(function (Content $content) {

            $content->header('维修配件价格查询');
            $content->description('维修配件价格查询');

            $content->body(view('admin/accessory/calculatePrice'));
        });
    }
}
