<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\User;
use Illuminate\Support\Facades\DB;

class RepairBill extends Model
{
    //
    protected $table = 'repair_bill';
    use SoftDeletes;

    protected $dates = ['deleted_at'];

    public function machine_accessory_relation()
    {
        return $this->hasMany(RepairBillMachineAccessoryRelation::class, 'repair_bill_id',
            'id');
    }

    public function machine_malfunction_relation()
    {
        return $this->hasMany(RepairBillMachineMalfunctionRelation::class, 'repair_bill_id', 'id');
    }

    public function machine(){
        return $this->hasOne(Machine::class,'model_id','model_id');
    }

    public function endpoint()
    {
        return $this->hasOne(AfterSalesEndpoint::class, 'id', 'endpoint_id');
    }

    public function admin_user()
    {
        return $this->hasOne(User::class, 'id', 'create_uid');
    }
}
