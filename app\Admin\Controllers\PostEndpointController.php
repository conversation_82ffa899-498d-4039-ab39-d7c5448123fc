<?php

namespace App\Admin\Controllers;

use App\Models\ChinaArea;
use App\Models\PostRepairEndpoint;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;

class PostEndpointController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('售后服务点');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('售后服务点');
            $content->description('编辑');

            $content->body($this->form()->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('售后服务点新建');
            $content->description('新建');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(PostRepairEndpoint::class, function (Grid $grid) {

            $grid->id('ID')->sortable();
            $grid->name('维修终端名称');
            $grid->contact('联系人');
            $grid->phone('电话');
            $grid->address('地址详情');
            $grid->status('状态')->switch()->editable();

            $grid->created_at('创建时间');
            $grid->updated_at('更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Admin::form(PostRepairEndpoint::class, function (Form $form) {

            $form->display('id', 'ID');
            $form->text('name', '终端名称')->rules('required');
            $status = [
                'on' => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ];
            $form->switch('status', '状态')->states($status)->default(1);
            $form->text('contact', '联系人')->rules('required');
            $form->text('phone', '联系方式')->rules('required');
            $form->select('province', '所属省')->options(
                ChinaArea::province()->pluck('region_name', 'region_id')->prepend('请选择', '')
            )->load('city', '/admin/region/city')->rules('required');

            $form->select('city', '所属市')->options(function ($id) {
                return ChinaArea::options($id);
            })->load('district', '/admin/region/district')->rules('required');

            $form->select('district', '所属区')->options(function ($id) {
                return ChinaArea::options($id);
            })->rules('required');

            $form->text('address', '终端地址')->help('创建时不需要填写省市区,请直接填写具体位置')->rules('required');
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
