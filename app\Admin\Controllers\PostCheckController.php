<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\Express;
use App\Admin\Extensions\Sms;
use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\PostCheckOneOrder;
use App\Admin\Extensions\Tools\QuickPickTool;
use App\Models\AgentOrder;
use App\Models\BrokenScreenInsurance;
use App\Models\ChinaArea;
use App\Models\Damage;
use App\Models\Endpoint;
use App\Models\Machine;
use App\Models\Order;
use App\Models\OrderExtend;
use App\Models\OrderOldAddress;
use Illuminate\Support\Facades\DB;
use App\Models\PostExpress;
use App\Models\PostRepairEndpoint;
use App\Models\RepairStaff;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Widgets\Box;
use Encore\Admin\Widgets\Table;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\MessageBag;
use SimpleXMLElement;
use Illuminate\Support\Facades\Input;

class PostCheckController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修审核');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修审核');
            $content->description('编辑');

            $content->body($this->form($id)->edit($id));
        });
    }

    public function view(Content $content, $id = null)
    {
//        $order = Order::where(['id' => $id])->first();
//        $data = [
//            '机型名称' => $order->model_name,
//            'S/N码' => $order->barcode,
//            '保修状态' => Order::in_period[$order->in_period],
//            '受损状态' => Order::reason[$order->reason],
//            '故障类型' => $order->damage,
//        ];
//        $table = new Table([], $data);
//        $table->setStyle();
//        $content->header('寄修审核--查看');
//        $content->body((new Box('Table-2', $table))->style('info')->solid());
//        $content->body($table);
//        return $content;
        return Admin::content(function (Content $content) use ($id) {

            $content->header('寄修审核--查看');
            $order = Order::where(['id' => $id])->first();
            $order_old_address = OrderOldAddress::where(['sn' => $order->sn])->first();
            $agent_order = null;
            if ($order->type == 3) {
                $agent_order = AgentOrder::rightJoin('agent_order_correlation as aoc', 'agent_order.sn', '=', 'aoc.agent_order_sn')
                    ->where([['aoc.order_sn', $order->sn]])->select('agent_order.*')->first();
            }
            $content->body(view('admin/post_check/view', compact(['order', 'order_old_address', 'agent_order'])));
        });
    }

    public function express(Content $content, $id = null)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
//            dd($data);
            $data['custid'] = env('SF_CUSTID');//公司支付
            $data['express_type'] = 103;
            $data['is_docall'] = 1;
            $data['pay_method'] = 2;
//            $data['template'] = 'swt-下call';
            $data['type'] = 1;
            $data['remark'] = '读书郎客户 运费到付';
            $id = $data['id'];
            unset($data['id']);
            $express = new Express();
            $result = $express->create_express_order($data);
//            dd($result['Body']['OrderResponse']['@attributes']);
            if ($result['Head'] == 'OK' && $result['Body']['OrderResponse']['@attributes']['filter_result'] < 3) {
                //更新快递信息
                PostExpress::where(['readboy_sn' => $result['Body']['OrderResponse']['@attributes']['orderid']])->update([
                    'status' => 1,
                    'exp_sn' => $result['Body']['OrderResponse']['@attributes']['mailno'],
                ]);
                //写入订单信息
                $order = Order::where(['id' => $id])->first();
                $order->status = Order::EXP_COME_SUCCESS;
                $order->rb_come_exp_sn = $result['Body']['OrderResponse']['@attributes']['orderid'];
                $order->come_exp_sn = $result['Body']['OrderResponse']['@attributes']['mailno'];
                $order->come_exp_com = '顺丰快递';
                $order->save();
                return redirect('/admin/post_check');
            } else {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => $result['ERROR'],
                ]);
//                Order::where(['id' => $id])->update(['status' => Order::EXP_COME_FAIL]);
                return back()->withInput()->with(compact('error'));
            }
        }

        $order = Order::where(['id' => $id])->first();
//        如果为终端代寄 且地址为实际收件人地址  取件地址为代寄人地址
        if ($order->type == 3) {
            $order_extend = OrderExtend::where(['sn' => $order->sn])->first();
            if ($order_extend && $order_extend->is_user_address == 1) {
                $agent_order = AgentOrder::rightJoin('agent_order_correlation as aoc', 'agent_order.sn', '=', 'aoc.agent_order_sn')
                    ->where([['aoc.order_sn', $order->sn]])->select('agent_order.*')->first();
                $order->name = $agent_order->name;
                $order->phone = $agent_order->phone;
                $order->province = $agent_order->province;
                $order->city = $agent_order->city;
                $order->district = $agent_order->district;
                $order->address = $agent_order->address;
            }
        }
        $endpoint = PostRepairEndpoint::where(['id' => $order->repair_endpoint])->first();
//        $endpoint = Endpoint::where(['name' => $repair_endpoint->name])->first();
        $province = ChinaArea::where(['region_id' => $endpoint->province])->first();
        $city = ChinaArea::where(['region_id' => $endpoint->city])->first();
        $district = ChinaArea::where(['region_id' => $endpoint->district])->first();

        $content->header('上门取件-快递下单');

        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/post_check/express');
        $form->hidden('id')->default($id);
        $form->hidden('sn')->default($order->sn);
        $form->text('j_contact', '用户-姓名')->default($order->name)->rules('required');
        $form->text('j_tel', '用户-联系电话')->default($order->phone)->rules('required');
        $form->text('j_province', '用户-省')->default($order->province)->rules('required');
        $form->text('j_city', '用户-市')->default($order->city)->rules('required');
        $form->text('j_county', '用户-区')->default($order->district)->rules('required');
        $form->text('j_address', '用户-地址')->default($order->address)->rules('required');
        if ($order->pickup_time) {
            $form->text('sendstarttime', '用户-取件时间')->default($order->pickup_time)->rules('required');
        }
        $form->divide();
        $form->text('d_contact', '终端-姓名')->default($endpoint->name)->rules('required');
        $form->text('d_tel', '终端-联系电话')->default($endpoint->phone)->rules('required');
        $form->text('d_province', '终端-省')->default($province->region_name)->rules('required');
        $form->text('d_city', '终端-市')->default($city->region_name)->rules('required');
        $form->text('d_county', '终端-区')->default($district->region_name)->rules('required');
        $form->text('d_address', '终端-地址')->default($endpoint->address)->rules('required');
        $content->body($form);
        return $content;
    }

    public function express_one_order(Content $content, $id = null)
    {
        if (request()->isMethod('post')) {
            $data = request()->all();
//            dd($data);
            $data['custid'] = env('SF_CUSTID');//公司支付
            $data['express_type'] = 103;
            $data['is_docall'] = 1;
            $data['pay_method'] = 2;
//            $data['template'] = 'swt-下call';
            $data['type'] = 1;
            $data['remark'] = '读书郎客户 运费到付';
            $ids = explode(',', $data['ids']);
            unset($data['ids']);
            $express = new Express();
            $result = $express->create_express_order($data);
            $ret = false;
            foreach ($ids as $id) {
//            dd($result['Body']['OrderResponse']['@attributes']);
                if ($result['Head'] == 'OK' && $result['Body']['OrderResponse']['@attributes']['filter_result'] < 3) {
                    //更新快递信息
                    PostExpress::where(['readboy_sn' => $result['Body']['OrderResponse']['@attributes']['orderid']])->update([
                        'status' => 1,
                        'exp_sn' => $result['Body']['OrderResponse']['@attributes']['mailno'],
                    ]);
                    //写入订单信息
                    $order = Order::where(['id' => $id])->first();
                    $order->status = Order::EXP_COME_SUCCESS;
                    $order->rb_come_exp_sn = $result['Body']['OrderResponse']['@attributes']['orderid'];
                    $order->come_exp_sn = $result['Body']['OrderResponse']['@attributes']['mailno'];
                    $order->come_exp_com = '顺丰快递';
                    $order->save();
                    $ret = true;

                } else {
                    Order::where(['id' => $id])->update(['status' => Order::EXP_COME_FAIL]);
                }
            }
            if ($ret) {
                return redirect('/admin/post_check');
            } else {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => $result['ERROR'],
                ]);
                return back()->withInput()->with(compact('error'));
            }
        }
        $ids = Request::get('ids');

        $count = Order::whereIn('id', $ids)
            ->where('type', '!=', '3')
            ->count();
        if ($count > 0) {
            $error = new MessageBag([
                'title' => '错误提示',
                'message' => '用户寄修无法统一下单',
            ]);
            return back()->withInput()->with(compact('error'));
        }
        $orders = Order::whereIn('order.id', $ids)
            ->rightJoin('agent_order_correlation as aoc', 'aoc.order_sn', '=', 'order.sn')
            ->rightJoin('agent_order as ao', 'ao.sn', '=', 'aoc.agent_order_sn')
            ->select('order.sn', 'ao.name', 'ao.phone', 'ao.province', 'ao.city', 'ao.district', 'ao.address', 'order.repair_endpoint')
            ->get();
//        dd($orders);
        $sns = array();
        $cmp_order = $orders[0];
        foreach ($orders as $order) {
            if ($order->name != $cmp_order->name
                || $order->phone != $cmp_order->phone
                || $order->uid != $cmp_order->uid
                || $order->province != $cmp_order->province
                || $order->city != $cmp_order->city
                || $order->district != $cmp_order->district
                || $order->address != $cmp_order->address) {
                $error = new MessageBag([
                    'title' => '错误提示',
                    'message' => '快递订单信息不相同',
                ]);
                return back()->withInput()->with(compact('error'));
            }
            $sns[] = $order->sn;
        }
        $order = $cmp_order;
        $endpoint = PostRepairEndpoint::where(['id' => $order->repair_endpoint])->first();
//        $endpoint = Endpoint::where(['name' => $repair_endpoint->name])->first();
        $province = ChinaArea::where(['region_id' => $endpoint->province])->first();
        $city = ChinaArea::where(['region_id' => $endpoint->city])->first();
        $district = ChinaArea::where(['region_id' => $endpoint->district])->first();

        $content->header('上门取件-快递下单');

        $form = new \Encore\Admin\Widgets\Form();
        $form->action('/admin/post_check/express_one_order');
        $form->hidden('ids')->default(implode(',', $ids));
//        用来生成快递单号
        $form->hidden('sn')->default(implode(',', $sns));
        $form->text('j_contact', '用户-姓名')->default($order->name)->rules('required');
        $form->text('j_tel', '用户-联系电话')->default($order->phone)->rules('required');
        $form->text('j_province', '用户-省')->default($order->province)->rules('required');
        $form->text('j_city', '用户-市')->default($order->city)->rules('required');
        $form->text('j_county', '用户-区')->default($order->district)->rules('required');
        $form->text('j_address', '用户-地址')->default($order->address)->rules('required');
        if ($order->pickup_time) {
            $form->text('sendstarttime', '用户-取件时间')->default($order->pickup_time)->rules('required');
        }
        $form->divide();
        $form->text('d_contact', '终端-姓名')->default($endpoint->name)->rules('required');
        $form->text('d_tel', '终端-联系电话')->default($endpoint->phone)->rules('required');
        $form->text('d_province', '终端-省')->default($province->region_name)->rules('required');
        $form->text('d_city', '终端-市')->default($city->region_name)->rules('required');
        $form->text('d_county', '终端-区')->default($district->region_name)->rules('required');
        $form->text('d_address', '终端-地址')->default($endpoint->address)->rules('required');
        $content->body($form);
        return $content;
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('header');
            $content->description('description');

            $content->body($this->form());
        });
    }

    public function cancel(Request $request)
    {
        foreach (Order::find(request()->get('ids')) as $post) {
            $post->status = -900;
            $post->save();
        }
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script = <<<EOF
            //图片弹出层
            $('.img-thumbnail').click(
                 function(){
                     let src = $(this).attr('src');
                     let html = '<div class="message"><img style="max-width:900px;max-height:900px;" src="'+ src +'"><//div>'
                     $.fancybox.open(html);
                 }
             );

            //拨打电话
            $('.call_phone').click(function(){
                var id = $(this).attr('value');
                var login_name = $(this).attr('login_name');
                var pass_word = $(this).attr('pass_word');
                layer.open({
                      type: 2,
                      title: '拨打电话',
                      shadeClose: true,
                      shade: 0.8,
                      area: ['300px', '30%'],
                      content: '/packages/edb_bar/phoneBar/phonebar.html?loginType=sip&loginName='+login_name+'&password='+pass_word+'&callNumber='+id //iframe的url
                });
            });

                // 订单标注
            $('.order_mark').click(function(){
                var param = $(this).attr('value');
                $.ajax({
                    url: 'repair_check/order_mark?' + param,
                    method: 'get',
                    success: function () {
                        $.pjax.reload('#pjax-container');
                        toastr.success('操作成功');
                    }
                });
            });
EOF;
        Admin::script($script);
        return Admin::grid(Order::class, function (Grid $grid) {
            $grid->disableCreation();
            $grid->disableExport();
            //快捷筛选条
            $option = [
                -1 => [
                    'name' => '全部',
                    'param' => [['status', '>=', Order::EXP_COME_FAIL], ['status', '<=', Order::EXP_COME_SUCCESS], ['type', '=', '1']],
                ],
                1 => [
                    'name' => '待审核',
                    'param' => [['status', '=', Order::WAIT_AUDIT], ['type', '=', '1']],
                ],
                2 => [
                    'name' => '审核已通过',
                    'param' => [['status', '=', Order::AUDIT_PASS], ['type', '=', '1']],
                ],
                3 => [
                    'name' => '审核不通过',
                    'param' => [['status', '=', Order::AUDIT_NO_PASS], ['type', '=', '1']],
                ],
                4 => [
                    'name' => '用户已发货',
                    'param' => [['status', '=', Order::EXP_COME_SUCCESS], ['type', '=', '1']],
                ],
                5 => [
                    'name' => '用户发货失败',
                    'param' => [['status', '=', Order::EXP_COME_FAIL], ['type', '=', '1']],
                ],
                6 => [
                    'name' => '待发货',
                    'param' => [['status', '=', Order::AUDIT_PASS], ['come_exp_type', 1], ['type', '=', '1']],
                ],
            ];
            //筛选条数
            foreach ($option as $key => $value) {
                $option[$key]['count'] = Order::where($value['param'])->count();
            }
            //自定义状态快捷筛选按钮
            $grid->tools(function ($tools) use ($option) {
                $tools->append(new QuickPickTool($option));
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                    $batch->add('取消订单', new Cancel());
                    $batch->add('统一下单', new PostCheckOneOrder());
                });
            });
            //根据自定义状态按钮搜索数据
            if (Request::get('quick_pick')) {
                $quick_pick = Request::get('quick_pick');
                $grid->model()->where($option[$quick_pick]['param'])->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');
            }

            $grid->model()->where($option[-1]['param'])->orderBy('repeat_order', 'desc')->orderBy('id', 'desc');

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('sn', '寄修单号');
                $filter->like('barcode', 'SN条码');
                $filter->like('name', '联系人');
                $filter->like('phone', '联系人手机号');
                $filter->equal('audit_status', '审批状态')->select([0 => '未审批', 1 => '同意寄修', 2 => '不需要寄修']);
                $filter->equal('damage', '故障类型')->select(
                    Damage::pluck('title', 'title')->all()
                );
                $filter->equal('model_name', '产品型号')->select(
                    Machine::orderBy('name')->pluck('name', 'name')->all()
                );
                $filter->like('auditor_user.name', '审核人');
                $filter->like('agent_sn.agent_order_sn', '代理单号');
                //$filter->equal('type', '寄修类型')->select([1 => '用户寄修', 3 => '终端代修']);
                $filter->is('repeat_order', '二次寄修')->select(Order::is_direct_sales);

            });

            $grid->id('ID')->sortable();
            $grid->uid('UID')->sortable();
            Order::order_priority_column($grid);
            $grid->column('agent_sn.agent_order_sn', '代理单号')->sortable();
            $grid->sn('寄修单号')->sortable();
            //$grid->model_name('产品型号');
            //$grid->barcode('SN');
            $grid->column('S/N码-机型')->display(function () {
                return $this->barcode . '<br/>' . $this->model_name;
            });
            //$grid->damage('故障类型');
            $grid->description('故障描述详情')->display(function ($value) {
                return mb_strlen($value) > 20 ? mb_substr($value, 0, 20) . '...' : $value;
            });
            $grid->name('联系人');
//            $grid->column('保修期证明')->display(function ($value) {
//                $ret = '无';
//                $p = $this->period_file;
////                dd($p);
//                if (is_array($p)) {
//                    $ret = '';
//                    foreach ($p as $key => $value) {
//                        $path = rtrim(config('admin.upload.host'), '/') . '/' . $value;
//                        $ret .= '<img src="' . $path . '" style="max-height:80px;max-width:80px" class="img img-thumbnail fancybox-thumbs" data=' . $this->id . ' i=' . $key . ' />';
//                        $ret .= '&nbsp;';
//                    }
//                }
//                return $ret;
//            });
//            $grid->column('上传说明')->display(function ($value) {
//                $ret = '无';
//                $p = $this->upload_file;
////                dd($p);
//                if (is_array($p)) {
//                    $ret = '';
//                    foreach ($p as $key => $value) {
//                        $path = rtrim(config('admin.upload.host'), '/') . '/' . $value;
//                        $ret .= '<img src="' . $path . '" style="max-height:80px;max-width:80px" class="img img-thumbnail fancybox-thumbs" data=' . $this->id . ' i=' . $key . ' />';
//                        $ret .= '&nbsp;';
//                    }
//                }
//                return $ret;
//            });
            $grid->column('联系信息')->expand(function () {
                //$info = array();
                $data[] = array('联系人', $this->name);
                $data[] = array('联系电话', $this->phone);
                $data[] = array('地址详情', $this->province . $this->city . $this->district . $this->address);
                $info = $data;
                return new Table(['联系信息', ''], $info);
            });
            $grid->phone('电话拨打')->display(function ($value) {
                $user_id = Admin::user()->id;
                $staff = RepairStaff::where('user_id', $user_id)->first();
                if (!empty($staff) && !empty($staff->service_phone_username) && !empty($staff->service_phone_password)) {
                    $login_name = $staff->service_phone_username;
                    $pass_word = $staff->service_phone_password;
                    return '<a href="javascript:void(0);" class="call_phone" value="' . $value . '" login_name="' . $login_name . '" pass_word="' . $pass_word . '">' . $value . '</a>';
                } else {
                    return $value;
                }
            });
            $grid->come_exp_type('寄来方式')->display(function ($come_exp_type) {
                return Order::come_exp_type[$come_exp_type];
            });
            $grid->created_at('提交时间')->display(function ($value) {
                if ($this->repeat_order) {
                    return '<span style="color:red">' . $value . '</span>';
                } else {
                    return $value;
                }
            });
            $grid->pickup_time('取件时间')->editable('datetime');
            $grid->audit_status('审批状态')
                ->display(function ($audit_status) {
                    if ($audit_status == 1)
                        return '同意寄修';
                    elseif ($audit_status == 2)
                        return '不同意寄修';
                    else
                        return '未审批';
                });
            $grid->status('订单状态')->display(function ($value) {

                // 获取是否知会状态
                $isTell = OrderExtend::where('sn', $this->sn)->value("is_tell");
                if ($isTell == 1) {
                    $isTellName = "（需要知会）";
                } else {
                    $isTellName = "（无需知会）";
                }

                $s = Order::STATUS;
                if (array_key_exists($value, $s)) {

                    if ($value == Order::WAIT_AUDIT) {
                        return $value . $s[$value] . $isTellName;
                    } else {
                        return $value . $s[$value];
                    }
                } else {
                    return "————";
                }
            });
            $grid->column('auditor_user.name', '审核人');
            $grid->repeat_order('二次维修')->display(function ($value) {
                if ($value == 1) {
                    return '是';
                } else {
                    return '否';
                }
            });

            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $status = $actions->row->status;
//                if ($actions->row->come_exp_type == 1 && $actions->row->audit_status == 1 && $status == Order::AUDIT_PASS) {
//                    $c = 'post_check/express/' . $actions->getKey();
//                    $html = '<a href="' . $c . '"><span style="color:blue">下单 </span></a>';
//                    $actions->append($html);
//                }
                $c = 'post_check/view/' . $actions->getKey();
                $html = '<a href="' . $c . '"><span style="color:blue">查看 </span></a>';
                $actions->append($html);
                if ($status == Order::WAIT_AUDIT) {
                    $c = 'post_check/' . $actions->getKey();
                    $html = '<a href="' . $c . '/edit"><span style="color:orange">【审核】</span></a>';
                    $actions->append($html);
                    //审核异常逻辑
                    $sn = $actions->row->sn;
                    $order_mark = $actions->row->order_extend['order_mark'];
                    if ($order_mark != Order::ORDER_MARK_AUDIT_ABNORMAL) {
                        $param = 'sn=' . $sn . '&order_mark=' . Order::ORDER_MARK_AUDIT_ABNORMAL;
                        $html_inform_abnormal = '<a href="javascript:void(0);" class="order_mark" value="' . $param . '"><span style="color:orange"> 【审核异常】 </span></a>';
                    } else {
                        $param = 'sn=' . $sn . '&order_mark=' . Order::ORDER_MARK_NORMAL;
                        $html_inform_abnormal = '<a href="javascript:void(0);" class="order_mark" value="' . $param . '"><span style="color:blue"> 【审核正常】 </span></a>';
                    }
                    if (Admin::user()->inRoles(['administrator', 'manager', 'repair_service', 'customer_service_team_leader'])) {
                        $actions->append($html_inform_abnormal);
                    }
                } else {
                    $c = 'post_check/' . $actions->getKey();
                    $html = '<a href="' . $c . '/edit"><span style="color:green">【修改】</span></a>';
                    $actions->append($html);
                    if ($status == Order::AUDIT_PASS || $status == Order::EXP_COME_SUCCESS) {
                        $c = 'post_check/express/' . $actions->getKey();
                        $html = '<a href="' . $c . '"><span style="color:blue">下单 </span></a>';
                        $actions->append($html);
                    }
                }
            });
        });
    }

    private function mes_info($barcode)
    {
        $appId = 'Web';
        $time = time();
        $appSecret = 'M4S8tUB8OBBvIUN7';
        $md5Str = md5("$appId-$time-$appSecret");

        $authKey = "$appId-$time-$md5Str";
        $client = new \GuzzleHttp\Client();
        $url = "http://api-mes.readboy.com/index.php?s=/Api/Barcode/all.html&barcode=$barcode&authKey=$authKey";
        $response = $client->get($url);

        if ($response->getStatusCode() == '200') {
            $body = $response->getBody();
            return json_decode($body->getContents(), true);
        } else {
            return [];
        }
    }

    private function wear_info($imei)
    {
        $client = new \GuzzleHttp\Client();
        $url = 'http://wear.readboy.com:8080/api/info?imei=' . $imei;
        $response = $client->request('GET', $url, [
            'auth' => ['wearapi', 'readboy999']
        ]);

        if ($response->getStatusCode() == '200') {
            $data = json_decode($response->getBody());
            return isset($data->data->device) ? array('add_date' => $data->data->device->addtime, 'bind_date' => $data->data->device->bindtime, 'type' => 'machine', 'model' => $data->data->device->type) : [];
        } else {
            return [];
        }
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id = null)
    {
        $script = <<<js
            //图片弹出层
            $('.img-thumbnail').click(
                 function(){
                     let src = $(this).attr('src');
                     let html = '<div class="message" style=""><button id="button" style="display:block;margin: 0 auto;"><img id="img" style="max-width:900px;max-height:900px; transform: rotate(0deg)" src="'+ src +'"><//button><//div>'
                     $.fancybox.open(html);
                 }
             );
           $(document).on('click','#button',function(){
                var tr = $('#img').css("transform");
                a = eval('get'+$('#img').css('transform'));
                var step=90; 
                $('#img').css("transform","rotate("+(a+step)%360+"deg)");
              });
              
              function getmatrix(a,b,c,d,e,f){  
                var aa=Math.round(180*Math.asin(a)/ Math.PI);  
                var bb=Math.round(180*Math.acos(b)/ Math.PI);  
                var cc=Math.round(180*Math.asin(c)/ Math.PI);  
                var dd=Math.round(180*Math.acos(d)/ Math.PI);  
                var deg=0;  
                if(aa==bb||-aa==bb){  
                    deg=dd;  
                }else if(-aa+bb==180){  
                    deg=180+cc;  
                }else if(aa+bb==180){  
                    deg=360-cc||360-dd;  
                }  
                return deg>=360?0:deg;  
                //return (aa+','+bb+','+cc+','+dd);

	          }
js;
        Admin::script($script);

        return Admin::form(Order::class, function (Form $form) use ($id) {

            $form->html('<label>设备信息</label>');
            $form->display('sn', '寄修单号');
            $form->display('model_name', '机型');
            $form->display('barcode', 'S/N码');

            $form->display("barcode", '机器信息')->with(function ($value) {
                if ($this->has_warranty == 1) {
                    $data = DB::connection('mysql2')
                        ->table('warranty as w')
                        ->leftJoin("endpoint as e", "w.endpoint", "=", "e.id")
                        ->leftJoin("agency as a", DB::raw("IF(e.second_agency=0,e.top_agency,e.second_agency)"), '=', 'a.id')
                        ->where([['w.barcode', '=', $value], ['w.status', '=', '1']])
                        ->select('w.buy_date', "w.created_at", "w.activated_at_old",
                            "e.name AS endpoint_name", "a.name AS agency_name")->first();
                    if (empty($data)) {
                        return '<span style="color:red;">此机器无保卡信息，但记录为有保卡, 请确认</span>&nbsp;';
                    } else {
                        $buy_date_str = '';
                        $created_at_str = '';
                        $activated_at_old_str = '';
                        $format_duration = '%d月%d天';
                        $buy_date = strtotime($data->buy_date);
                        if (!empty($buy_date)) {
                            $buy_date_duration = self::dateDiffFormat($format_duration, $buy_date, null);
                            $buy_date_str = '<span title="一个月是30天" style="color: blue;padding-left: 1em">' .
                                '已购机 ' . $buy_date_duration . '</span>';
                        }
                        $created_at = strtotime($data->created_at);
                        if (!empty($created_at)) {
                            $created_at_duration = self::dateDiffFormat($format_duration, $created_at, null);
                            $created_at_str = '<span title="一个月是30天" style="color: blue;padding-left: 1em">' .
                                '已录入 ' . $created_at_duration . '</span>';
                        }
                        $activated_at_old = strtotime($data->activated_at_old);
                        if (!empty($activated_at_old)) {
                            $activated_at_old_duration = self::dateDiffFormat($format_duration, $activated_at_old, null);
                            $activated_at_old_str = '<span title="一个月是30天" style="color: blue;padding-left: 1em">' .
                                '已激活 ' . $activated_at_old_duration . '</span>';
                        }
                        return '<div style="color:red;">保卡购机时间：' . $data->buy_date . $buy_date_str . '</div>' .
                            '<div style="color:red;">保卡录入时间：' . $data->created_at . $created_at_str . '</div>' .
                            '<div style="color:red;">保卡激活时间：' . $data->activated_at_old . $activated_at_old_str . '</div>' .
                            '<div style="color:red;">终端名称：' . $data->endpoint_name . '</div>' .
                            '<div style="color:red;">所属一代/二代：' . $data->agency_name . '</div>';
                    }
                }
                return '<span style="color:red;">此机器无保卡信息</span>&nbsp;';
            });

            $form->display('come_exp_type', '寄件方式')->with(function ($value) {
                return Order::come_exp_type[$value];
            });
            $form->display('in_period', '保修状态')->with(function ($value) {
                switch ($value) {
                    case 1:
                        return '保修期内';
                    case 2:
                        return '保修期外';
                    default:
                        return '无保修信息';
                }
            });
            $form->display('reason', '受损状态')->with(function ($value) {
                return $value == 1 ? '人为损坏' : '非人为损坏';
            });
            $form->display('damage', '故障类型');
            $form->display('description', '故障详情描述');
            $form->display('attachment', '附件信息');

            $form->html('<label>联系人信息</label>');
            $form->display('name', '联系人');
            $form->display('phone', '联系方式');
            $form->display('province', '省份');
            $form->display('city', '城市');
            $form->display('district', '地区');
            $form->display('address', '详细地址');
            $states = [
                'off' => ['value' => 0, 'text' => '否', 'color' => 'danger'],
                'on' => ['value' => 1, 'text' => '是', 'color' => 'success'],
            ];
            $form->divider();
            $form->html('<label>审核</label>');
            $form->switch('is_agency', '是否终端寄修')->states($states);
            $form->select('reason', '受损状态')->options(Order::reason);
//            if (!empty($id)){
//                $order = Order::where('id', $id)->first();
//                $barcode = $order->barcode;
//                $has_warranty = $order->has_warranty;
//
////                dump(date('Y-m-d',$time2));
//            }
            $special_sales_tips = '根据提交材料判断是否在保修期内';
            if (!empty($id)) {
                $order = Order::where('id', $id)->first();
                $barcode = $order->barcode;
                $mes_info = $this->mes_info($barcode);
                if ($mes_info) {
                    if($mes_info['errcode'] == '0'){
                        $html = <<<EOF
                        <span style="color:red;">MES生产时间：{$mes_info['data']['update_time']}</span>&nbsp;
EOF;
                        $form->html($html);
                        if (!empty($mes_info['data']['imei1'])) {
                            $wear_info = $this->wear_info($mes_info['data']['imei1']);
                            //                        dd($wear_info);
                            if ($wear_info) {
                                $html = <<<EOF
                                    <span style="color:red;">手表激活时间：{$wear_info['add_date']}</span>&nbsp;
EOF;
                                $form->html($html);
                                $html = <<<EOF
                                    <span style="color:red;">手表绑定时间：{$wear_info['bind_date']}</span>&nbsp;
EOF;
                                $form->html($html);
                            }
                        }
                    }
                }
               //查询机器是否特批出库
                $special_sales = DB::table('special_sales')->where(['sn' =>$barcode])->exists();
                if ( $special_sales){
                    $special_sales_tips = <<<EOF
                    <span style="color:red;">特批出库不保修</span>&nbsp;
EOF;
                }
            } 
            $form->switch('order_extend.discount', '是否打折')->states([
                'on' => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ]);
            $form->select('in_period', '审核保修')
                ->options([0 => '无保修信息', 1 => '保修期内', 2 => '保修期外'])->help($special_sales_tips)->rules('required|between:1,2');
            $states_extend = [
                'on' => ['value' => 1, 'text' => '是'],
                'off' => ['value' => 0, 'text' => '否'],
            ];
            $form->switch('extend_warranty', '是否延保标记')->states($states_extend);

            if (!empty($id)) {
                $order = Order::where('id', $id)->first();
                $barcode = $order->barcode;
                $has_screen_insurance = $order->has_screen_insurance;
                if ($has_screen_insurance == 1) {
                    $data = BrokenScreenInsurance::where([['barcode', '=', $barcode], ['status', '=', 300]])
                        ->leftJoin('broken_screen_insurance_standard as bsis', 'broken_screen_insurance.standard', '=', 'bsis.id')
                        ->select('broken_screen_insurance.created_at', 'bsis.month')->first();
                    if ($data) {
//                        dd($data);
                        $buy_date = $data->created_at;
                        $month = '+' . $data->month . ' month';   //碎屏保投保月份
                        // 日期加投保月份  减去一天
                        $buy_date = strtotime('-0 day', strtotime($month, strtotime(date('Y-m-d', strtotime($buy_date)))));
                        $buy_date = date('Y-m-d', $buy_date);

                        $now = date('Y-m-d');
//                        dump($now);
                        if ($now < $buy_date) {
                            $html = <<<EOF
                    <span style="color:red;">碎屏保在保修期内,到期时间：{$buy_date}</span>&nbsp;
EOF;
                            $form->html($html);
                        } else {
                            $html = <<<EOF
                    <span style="color:red;">碎屏保已过期</span>&nbsp;
EOF;
                            $form->html($html);
                        }
                    }
                }
            }
            $form->display('has_screen_insurance', '是否有碎屏保')->with(function ($value) {
                return Order::has_screen_insurance[$value];
            });
            $form->select('in_si_period', '审核碎屏保')
                ->options([0 => '无保修信息', 1 => '保修期内', 2 => '保修期外'])->rules('required|between:1,2');


            $form->display('period_file', '保修材料')->with(function ($value) {
                $ret = '无';
                $p = $value;
                if (is_array($p)) {
                    $ret = '';
                    foreach ($p as $key => $value) {
                        $path = rtrim(config('admin.upload.host'), '/') . '/' . $value;
                        $ret .= '<img src="' . $path . '" style="max-height:80px;max-width:80px" class="img img-thumbnail fancybox-thumbs" data=' . $this->id . ' i=' . $key . ' />';
                        $ret .= '&nbsp;';
                    }
                }
                return $ret;
            });
            $form->display('upload_file', '损坏资料')->with(function ($value) {
                $ret = '无';
                $p = $value;
                if (is_array($p)) {
                    $ret = '';
                    foreach ($p as $key => $value) {
                        $path = rtrim(config('admin.upload.host'), '/') . '/' . $value;
                        $ret .= '<img src="' . $path . '" style="max-height:80px;max-width:80px" class="img img-thumbnail fancybox-thumbs" data=' . $this->id . ' i=' . $key . ' />';
                        $ret .= '&nbsp;';
                    }
                }
                return $ret;
            });
            $form->display('video_file', '视频')->with(function ($value) {
                $ret = '无';
                $p = $value;
                if (is_array($p)) {
                    $ret = '';
                    foreach ($p as $key => $value) {
                        $path = rtrim(config('admin.upload.host'), '/') . '/' . $value;

                        $ret .= '<video src="' . $path . '" style="height:200px;width:400px"  controls="controls"  data=' . $this->id . ' i=' . $key . ' ></video>';
                        $ret .= '&nbsp;';
                    }
                }
                return $ret;
            });
            $form->select('repeat_order', '是否二次维修')->options(function ($v) {
                if ($v == 0) {
                    return [0 => "首次寄修"];
                } else {
                    return [1 => "二次寄修", 2 => "二次返修"];
                }
            });
            $form->display('barcode', '维修记录')->with(function ($value) use ($form, $id) {
                $data = $form->model()->where('barcode', $value)->where('id', '<>', $id)->get()->toArray();
//                dd($data);
                $str = '';
                if (count($data) > 0) {
                    foreach ($data as $i => $d) {
                        $str .= '<span style="color:red">最后更新时间：' . $d['updated_at'] . '</span>  &nbsp; <a href="/admin/post_repair/view/' . $d['id'] . '">查看维修记录</a>';
                        if ($i < count($data) - 1) {
                            $str .= '<br>';
                        }
                    }
                } else {
                    $str = '无';
                }
                return $str;
            });
            $form->select('audit_status', '审核寄修')
                ->options([1 => '支持寄修（上门取件的同时快递下单）', 2 => '不支持寄修']);
            //$form->datetime('pickup_time','取件时间');
            $form->textarea('audit_opinion', '审核备注');
            Order::order_priority_form($form);
            $form->hidden('status');
            $form->display('auditor_user.name', '审核人');
            $form->saving(function (Form $form) {
                //操作状态判断
                $form->model()->auditor = Admin::user()->id;
                if (abs($form->model()->status) > Order::EXP_COME_SUCCESS) {
                    $error = new MessageBag([
                        'title' => '无法操作',
                        'message' => '当前状态不可编辑！',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                //保修期检查
                if ($form->audit_status == 1 && empty(intval($form->in_period))) {
                    $error = new MessageBag([
                        'title' => '缺少保修期信息',
                        'message' => '保修期信息必须审核！',
                    ]);
                    return back()->withInput()->with(compact('error'));
                }
                if (abs($form->status) < Order::EXP_COME_SUCCESS) {
                    if ($form->audit_status == 1) {
                        $form->status = Order::AUDIT_PASS;
                    } else {
                        $form->status = Order::AUDIT_NO_PASS;
                    }
                }
                if ($form->status == Order::AUDIT_PASS) {
                    $sms = new Sms();
                    $phone = $form->model()->phone;
                    $template = '787518';
                    $data = null;
                    $sms->send($phone, $template, $data);
                }

            });
            //顺丰下单
            $form->saved(function (Form $form) {
                $order = $form->model();
                if ($order->audit_status == 1 && $order->come_exp_type == 1 && empty($order->rb_come_exp_sn) && empty($order->come_exp_sn)) {
                    $id = $order->id;
                    $endpoint = PostRepairEndpoint::where(['id' => $order->repair_endpoint])->first();
                    $province = ChinaArea::where(['region_id' => $endpoint->province])->first();
                    $city = ChinaArea::where(['region_id' => $endpoint->city])->first();
                    $district = ChinaArea::where(['region_id' => $endpoint->district])->first();
                    $data = array();
                    $data['sn'] = $order->sn;

                    $phone = $order->phone;
                    if ($order->type == 3) {  // 如果是终端代寄   则下单手机号为终端手机号
                        $agent_order = AgentOrder::rightJoin('agent_order_correlation as aoc', 'agent_order.sn', '=', 'aoc.agent_order_sn')
                            ->where([['aoc.order_sn', $order->sn]])->select('agent_order.*')->first();
                        $phone = $agent_order->phone;
                        $order->name = $agent_order->name;
                        $order->phone = $agent_order->phone;
                        $order->province = $agent_order->province;
                        $order->city = $agent_order->city;
                        $order->district = $agent_order->district;
                        $order->address = $agent_order->address;
                    }

                    $data['j_tel'] = $phone;
                    $data['j_contact'] = $order->name;
                    $data['j_province'] = $order->province;
                    $data['j_city'] = $order->city;
                    $data['j_county'] = $order->district;
                    $data['j_address'] = $order->address;
                    $data['d_contact'] = $endpoint->name;
                    $data['d_tel'] = $endpoint->phone;
                    $data['d_province'] = $province->region_name;
                    $data['d_city'] = $city->region_name;
                    $data['d_county'] = $district->region_name;
                    $data['d_address'] = $endpoint->address;
                    if ($order->pickup_time != null) {
                        $data['sendstarttime'] = $order->pickup_time;
                    }
                    $data['is_docall'] = 1;
                    $data['pay_method'] = 2;
                    $data['custid'] = env('SF_CUSTID');//公司支付
//                    $data['template'] = 'swt-下call';
                    $data['express_type'] = 103;  // 原来是2   要改成103
                    $data['type'] = 1;
                    $data['remark'] = '读书郎客户 运费到付';
//                    dd($data);
                    $express = new Express();
                    $result = $express->create_express_order($data);
//                    dd( $result);
                    if ($result['Head'] == 'OK' && $result['Body']['OrderResponse']['@attributes']['filter_result'] < 3) {
                        //更新快递信息
                        PostExpress::where(['readboy_sn' => $result['Body']['OrderResponse']['@attributes']['orderid']])->update([
                            'status' => 1,
                            'exp_sn' => $result['Body']['OrderResponse']['@attributes']['mailno'],
                        ]);
                        //写入订单信息
                        $order = Order::where(['id' => $id])->first();
                        $order->status = Order::EXP_COME_SUCCESS;
                        $order->rb_come_exp_sn = $result['Body']['OrderResponse']['@attributes']['orderid'];
                        $order->come_exp_sn = $result['Body']['OrderResponse']['@attributes']['mailno'];
                        $order->come_exp_com = '顺丰快递';
                        $order->save();
                    } else {
                        $error = new MessageBag([
                            'title' => '错误提示',
                            'message' => $result['ERROR'],
                        ]);
//                        Order::where(['id' => $id])->update(['status' => Order::EXP_COME_FAIL]);
                        return back()->withInput()->with(compact('error'));
                    }
                }
                return redirect('/admin/post_check?quick_pick=1');
            });
        });
    }

    /**
     * 审核备注修改
     */
    public function setAuditOpinion()
    {
        $sn = request()->input('sn');
        $audit_opinion = request()->input('audit_opinion');

        Order::Where('sn', $sn)->update(array('audit_opinion' => $audit_opinion));

        return array('status' => 1, 'info' => '操作成功');

    }

    public static function dateDiffFormat($format, $dateLeft, $dateRight): string
    {
        if (empty($dateRight)) {
            $dateRight = time();
        }
        //计算两个日期之间的时间差
        $diff = abs($dateRight - $dateLeft);
        //转换时间差的格式
        //$years = floor($diff / (365 * 60 * 60 * 24));
        $months = floor($diff / (30 * 60 * 60 * 24));
        $days = floor(($diff - $months * 30 * 60 * 60 * 24) / (60 * 60 * 24));
        $hours = floor(($diff - $months * 30 * 60 * 60 * 24 - $days * 60 * 60 * 24) / (60 * 60));
        $minutes = floor(($diff - $months * 30 * 60 * 60 * 24 - $days * 60 * 60 * 24 - $hours * 60 * 60) / 60);
        $seconds = floor(($diff - $months * 30 * 60 * 60 * 24 - $days * 60 * 60 * 24 - $hours * 60 * 60 - $minutes * 60));
        return sprintf($format, $months, $days, $hours, $minutes, $seconds);
    }

}
