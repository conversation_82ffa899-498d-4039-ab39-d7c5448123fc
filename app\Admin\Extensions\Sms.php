<?php
/**
 * Created by PhpStorm.
 * User: qzl
 * Date: 2019/10/10
 * Time: 14:09
 */

namespace App\Admin\Extensions;


use GuzzleHttp\Client;

class Sms
{
    const SMS_HOST = 'https://api-sms.readboy.com';

    public function send($phone, $template, $data) {
        $client = new Client([
            'base_uri' => 'https://www.baidu.com',
            'timeout' => 10.0,
        ]);
        $url = self::SMS_HOST . '/index.php?s=/Sms/Api/send';
        $code = random_int(100000, 999999);
        $authKey = time() . '-' . $code . '-' . md5(time() . '-' . $code . '-' . env('SMS_KEY'));
        $param = [
            'form_params' => [
                'authKey' => $authKey,
                'appName' => 'care.readboy.com',
                'templateCode' => $template,
                'phoneNumber' => $phone,
//                'templateParam' => json_encode($data),
            ]
        ];
        if($data){
            $param['form_params']['templateParam'] = json_encode($data);
        }
//        dd($param);
        $response = $client->request('POST', $url, $param
        );
        if ($response->getStatusCode() != '200') {
            return false;
        }
//        dump($response->getBody()->getContents());
        \Log::info($response->getBody());
        return true;
    }

    public function query_info($phone) {
        $client = new Client([
            'base_uri' => 'https://www.baidu.com',
            'timeout' => 10.0,
        ]);
        $url = self::SMS_HOST . '/index.php?s=/Sms/Api/queryDetail';
        $code = random_int(100000, 999999);
        $authKey = time() . '-' . $code . '-' . md5(time() . '-' . $code . '-' . env('SMS_KEY'));
        $param = [
            'form_params' => [
                'authKey' => $authKey,
                'phone_number' => $phone,
            ]
        ];
//        dd($param);
        $response = $client->request('POST', $url, $param
        );
        if ($response->getStatusCode() != '200') {
            return false;
        }
        dump($response->getBody()->getContents());
        \Log::info($response->getBody());
        return true;
    }
}
