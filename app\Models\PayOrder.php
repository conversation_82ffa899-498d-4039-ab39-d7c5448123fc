<?php
/**
 * Created by PhpStorm.
 * User: Admin
 * Date: 2019/7/30
 * Time: 13:50
 */

namespace App\Models;

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PayOrder extends Model
{
    protected $table = "pay_order";

    const COM = [0 => '未知', 1 => '微信', 2 => '支付宝'];
    const IS_PAID = [0 => '未支付', 1 => '已支付'];

    public function notify() {
//        return $this->belongsToMany(Question::class, 'paper_question', 'paper_id', 'question_id');
        return $this->hasMany('pay_notify_log', 'readboy_sn', 'readboy_sn');
    }


}