<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\EndpointWarrantyFlowExporter;
use App\Admin\Extensions\EndpointWarrantyStatisticsExporter;
use App\Admin\Extensions\SalesStarExporter;
use App\Admin\Extensions\Tools\DayGender;
use App\Http\Controllers\Controller;
use App\Models\Action;
use App\Models\Endpoint;
use App\Models\MachineType;
use App\Models\Order;
use App\Models\Statistics;
use App\Models\Warranty;
use App\Services\Admin\MachineTypeService;
use App\Services\Admin\StatisticsService;
use Carbon\Carbon;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use Encore\Admin\Widgets\InfoBox;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Encore\Admin\Grid;
use App\Models\Agency;
use Illuminate\Support\Facades\Request;
use Encore\Admin\Widgets\Table;
use Illuminate\Validation\Rules\In;

//统计,都是保卡相关的
//别问我为什么不起一个和保卡相关的名称,因为当时还不知道要加入那么多其他的东西..
class StatisticsController extends Controller
{

    protected $service;

    protected $machineTypeService;

    public function __construct(StatisticsService $service, MachineTypeService $machineTypeService)
    {
        $this->service = $service;
        $this->machineTypeService = $machineTypeService;
    }

    //每天的保卡录入情况还有终端分布等信息的统计,有一个单独的大统计页面,这个是v1版本
    public function summary()
    {
        //先获取是不是总代
        $topAgency = 0;
        if (Admin::user()->inRoles(['topAgency'])) {
            //获取当前用户的总代id
            $topAgency = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('top_agency');
        }

        return view('admin.statistics.summary.index', compact('topAgency'));
    }

    //每天的保卡录入情况还有终端分布等信息的统计,有一个单独的大统计页面,这个是v2版本
    public function summaryV2()
    {
        //先获取是不是总代
        $topAgency = 0;
        if (Admin::user()->inRoles(['topAgency'])) {
            //获取当前用户的总代id
            $topAgency = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('top_agency');
        }

        return view('admin.statistics.summary_v2.index', compact('topAgency'));
    }

    //代理地区保卡销售情况统计
    public function agencyWarranty()
    {
        return Admin::content(function (Content $content) {
            $content->header('代理保卡统计');
            $content->description('代理保卡统计');
            //获取代理下对应的保单数量统计
            $statistics = new Statistics();
            $gender = Input::get('gender');
            $createTime = Input::get('created_at');
            $agencyWarrantyStatistics = $statistics->getAgencyWarrantyStatistics($gender, $createTime);
            $content->body(view('admin/statistics/agency_warranty', $agencyWarrantyStatistics));
        });
    }

    //不同机型保卡销售情况统计
    public function modelWarranty()
    {
        return Admin::content(function (Content $content) {

            $content->header('机型保卡数量');
            $content->description('机型保卡数量统计');

            $topAgency = Input::get('top_agency');
            $secondAgency = Input::get('second_agency');
            $createdAt = Input::get('created_at');
            $gender = Input::get('gender');
            $data = Statistics::getModelWarrantyStatistics($topAgency, $secondAgency, $createdAt, $gender);

            $formatData = [];
            $modelPrices = $this->machineTypeService->getCachedAllModelPrices();

            foreach ($data as $key => $value) {
                //加入预估销售额统计
                $price = isset($modelPrices[$value['model']]) ? $modelPrices[$value['model']] : 0;
                $data[$key]['sum_price'] = $price * $value['warranty_count'];
                $formatData[] = [
                    'name' => $value['model'],
                    'value' => $value['warranty_count'],
                ];
            }
            $content->body(view('admin/statistics/model_warranty', compact('data', 'formatData')));
        });
    }

    //
    protected function modelWarrantyGrid()
    {
        return Admin::grid(Warranty::class, function (Grid $grid) {
            //默认只显示一个星期
            $genderDay = Request::get('gender');
            $time = Statistics::dayGender($genderDay);
            if (Admin::user()->inRoles(['topAgency', 'topAgencyFinance'])) {
                //获取当前用户的总代id
                $topAgency = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('top_agency');
                $map = [
                    ['warranty.status', '=', 1],
                    ['endpoint.top_agency', '=', $topAgency],
                ];

            } elseif (Admin::user()->inRoles(['secondAgency'])) {
                //获取当前用户的总代id
                $secondAgency = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('second_agency');
                $map = [
                    ['warranty.status', '=', 1],
                    ['endpoint.second_agency', '=', $secondAgency],
                ];

            } else {
                $map = [['warranty.status', '=', 1]];
            }

            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('model', '机型');
                $minDate = Carbon::now()->subMonth(3)->startOfMonth()->toDateString();
                $filter->between('buy_date', '录入时间')->datetime(['minDate' => $minDate]);
                if (Admin::user()->inRoles(['topAgency', 'topAgencyFinance'])) {
                    $filter->equal('second_agency', '二级代理')->select(function () {
                        $topAgency = DB::table('user_agency')
                            ->where('uid', '=', Admin::user()->id)->value('top_agency');

                        return Agency::where('pid', '=', $topAgency)->pluck('name', 'id');
                    });
                } elseif (Admin::user()->inRoles(['secondAgency'])) {

                } else {
                    $filter->equal('top_agency', '一级代理')
                        ->select(Agency::top()->pluck('name', 'id'))
                        ->load('second_agency', '/admin/agency/secondAgency');
                    $filter->equal('second_agency', '二级代理')->select(function ($id) {
                        return Agency::options($id);
                    });
                }
            });
            $grid->disableExport();
            $grid->disableCreation();
            $grid->tools(function ($tools) {
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                });
                $tools->append(new DayGender());
            });
            $grid->model()
                ->leftjoin('endpoint', 'warranty.endpoint', 'endpoint.id')
                ->select(DB::raw('warranty.id,warranty.barcode,warranty.model,warranty.buy_date,count(warranty.id) as warranty_count,
               endpoint.top_agency,endpoint.second_agency'))
                ->where($map)
                ->whereBetween('buy_date', $time)
                ->orderBy('warranty_count', 'desc')
                ->groupBy('model');
            $grid->column('model', '机型')->label('primary');
            $grid->warranty_count('保卡数量')->prependIcon('fa fa-hospital-o')->sortable();
            $grid->actions(function ($actions) {
                if ($actions->row->barcode != "") {
                    $actions->disableDelete();
                    $actions->disableEdit();
                    $actions->append("无");
                }
            });
        });
    }

    //终端保卡统计
    public function endpointWarranty()
    {
        return Admin::content(function (Content $content) {

            $content->header('终端保卡数量');
            $content->description('终端保卡数量统计');

            $content->body($this->endpointWarrantyGrid());
        });
    }

    protected function endpointWarrantyGrid()
    {
        $route = route('endpoint_warranty_flow_model_count', Input::all());
        $modelSumPriceRoute = route('endpoint_warranty_model_sum_price');
        $gender = Input::get('gender', 'today');
        $createdAt = json_encode(Input::get('buy_date', null));
        $script = <<<SCRIPT
                $('.model_count').on('click',function(){
                    let endpointId = $(this).attr('id');
                    layer.open({
                                  type: 2,
                                  title: '机型数量分布',
                                  shadeClose: true,
                                  shade: false,
                                  maxmin: true,
                                  area: ['40%', '60%'],
                                  content: '$route' + '&endpoint_id=' + endpointId
                               });
                });
                
                $(function(){
                  let ids = [];
                  $('.endpoint_name').each(function(){
                    ids.push($(this).attr('id'));
                  });
                  let gender = '$gender';
                  let createdAt = '$createdAt';
                  //去请求获得每个终端当前的机型销售额
                  $.get('$modelSumPriceRoute',{ids:ids,gender:gender,createdAt:createdAt},function(result){
                    for(x in result){
                      if(result[x] > 1000000){
                        result[x] = '<span style="color: red">' + result[x] + '</span>'
                      }
                      $('#'+x ).parents('tr').find('td:eq(4)').html(result[x]);
                    };
                  })
                });
SCRIPT;

        Admin::script($script);

        return Admin::grid(Warranty::class, function (Grid $grid) {

            $genderDay = Input::get('gender');
            $createdAt = Input::get('buy_date');
            if (!empty($createdAt['start'])) {
                $time = [$createdAt['start'], $createdAt['end']];
            } else {
                $time = Statistics::dayGender($genderDay);
            }
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->equal('endpoints.name', '终端名称');
                $minDate = Carbon::now()->subMonth(12)->startOfMonth()->toDateString();
                $filter->between('buy_date', '录入时间')->datetime(['minDate' => $minDate]);
                if (Admin::user()->inRoles(['topAgency', 'topAgencyFinance'])) {
                    $filter->equal('endpoints.second_agency', '二级代理')->select(function () {
                        $topAgency = DB::table('user_agency')
                            ->where('uid', '=', Admin::user()->id)->value('top_agency');

                        return Agency::where('pid', '=', $topAgency)->pluck('name', 'id');
                    });

                } elseif (Admin::user()->inRoles(['secondAgency'])) {

                } else {
                    $filter->where(function ($query) {

                        $query->whereIn('endpoint', function ($query) {
                            $query->from('endpoint')
                                ->select('endpoint.id')
                                ->where('top_agency', $this->input)
                                //要加入second_agency的判断,因为直接用这个后台的where是没办法得到second_agency的默认值的
                                ->where('second_agency', Input::get('second_agency'));
                        });

                    }, '一级代理', 'top_agency')->select(Agency::top()->pluck('name', 'id'))
                        ->load('second_agency',
                            '/admin/agency/secondAgency');
                    $filter->where(function ($query) {

                        $query->whereIn('endpoint', function ($query) {
                            $query->from('endpoint')
                                ->select('endpoint.id')
                                ->where('second_agency', $this->input);
                        });

                    }, '二级代理', 'second_agency')->select(function ($id) {
                        return Agency::options($id);
                    });
                }
            });
            $grid->exporter(new EndpointWarrantyStatisticsExporter($time));
            $grid->disableCreation();
            $grid->tools(function ($tools) {
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                });
                $tools->append(new DayGender());
            });
            $tt = $grid->model()
                ->select(DB::raw('warranty.id,endpoint,barcode,count(warranty.id) as warranty_count'))
                ->where('warranty.status', '=', 1)
                ->whereBetween('buy_date', $time)
                ->orderBy('warranty_count', 'desc')
                ->groupBy('endpoint');

            if (Admin::user()->inRoles(['topAgency', 'topAgencyFinance'])) {
                //获取当前用户的总代id
                $topAgency = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('top_agency');
                $tt->leftJoin('endpoint', 'warranty.endpoint', 'endpoint.id')->where('endpoint.top_agency', $topAgency);
            } elseif (Admin::user()->inRoles(['secondAgency'])) {
                //获取当前用户的总代id
                $secondAgency = DB::table('user_agency')->where('uid', '=', Admin::user()->id)->value('second_agency');
                $tt->leftJoin('endpoint', 'warranty.endpoint', 'endpoint.id')
                    ->where('endpoint.second_agency', $secondAgency);
            }
            $grid->column('endpoints.name', '终端名称')->display(function ($value) {
                $endpointId = $this->endpoints['id'];

                return "<span class='endpoint_name' id='$endpointId'>$value</span>";
            });
            $grid->column('终端')->expand(function () {
                $endpoint = [
                    '终端名称' => $this->endpoints['name'],
                    '终端地址' => $this->endpoints['address'],
                    '终端电话' => $this->endpoints['phone'],
                    '终端负责人' => $this->endpoints['manager'],
                ];

                return new Table([], $endpoint);
            }, '信息');
            $grid->warranty_count('保卡数量')->prependIcon('fa fa-hospital-o')->sortable();
            $grid->column('price', '销售额');
            $grid->actions(function ($actions) use ($genderDay) {
                if ($actions->row->barcode != "") {
                    $actions->disableDelete();
                    $actions->disableEdit();
                    $inputs = Input::all();
                    unset($inputs['_sort']);
                    $route = route('endpoint_warranty_flow',
                        array_merge(['endpoint_id' => $actions->row->endpoints['id']], $inputs));
                    $actions->append("<a href='$route'>流水</a>");
                    $actions->append("<a href='#' id='{$actions->row->endpoints['id']}' class='model_count'>　机型分布</a>");
                }
            });
        });
    }

    //终端保卡流水
    public function endpointWarrantyFlow()
    {
        return Admin::content(function (Content $content) {
            $genderDay = Input::get('gender');
            $createdAt = Input::get('buy_date');
            $time = $this->service::getWarrantyFlowStatisticsQueryTime($createdAt, $genderDay);
            //获取当前终端机型数量的统计
            $content->header("终端保卡流水");
            $content->description("终端保卡流水,时间: [{$time[0]} - {$time[1]}]");

            $content->body($this->endpointWarrantyFlowGrid($time));
        });
    }

    private function endpointWarrantyFlowGrid($time)
    {

        return Admin::grid(Warranty::class, function (Grid $grid) use ($time) {
            //构建查询条件
            $map = ['endpoint' => Input::get('endpoint_id')];
            //自定义工具：筛选状态、打印页面
            $grid->tools(function ($tools) {
                //返回按钮
                $backRoute = route('endpoint_warranty');
                $backButton = "<a href='{$backRoute}' class='btn btn-sm btn-success'>
                              <i class='fa fa-backward'></i>&nbsp;&nbsp;返回
                          </a>";
                $tools->append($backButton);
            });
            $grid->model()
                ->where($map)
                ->whereBetween('buy_date', $time)->where('status', '=', 1);
            $grid->disableCreation();
            $grid->disableExport();
            $grid->disableRowSelector();
            $grid->disableActions();
            $grid->disableFilter();
            $grid->endpoint('终端id');
            $grid->barcode('条码')->sortable();
            $grid->salesman('导购员');
            $grid->customer_name('用户姓名');
            $grid->customer_phone('用户电话');
            $grid->buy_date('购机日期')->prependIcon('calendar-times-o')->sortable();
            $grid->column('model', '型号')->label('primary');
        });
    }

    //终端保卡流水统计页面
    public function endpointWarrantyFlowModelCount()
    {
        $genderDay = Input::get('gender');
        $createdAt = Input::get('buy_date');
        if (!empty($createdAt['start'])) {
            $time = [$createdAt['start'], $createdAt['end']];
        } else {
            $time = Statistics::dayGender($genderDay);
        }
        $endpointId = Input::get('endpoint_id');
        $data = Statistics::getModelCountOfEndpointStatisticsFlow($time, $endpointId);

        return view('admin/statistics/agency_warranty_flow_model_count', compact('data'));
    }

    //点击获取当前终端对应的不同机型的销售额情况
    public function endpointWarrantyModelSumPrice()
    {
        $genderDay = Input::get('gender');
        $createdAt = json_decode(Input::get('createdAt'), true);
        $time = $this->service::getWarrantyFlowStatisticsQueryTime($createdAt, $genderDay);
        $endpointIds = Input::get('ids');

        return $this->service->calculateEndpointWarrantyPrice($endpointIds, $time);
    }

    //每日保卡统计
    public function dailyWarranty()
    {
        return Admin::content(function (Content $content) {
            $content->header('保卡每日统计');
            $content->description('保卡每日统计');

            $topAgency = Input::get('top_agency');
            $secondAgency = Input::get('second_agency');
            $createdAt = Input::get('created_at');
            $gender = Input::get('gender');
            $data = Statistics::getDailyWarrantyStatistics($topAgency, $secondAgency, $createdAt, $gender);
            $content->body(view('admin/statistics/daily_warranty', compact('data')));
        });
    }


    public function orderView() {
        return Admin::content(function(Content $content){
            //不同的角色显示的数量应该不一样
            $content->row(function ($row) {
                if (!Admin::user()->inRoles([])) {
                    $endpointCount = DB::table('order')
                        ->where('status', '<>', Order::ORDER_CANCEL)
                        ->count(DB::raw('distinct(uid)'));
                    $row->column(2, new InfoBox('寄修总人数', 'users', 'aqua', '/admin/statistics/order_view', $endpointCount));
                    if (Admin::user()->inRoles(['administrator', 'manager', 'endpointManager'])) {
                        //获得寄修总台数
                        $warrantyCount = DB::table('order')
                            ->where('status', '<>', Order::ORDER_CANCEL)
                            ->count(DB::raw('distinct(barcode)'));
                        $row->column(2,
                            new InfoBox('寄修总台数', 'shopping-cart', 'green', '/admin/post_repair_manage', $warrantyCount));

                    }

                }
            });
        });
    }


    //每日寄修统计
    public function dailyOrder()
    {
        return Admin::content(function (Content $content) {
            $content->header('每日统计');
            $content->description('每日统计');
            $machine_category = Input::get('category');
            $model_id = Input::get('model');
            $createdAt = Input::get('created_at');
            $gender = Input::get('gender');
            $data = Statistics::getDailyOrderStatistics($createdAt, $gender, $machine_category, $model_id);
            $data2 = Statistics::getDailyBlockStatistics($gender, $createdAt, $machine_category, $model_id);
            $content->body(view('admin/statistics/daily_order', compact('data', 'data2')));
        });
    }
//    订单分布
    public function order_distribute()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修订单分布');
            $content->description('寄修订单分布统计');
            $createdAt = Input::get('created_at');
            $gender = Input::get('gender');
            $province = Input::get('province');
            $city = Input::get('city');
//            dump($gender);
            $data = Statistics::getOrderDistributeStatistics($gender, $createdAt, $province, $city);
            $content->body(view('admin/statistics/order_distribute', compact('data')));
        });
    }
//    保内保外占比
    public function order_in_period()
    {
        return Admin::content(function (Content $content) {

            $content->header('寄修保内保外占比');
            $content->description('寄修保内保外占比');
            $createdAt = Input::get('created_at');
            $gender = Input::get('gender');
            $in_period = Input::get('in_period');
            $machine_category = Input::get('category');
            $model_id = Input::get('model');

            $data = Statistics::getInPeriodStatistics($gender, $in_period,$createdAt,$machine_category, $model_id);
//            dump($data);
            $content->body(view('admin/statistics/order_in_period', compact('data')));
        });
    }

    public function damageAccessory(){
        return Admin::content(function (Content $content){
            $content->header('配件损坏占比');
            $content->description('配件损坏占比');
            $createdAt = Input::get('created_at');
            $gender = Input::get('gender');
            $model_id = Input::get('model');

            if ($model_id == 0){
                $data = array();
            }else{
                $data = Statistics::getDamageAccessoryStatistics($gender,$model_id, $createdAt);
            }

//            dd($data);
            $content->body(view('admin/statistics/damage_accessory', compact('data')));
        });
    }

    //地区活动统计
    public function orderInfo()
    {
        return Admin::content(function (Content $content) {

            $content->header('详情统计');
            $content->description('寄修详情');
            $createdAt = Input::get('created_at');
            $gender = Input::get('gender');
            $data = Statistics::getAgencyActionStatistics($gender, $createdAt);
            $rateData = Statistics::calculateActionAmountRate($data);
            $data = Statistics::calculateAgencyActionStatistics($data);
            //必须要在计算rateData之后才四舍五入，因为计算$rateData的时候不是用的四舍五入后的数据
            $content->body(view('admin/statistics/agency_action', compact('data', 'rateData')));
        });
    }

    //地区活动统计
    public function agencyAction()
    {
        return Admin::content(function (Content $content) {

            $content->header('地区活动');
            $content->description('地区活动统计,统计中的金额只统计已核销的活动');
            $createdAt = Input::get('created_at');
            $gender = Input::get('gender');
            $data = Statistics::getAgencyActionStatistics($gender, $createdAt);
            $rateData = Statistics::calculateActionAmountRate($data);
            $data = Statistics::calculateAgencyActionStatistics($data);
            //必须要在计算rateData之后才四舍五入，因为计算$rateData的时候不是用的四舍五入后的数据
            $content->body(view('admin/statistics/agency_action', compact('data', 'rateData')));
        });
    }

    private static function getSalesStarRankTimeInterval($dayType, $data)
    {
        foreach ($data as $key => $value) {
            switch ($dayType) {
                case 'day':
                    $date = Carbon::parse($value->dayType);
                    $value->time['startTime'] = $date->startOfDay()->toDateTimeString();
                    $value->time['endTime'] = $date->endOfDay()->toDateTimeString();
                    break;
                case 'month':
                    $date = Carbon::createFromFormat('Ym', $value->dayType);
                    $value->time['startTime'] = $date->startOfMonth()->toDateTimeString();
                    $value->time['endTime'] = $date->endOfMonth()->toDateTimeString();
                    break;
                case 'year':
                    $date = Carbon::createFromFormat('Y', $value->dayType);
                    $value->time['startTime'] = $date->startOfYear()->toDateTimeString();
                    $value->time['endTime'] = $date->endOfYear()->toDateTimeString();
                    break;
            }
        }

        return $data;
    }

    public function salesStar()
    {
        return Admin::content(function (Content $content) {
            $map = [];
            $createdAt = Input::get('created_at');
            if ($createdAt) {
                $map = [
                    ['created_at', '>=', $createdAt['start']],
                    ['created_at', '<=', $createdAt['end']],
                ];
            }
            $dayType = Input::get('gender', 'day');
            $rawSelectSql = self::getDayFormatRawSql($dayType);
            $content->header('销售排名');
            $content->description('根据终端导购销售量做销售排名统计');
            $data = DB::table('warranty')
                ->select(DB::Raw($rawSelectSql))
                ->where($map)
                ->orderBy('dayType', 'desc')
                ->groupBy('dayType')->simplePaginate(20);
            $data = self::getSalesStarRankTimeInterval($dayType, $data);
            $content->body(view('admin/statistics/sales_star', compact('data')));
        });
    }

    //根据不同的时间类型返回需要查询的条件
    private static function getDayFormatRawSql($dayType)
    {
        $rawSelectSql = '';
        switch ($dayType) {
            case 'day':
                $rawSelectSql = "DATE_FORMAT(warranty.created_at, '%Y%m%d') dayType";
                break;
            case 'month':
                $rawSelectSql = "DATE_FORMAT(warranty.created_at, '%Y%m') dayType";
                break;
            case 'year':
                $rawSelectSql = "DATE_FORMAT(warranty.created_at, '%Y') dayType";
                break;
        }

        return $rawSelectSql;
    }

    public function salesStarDetail()
    {
        return Admin::content(function (Content $content) {

            $content->header('销售排名详情');
            $date = self::getRequestDateInterval();
            $content->description("{ $date 销售表单｝统计详细");
            $data = Statistics::getSalesStarDetail(Input::all());
            $content->body(view('admin/statistics/sales_star_detail', compact('data')));
        });
    }

    private static function getRequestDateInterval()
    {
        return Input::get('startTime') . '-' . Input::get('endTime');
    }

    public function salesStarDetailExport()
    {
        $data = Statistics::getSalesStarDetail(Input::all());
        SalesStarExporter::detailExport($data);
    }

    //销售之星具体排名
    public function salesStarRank()
    {
        return Admin::content(function (Content $content) {

            $content->header('销售排名详情');
            $date = self::getRequestDateInterval();
            $content->description("{ $date 销售表单｝统计详细");
            $data = Statistics::getSalesStarRank(Input::all());
            $content->body(view('admin/statistics/sales_star_rank', compact('data')));
        });
    }

    public function salesStarRankExport()
    {
        $data = Statistics::getSalesStarRank();
        SalesStarExporter::rankExport($data);
    }

    private function salesStarDetailGrid()
    {
        return Admin::grid(Warranty::class, function (Grid $grid) {
            $grid->tools(function ($tools) {
                //返回按钮
                $backRoute = route('sales_star');
                $backButton = <<<EOF
                                  <a href='{$backRoute}' class='btn btn-sm btn-success'>
                                    <i class='fa fa-backward'></i>&nbsp;&nbsp;返回
                                  </a>
EOF;

                $tools->append($backButton);
            });
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->where(function ($query) {
                    $flag = $this->input;
                    if ($flag) {
                        $query->whereIn('endpoint', Endpoint::where('top_agency', '=', $flag)->pluck('id'));
                    } else {
                        $query->whereRaw('1=1');
                    }
                }, '地区')->select(Agency::top()->pluck('name', 'id')->prepend('全国', '0'));
            });
            $grid->disableRowSelector();
            $grid->disableActions();
            $agencyArray = Agency::getAllAgencySimpleArrayCache();
            $grid->disableExport();
            $grid->disableCreation();
            $grid->model()
                ->select(DB::Raw("count(warranty.id) as count,warranty.id,salesman,endpoint"))
                ->whereBetween('created_at', [Input::get('startTime'), Input::get('endTime')])
                ->where('status', '=', 1)
                ->groupBy('salesman')
                ->orderBy('count', 'desc')
                ->limit(10);
            $grid->disablePagination();
            $grid->endpoints()->top_agency('总代')->display(function ($value) use ($agencyArray) {
                return $agencyArray[$value];
            });
            $grid->endpoints()->second_agency('二代')->display(function ($value) use ($agencyArray) {
                return $value ? $agencyArray[$value] : '无';
            });
            $grid->endpoints()->name('终端店名');
            $grid->salesman('导购员');
            $grid->count('销售数量');

        });
    }

    //终端位置分布热点位置图
    public function endpointPositions()
    {
        return Admin::content(function (Content $content) {
            $topAgencyEndpointType = $this->service->getTopAgencyEndpointType();

            $map = [];
            $inputs['top_agency'] = Input::get('top_agency', 0);
            $inputs['second_agency'] = Input::get('second_agency', 0);
            $inputs['type'] = Input::get('type', 0);
            foreach ($inputs as $key => $value) {
                if ($value) {
                    $map[$key] = $value;
                }
            }

            $content->header('终端热点图');
            $endpointPositions = Endpoint::select(['name', DB::raw("concat(lng,',',lat) as lnglat"), 'type'])
                ->where('status', 1)
                ->where($map)
                ->get()
                ->toArray();
            $centerPosition = $this->service->getCenterPosition($endpointPositions);
            $zoom = 5;
            if ($inputs['second_agency'] || $inputs['top_agency']) {
                $zoom = 8;
            }

            $content->description('终端热点图');
            $content->body(view('admin.statistics.endpoint_positions',
                compact('endpointPositions', 'centerPosition', 'topAgencyEndpointType', 'zoom')));
        });

    }


    public function report(){
        return view('admin.h5', ['path'=> '/repair_report/']);
    }

    public function daily_statistics(){

        return view('admin.daily_statistics', ['path'=> '/post_repair_admin_h5/repair_board/']);
    }

}
