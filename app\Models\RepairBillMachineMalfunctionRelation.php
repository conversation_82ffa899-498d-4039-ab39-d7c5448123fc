<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class RepairBillMachineMalfunctionRelation extends Model
{
    //
    protected $table = 'repair_bill_machine_malfunction_relation';

    protected $fillable = ['malfunction_top', 'malfunction_id'];

    protected static function boot()
    {
        parent::boot();

        self::saved(function(){
            DB::table('repair_bill_machine_malfunction_relation')
                ->where('malfunction_id',0)->delete();
        });
    }
}
