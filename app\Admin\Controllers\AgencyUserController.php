<?php

namespace App\Admin\Controllers;

use App\Models\RoleUser;
use App\Models\UserAgency;

use App\Models\Role;
use App\User;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use App\Models\Agency;
use App\Models\Partition;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Request;
use Illuminate\Validation\Rule;

//代理用户管理,这个模块主要是给后台管理员可以直接新建代理的账户
//手动指定账号和密码
class AgencyUserController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('用户管理');
            $content->description('用户管理列表');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('用户管理');
            $content->description('用户信息的所属一二代信息建立后不允许修改');
            $content->body($this->form($id)->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('用户管理');
            $content->description('用户信息的所属一二代信息建立后不允许修改');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Admin::grid(UserAgency::class, function (Grid $grid) {
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();
                $filter->like('admin_users.username', '用户名');
                $filter->like('admin_users.name', '名称');

            });
            $grid->actions(function ($actions) {
                $actions->disableDelete();
            });
            $grid->model()->orderBy('created_at', 'desc');
            $grid->column('admin_users.username', '用户名');
            $grid->column('admin_users.name', '名称');
            $grid->created_at('创建时间');

        });
    }

    public function update($id)
    {
        return $this->form($id)->update($id);
    }

    /**
     * Make a form builder.
     * @param $id
     * @return Form
     */
    protected function form($id = null)
    {
        $js = <<<EOF
          $("form").submit(function(e){
            $('.form-control').attr('disabled',false);
          });
EOF;

        Admin::script($js);

        return Admin::form(UserAgency::class, function (Form $form) use ($id) {

            $roleIds = [];
            if ($id) {
                //获取当前user_agency表里id有的角色id
                $uid = UserAgency::where('id', '=', $id)->value('uid');
                $roleIds = RoleUser::where('user_id', '=', $uid)->pluck('role_id')->toArray();
            }
            //只允许建立这三个角色的帐号
            $form->multipleSelect('roles', '所属角色')
                ->options(Role::all()->pluck('name', 'id')->only([
                    $topAgencyRoleId = 10,
                    $secondAgencyRoleId = 11,
                    $leaderRoleId = 12,
                ]))
                ->value($roleIds);
            $form->select('partition', '所属大区')->options(Partition::pluck('name', 'id')
                ->prepend('无', 0));

            //用户信息的所属一二代信息建立后不允许修改
            $attr = $id ? ['disabled' => true] : '';
            $form->select('top_agency', '所属一级代理')->options(
                Agency::top()->pluck('name', 'id')->prepend('无', 0)
            )->load('second_agency', '/admin/agency/secondAgency')
                ->help('如果不属于某个一二代(比如领导帐号),可不选,用户信息的所属一二代信息建立后不允许修改')
                ->attribute($attr);

            $form->select('second_agency', '所属二级代理')->options(function ($id) {
                return Agency::options($id);
            })
                ->help('如果不属于某个一二代,可不选,用户信息的所属一二代信息建立后不允许修改')
                ->attribute($attr);
            $states = [
                'on' => ['value' => 1, 'text' => '启用'],
                'off' => ['value' => 0, 'text' => '禁用'],
            ];
            $form->switch('status', '是否启用')->states($states)->default(1);

            $form->text('admin_users.name', '名称');

            $method = Request::method();

            $rules = ($method === 'PUT') ? 'required' : 'required|unique:admin_users';
            $form->text('admin_users.username', '用户名')->help('必填')->rules($rules);

            $form->password('admin_users.password', '密码')->help('必填')->rules('confirmed|required')
                ->default(function ($form) use ($id) {
                    return $id ? $form->model()->admin_users->password : '';
                });
            $form->password('admin_users.password_confirmation', '重复密码')->help('必填')->rules('required')
                ->default(function ($form) use ($id) {
                    return $id ? $form->model()->admin_users->password : '';
                });
            $form->text('admin_users.phone', '电话号码');
            $form->email('email', '邮箱');
            $form->text('weixin_id', '微信Id');

            $form->ignore(['admin_users.password_confirmation', 'roles']);
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
