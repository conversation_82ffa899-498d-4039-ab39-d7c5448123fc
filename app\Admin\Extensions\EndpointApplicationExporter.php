<?php

namespace App\Admin\Extensions;

use App\Models\Endpoint;
use App\Models\EndpointApplication;
use App\Models\UserAfterSales;
use App\Services\Admin\EndpointApplicationService;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Exporters\AbstractExporter;

class EndpointApplicationExporter extends AbstractExporter
{
    use ExcelExportTrait;

    public function export()
    {
        $filename = '专卖店申请列表';
        // 根据上面的数据拼接出导出数据
        $titles = [
            '总代区域',
            '门店详细地址',
            '终端类型',
            '门店类别',
            '申请时间',
            '状态',
            '审核意见',
            '投资人',
            '联系方式',
            '保卡帐号',
            '保卡初始密码',
            '支持金额',
        ];
        $data = $this->getData();
        $formatData = [];

        if (!empty($data)) {
            foreach ($data as $key => $row) {
                $formatData[] = [
                    $row['top_agency_relation']['name'],
                    $row['address'],
                    Endpoint::ENDPOINT_TYPE[$row['type']],
                    $row['set_point_type'] . '类',
                    $row['created_at'],
                    $this->getStatusLabel($row),
                    $this->getAdvice($row),
                    $row['investor'],
                    $row['investor_phone'],
                    $row['admin_user']['username'] ?: '暂无',
                    $row['admin_user']['username'] ? substr(md5($row['admin_user']['username'] . $row['add_to_endpoint_id']),
                        0, 8) : '暂无',
                    $row['pay'],
                ];
            }
        }
        array_unshift($formatData, $titles);
        ExcelExportTrait::exportToExcel($filename, $formatData);
    }

    protected function getStatusLabel($data)
    {
        $statusLabel =   [
            -5 => '核销未通过',
            -3 => '支持申请审核不通过',
            -1 => '建店审核不通过',
            0 => '未审核',
            1 => '建店审核通过,等待申请支持',
            3 => '已通过支持,请尽快上传核销文件',
            5 => '核销通过',
        ];
        $status = $data['status'];
        switch ($status) {
            case EndpointApplication::SUPPORT_AUDIT_PASS:
                //为什么只判断一个字段就好,因为这个在上传的时候做了限制,肯定是一起上传的
                //所以只要有一个就代表其他也有了
                if ($data['renovation_photos']) {
                    return '已回传核销文件,待核销';
                }
                break;
            case EndpointApplication::SET_POINT_AUDIT_PASS:
                if ($data['policy_id']) {
                    return '已申请支持,等待审核';
                }
                break;
        }

        return $statusLabel[$status];
    }

    protected function getAdvice($data){
        switch ($data['status']) {
            case -1 or 1:
                return $data['audit_advice'] ?: '';
                break;
            case -3 or 3:
                return $data['support_audit_advice'] ?: '';
                break;
            case -5 or 5:
                return $data['write_off_advice'] ?: '';
            default:
                return '';
        }
    }

}