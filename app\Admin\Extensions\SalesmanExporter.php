<?php

namespace App\Admin\Extensions;

use App\Models\Endpoint;
use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;
use App\Models\Agency;

class SalesmanExporter extends AbstractExporter
{
    public function export()
    {
        $filename = '导购员列表';
        $data = $this->getData();
        if (!empty($data)) {
            $titles = [
                '导购id',
                '代理区域',
                '终端名称',
                '终端地址',
                '姓名',
//                '手机号',
                '更新时间',
            ];
            $formatData = $this->getFormatData($data);
            array_unshift($formatData, $titles);
            ExcelExportTrait::exportToExcel($filename, $formatData);
        }
    }

    private function getFormatData($data)
    {
        foreach ($data as $row) {
            $row = [
                $row['id'],
                $row['top_name'].$row['second_name'],
                $row['endpoint']['name'],
                $row['endpoint']['address'],
                $row['name'],
//                $row['phone'],
                $row['updated_at'],
            ];
            $formatData[] = $row;
        }

        return $formatData;
    }


}