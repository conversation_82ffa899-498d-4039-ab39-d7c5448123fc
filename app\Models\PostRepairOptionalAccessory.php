<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class PostRepairOptionalAccessory extends Model
{
    const TABLE = 'pr_optional_accessory';
    protected $table = self::TABLE;

    const STATUS_NORMAL = 0;
    const STATUS_CANCEL = -1;
    const STATUS = [
        self::STATUS_NORMAL => '正常',
        self::STATUS_CANCEL => '已取消',
    ];

    public static function getListBySn(String $sn): Collection
    {
        return self::where('pr_sn', '=', $sn)->get();
    }

    public static function getShowListBySn(String $sn): Array
    {
        $table = self::TABLE;
        $sql_fields_pr_oa = "pr_oa.pr_sn, pr_oa.count, pr_oa.price, pr_oa.status, pr_oa.created_at, pr_oa.oa_id";
        $sql_fields_oa_calc = "pr_oa.count * pr_oa.price as price_sum";
        $sql_fields_oa = "oa.name as oa_name";
        $sql_fields_oac = "oac.name as oac_name";
        $sql_fields_m = "m.code as m_code, m.name as m_name";
        $sql_fields = $sql_fields_pr_oa . ', ' . $sql_fields_oa_calc . ', ' . $sql_fields_oa . ', ' . $sql_fields_oac . ', ' . $sql_fields_m;
        $sql_base = "select $sql_fields from $table as pr_oa ";
        $sql_join_oa = "left join optional_accessory as oa on pr_oa.oa_id = oa.id ";
        $sql_join_oac = "left join optional_accessory_category as oac on oa.category_id = oac.id ";
        $sql_join_m = "left join material as m on pr_oa.material_id = m.id ";
        $sql_where = "where pr_oa.pr_sn = :sn ";
        $sql = $sql_base . $sql_join_oa . $sql_join_oac . $sql_join_m . $sql_where;
        $data = DB::select($sql, ['sn' => $sn]);
        // foreach($data as $index => $value) {
        //     $value->price_sum = $value->count * $value->price;
        // }
        return $data;
    }

    public static function statusToTextInList(Array $list): Array
    {
        foreach($list as $k => $v) {
            if (!isset($v->status)) {
                continue;
            }
            if (array_key_exists($v->status, self::STATUS)) {
                $v->status = self::STATUS[$v->status];
            }
        }
        return $list;
    }
}
