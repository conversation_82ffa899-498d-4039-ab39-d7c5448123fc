<?php

namespace App\Models;

use Encore\Admin\Traits\AdminBuilder;
use Encore\Admin\Traits\ModelTree;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class MachineQualityTree extends Model
{
    use ModelTree, AdminBuilder;
    //
    protected $table = 'machine_quality_tree';

    public function parent() {
        return $this->belongsTo(MachineAccessoryTree::class, 'parent_id');
    }

    public function children() {
        return $this->hasMany(MachineAccessoryTree::class, 'parent_id');
    }

    public function brothers() {
        return $this->parent->children();
    }

    public static function options($id) {

        if (!$self = static::find($id)) {
            return [];
        }

        return $self->brothers()->pluck('title', 'id');
    }

    public static function category_options($category_id) {
        $nodes = self::where('machine_category_id', $category_id)->orderBy('order')->get()->toArray();
        if ($nodes) {
            $nodes = (new static())->buildSelectOptions($nodes);
        }
        return collect($nodes)->prepend('Root', 0)->all();
//        return $nodes;
    }

    public static function category_tree($category_id) {
        $nodes = self::where('machine_category_id', $category_id)->orderBy('order')->get()->toArray();
        if ($nodes) {
            $nodes = (new static())->buildNestedArray($nodes);
        }
        return $nodes;
    }
}
