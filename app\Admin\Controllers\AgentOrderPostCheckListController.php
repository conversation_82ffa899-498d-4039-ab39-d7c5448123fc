<?php


namespace App\Admin\Controllers;


use App\Admin\Extensions\Tools\Cancel;
use App\Admin\Extensions\Tools\DeleteAll;
use App\Http\Controllers\Controller;
use App\Models\AgentOrder;
use App\Models\AgentOrderCorrelation;
use App\Models\Order;
use App\Models\OrderLog;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Form\Tools;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;
use function foo\func;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

class AgentOrderPostCheckListController extends Controller
{
    use ModelForm;

    /**
     *Index interface.
     *
     * @return content
     */
    public function index(){
        return Admin::content(function (Content $content){

            $content->header('经销商寄修订单');
            $content->description('经销商寄修大单列表');
            $content->body($this->grid());
        });
    }

    /**
     * @param $id
     * @return mixed
     */
    public function edit($id){
        return Admin::content(function (Content $content) use ($id){
           $content->header('经销商寄修');
           $content->description('经销商寄修编辑');
           $content->body($this->form($id)->edit($id));
        });
    }



    public function grid(){
        return Admin::grid(AgentOrder::class, function (Grid $grid){

            $grid->disableCreation();
            $grid->disableExport();
            $grid->tools(function ($tools){
                $tools->batch(function ($batch) {
                    $batch->disableDelete();
                    $batch->add('取消订单', new Cancel());
                    $batch->add('删除', new DeleteAll());
                });
                $button = <<<EOF
                    <div class="btn-group pull-right" style="margin-right:15px; margin-top: 0px;">
                      <a href ="agent_order_repair_exp_go" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 经销商总单
                      </a >
                    </div >
                    <div class="btn-group pull-right" style="margin-right:15px;">
                    <a href ="agent_order_go_confirm" class="btn btn-sm btn-warning" >
                        <i class="fa fa-back" ></i > 回寄确认
                      </a >
                     </div>
EOF;
                $tools->append($button);
            });
//            dd(bcrypt('104166'));
            $grid->model()->where([['status', '=', '1'], ['type', '=', 1]])->orderBy('id', 'desc');
            $grid->filter(function ($filter){
                $filter->like('sn', '寄修单号');
                $filter->like('name', '寄修人');
                $filter->like('phone', '手机号');
                $filter->like('agency', '区域');
                $filter->where(function ($query){
                    $input = $this->input;
//                    $query->join('agent_order_correlation as aoc', 'agent_order.sn', '=', 'aoc.agent_order_sn')
//                        ->join('order as o', 'aoc.order_sn', '=', 'o.sn')
//                        ->where('o.barcode', $this->input)->get();
                    $query->whereHas('agent_order_correlation', function ($query) use ($input){
                        $query->whereHas('order', function ($query){
                            $query->where('barcode', $this->input);
                        });
                    });
                }, '条码');
//                $filter->where(function ($query){
//                    $input = $this->input;
//                    $query->whereHas('agent_order_correlation', function ($query) use ($input){
//                        $query->whereHas('order', function ($query){
//                            $query->where('updated_at_last', '>=', $this->input);
//                        });
//                    });
//                }, '下单开始时间')->datetime();
//                $filter->where(function ($query){
//                    $input = $this->input;
//                    $query->whereHas('agent_order_correlation', function ($query) use ($input){
//                        $query->whereHas('order', function ($query){
//                            $query->where('updated_at_last', '<', $this->input);
//                        });
//                    });
//                }, '下单结束时间')->datetime();
                $filter->where(function ($query){
                    $query->whereHas('agent_order_correlation', function ($query){
                        $query->whereHas('order', function ($query){
                            $query->where('status', $this->input);
                        });
                    });
                }, '状态')->select(Order::STATUS);
                $filter->where(function ($query){
                    $input = $this->input;
                    $query->whereHas('agent_order_correlation', function ($query) use ($input){
                        $query->whereHas('order', function ($query){
                            $query->where('come_exp_sn', $this->input);
                        });
                    });
                }, '快递单号');

            });

            $grid->id('ID')->sortable();
            $grid->agency('区域');
            $grid->sn('寄修大单单号');
            $grid->name('联系人');
            $grid->phone('手机号');
            $grid->province('省份');
            $grid->city('城市');
            $grid->district('地区');
            $grid->address('详细地址');
            $grid->column('come_exp_sn', '寄来快递单号');
            $grid->column('小单单数')->display(function (){
//                dd($this->sn);
                $count = AgentOrderCorrelation::where([['agent_order_correlation.agent_order_sn', '=', $this->sn], ['o.status', '<>', Order::ORDER_CANCEL]])
                    ->rightjoin('order as o','o.sn', '=', 'agent_order_correlation.order_sn')
                    ->count();
                return $count;
            });
            $grid->column('已回寄或已完成数')->display(function (){
//                dd($this->sn);
                $count_post_go = Order::where([['status', '=', Order::EXP_GO_SUCCESS], ['go_sure', '=', 1],['aoc.agent_order_sn', '=', $this->sn]])
                    ->orwhere([['status', '=', Order::ORDER_FINISH],['aoc.agent_order_sn', '=', $this->sn]])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
                if ($count_post_go == 0){
                    return 0;
                }
                return $count_post_go;
            });
            $grid->column('审核状态')->display(function (){
//                dd($this->sn);
                $count = Order::where([['order.status', '=', 100],['aoc.agent_order_sn', '=', $this->sn]])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
                if ($count == 0){
                    return '审核已完成';
                }
                return '<span style="color: red;">审核未完成</span>';
            });
            $grid->column('签收状态')->display(function (){
//                已发货--已收货--审核通过自主寄件
                $count_post_sign = Order::where([['order.status', '>=', Order::EXP_COME_SUCCESS],
                    ['order.status', '<',Order::COME_SURE], ['order.type', '=', '2'],
                    ['aoc.agent_order_sn', '=', $this->sn]])
                    ->orwhere([['order.come_exp_type', '=', 2], ['order.status', '=', Order::AUDIT_PASS],
                        ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $this->sn]])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
                if ($count_post_sign > 0){
                    return '<span style="color: red;">签收未完成</span>';
                }
                return '无可签收订单';
            });
            $grid->column('待回寄数量数')->display(function (){
//                dd($this->sn);
                $count_post_go = Order::where([['status', '=', Order::REPAIR_FINISH], ['quality', '=', 1],['aoc.agent_order_sn', '=', $this->sn]])
                    ->orwhere([['status', '=', Order::REPAIR_REFUSE],['aoc.agent_order_sn', '=', $this->sn]])
//                    ->orwhere([['go_sure', '=', 1], ['status', '=', Order::EXP_GO_SUCCESS]])
                    ->orwhere([['status', '=', Order::EXP_GO_FAIL],['aoc.agent_order_sn', '=', $this->sn]])
//                    ->orwhere([['status', '=', Order::ORDER_FINISH]])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
                if ($count_post_go == 0){
                    return '暂无回寄订单';
                }
                return $count_post_go;
            });
            $grid->actions(function ($actions){
                $actions->disableDelete();
                $c = 'agent_order_post_check';
                $k = $actions->row->sn;
                $html = '<a href="agent_order_post_repair?agent_order_sn=' . $k . '"><span style="color:red">【寄修总览】</span></a>';
                $actions->append($html);
                $count_post_check = Order::where([['order.status', '>=', Order::EXP_COME_FAIL],['order.status', '<=',
                    Order::EXP_COME_SUCCESS], ['order.type', '=', '2'],['aoc.agent_order_sn', '=', $k]])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
                if ($count_post_check > 0){
                    $html = '<a href="'.$c.'?agent_order_sn='.$k.'"><span style="color:red">【审核详情】</span></a>';
                    $actions->append($html);
                }
                $count_post_sign = Order::where([['order.status', '>=', Order::EXP_COME_SUCCESS],
                    ['order.status', '<=',Order::COME_SURE_IS_TELL], ['order.type', '=', '2'],
                    ['aoc.agent_order_sn', '=', $k]])
                    ->orwhere([['order.come_exp_type', '=', 2], ['order.status', '=', Order::AUDIT_PASS],
                        ['order.type', '=', '2'], ['aoc.agent_order_sn', '=', $k]])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
                if ($count_post_sign > 0){
                    $html = '<a href="agent_order_post_sign?agent_order_sn='.$k.'"><span style="color:red">【签收详情】</span></a>';
                    $actions->append($html);
                }
                $count_post_connect = Order::where([['order.status', '=', 500],['aoc.agent_order_sn', '=', $k]])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
                if ($count_post_connect > 0) {
                    $html = '<a href="post_connect?agent_order_sn=' . $k . '"><span style="color:red">【支付确认】</span></a>';
                    $actions->append($html);
                }
                $count_post_go = Order::where([['status', '=', Order::REPAIR_FINISH], ['quality', '=', 1],['aoc.agent_order_sn', '=', $k]])
                    ->orwhere([['status', '=', Order::REPAIR_REFUSE],['aoc.agent_order_sn', '=', $k]])
                    ->orwhere([['go_sure', '=', 1], ['status', '=', Order::EXP_GO_SUCCESS],['aoc.agent_order_sn', '=', $k]])
                    ->orwhere([['status', '=', Order::EXP_GO_FAIL],['aoc.agent_order_sn', '=', $k]])
                    ->orwhere([['status', '=', Order::ORDER_FINISH], ['aoc.agent_order_sn', '=', $k]])
                    ->rightjoin('agent_order_correlation as aoc','order.sn', '=', 'aoc.order_sn')
                    ->count();
//                dd($count_post_go);
                if ($count_post_go > 0) {
                    $html = '<a href="agent_order_repair_exp_go?agent_order_sn=' . $k . '"><span style="color:red">【回寄详情】</span></a>';
                    $actions->append($html);
                }
            });

        });
    }

    public function form($id = null){
        return Admin::form(AgentOrder::class, function (Form $form) use ($id){
//            dd(id);
            $form->display('id');
            $form->display('sn', '寄修单号');
            $form->text('name','联系人');
            $form->text('phone', '手机号');
            $form->text('province', '省份');
            $form->text('city', '城市');
            $form->text('district', '地区');
            $form->text('address', '详细地址');
        });
    }

    public function agent_order_bill(){
        return Admin::content(function (Content $content){
            $content->header('费用账单');
            $content->description('经销商寄修账单');
            $data = AgentOrder::getAgentOrderBill();
            $content->body(view('admin/agent_order_list/agent_order_bill', compact('data')));
        });

    }

    public function cancel(Request $request)
    {
        foreach (AgentOrder::find(request()->get('ids')) as $post) {
            $post->status = -1;
            DB::table('order as o')
                ->where('aoc.agent_order_sn','=', $post->sn)
                ->leftJoin('agent_order_correlation as aoc', 'aoc.order_sn', '=', 'o.sn')
                ->update(['o.status'=> -900]);

            $post->save();

        }
    }

    public function deleteAll(Request $request)
    {
        foreach (AgentOrder::find(request()->get('ids')) as $post) {
            $post->status = -1;
            DB::table('agent_order')->where('sn','=', $post->sn)->delete();
            DB::table('order as o')
                ->where('aoc.agent_order_sn','=', $post->sn)
                ->leftJoin('agent_order_correlation as aoc', 'aoc.order_sn', '=', 'o.sn')
                ->delete();
            DB::table('agent_order_correlation')->where('agent_order_sn','=', $post->sn)->delete();
        }
    }
}