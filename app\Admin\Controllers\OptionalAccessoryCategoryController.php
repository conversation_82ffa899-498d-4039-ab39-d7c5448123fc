<?php

namespace App\Admin\Controllers;

use App\Models\OptionalAccessory;
use App\Models\OptionalAccessoryCategory;

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Illuminate\Database\Eloquent\Collection;

class OptionalAccessoryCategoryController extends Controller
{
    use ModelForm;

    const SWITCH_TEXT = [
        'on' => ['text' => '是', 'value' => 1],
        'off' => ['text' => '否', 'value' => 0],
    ];

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header('自选配件类别');
            $content->description('');

            $content->body($this->grid());
        });
    }

    /**
     * Edit interface.
     *
     * @param $id
     * @return Content
     */
    public function edit($id)
    {
        return Admin::content(function (Content $content) use ($id) {

            $content->header('编辑配件分类');
            $content->description('');

            $content->body($this->form()->edit($id));
        });
    }

    /**
     * Create interface.
     *
     * @return Content
     */
    public function create()
    {
        return Admin::content(function (Content $content) {

            $content->header('添加配件分类');
            $content->description('');

            $content->body($this->form());
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $script =
<<<script
    $('.grid-row-delete-has-count').unbind('click').click(function() {
        alert('删除失败，已有关联配件');
    });
script;
        Admin::script($script);
        return Admin::grid(OptionalAccessoryCategory::class, function (Grid $grid) {

            $grid->model()->orderBy('enable', 'desc')->orderBy('sort', 'desc');
            $grid->model()->collection(function (Collection $collection) {
                $ids = $collection->pluck('id');

                // 读取分类已关联配件数
                $idc = OptionalAccessory::getCategoryCount($ids->toArray());
                foreach($collection as $index => $item) {
                    $item->accessory_count = $idc->get($item->id, 0);
                }

                return $collection;
            });

            $grid->actions(function ($actions) {
                $key = $actions->getKey();
                $a_count = $actions->row->accessory_count;
                if ($a_count > 0) {
                    $actions->disableDelete();
                    $actions->append('<a href="javascript:void(0);" data-id="' . $key . '" class="grid-row-delete-has-count"><i class="fa fa-trash"></i></a>');
                }
            });

            $grid->id('ID')->sortable();
            $grid->name('名称')->editable();

            $grid->column('accessory_count', '已关联配件数');
            /* $grid->column('optionalAccessory', '已关联配件数')->display(function ($optionalAccessory) {
                $count = count($optionalAccessory);
                return '<span>' . $count . '</span>';
            }); */

            $grid->sort('排序')->editable();
            $grid->enable('是否启用')->switch(self::SWITCH_TEXT);

            $grid->created_at('创建时间');
            $grid->updated_at('更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Admin::form(OptionalAccessoryCategory::class, function (Form $form) {

            $form->display('id', 'ID');

            $form->text('name', '名称')->rules('required');
            $form->text('sort', '排序')->default(0);
            $form->switch('enable', '是否启用')->states(self::SWITCH_TEXT);
        });
    }
}
