<?php

namespace App\Admin\Extensions\Tools;

use Encore\Admin\Admin;
use Encore\Admin\Grid\Tools\AbstractTool;
use Illuminate\Support\Facades\Request;

class DayGender extends AbstractTool {
    public function script() {
        $url = Request::fullUrlWithQuery(['gender' => '_gender_']);

        return <<<EOT

    $('input:radio.day-gender').change(function () {
        var url = '/admin/statistics/endpoint_warranty?gender=' + $(this).val();
       $.pjax({container: '#pjax-container', url: url});
    });

EOT;
    }

    public function render() {
        Admin::script($this->script());

        $options = [
            'today'  => '今天',
            'yesterday'  => '昨天',
            'thisWeek'  => '本周',
            'thisMonth' => '本月',
            'lastMonth' => '上月'
        ];

        return view('admin.tools.day_gender', compact('options'));
    }
}