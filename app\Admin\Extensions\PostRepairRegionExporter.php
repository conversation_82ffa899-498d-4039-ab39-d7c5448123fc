<?php


namespace App\Admin\Extensions;


use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

class PostRepairRegionExporter extends AbstractExporter
{

    public function export()
    {
        // TODO: Implement export() method.

        $start = date('Y-m-d 00:00:00', strtotime(date('Y-m-01 00:00:00'). '-1 month'));
        $end = date('Y-m-01 00:00:00');

        $updated_at_last = Request::get('updated_at_last');
        if ($updated_at_last){
            $start = $updated_at_last['start'];
            $end = $updated_at_last['end'];
        }
        $start_str = date('Y-m-d', strtotime($start));
        $end_str = date('Y-m-d', strtotime($end));
        $filename = '寄修数量区域排名'.$start_str.'-'.$end_str;

        $title = [
            '排名',
            '区域',
            '寄修数量',
            '区域占比(%)',
            '保内数量',
            '保内占比(%)',
            '保外数量',
            '保外占比(%)',
            '用户下单',
            '终端下单',
            '代理商下单',
            '保内数量（代理商下单）',
            '保外数量（代理商下单）',
        ];
//        dump($start.$end);
        $formatData = $this->getFormatData($start, $end);
        array_unshift($formatData, $title);
//        dump($start.$end);
//        dump($start_str.$end_str);
//        dd($formatData);
        ExcelExportTrait::exportToExcel($filename, $formatData);
    }

    private function getFormatData($start, $end){
//        $where1 = [['rv.region_type', '=', 1], ['av.level',  '=', 1], ['o.type', '!=', 2]];
//        $first_where = [['rv.region_type', '=', 2], ['av.level', '=', 1], ['o.type', '!=', 2]];
        $where1 = [['rv.region_type', '=', 1], ['av.level',  '=', 1]];
        $first_where = [['rv.region_type', '=', 2], ['av.level', '=', 1]];
//        有部分扁平化处理
        $first = DB::table('order as o')->rightJoin('region_view as rv', 'o.district','=','rv.region_name')
            ->rightJoin('agency_region_view as arv', 'rv.region_id', '=', 'arv.region_id')
            ->rightJoin('agency_view as av', 'arv.agency_id', '=', 'av.id')
            ->where($first_where)
            ->whereNull('av.deleted_at')
            ->whereBetween('o.updated_at_last', [$start, $end])
            ->select(DB::raw('sum(o.type != 2) as cnt, sum(o.type = 2) as agency_cnt, av.`name`,
            SUM(o.type = 1 and o.is_agency = 0) as type_1, 
            SUM(o.type = 2) as type_2,
            SUM(o.type = 3 or (o.type = 1 and o.is_agency = 1)) as type_3, 
            SUM(o.type != 2 and o.pay_amount = 0) as in_period, 
            SUM(o.type != 2 and o.pay_amount > 0) as no_in_period,
            SUM(o.type = 2 and o.in_period = 1) as agency_in_period, 
            SUM(o.type = 2 and o.in_period = 2) as agency_no_in_period'));

        $datas = DB::table('order as o')->rightJoin('region_view as rv', 'o.city','=','rv.region_name')
            ->rightJoin('agency_region_view as arv', 'rv.region_id', '=', 'arv.region_id')
            ->rightJoin('agency_view as av', 'arv.agency_id', '=', 'av.id')
            ->where($where1)
            ->whereNull('av.deleted_at')
            ->whereBetween('o.updated_at_last', [$start, $end])
            ->select(DB::raw('sum(o.type != 2) as cnt, sum(o.type = 2) as agency_cnt, av.`name`,
            SUM(o.type = 1  and o.is_agency = 0) as type_1,
            SUM(o.type = 2) as type_2 ,
            SUM(o.type = 3 or (o.type = 1 and o.is_agency = 1)) as type_3, 
            SUM(o.type != 2 and pay_amount = 0) as in_period, 
            SUM(o.type != 2 and pay_amount > 0) as no_in_period,
            SUM(o.type = 2 and o.in_period = 1) as agency_in_period, 
            SUM(o.type = 2 and o.in_period = 2) as agency_no_in_period'))
            ->union($first)
            ->groupBy('av.id')
            ->orderBy('cnt', 'DESC')
            ->get()
            ->toArray();
        $formatData = [];
        $i = 1;
        $cnt = array_column($datas, 'cnt');
        $in_period = array_column($datas, 'in_period');
        $no_in_period = array_column($datas, 'no_in_period');
        $agency_in_period = array_column($datas, 'agency_in_period');
        $agency_no_in_period = array_column($datas, 'agency_no_in_period');
        $type_1 = array_column($datas, 'type_1');
        $type_2 = array_column($datas, 'type_2');
        $type_3 = array_column($datas, 'type_3');
        $number = array_sum($cnt);
        $total_in_period = array_sum($in_period);
        $total_no_in_period = array_sum($no_in_period);
        $total_agency_in_period = array_sum($agency_in_period);
        $total_agency_no_in_period = array_sum($agency_no_in_period);
//        dd($data->in_period + $data->no_in_period);
//        dd($datas);
        foreach ($datas as $data){
            $in_period_data = $data->in_period + $data->no_in_period;
            $a = 0;
            $b = 0;
            if ($in_period_data != 0){
                $a = round($data->in_period*100/($data->in_period + $data->no_in_period), 2);
                $b = round($data->no_in_period*100/($data->in_period + $data->no_in_period), 2);
            }
//            dd($data);
            if ($data->cnt > 0 || $data->agency_cnt > 0){
                $row = [
                    $i,
                    $data->name,
                    $data->cnt,
                    round($data->cnt*100/$number, 2),
                    $data->in_period,
                    $a,
                    $data->no_in_period,
                    $b,
                    $data->type_1,
                    $data->type_3,
                    $data->type_2,
                    $data->agency_in_period,
                    $data->agency_no_in_period,
                ];
                $formatData[] = $row;
                $i++;
            }
        }
        if (count($formatData) > 0){
            $in_period_total = $data->in_period + $data->no_in_period;
            $c = 0;
            $d = 0;
            if ($in_period_total != 0){
                $c = round($total_in_period*100/($total_in_period+$total_no_in_period), 2);
                $d = round($total_no_in_period*100/($total_in_period+$total_no_in_period), 2);
            }
            $row_total = [
                '',
                '合计',
                $number,
                '100',
                $total_in_period,
                $c,
                $total_no_in_period,
                $d,
                array_sum($type_1),
                array_sum($type_3),
                array_sum($type_2),
                $total_agency_in_period,
                $total_agency_no_in_period,
            ];
            $formatData[] = $row_total;
        }

//        dd(count($formatData));
        return $formatData;
    }
}
