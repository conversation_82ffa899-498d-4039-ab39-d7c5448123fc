<?php

namespace App\Http\Middleware;

use Closure;
use App\Api\Permission\ApiPermission;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Tymon\JWTAuth\Facades\JWTAuth;

class ApiPermissionMiddleware {
    /**
     * @param $request
     * @param Closure $next
     * @param array ...$args
     * @return mixed
     */
    public function handle($request, Closure $next, ...$args) {
        //判断是否有token
        if (JWTAuth::getToken()) {
            $userId = JWTAuth::parseToken()->toUser()->id;
            $user = ApiPermission::find($userId);
            foreach ($args as $key => $value) {
                if ($user->can($value)) {
                    return $next($request);
                }
            }
            throw new AccessDeniedHttpException('Permission deny!');
        }
        return $next($request);
    }

}