<?php


namespace App\Admin\Controllers;

use App\Models\FaqCategory;
use App\Models\MachineCategory;
use App\User;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;
use App\Http\Controllers\Controller;
use Encore\Admin\Controllers\ModelForm;
use Encore\Admin\Form;
use function foo\func;
use phpDocumentor\Reflection\Types\Context;

class FaqCategoryController extends Controller
{
    use ModelForm;

    /**
     * Index interface.
     *
     * @return Content
     */
    public function index()
    {
        return Admin::content(function (Content $content) {

            $content->header("常见问题分类");
            $content->description('常见问题分类');
            $content->body($this->grid());
        });
    }

    public function edit($id){
        return Admin::content(function (Content $content) use ($id){
            $content->header('自检问题');
            $content->description('');

            $content->body($this->form($id)->edit($id));
        });
    }
    public function create(){
        return Admin::content(function (Content $content){
            $content->header('自检问题');
            $content->description('');

            $content->body($this->form());
        });
    }

    public function grid()
    {
//        $grid = new Grid(new FaqCategory);
//        $grid->id('ID')->sortable();
//        return $grid;
        return Admin::grid(FaqCategory::class, function (Grid $grid){

            $grid->filter(function ($filter){
               $filter->is('machine_category_id', '机型品类')->select(MachineCategory::where('visible', 1)->pluck('name', 'id'));
            });
            $grid->id('ID')->sortable();
            $grid->title('标题')->editable();
            $grid->description('描述')->editable();
            $grid->machine_category_id('机型品类')->display(function($value){
                if ($value) {
//                    dd($value);
                    return MachineCategory::find($value)->name;
                }
                return '无';
            });
            $grid->order('排序')->editable();
            $grid->visibility('是否可见')->switch();
            $grid->repair_visible('寄修可见')->switch();
            $grid->endpoint_visible('终端可见')->switch();
            $grid->tablet_visible('平板可见')->switch();
        });
    }

    public function form($id = null){
        return Admin::form(FaqCategory::class, function (Form $form) use ($id){
            $form->display('id');
            $form->text('title','标题')->rules('required');
            $form->text('description','描述');
            $form->switch('visibility', '是否可见')->default(0);
            $form->select('machine_category_id', '机型品类')
                ->options(MachineCategory::all()->pluck('name','id')->prepend('请选择', 0))
                ->rules('required');
            $form->text('order', '排序');
            $form->switch('repair_visible', '寄修可见')->default(0);
            $form->switch('endpoint_visible', '终端可见')->default(0);
            $form->switch('tablet_visible', '平板可见')->default(0);
        });
    }
}

