<?php

namespace App\Admin\Extensions;

use App\Traits\ExcelExportTrait;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\DB;

class EndpointAccountExporter extends AbstractExporter
{
    use ExcelExportTrait;

    public function export()
    {
        $agency = DB::table('agency')
            ->whereIn('id', [Input::get('top_agency'), Input::get('second_agency')])
            ->pluck('name')->toArray();
        $topAgency = isset($agency[0]) ? $agency[0] : '';
        $secondAgency = isset($agency[1]) ? $agency[1] : '';
        $filename = $topAgency . $secondAgency . '终端帐号列表';
        // 根据上面的数据拼接出导出数据
        $titles = ['一级代理', '二级代理', '所属省', '所属市', '负责人', '电话', '名称', '地址', '帐号', '初始密码'];
        $data = $this->getData();
        $formatData = [];
        if (!empty($data)) {
            foreach ($data as $key => $row) {
                if ($row['status'] == 0) {
                    continue;
                }
                $password = substr(md5($row['username'] . $row['eid']), 0, 8);
                $formatData[] = [
                    $row['top_agency'],
                    $row['second_agency'],
                    $row['province'],
                    $row['city'],
                    $row['manager'],
                    $row['phone'],
                    $row['endpoint_name'],
                    $row['address'],
                    $row['username'],
                    $password,
                ];
            }
        }
        array_unshift($formatData, $titles);
        ExcelExportTrait::exportToExcel($filename, $formatData);
    }
}